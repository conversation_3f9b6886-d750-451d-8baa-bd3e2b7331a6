.ast-error {
	padding: 20px;
	text-align: center;
}

.ast-newwin-container, .ast-samewin-container {
	float: right;
	margin-top: 8px;
	margin-right: 2px;
	width: 9px;
	height: 9px;
}
.ast-samewin-container {
	margin-top: 9px;
}

.ast-advising-table {
	margin-left: 6px;
	margin-bottom: 6px;
	line-height: 1.5em;
	/*font-size: 0.9em;*/
}

.ast-advising-table .label {
	color: var(--myasu-gray-80);
	padding-right: 8px;
	padding-bottom: 8px;
}

.ast-advising-table .advsched-button {
	color: #8C1D40;
	border: 1px solid ;
	padding: 3px;
	line-height: 2.5em;
	
}

.ast-advising-wall-of-text {
	font-size: .9em;
	color: #444;
	line-height: 1.3em;
	margin: 10px;
}

.ast-advising-wall-of-text a {
	color: var(--myasu-maroon);
	text-decoration: underline;
	border-bottom: none;
}

.ast-advising-wall-of-text a:hover {
	text-decoration: none;
}

.ast-advisor-image {
	display: inline-block;
	padding: 11px 11px 10px 6px;
} 

.ast-advisor-image img {
	width: 120px;
}

.ast-advisor-info {
	display: inline-block;
	vertical-align: top;
	padding: 11px 10px 15px 5px;
}

.ast-advisor-info-table {
	line-height: 1.5em;
	/*font-size: 0.9em;*/
}

.ast-advisor-info-table .label {
	color: var(--myasu-gray-80);
	padding-right: 8px;
	padding-bottom: 8px;
}

.ast-committeemembers-table {
	margin-left: 20px;
	line-height: 1.5em;
	/*font-size: 0.9em;*/
}

.ast-committeemembers-table .label {
	color: var(--myasu-gray-80);
}

.ast-coach-image {
	display: inline-block;
	padding: 15px 5px 15px 10px;
} 

.ast-coach-image img {
	width: 120px;
}

.ast-coach-image a {
	border: none ;
}

.ast-coach-info {
	display: inline-block;
	vertical-align: top;
	padding: 15px 10px 15px 5px;
}

.ast-coach-name {
	font-size: 1.2em;
	padding-bottom: 10px;
}

.ast-coach-name span {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
}

.ast-coach-info-table {
	line-height: 1.5em;
	/*font-size: 0.9em;*/
}

.ast-coach-info-table .label {
	color: var(--myasu-gray-80);
	padding-right: 15px;
	padding-bottom: 5px;
}

.ast-coach-info-table .advsched-button {
	color: #8C1D40;
	border: 1px solid;
	padding: 3px;
	line-height: 2.5em;
}

.ast-openchat-button {
	color: grey ;
	border: 1px solid ;
	padding: 3px 8px;
	line-height: 2.5em;
	background-color: #fcfcfc;
	border-radius: 2px;	
}

#acad-support-team-box hr {
	border: 1px solid #D0D0D0;
	margin-left: 6px;
}

.ast-bottom-margin-16 {
	margin-bottom: 16px;
}

.ast-coaching-peer-button-container {
	padding-bottom: 15px;
}

.ast-coaching-peer-text a {
	color: var(--myasu-maroon);
	text-decoration: underline;
	border-bottom: none;
}

.ast-coaching-peer-text a:hover {
	text-decoration: none;
}
