$(document).ready(function() {
	$.ajax({
		url: "showGraduationCongrats",
		success: function(data) {
			executeGradCongratsView(data);
		}
	});
	
	function executeGradCongratsView(data) {
		if (data.showGraduationCongratsBox) {
			$("#congrats-grad-content-year").html(data.graduationCongratsBoxYear);

			$("#congrats-grad-box").show();

			textFit($("#congrats-grad-content-name"), {minFontSize: 10, maxFontSize: 50, alignVert: true});
			textFit($("#congrats-grad-content-year"), {minFontSize: 14, maxFontSize: 35, alignVert: true});
			$( window ).resize(function() {
				textFit($("#congrats-grad-content-name"), {minFontSize: 10, maxFontSize: 50, alignVert: true});
				textFit($("#congrats-grad-content-year"), {minFontSize: 14, maxFontSize: 35, alignVert: true});
			});
			
			
			$("#congrats-grad-content-replay-container").show();
			
			var preloadConfettiImage = new Image();

			preloadConfettiImage.src = "images/congrats_confetti.gif";
			
			if (data.showGraduationCongratsConfetti) {
				preloadConfettiImage.onload = function() {
					$("#graduation-congrats-confetti-wrapper").show();
					$("#graduation-congrats-confetti-wrapper").css("background-image", "url(" + preloadConfettiImage.src + ")");
					
					enableConfettiRefresh(preloadConfettiImage);

					if (!$('#global-user-masqueraded').val()) {
						var ajaxData = {
							token: $("#grad-congrats-user-form-token").val(),
							func: "set-grad-congrats-confetti-viewed"
						};
						var options = {
							cache: false,
							url: "pref",
							type: "POST",
							data: ajaxData
						};
						options['error'] = function() {
							//console.log('An error occurred while trying to save when you saw your celebration. Please try again or check back later.');
						};
						$.ajax(options);
					}
				};
			} else {
				enableConfettiRefresh(preloadConfettiImage);
			}
		}
	}
	
	function enableConfettiRefresh(preloadConfettiImage) {
		setTimeout(function() {
			$("#congrats-grad-box").on("click", "#congrats-grad-content-replay", function() {
				$("#graduation-congrats-confetti-wrapper").show();
				$("#congrats-grad-box").off("click");
				$("#graduation-congrats-confetti-wrapper").css("background-image", "url(" + preloadConfettiImage.src + "?r=" + Math.random() + ")");
				
				setTimeout(function() {
					enableConfettiRefresh(preloadConfettiImage);
				}, 2000);
			});
		}, 2000);
	}
});