#nc_canvas {
	height: 57px;
	display: flex;
	-webkit-transition: background-color 500ms linear;
		-moz-transition: background-color 500ms linear;
		-o-transition: background-color 500ms linear;
		-ms-transition: background-color 500ms linear;
		transition: background-color 500ms linear;
}

#nc_carousel_view {
	margin-left: auto ;
	margin-right: auto ;
	position:relative;
	height:57px;
	width: 280px;
	overflow:hidden;
}

#nc_carousel_view .slide {
	position:absolute;
	top: 0;
	width:700px; height:250px;
	font-family: Arial, Verdana, Sans-Serif;
	font-weight: bold;
	font-size: 10px;
	letter-spacing: -1px;
	text-align: left;
}

.btn_pending {
	background-image: none !important;
}

#btn_bar {
	flex-shrink: 0;
	opacity: 0.75;
	background: white;
	width: 17px;
	height: 100%;
}

#btn_bar .btn {
	height: 33%;
	width: auto;
	background-repeat: no-repeat;
	background-color: #FFF;
	box-sizing: border-box;
	border: 3px solid #FFF;
	margin-right: -1px;
}

#btn_previous {
	background-image:url(../../resources/images/icon/carousel/previous.svg);
}

#btn_dismiss{
	background-image:url(../../resources/images/icon/carousel/thumbs-down.svg);
}

#btn_bar .btn_pause {
	background-image:url(../../resources/images/icon/carousel/pause.svg);
}

#btn_bar .btn_play {
	background-image:url(../../resources/images/icon/carousel/play.svg);
}
