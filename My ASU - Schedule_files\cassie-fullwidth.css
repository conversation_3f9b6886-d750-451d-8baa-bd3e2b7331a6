#cassieOptOut {
    background-color: #e8e8e8;
}
#cassieOptOut button#manualConsentoptout {
    color: #484848;
    margin-right: 1.5rem;
    text-decoration: none;
    border: none;
    padding: 0 0 1rem;
    background-color: transparent;
    width: auto;
}

.footer-legal-links #manualConsentoptout, .footer-menu #manualConsentoptout, .nav.colophon #manualConsentoptout, .nav #manualConsentoptout {
    color: #484848;
    border: none;
    background-color:transparent;
    width: auto;
    cursor: pointer;
    padding: 0;
    text-align: right;
    justify-self: end;
}
/**
* TODO: Should we compile this css and include it in our css bundle or just import it and add it into a script in webspark?
*/
/**
  * pre-banner styles
  */
.cassie-pre-banner, #cassie_gpc_pre_banner {
    align-items: center;
    width: 100% !important;
    border: 2px solid #D0D0D0 !important;
    bottom: 0px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
}
.cassie-cookie-module {
    top: 0;
}
.cassie-pre-banner .close-button, #cassie_gpc_pre_banner .close-button {
    background-color: #ffffff !important;
    color: #191919 !important;
    font-size: 0.8rem !important;
    padding: 0 !important;
    width: 1.75rem !important;
    height: 1.75rem !important;
    border: 1px solid #d0d0d0 !important;
    position: absolute;
    top: 1.25rem;
    right: 25px;
    color: #191919;
}

.cassie-cookie-module .cassie-bottom, .cassie-cookie-module #cassie_gpc_pre_banner.cassie-gpc-bottom {
    margin: 0;
    padding: 1rem 2rem;
    background-color: #E8E8E8!important;
    width: 100%;
    display: flex;
    flex-wrap: wrap !important;
    @media only screen and (max-width: 992px) {
        padding: 1rem 1rem 1rem 0;
    }
}
.cassie-gpc-pre-banner .cassie-gpc-pre-banner--text-container {
    margin-bottom: 0!important;
}
.cassie-gpc-pre-banner .cassie-gpc-back {
    bottom: 0;
    left: 0;
    position: relative;
    margin-left: 0;
}

button.cassie-pre-banner--button.cassie-view-all {
    background-color: black !important;
}

button.cassie-pre-banner--button.cassie-view-all svg {
    display: none;
}

.cassie-bottom .cassie-pre-banner--text, #cassie_gpc_pre_banner .cassie-gpc-pre-banner--text-container {
    flex: 1 1 300px;
    justify-content: center;
    font-size: 1rem;
    width: calc(40% - 40px);
}
#cassie_pre_banner_text .close-button i {
    font-family: "Font Awesome 5 Free"!important;
}
.cassie-pre-banner--button--container {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    flex: 1 1 400px;
    gap: 1rem;
}

.cassie-pre-banner > .cassie-pre-banner--text, #cassie_gpc_pre_banner > .cassie-pre-banner--text {
    padding: 1.25rem;
    margin: 0;
}

.cassie-pre-banner .cassie-pre-banner--button--container,  #cassie_gpc_pre_banner .cassie-pre-banner--button--container {
    margin: 0;
    padding-left: 1.25rem;
}

/* Don't allow drilldown into cookie script descriptions or show them */

.cassie-cookie-modal--center .cassie-close-modal-button.cassie-d-block {
    display:inline-block!important;
}
.cassie-close-modal--container .cassie-close-modal-button.cassie-d-none {
    display: inline-block!important;
}
[id^="cassie_expand_cookies_icon"] {
    fill: var(--expand-cookies-icon-color);
    fill: var(--banner-submit-preferences-button-color);
    padding-left: 0px;
}

.cassie-cookie-modal--group .cassie-cookie-children--container {
    display: none;
}

div.cassie-cookie-modal.cassie-cookie-modal--center {
    background-color: #ffffff;
    color: #191919;
    max-width: 800px;
}

.cassie-strictly-necessary--expand::after {
    content: 'Always On';
    position: absolute;
    right: 0;
    top: 25%;
}

#cassie_cookie_footer_content {
    margin-bottom: 0;
}

#cassie_cookie_footer_content > p {
    margin: 0 0 0 15px;
    padding-top: 32px;
}

.cookieHeadersmall {
    font-size: 16px;
    font-weight:normal;
}

.cassie-cookie-modal.cassie-cookie-modal--center > *:nth-child(1) {
    border-bottom: 1px solid #d0d0d0 !important;
}

/* Change borders and spacing around buttons */

.cassie-cookie-modal--center .cassie-toggle-switch--slider:before {
    left: 4px;
    bottom: 3px;
}
.cassie-cookie-modal--center .cassie-toggle-switch--slider--active:before {
    left: 20px;
}

.cassie-cookie-modal--top-header .cassie-close-modal-button {
    height: 41px;
    margin-top: 0px;
    line-height: normal;
}

.cassie-cookie-modal .cassie-cookie-modal--main {
    border: 0px solid var(--banner-secondary-color);
}

.cassie-cookie-module > .cassie-cookie-modal {
    border-right: unset;
}

#cassie_cookie_footer_content {
    margin-bottom: 0;
}

#cassie_cookie_footer_content > p {
    margin: 0 0 0 15px;
    padding-top: 32px;
}

.cookieHeadersmall {
    font-size: 16px;
    font-weight:normal;
}

.cassie-strictly-necessary--expand::after {
    content: 'Always On';
    position: absolute;
    right: 0;
    top: 25%;
}

.cassie-cookie-modal--footer .cassie-cookie-modal--footer-extra {
    padding-top: 30px;
}

.cassie-cookie-module p a {
    color: #8c1d40;
    text-decoration: underline;
}

.cassie-cookie-modal--footer > p {
    font-size: 0.75rem !important;
}

.cassie-cookie-modal--group,
.cassie-cookie-modal--select-group {
    border-bottom: 1px solid #d0d0d0 !important;
}

.cassie-cookie-modal--group-head-container .cassie-cookie-group--description {
    width: 100%;
}

/*Text font and remove unwanted items */
.cassie-cookie-modal--header .cassie-cookie-modal--header--content {
    border: 0px;
    color: #0f0f0f;
    word-spacing: normal;
    word-break: break-word;
    font-size: 1.25rem;
    font-weight: 700;
    font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans,
    sans-serif;
}

.cassie-cookie-module *,
.cassie-cookie-module button {
    font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans,
    sans-serif !important;
    box-sizing: border-box;
}

.cassie-cookie-module p,
.cassie-cookie-module div[role="switch"] {
    font-weight: 400;
}

.cassie-expand-cookies--container .cassie-cookie-group--heading {
    font-size: 20px;
    line-height: 1.6;
}

.cassie-cookie-modal--footer-image--container {
    display: none !important;
}

.cassie-cookie-modal--header .cassie-cookie-modal--accept-all {
    display: none;
}

.cassie-cookie-modal--save-preferences--container .cassie-cookie-modal--save-preferences {
    width: 10rem;
    margin: 0px 15px 0px 15px;
}

.cassie-cookie-modal--group-head-container .cassie-cookie-group--description {
    display: flex;
}

.cassie-cookie-modal .cassie-cookie-modal--footer {
    margin-top: unset;
}

.cassie-cookie-module > .cassie-cookie-modal--center {
    height: auto;
    max-height: 80%;
    width: 30%;
    margin: auto;
}

.cassie-cookie-modal--group-head-container .cassie-cookie-group--description {
    display: none;
}

.cassie-cookie-modal--group-head-container .cassie-expandable-description--show {
    display: flex;
    font-size: 16px;
    flex-direction:column;
}

.cassie-strictly-necessary--description.cassie-cookie-group--description.cassie-expand-cookies--icon--open {
    display: block;
    font-size: 16px;
}

.cassie-expand-cookies--container span > svg.cassie-expand-cookies--icon {
    width: 1.25rem;
    height: 1.25rem;
}

.cassie-cookie-module button {
    border-radius: 400rem !important;
    border: none !important;
    background-color: #8c1d40 !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
    font-weight: 700 !important;
    white-space: nowrap;
    padding: 0.65rem 1rem !important;
    width: fit-content !important;
    font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans,
    sans-serif !important;
}

.cassie-cookie-module button:hover {
    transform: scale(1.05) !important;
    opacity: 1 !important;
    cursor: pointer;
    outline: 0px !important;
}
.cassie-cookie-module button:focus,
.cassie-cookie-module *[role="button"]:focus,
.cassie-cookie-module a:focus,
.cassie-cookie-module *[role="switch"]:focus {
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px #191919 !important;
    outline: none !important;
}

.cassie-pre-banner .cassie-view-all {
    margin-top: 10px !important;
}

.cassie-strictly-necessary--description.cassie-cookie-group--description {
    display: none;
}

.cassie-cookie-modal--center .cassie-toggle-switch--slider {
    left: -35px;
    right: 40px;
    background-color: #d0d0d0;
}

.cassie-cookie-modal--center .cassie-toggle-switch--slider--active {
    background-color: #78be20;
}

.cassie-cookie-modal--center .cassie-toggle-switch--status {
    right: -20px;
}

.cassie-cookie-module > .cassie-pre-banner {
    border-radius: 0 !important;
    align-self: end;
}

/* Mobile and other devices */

@media only screen and (max-width: 1260px) {
    .cassie-pre-banner--text {
        width: calc(100% - 40px) !important;
        margin-bottom: 1rem;
    }

    .cassie-bottom .cassie-pre-banner--button--container {
        min-width: calc(100% - 40px) !important;
    }
    .cassie-pre-banner .close-button {
        top: 2.25rem;
    }
}
@media only screen and (min-width: 768px) and (max-width: 1023px) and (orientation: landscape) {
    .cassie-cookie-module > .cassie-cookie-modal--center {
        width: 50%;
        margin-top: 1%;
        margin-bottom: 1%;
    }
}

@media only screen and (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
    .cassie-cookie-module > .cassie-cookie-modal--center {
        height: auto;
        max-height: 80%;
    }
}

/* Phones */
@media only screen and (min-width: 375px) and (max-width: 767px) and (orientation: landscape) {
    .cassie-cookie-module > .cassie-cookie-modal--center {
        width: 50%;
        margin-top: 1%;
        margin-bottom: 1%;
        height: auto;
        max-height:80%;
    }
}

@media only screen and (min-width: 375px) and (max-width: 767px) and (orientation: portrait) {
    .cassie-cookie-module>.cassie-cookie-modal--center {
        height: auto;
        max-height:80%;
    }
    .cassie-strictly-necessary--expand {
        width: 73%;
    }
    .cassie-strictly-necessary--expand::after {
        right: -30%;
    }
}

@media only screen and (min-width: 768px) {

    .cassie-cookie-modal--group-head-container .cassie-expand-cookies--container.cassie-strictly-necessary--expand {
        width: 100%;
    }
}

@media only screen and (min-width: 200px) {

    .cassie-cookie-modal .cassie-cookie-modal--footer {
        margin-top: inherit;
    }

    .cassie-cookie-modal--group-head-container .cassie-expand-cookies--container.cassie-strictly-necessary--expand {
        position: relative;
    }

    .cassie-cookie-modal--footer-extra
    .cassie-cookie-modal--save-preferences--container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .cassie-cookie-modal--save-preferences--container
    .cassie-cookie-modal--save-preferences {
        width: 90%;
    }

    .cassie-cookie-modal--group-head-container .cassie-expand-cookies--container {
        width: 73%;
    }

    .cassie-cookie-module > .cassie-cookie-modal--center {
        width: 90%;
    }
}
