function initMyAsuModal(elementSelector, options) {
	var modal = document.getElementById(elementSelector);
	var modalClose = null;

	if (options.closeElementSelector != null) {
		modalClose = document.getElementById(options.closeElementSelector);

		if (options.closeTrackingPath != null) {
			modalClose.dataset.tracking = "customize/customize-background/close";
		}
	}

	if (modalClose != null) {
		modalClose.onclick = function() {
			modal.style.display = "none";
		}
	}

	if (options.clickModalToClose == null || options.clickModalToClose) {
		window.addEventListener("mousedown", function(event) {
			if (event.target == modal) {
				modal.style.display = "none";
			}
		});
	}

	return modal;
}