$(document).ready(function() {
	$('.task-dismiss-button').click(dismissTodo);
	
	$(".task-summary").keydown(function(event) {
		if (event.which === 13 || event.which === 32) {
			event.preventDefault();
			var taskNum = $(this).data("task-num");
			$("#task-details-" + taskNum).slideToggle();
			toggleCaret($("#task-drawer-caret-" + taskNum));
		}
	});

	$(".task-summary").click(function() {
		$(".task-summary").removeClass("task-summary-focused");
		var taskNum = $(this).data("task-num");
		$("#task-details-" + taskNum).slideToggle();
		toggleCaret($("#task-drawer-caret-" + taskNum));
	});
});

//check if task has been tabbed into
$(window).keyup(function (e) {
    var code = (e.keyCode ? e.keyCode : e.which);
    
    if (code == 9) {
    	var tasks = document.querySelectorAll(".task-summary");

    	if (tasks != null) {
	    	for (var i = 0; i < tasks.length; i++) {
	    		if (document.activeElement == tasks[i]) {
	    			tasks[i].classList.add("task-summary-focused");
	    		} else {
	    			tasks[i].classList.remove("task-summary-focused");
	    		}
	    	}
    	}
    }
});

function toggleCaret(caretId) {
	if ($(caretId).children(".fa-chevron-down").length) {
		$(caretId).html('<i class="fas fa-chevron-up"></i>');
	} else {
		$(caretId).html('<i class="fas fa-chevron-down"></i>');	
	}
}

function dismissTodo() {
	var dismissButton = $(this);
	var task = dismissButton.closest('.task');
	var processingMsg = dismissButton.parent().find('.task-dismiss-processing');
	var errorMsg = dismissButton.parent().find('.task-dismiss-error');

	dismissButton.hide();

	if ($('#global-user-masqueraded').val()) {
		errorMsg.show();
	} else {
		processingMsg.show();

		var ajaxData = {
			token: $('#task-dismiss-csrf-token').val(), 
			sequence3c: task.attr('data-sequence3c'),
			checklistSequence: task.attr('data-checklistSequence'),
			checklistItemCode: task.attr('data-checklistItemCode'),
		};

		$.ajax({
			cache: false,
			url: "task-action/dismiss",
			type: "POST",
			data: ajaxData,
			error: function() {
				processingMsg.hide();
				errorMsg.show();
			},
			success: function(data) {
				if (data.success) {
					var taskCount = task.siblings().length;
					task.find('.task-details').slideToggle();
					setTimeout(function() {
						task.remove();
						if (taskCount <= 1) {
							$('#tasks-no-tasks-msg').show();
						}
					}, 500);
				} else {
					processingMsg.hide();
					errorMsg.show();
				}
			},
		});
	}

	return false;
}
