$(document).ready(function() {
	$.ajax({
		url: "customizeMyAsu",
		success: function(data) {
			var customizeMyAsuShortcutsContainerTemplate = $("#customize_myasu_shortcuts_container_tpl").html();

			if (customizeMyAsuShortcutsContainerTemplate != null) {
				Mustache.parse(customizeMyAsuShortcutsContainerTemplate);
				$("#quicklinks-modal").html(Mustache.render(customizeMyAsuShortcutsContainerTemplate, data));
				
				quicklinksPageInit();
	
				initializeTablist("customize-myasu-tab-pane");
			}
		}
	});

	var options = {
		//closeElementSelector: "customize-background-modal-close",
		//closeTrackingPath: "customize/customize-background/close",
		clickModalToClose: false
	};
	
	var customizeBackgroundModal = initMyAsuModal("customize-background-modal", options);
	
	$(document).on("click", "#customize-myasu-button", function(event) {
		event.preventDefault();
		customizeBackgroundModal.style.display = "block";
	});
	
	$(document).on("click", "#customize-myasu-background-cancel", function() {
		customizeBackgroundModal.style.display = "none";

		resetCustomBackground();
	});

	$(document).on("click", "#customize-myasu-background-save", function() {
		customizeBackgroundModal.style.display = "none";

		saveMyAsuCustomization();
	});

	$(document).on("click", ".background-container", function() {
		$(".background-container").removeClass("background-container-selected");
		$(this).addClass("background-container-selected");

		var type = $(".background-container-selected").data("type");
		var value = $(".background-container-selected").data("value");
		var backgroundValue = $(".background-container-selected").data("background-value");

		if (type == "SOLID") {
			$("#myasu-main-content").css("background", backgroundValue);
			$("#myasu-main-content").css("background-repeat", "repeat");
			$("#myasu-main-content").css("background-attachment", "scroll");
			$("#myasu-main-content").css("background-size", "auto");
		} else if (type == "PHOTOGRAPHY" || type == "ABSTRACT_PATTERNS") {
			$("#myasu-main-content").css("background", "url(" + backgroundValue + ")");
			$("#myasu-main-content").css("background-repeat", "no-repeat");
			$("#myasu-main-content").css("background-attachment", "fixed");
			$("#myasu-main-content").css("background-size", "cover");
		}
	});
	
	$(document).on("click", "#customize-background-modal-close", function() {
		resetCustomBackground();
	});
	
	$(document).on("click", "#customize-myasu-shortcuts-cancel", function() {
		customizeBackgroundModal.style.display = "none";

		resetCustomBackground();
	});

	$(document).on("click", "#customize-myasu-shortcuts-save", function() {
		customizeBackgroundModal.style.display = "none";

		saveMyAsuCustomization();
	});
	
	$("#customize-background-modal").on("focus", "#customize-myasu-shortcuts-tab-guard", function() {
		$("#bookmarks-show-icons-checkbox").focus();
	});
});

function saveMyAsuCustomization() {
	var type = $(".background-container-selected").data("type");
	var value = $(".background-container-selected").data("value");
	var backgroundValue = $(".background-container-selected").data("background-value");

	var ajaxData = {
		token: $("#customize-background-user-form-token").val(),
		func: "set-custom-background",
		type: type,
		value: value
	};
	var options = {
		cache: false,
		url: "pref",
		type: "POST",
		data: ajaxData 
	};
	options['success'] = function() {
		if (type == "SOLID") {
			$("#myasu-main-content").css("background", backgroundValue);
			$("#myasu-main-content").css("background-repeat", "repeat");
			$("#myasu-main-content").css("background-attachment", "scroll");
			$("#myasu-main-content").css("background-size", "auto");
		} else if (type == "PHOTOGRAPHY" || type == "ABSTRACT_PATTERNS") {
			$("#myasu-main-content").css("background", "url(" + backgroundValue + ")");
			$("#myasu-main-content").css("background-repeat", "no-repeat");
			$("#myasu-main-content").css("background-attachment", "fixed");
			$("#myasu-main-content").css("background-size", "cover");
		}

		$("#current-user-custom-background-type").val(type);
		$("#current-user-custom-background-value").val(value);
		$("#current-user-custom-background-value-background-value").val(backgroundValue);
	}
	options['error'] = function() {
		console.log('An error occurred while trying to save your custom background. Please try again or check back later.');
	};

	$.ajax(options);
}

function resetCustomBackground() {
	var type = $("#current-user-custom-background-type").val();
	var value = $("#current-user-custom-background-value").val();
	var backgroundValue = $("#current-user-custom-background-value-background-value").val();
	
	$(".background-container").removeClass("background-container-selected");
	
	if (type != null && value != null && backgroundValue != null) {
		if (type == "SOLID") {
			$("#myasu-main-content").css("background", backgroundValue);
			$("#myasu-main-content").css("background-repeat", "repeat");
			$("#myasu-main-content").css("background-attachment", "scroll");
			$("#myasu-main-content").css("background-size", "auto");
		} else if (type == "PHOTOGRAPHY" || type == "ABSTRACT_PATTERNS") {
			$("#myasu-main-content").css("background", "url(" + backgroundValue + ")");
			$("#myasu-main-content").css("background-repeat", "no-repeat");
			$("#myasu-main-content").css("background-attachment", "fixed");
			$("#myasu-main-content").css("background-size", "cover");
		}
		
		$(".background-container").each(function() {
			var thisBackgroundType = $(this).data("type");
			var thisBackgroundValue = $(this).data("value");
			
			if (thisBackgroundType == type && thisBackgroundValue == value) {
				$(this).addClass("background-container-selected");
			}
		});
	} else {
		$("#myasu-main-content").css("background", "#FFFFFF");
		$("#myasu-main-content").css("background-repeat", "repeat");
		$("#myasu-main-content").css("background-attachment", "scroll");
		$("#myasu-main-content").css("background-size", "auto");
		
		$(".background-container").each(function() {
			var thisBackgroundType = $(this).data("type");
			var thisBackgroundValue = $(this).data("value");
			
			if (thisBackgroundType == "SOLID" && thisBackgroundValue == "WHITE") {
				$(this).addClass("background-container-selected");
			}
		});
	}

	$('#add_bookmark_panel').hide();
	$('#edit_bookmark_panel').hide();
	$("#bookmarks-add-bookmark-container").show();
	$("#modal-quicklinks-table").show();
}