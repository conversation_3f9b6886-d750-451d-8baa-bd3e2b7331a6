var MyASUJS={sideMenuOpen:false,sideMenuCloseOverride:false,setupQtip:function(c,b){var a=null;if(b.at!=undefined&&b.my!=undefined){a.at=b.at;a.my=b.my;}var d=null;if(b.text!=undefined||b.location!=undefined){if(b.text){d.text=b.text;}else{d.location=b.attr;}}$(c).each(function(){$(this).qtip({content:{text:b.text},position:{my:b.my,at:b.at,target:c,adjust:{x:b.adjx,y:b.adjy}},show:{event:"mouseenter",delay:200},hide:false,style:"ui-tooltip-rounded"});});},qTipUtil:function(a){if(a.event==="open"){this.sideMenuCloseOverride=true;}if(a.event==="close"){this.sideMenuCloseOverride=false;$(document).focus();}},setDefaults:function(){$.fn.qtip.zindex=70;$.fn.qtip.defaults=$.extend(true,{},$.fn.qtip.defaults,{style:{classes:"ui-tooltip-asu-theme",tip:{corner:true,mimic:"center",offset:10,width:24,height:15}},show:{event:"click"},hide:{event:"unfocus",fixed:true},position:{adjust:{method:"flip none",y:-5},viewport:$(window),my:"right top",at:"left top"}});},position:{left:{at:"left top",my:"right top"},right:{at:"right top",my:"left top"},bottom:{at:"bottom center",my:"top center",offset:0}}};