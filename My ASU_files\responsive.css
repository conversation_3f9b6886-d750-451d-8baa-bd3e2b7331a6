@media (max-width: 990px) {
	/* asuthemes breakpoint */
	
	/* Overrides asuthemes */
	#blackOut {
		z-index: 99;
	}
}

@media (max-width: 990px) {
	/* Navbar disappear breakpoint */
	#headerContainer .css-10dnl2k nav.header-nav {
		width: 100%;
	}
	#myasu-header-top-container {
		display: none;
	}

	.guest-navbar-nav-container {
		padding: 8px 24px 0px 24px;
	}
	
	#myasu-main-container {
		padding: 0 10px;
	}
	
	.asu_container {
		min-width: 0;
	}
	
	#myasu-header, #myasu-footer {
		padding-left: 0;
		padding-right: 0;
		min-width: 0;
	}
	
	.column-right
	, .column-left {
		min-width: 0;
	}

	.nagMessage .nagMessageContainer {
		width: 100%;
	}
}

@media (max-width: 950px) {
	/* Quicklinks disappear breakpoint */

	#quicklinks, .column-container .column-container-row .first-column {
		display: none;
	}
	
	.column-container .content-column {
		padding-left: 0;
	}
	
	.ui-tooltip-asu-notification {
		display: none !important;
	}
}

@media (max-width: 890px) {
	/* Single column viewport breakpoint */
	
	body {
		font-size: 0.875rem;
	}
	
	#myasu-main-container {
		padding: 0 20px;
	}
	
	#single-column-boxes-container {
		/* max-width: 624px; */
	}
	
	
	.myasu-container {
		min-width: 320px;
		max-width: none;
		margin-right: 0;
		margin-left: 0;
		padding-right: 0;
		padding-left: 0;
	}
	
	#myasu-header
	, #myasu-footer {
		min-width: 0;
	}
	
	.box {
		margin-bottom: 2em;
		/* box-shadow: 0 3px 8px 0 #cfcfcf; */ 
	}
	
	.column-container .content-column {
		padding: 0;
		margin: 0;
	}

	#myasu-main-top #myasu-main-top-button-container {
		display: none;
	}
	
	.shortcuts-box {
		display: block;
	}
	
	#single-column-boxes-container > .box {
		width: auto;
		max-width: none;
	}
	
	.bottom-utility {
		font-size: 0.857em; /* 12px */
	}
	
	table.class-schedule {
		font-size: 0.857em; /* 12px */
	}
	
	#account-charges, #financial-aid, #account-calendar-merged {
		min-width: 0;
	}
	
	.mycollege {
	    max-height: 450px;
	}
	.mycollege .tab-page {
	    max-height: 390px;
	}
	
	#myclasses-sched .class-title {
		margin-right: 0px;
	}
	
	#myclasses-sched .class-remote-classification-label {
		display: block;
		margin-top: 20px;
	}
	
	.schedule-page .class-title {
		margin-right: 0px;
	}
}

/* Responsive schedule table styling */
@media (max-width: 600px) {
	
	table.card-collapse thead {
		display: none;
	}
	
	table.card-collapse > tbody {
		display: block;
		border-bottom: 1px solid #ccc;
		padding-bottom: 1em;
	}

	table.card-collapse > tbody > tr {
		display: block;
		background-color: inherit;	
	}
	
/* 	table.class-schedule.card-collapse > tbody:not(.even) > tr {
		background-color: #fff;
	} */
		
	table.card-collapse tr {
		/* margin: 1em; */
		/* border-top: 1px solid #ddd; */
	}
	
	table.card-collapse > tbody > tr > td {
		display: block;
		height: inherit;
		text-align: left;
	}
	
	table.class-schedule {
		font-size: 1em;
	}
		
/* 	table.class-schedule.card-collapse > tbody:not(.total) > tr > td:not([rowspan]) {
		margin-left: 2em;
		border-left: 1px solid #999;
	} */
	
	table.class-schedule.card-collapse > tbody > tr:nth-child(1n+2) {
		margin-top: 1em;
		margin-bottom: 1em;
	}
		
	table.card-collapse td.numerical {
		text-align: inherit;
		padding-right: inherit;
	}

	table.card-collapse > tbody > tr > td:not(:empty):before {
		content: attr(data-label) ":";
		font-weight: 700;
		font-weight: var(--myasu-font-bold);
		margin-right: 0.5em;
	}

/* 	table.class-schedule.card-collapse > tbody:not(.total) > tr > td:not(:empty):before {
		content: attr(data-label) ":";
		font-weight: 500;
		margin-right: 0.5em;
	} */
	
/* 	table.card-collapse > tbody.total td {
		margin: 0.5em;
	} */
	
	table.card-collapse table.promod-child-subtable tbody tr {
		background-color: inherit;
	}
	
	table.card-collapse .card-collapse-hidden {
		display: none;
	}

	#ie_sunset_alert .alert_text {
	    width: 100%;
	}
}

@media (max-width: 540px) {
	/* SMALL viewport */
	
	body {
		font-size: 1rem;
	}
	
	#myasu-main-container {
		padding: 0 10px;
	}
	
	tbody tr:nth-child(even) {
		background-color: #eee;
	}
	
	.bottom-utility {
		font-size: 0.75em; /* 12px */
	}
	
	.splitcontentleft, .splitcontentright {
		display: block;
		float: none;
		width: auto;
		margin: auto;
	}

	.splitcontentleft > ul.link-farm-list {
		margin-left: auto;
	}
	
	.splitcontentright > ul.link-farm-list {
		margin-right: auto;
	}
	
	#myclasses-back-link, #myclasses-next-link {
		/* display: none; */
	}
	
	#classes_content button[role=tab]:not(.box-tab-selected) {
		display: none;
	}
	
	.schedule button[role=tab]:not(.box-tab-selected) {
		display: none;
	}
	
	#myclasses-sched .tab:not(.selected) {
		display: none;
	}
	
	#myclasses-sched .tab.selected {
		width: 8em;
		text-align: center;
	}
	
	#congrats-grad-content-name {
	    min-height: 25px;
	}
	
	#congrats-grad-content-year {
	    min-height: 18px;
	}
	
	.admit-content-text-container {
	    padding: 4px 0px;
	}
	#events_container_cal {
		display: block;
		justify-content: normal;
	}
}

@media (max-width: 580px) {
	
	#financial-aid-box .fa-tracker-label
	, #financial-aid-box .progress-bar
	, #financial-aid-box .fa-tracker-steps-summary-background {
		display: none;
	}
	
	#financial-aid-box .fa-tracker-steps-summary-container
	, #financial-aid-box .fa-tracker-steps-summary {
		display: block;
	}
}
