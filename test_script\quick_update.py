#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASU学生信息快速更新脚本 - 简化版
支持预设配置，快速切换学生信息
"""

import json
import os
from update_student_info import update_my_asu_html, update_my_asu_schedule_html

# 预设学生信息配置
PRESETS = {
    "1": {
        "name": "<PERSON><PERSON>",
        "id": "12847593",
        "description": "<PERSON><PERSON> (原始数据)"
    },
    "2": {
        "name": "<PERSON>", 
        "id": "1239458054",
        "description": "<PERSON> (测试数据)"
    },
    "3": {
        "name": "<PERSON>",
        "id": "98765432", 
        "description": "<PERSON> (示例数据)"
    },
    "4": {
        "name": "<PERSON>",
        "id": "11223344",
        "description": "<PERSON> (示例数据)"
    }
}

def show_presets():
    """显示预设配置"""
    print("\n可用的预设配置:")
    print("-" * 40)
    for key, preset in PRESETS.items():
        print(f"{key}. {preset['description']}")
        print(f"   姓名: {preset['name']}")
        print(f"   学号: {preset['id']}")
        print()

def get_current_info():
    """尝试从文件中获取当前学生信息"""
    try:
        # 从My ASU.html中读取当前信息
        with open("My ASU.html", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的正则匹配来获取当前信息
        import re
        
        # 查找学号
        id_match = re.search(r'<div class="user-info-profile-information-value">(\d+)</div>', content)
        current_id = id_match.group(1) if id_match else "未知"
        
        # 查找姓名
        name_match = re.search(r'<span class="name">([^<]+)</span>', content)
        current_first = name_match.group(1) if name_match else "未知"
        
        # 查找完整姓名
        full_name_match = re.search(r'data-tracking="profilename">([^<]+)<', content)
        current_full_name = full_name_match.group(1) if full_name_match else f"{current_first} 未知"
        
        return current_full_name, current_id
        
    except Exception as e:
        return "未知", "未知"

def main():
    """主函数"""
    print("=" * 50)
    print("ASU学生信息快速更新脚本 - 简化版")
    print("=" * 50)
    
    # 显示当前信息
    current_name, current_id = get_current_info()
    print(f"\n当前学生信息:")
    print(f"  姓名: {current_name}")
    print(f"  学号: {current_id}")
    
    # 显示预设配置
    show_presets()
    
    print("选择操作:")
    print("1-4: 使用预设配置")
    print("c: 自定义输入")
    print("q: 退出")
    
    choice = input("\n请选择 (1-4/c/q): ").strip().lower()
    
    if choice == 'q':
        print("退出程序")
        return
    
    if choice in PRESETS:
        # 使用预设配置
        new_preset = PRESETS[choice]
        new_name = new_preset["name"]
        new_id = new_preset["id"]
        print(f"\n选择了预设: {new_preset['description']}")
    elif choice == 'c':
        # 自定义输入
        print("\n请输入新学生信息:")
        new_name = input("新姓名 (格式: First Last): ").strip()
        new_id = input("新学号: ").strip()
        
        # 验证输入
        if not all([new_name, new_id]):
            print("✗ 错误: 所有字段都必须填写")
            return
        
        if len(new_name.split()) != 2:
            print("✗ 错误: 姓名格式必须是 'First Last'")
            return
    else:
        print("✗ 无效选择")
        return
    
    # 确认更新
    print(f"\n即将进行以下更新:")
    print(f"  当前: {current_name} (ID: {current_id})")
    print(f"  更新为: {new_name} (ID: {new_id})")
    
    confirm = input("\n确认更新? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消更新")
        return
    
    # 定义文件路径
    files_to_update = [
        ("My ASU.html", update_my_asu_html),
        ("My ASU - Schedule.html", update_my_asu_schedule_html)
    ]
    
    # 更新文件
    print("\n开始更新文件...")
    success_count = 0
    
    for file_name, update_func in files_to_update:
        if os.path.exists(file_name):
            if update_func(file_name, current_name, new_name, current_id, new_id):
                success_count += 1
        else:
            print(f"✗ 文件不存在: {file_name}")
    
    # 总结
    print(f"\n更新完成! 成功更新 {success_count}/{len(files_to_update)} 个文件")
    
    if success_count == len(files_to_update):
        print("✓ 所有文件更新成功!")
    else:
        print("⚠ 部分文件更新失败，请检查错误信息")

if __name__ == "__main__":
    main()
