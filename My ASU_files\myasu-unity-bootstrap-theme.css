/*!************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/css-loader/dist/cjs.js??ruleSet[1].rules[0].use[1]!./node_modules/postcss-loader/dist/cjs.js??ruleSet[1].rules[0].use[2]!./node_modules/sass-loader/dist/cjs.js??ruleSet[1].rules[0].use[3]!./src/scss/unity-bootstrap-theme.scss ***!
  \************************************************************************************************************************************************************************************************************************************************************/
@charset "UTF-8";
/*
  TODO: all imports below belong to the previous bootstrap 4 package, you have to check each import and be careful when removing it.
*/
:focus {
  outline: 0;
  box-shadow: 0 0 8px #00baff !important;
}

/* body {
  font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans, sans-serif;
  font-weight: 400;
  color: #191919;
}

.text-white {
  color: #e8e8e8 !important;
}

.text-underline {
  text-decoration: underline;
}

.text-capitalize {
  text-transform: capitalize !important;
}

u,
ins {
  text-decoration-line: none;
  font-style: italic;
}

abbr {
  text-decoration-line: none !important;
  text-decoration-style: none !important;
  text-decoration-color: initial !important;
}

b,
strong {
  font-weight: 700 !important;
}
*/

/*!
 * Bootstrap  v5.3.0 (https://getbootstrap.com/)
 * Copyright 2011-2023 The Bootstrap Authors
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */
:root,
[data-bs-theme=light] {
  --bs-gold: #ffc627;
  --bs-maroon: #8c1d40;
  --bs-white: #ffffff;
  --bs-bluefocus: #00baff;
  --bs-darkgold: #7f6227;
  --bs-darkmaroon: #440e22;
  --bs-gray-1: #fafafa;
  --bs-gray-2: #e8e8e8;
  --bs-gray-3: #d0d0d0;
  --bs-gray-4: #bfbfbf;
  --bs-gray-5: #747474;
  --bs-gray-6: #484848;
  --bs-gray-7: #191919;
  --bs-gold: #ffc627;
  --bs-maroon: #8c1d40;
  --bs-success: #78be20;
  --bs-info: #00a3e0;
  --bs-warning: #ff7f32;
  --bs-danger: #cc2f2f;
  --bs-light: #e8e8e8;
  --bs-gray: #bfbfbf;
  --bs-dark: #191919;
  --bs-gray-1: #fafafa;
  --bs-gray-2: #e8e8e8;
  --bs-gray-3: #d0d0d0;
  --bs-gray-4: #bfbfbf;
  --bs-gray-5: #747474;
  --bs-gray-6: #484848;
  --bs-gray-7: #191919;
  --bs-gold-rgb: 255, 198, 39;
  --bs-maroon-rgb: 140, 29, 64;
  --bs-success-rgb: 120, 190, 32;
  --bs-info-rgb: 0, 163, 224;
  --bs-warning-rgb: 255, 127, 50;
  --bs-danger-rgb: 204, 47, 47;
  --bs-light-rgb: 232, 232, 232;
  --bs-gray-rgb: 191, 191, 191;
  --bs-dark-rgb: 25, 25, 25;
  --bs-gray-1-rgb: 250, 250, 250;
  --bs-gray-2-rgb: 232, 232, 232;
  --bs-gray-3-rgb: 208, 208, 208;
  --bs-gray-4-rgb: 191, 191, 191;
  --bs-gray-5-rgb: 116, 116, 116;
  --bs-gray-6-rgb: 72, 72, 72;
  --bs-gray-7-rgb: 25, 25, 25;
  --bs-primary-text-emphasis: #380c1a;
  --bs-secondary-text-emphasis: #664f10;
  --bs-success-text-emphasis: #304c0d;
  --bs-info-text-emphasis: #00415a;
  --bs-warning-text-emphasis: #663314;
  --bs-danger-text-emphasis: #521313;
  --bs-light-text-emphasis: #495057;
  --bs-dark-text-emphasis: #495057;
  --bs-primary-bg-subtle: #e8d2d9;
  --bs-secondary-bg-subtle: #fff4d4;
  --bs-success-bg-subtle: #e4f2d2;
  --bs-info-bg-subtle: #ccedf9;
  --bs-warning-bg-subtle: #ffe5d6;
  --bs-danger-bg-subtle: #f5d5d5;
  --bs-light-bg-subtle: #fcfcfd;
  --bs-dark-bg-subtle: #ced4da;
  --bs-primary-border-subtle: #d1a5b3;
  --bs-secondary-border-subtle: #ffe8a9;
  --bs-success-border-subtle: #c9e5a6;
  --bs-info-border-subtle: #99daf3;
  --bs-warning-border-subtle: #ffccad;
  --bs-danger-border-subtle: #ebacac;
  --bs-light-border-subtle: #e9ecef;
  --bs-dark-border-subtle: #adb5bd;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-font-sans-serif: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans, sans-serif;
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans, sans-serif;
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #191919;
  --bs-body-color-rgb: 25, 25, 25;
  --bs-body-bg: #ffffff;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-emphasis-color: #000;
  --bs-emphasis-color-rgb: 0, 0, 0;
  --bs-secondary-color: rgba(25, 25, 25, 0.75);
  --bs-secondary-color-rgb: 25, 25, 25;
  --bs-secondary-bg: #e9ecef;
  --bs-secondary-bg-rgb: 233, 236, 239;
  --bs-tertiary-color: rgba(25, 25, 25, 0.5);
  --bs-tertiary-color-rgb: 25, 25, 25;
  --bs-tertiary-bg: #f8f9fa;
  --bs-tertiary-bg-rgb: 248, 249, 250;
  --bs-heading-color: inherit;
  --bs-link-color: #8c1d40;
  --bs-link-color-rgb: 140, 29, 64;
  --bs-link-decoration: underline;
  --bs-link-hover-color: #8c1d40;
  --bs-link-hover-color-rgb: 140, 29, 64;
  --bs-link-hover-decoration: none;
  --bs-code-color: #d63384;
  --bs-highlight-bg: #fff3cd;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #dee2e6;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-xxl: 2rem;
  --bs-border-radius-2xl: var(--bs-border-radius-xxl);
  --bs-border-radius-pill: 50rem;
  --bs-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
  --bs-box-shadow-inset: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-focus-ring-width: 0.25rem;
  --bs-focus-ring-opacity: 0.25;
  --bs-focus-ring-color: rgba(140, 29, 64, 0.25);
  --bs-form-valid-color: #78be20;
  --bs-form-valid-border-color: #78be20;
  --bs-form-invalid-color: #cc2f2f;
  --bs-form-invalid-border-color: #cc2f2f;
}

[data-bs-theme=dark] {
  color-scheme: dark;
  --bs-body-color: #adb5bd;
  --bs-body-color-rgb: 173, 181, 189;
  --bs-body-bg: #212529;
  --bs-body-bg-rgb: 33, 37, 41;
  --bs-emphasis-color: #ffffff;
  --bs-emphasis-color-rgb: 255, 255, 255;
  --bs-secondary-color: rgba(173, 181, 189, 0.75);
  --bs-secondary-color-rgb: 173, 181, 189;
  --bs-secondary-bg: #343a40;
  --bs-secondary-bg-rgb: 52, 58, 64;
  --bs-tertiary-color: rgba(173, 181, 189, 0.5);
  --bs-tertiary-color-rgb: 173, 181, 189;
  --bs-tertiary-bg: #2b3035;
  --bs-tertiary-bg-rgb: 43, 48, 53;
  --bs-primary-text-emphasis: #ba778c;
  --bs-secondary-text-emphasis: #ffdd7d;
  --bs-success-text-emphasis: #aed879;
  --bs-info-text-emphasis: #66c8ec;
  --bs-warning-text-emphasis: #ffb284;
  --bs-danger-text-emphasis: #e08282;
  --bs-light-text-emphasis: #f8f9fa;
  --bs-dark-text-emphasis: #dee2e6;
  --bs-primary-bg-subtle: #1c060d;
  --bs-secondary-bg-subtle: #332808;
  --bs-success-bg-subtle: #182606;
  --bs-info-bg-subtle: #00212d;
  --bs-warning-bg-subtle: #33190a;
  --bs-danger-bg-subtle: #290909;
  --bs-light-bg-subtle: #343a40;
  --bs-dark-bg-subtle: #1a1d20;
  --bs-primary-border-subtle: #541126;
  --bs-secondary-border-subtle: #997717;
  --bs-success-border-subtle: #487213;
  --bs-info-border-subtle: #006286;
  --bs-warning-border-subtle: #994c1e;
  --bs-danger-border-subtle: #7a1c1c;
  --bs-light-border-subtle: #495057;
  --bs-dark-border-subtle: #343a40;
  --bs-heading-color: inherit;
  --bs-link-color: #ba778c;
  --bs-link-hover-color: #c892a3;
  --bs-link-color-rgb: 186, 119, 140;
  --bs-link-hover-color-rgb: 200, 146, 163;
  --bs-code-color: #e685b5;
  --bs-border-color: #495057;
  --bs-border-color-translucent: rgba(255, 255, 255, 0.15);
  --bs-form-valid-color: #aed879;
  --bs-form-valid-border-color: #aed879;
  --bs-form-invalid-color: #ea868f;
  --bs-form-invalid-border-color: #ea868f;
}

/* *,
*::before,
*::after {
  box-sizing: border-box;
} */

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

/* body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  border-top: var(--bs-border-width) solid;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
  color: var(--bs-heading-color);
}

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2rem;
  }
}

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.75rem;
  }
}

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.5rem;
  }
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul {
  padding-left: 2rem;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: 900;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.1875em;
  background-color: var(--bs-highlight-bg);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
  text-decoration: underline;
}
a:hover {
  --bs-link-color-rgb: var(--bs-link-hover-color-rgb);
  text-decoration: none;
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.875em;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-secondary-color);
  text-align: left;
}

th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

rtl:raw:
[type="tel"],
[type="url"],
[type="email"],
[type="number"] {
  direction: ltr;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}
*/

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  padding-left: 0;
  list-style: none;
}

.list-inline {
  padding-left: 0;
  list-style: none;
}

.list-inline-item {
  display: inline-block;
}
.list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--bs-body-bg);
  border: var(--bs-border-width) solid var(--bs-border-color);
  border-radius: var(--bs-border-radius);
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.875em;
  color: var(--bs-secondary-color);
}

.container,
.container-fluid,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  --bs-gutter-x: 24px;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 768px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 992px;
  }
}
@media (min-width: 1260px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1224px;
  }
}
:root {
  --bs-breakpoint-xs: 0;
  --bs-breakpoint-sm: 576px;
  --bs-breakpoint-md: 768px;
  --bs-breakpoint-lg: 992px;
  --bs-breakpoint-xl: 1260px;
}

.row {
  --bs-gutter-x: 24px;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

.offset-1 {
  margin-left: 8.33333333%;
}

.offset-2 {
  margin-left: 16.66666667%;
}

.offset-3 {
  margin-left: 25%;
}

.offset-4 {
  margin-left: 33.33333333%;
}

.offset-5 {
  margin-left: 41.66666667%;
}

.offset-6 {
  margin-left: 50%;
}

.offset-7 {
  margin-left: 58.33333333%;
}

.offset-8 {
  margin-left: 66.66666667%;
}

.offset-9 {
  margin-left: 75%;
}

.offset-10 {
  margin-left: 83.33333333%;
}

.offset-11 {
  margin-left: 91.66666667%;
}

.g-0,
.gx-0 {
  --bs-gutter-x: 0rem;
}

.g-0,
.gy-0 {
  --bs-gutter-y: 0rem;
}

.g-1,
.gx-1 {
  --bs-gutter-x: 0.5rem;
}

.g-1,
.gy-1 {
  --bs-gutter-y: 0.5rem;
}

.g-2,
.gx-2 {
  --bs-gutter-x: 1rem;
}

.g-2,
.gy-2 {
  --bs-gutter-y: 1rem;
}

.g-3,
.gx-3 {
  --bs-gutter-x: 1.5rem;
}

.g-3,
.gy-3 {
  --bs-gutter-y: 1.5rem;
}

.g-4,
.gx-4 {
  --bs-gutter-x: 2rem;
}

.g-4,
.gy-4 {
  --bs-gutter-y: 2rem;
}

.g-5,
.gx-5 {
  --bs-gutter-x: 2.5rem;
}

.g-5,
.gy-5 {
  --bs-gutter-y: 2.5rem;
}

.g-6,
.gx-6 {
  --bs-gutter-x: 3rem;
}

.g-6,
.gy-6 {
  --bs-gutter-y: 3rem;
}

.g-7,
.gx-7 {
  --bs-gutter-x: 3.5rem;
}

.g-7,
.gy-7 {
  --bs-gutter-y: 3.5rem;
}

.g-8,
.gx-8 {
  --bs-gutter-x: 4rem;
}

.g-8,
.gy-8 {
  --bs-gutter-y: 4rem;
}

.g-9,
.gx-9 {
  --bs-gutter-x: 4.5rem;
}

.g-9,
.gy-9 {
  --bs-gutter-y: 4.5rem;
}

.g-10,
.gx-10 {
  --bs-gutter-x: 5rem;
}

.g-10,
.gy-10 {
  --bs-gutter-y: 5rem;
}

.g-12,
.gx-12 {
  --bs-gutter-x: 6rem;
}

.g-12,
.gy-12 {
  --bs-gutter-y: 6rem;
}

.g-14,
.gx-14 {
  --bs-gutter-x: 7rem;
}

.g-14,
.gy-14 {
  --bs-gutter-y: 7rem;
}

.g-16,
.gx-16 {
  --bs-gutter-x: 8rem;
}

.g-16,
.gy-16 {
  --bs-gutter-y: 8rem;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }
  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-sm-0 {
    margin-left: 0;
  }
  .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .offset-sm-3 {
    margin-left: 25%;
  }
  .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .offset-sm-6 {
    margin-left: 50%;
  }
  .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .offset-sm-9 {
    margin-left: 75%;
  }
  .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .g-sm-0,
  .gx-sm-0 {
    --bs-gutter-x: 0rem;
  }
  .g-sm-0,
  .gy-sm-0 {
    --bs-gutter-y: 0rem;
  }
  .g-sm-1,
  .gx-sm-1 {
    --bs-gutter-x: 0.5rem;
  }
  .g-sm-1,
  .gy-sm-1 {
    --bs-gutter-y: 0.5rem;
  }
  .g-sm-2,
  .gx-sm-2 {
    --bs-gutter-x: 1rem;
  }
  .g-sm-2,
  .gy-sm-2 {
    --bs-gutter-y: 1rem;
  }
  .g-sm-3,
  .gx-sm-3 {
    --bs-gutter-x: 1.5rem;
  }
  .g-sm-3,
  .gy-sm-3 {
    --bs-gutter-y: 1.5rem;
  }
  .g-sm-4,
  .gx-sm-4 {
    --bs-gutter-x: 2rem;
  }
  .g-sm-4,
  .gy-sm-4 {
    --bs-gutter-y: 2rem;
  }
  .g-sm-5,
  .gx-sm-5 {
    --bs-gutter-x: 2.5rem;
  }
  .g-sm-5,
  .gy-sm-5 {
    --bs-gutter-y: 2.5rem;
  }
  .g-sm-6,
  .gx-sm-6 {
    --bs-gutter-x: 3rem;
  }
  .g-sm-6,
  .gy-sm-6 {
    --bs-gutter-y: 3rem;
  }
  .g-sm-7,
  .gx-sm-7 {
    --bs-gutter-x: 3.5rem;
  }
  .g-sm-7,
  .gy-sm-7 {
    --bs-gutter-y: 3.5rem;
  }
  .g-sm-8,
  .gx-sm-8 {
    --bs-gutter-x: 4rem;
  }
  .g-sm-8,
  .gy-sm-8 {
    --bs-gutter-y: 4rem;
  }
  .g-sm-9,
  .gx-sm-9 {
    --bs-gutter-x: 4.5rem;
  }
  .g-sm-9,
  .gy-sm-9 {
    --bs-gutter-y: 4.5rem;
  }
  .g-sm-10,
  .gx-sm-10 {
    --bs-gutter-x: 5rem;
  }
  .g-sm-10,
  .gy-sm-10 {
    --bs-gutter-y: 5rem;
  }
  .g-sm-12,
  .gx-sm-12 {
    --bs-gutter-x: 6rem;
  }
  .g-sm-12,
  .gy-sm-12 {
    --bs-gutter-y: 6rem;
  }
  .g-sm-14,
  .gx-sm-14 {
    --bs-gutter-x: 7rem;
  }
  .g-sm-14,
  .gy-sm-14 {
    --bs-gutter-y: 7rem;
  }
  .g-sm-16,
  .gx-sm-16 {
    --bs-gutter-x: 8rem;
  }
  .g-sm-16,
  .gy-sm-16 {
    --bs-gutter-y: 8rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }
  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-md-0 {
    margin-left: 0;
  }
  .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .offset-md-3 {
    margin-left: 25%;
  }
  .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .offset-md-6 {
    margin-left: 50%;
  }
  .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .offset-md-9 {
    margin-left: 75%;
  }
  .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .g-md-0,
  .gx-md-0 {
    --bs-gutter-x: 0rem;
  }
  .g-md-0,
  .gy-md-0 {
    --bs-gutter-y: 0rem;
  }
  .g-md-1,
  .gx-md-1 {
    --bs-gutter-x: 0.5rem;
  }
  .g-md-1,
  .gy-md-1 {
    --bs-gutter-y: 0.5rem;
  }
  .g-md-2,
  .gx-md-2 {
    --bs-gutter-x: 1rem;
  }
  .g-md-2,
  .gy-md-2 {
    --bs-gutter-y: 1rem;
  }
  .g-md-3,
  .gx-md-3 {
    --bs-gutter-x: 1.5rem;
  }
  .g-md-3,
  .gy-md-3 {
    --bs-gutter-y: 1.5rem;
  }
  .g-md-4,
  .gx-md-4 {
    --bs-gutter-x: 2rem;
  }
  .g-md-4,
  .gy-md-4 {
    --bs-gutter-y: 2rem;
  }
  .g-md-5,
  .gx-md-5 {
    --bs-gutter-x: 2.5rem;
  }
  .g-md-5,
  .gy-md-5 {
    --bs-gutter-y: 2.5rem;
  }
  .g-md-6,
  .gx-md-6 {
    --bs-gutter-x: 3rem;
  }
  .g-md-6,
  .gy-md-6 {
    --bs-gutter-y: 3rem;
  }
  .g-md-7,
  .gx-md-7 {
    --bs-gutter-x: 3.5rem;
  }
  .g-md-7,
  .gy-md-7 {
    --bs-gutter-y: 3.5rem;
  }
  .g-md-8,
  .gx-md-8 {
    --bs-gutter-x: 4rem;
  }
  .g-md-8,
  .gy-md-8 {
    --bs-gutter-y: 4rem;
  }
  .g-md-9,
  .gx-md-9 {
    --bs-gutter-x: 4.5rem;
  }
  .g-md-9,
  .gy-md-9 {
    --bs-gutter-y: 4.5rem;
  }
  .g-md-10,
  .gx-md-10 {
    --bs-gutter-x: 5rem;
  }
  .g-md-10,
  .gy-md-10 {
    --bs-gutter-y: 5rem;
  }
  .g-md-12,
  .gx-md-12 {
    --bs-gutter-x: 6rem;
  }
  .g-md-12,
  .gy-md-12 {
    --bs-gutter-y: 6rem;
  }
  .g-md-14,
  .gx-md-14 {
    --bs-gutter-x: 7rem;
  }
  .g-md-14,
  .gy-md-14 {
    --bs-gutter-y: 7rem;
  }
  .g-md-16,
  .gx-md-16 {
    --bs-gutter-x: 8rem;
  }
  .g-md-16,
  .gy-md-16 {
    --bs-gutter-y: 8rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }
  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-lg-0 {
    margin-left: 0;
  }
  .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .offset-lg-3 {
    margin-left: 25%;
  }
  .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .offset-lg-6 {
    margin-left: 50%;
  }
  .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .offset-lg-9 {
    margin-left: 75%;
  }
  .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .g-lg-0,
  .gx-lg-0 {
    --bs-gutter-x: 0rem;
  }
  .g-lg-0,
  .gy-lg-0 {
    --bs-gutter-y: 0rem;
  }
  .g-lg-1,
  .gx-lg-1 {
    --bs-gutter-x: 0.5rem;
  }
  .g-lg-1,
  .gy-lg-1 {
    --bs-gutter-y: 0.5rem;
  }
  .g-lg-2,
  .gx-lg-2 {
    --bs-gutter-x: 1rem;
  }
  .g-lg-2,
  .gy-lg-2 {
    --bs-gutter-y: 1rem;
  }
  .g-lg-3,
  .gx-lg-3 {
    --bs-gutter-x: 1.5rem;
  }
  .g-lg-3,
  .gy-lg-3 {
    --bs-gutter-y: 1.5rem;
  }
  .g-lg-4,
  .gx-lg-4 {
    --bs-gutter-x: 2rem;
  }
  .g-lg-4,
  .gy-lg-4 {
    --bs-gutter-y: 2rem;
  }
  .g-lg-5,
  .gx-lg-5 {
    --bs-gutter-x: 2.5rem;
  }
  .g-lg-5,
  .gy-lg-5 {
    --bs-gutter-y: 2.5rem;
  }
  .g-lg-6,
  .gx-lg-6 {
    --bs-gutter-x: 3rem;
  }
  .g-lg-6,
  .gy-lg-6 {
    --bs-gutter-y: 3rem;
  }
  .g-lg-7,
  .gx-lg-7 {
    --bs-gutter-x: 3.5rem;
  }
  .g-lg-7,
  .gy-lg-7 {
    --bs-gutter-y: 3.5rem;
  }
  .g-lg-8,
  .gx-lg-8 {
    --bs-gutter-x: 4rem;
  }
  .g-lg-8,
  .gy-lg-8 {
    --bs-gutter-y: 4rem;
  }
  .g-lg-9,
  .gx-lg-9 {
    --bs-gutter-x: 4.5rem;
  }
  .g-lg-9,
  .gy-lg-9 {
    --bs-gutter-y: 4.5rem;
  }
  .g-lg-10,
  .gx-lg-10 {
    --bs-gutter-x: 5rem;
  }
  .g-lg-10,
  .gy-lg-10 {
    --bs-gutter-y: 5rem;
  }
  .g-lg-12,
  .gx-lg-12 {
    --bs-gutter-x: 6rem;
  }
  .g-lg-12,
  .gy-lg-12 {
    --bs-gutter-y: 6rem;
  }
  .g-lg-14,
  .gx-lg-14 {
    --bs-gutter-x: 7rem;
  }
  .g-lg-14,
  .gy-lg-14 {
    --bs-gutter-y: 7rem;
  }
  .g-lg-16,
  .gx-lg-16 {
    --bs-gutter-x: 8rem;
  }
  .g-lg-16,
  .gy-lg-16 {
    --bs-gutter-y: 8rem;
  }
}
@media (min-width: 1260px) {
  .col-xl {
    flex: 1 0 0%;
  }
  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .offset-xl-0 {
    margin-left: 0;
  }
  .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .offset-xl-3 {
    margin-left: 25%;
  }
  .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .offset-xl-6 {
    margin-left: 50%;
  }
  .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .offset-xl-9 {
    margin-left: 75%;
  }
  .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .g-xl-0,
  .gx-xl-0 {
    --bs-gutter-x: 0rem;
  }
  .g-xl-0,
  .gy-xl-0 {
    --bs-gutter-y: 0rem;
  }
  .g-xl-1,
  .gx-xl-1 {
    --bs-gutter-x: 0.5rem;
  }
  .g-xl-1,
  .gy-xl-1 {
    --bs-gutter-y: 0.5rem;
  }
  .g-xl-2,
  .gx-xl-2 {
    --bs-gutter-x: 1rem;
  }
  .g-xl-2,
  .gy-xl-2 {
    --bs-gutter-y: 1rem;
  }
  .g-xl-3,
  .gx-xl-3 {
    --bs-gutter-x: 1.5rem;
  }
  .g-xl-3,
  .gy-xl-3 {
    --bs-gutter-y: 1.5rem;
  }
  .g-xl-4,
  .gx-xl-4 {
    --bs-gutter-x: 2rem;
  }
  .g-xl-4,
  .gy-xl-4 {
    --bs-gutter-y: 2rem;
  }
  .g-xl-5,
  .gx-xl-5 {
    --bs-gutter-x: 2.5rem;
  }
  .g-xl-5,
  .gy-xl-5 {
    --bs-gutter-y: 2.5rem;
  }
  .g-xl-6,
  .gx-xl-6 {
    --bs-gutter-x: 3rem;
  }
  .g-xl-6,
  .gy-xl-6 {
    --bs-gutter-y: 3rem;
  }
  .g-xl-7,
  .gx-xl-7 {
    --bs-gutter-x: 3.5rem;
  }
  .g-xl-7,
  .gy-xl-7 {
    --bs-gutter-y: 3.5rem;
  }
  .g-xl-8,
  .gx-xl-8 {
    --bs-gutter-x: 4rem;
  }
  .g-xl-8,
  .gy-xl-8 {
    --bs-gutter-y: 4rem;
  }
  .g-xl-9,
  .gx-xl-9 {
    --bs-gutter-x: 4.5rem;
  }
  .g-xl-9,
  .gy-xl-9 {
    --bs-gutter-y: 4.5rem;
  }
  .g-xl-10,
  .gx-xl-10 {
    --bs-gutter-x: 5rem;
  }
  .g-xl-10,
  .gy-xl-10 {
    --bs-gutter-y: 5rem;
  }
  .g-xl-12,
  .gx-xl-12 {
    --bs-gutter-x: 6rem;
  }
  .g-xl-12,
  .gy-xl-12 {
    --bs-gutter-y: 6rem;
  }
  .g-xl-14,
  .gx-xl-14 {
    --bs-gutter-x: 7rem;
  }
  .g-xl-14,
  .gy-xl-14 {
    --bs-gutter-y: 7rem;
  }
  .g-xl-16,
  .gx-xl-16 {
    --bs-gutter-x: 8rem;
  }
  .g-xl-16,
  .gy-xl-16 {
    --bs-gutter-y: 8rem;
  }
}
.table {
  --bs-table-color-type: initial;
  --bs-table-bg-type: initial;
  --bs-table-color-state: initial;
  --bs-table-bg-state: initial;
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: var(--bs-body-bg);
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  vertical-align: top;
  border-color: var(--bs-table-border-color);
}
.table > :not(caption) > * > * {
  padding: 0.5rem 0.5rem;
  color: var(--bs-table-color-state, var(--bs-table-color-type, var(--bs-table-color)));
  background-color: var(--bs-table-bg);
  border-bottom-width: var(--bs-border-width);
  box-shadow: inset 0 0 0 9999px var(--bs-table-bg-state, var(--bs-table-bg-type, var(--bs-table-accent-bg)));
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}

.table-group-divider {
  border-top: calc(var(--bs-border-width) * 2) solid currentcolor;
}

.caption-top {
  caption-side: top;
}

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: var(--bs-border-width) 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 var(--bs-border-width);
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-color-type: var(--bs-table-striped-color);
  --bs-table-bg-type: var(--bs-table-striped-bg);
}

.table-striped-columns > :not(caption) > tr > :nth-child(even) {
  --bs-table-color-type: var(--bs-table-striped-color);
  --bs-table-bg-type: var(--bs-table-striped-bg);
}

.table-active {
  --bs-table-color-state: var(--bs-table-active-color);
  --bs-table-bg-state: var(--bs-table-active-bg);
}

.table-hover > tbody > tr:hover > * {
  --bs-table-color-state: var(--bs-table-hover-color);
  --bs-table-bg-state: var(--bs-table-hover-bg);
}

.table-primary {
  --bs-table-color: #000;
  --bs-table-bg: #e8d2d9;
  --bs-table-border-color: #d1bdc3;
  --bs-table-striped-bg: #dcc8ce;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #d1bdc3;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #d7c2c9;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-secondary {
  --bs-table-color: #000;
  --bs-table-bg: #fff4d4;
  --bs-table-border-color: #e6dcbf;
  --bs-table-striped-bg: #f2e8c9;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e6dcbf;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #ece2c4;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-success {
  --bs-table-color: #000;
  --bs-table-bg: #e4f2d2;
  --bs-table-border-color: #cddabd;
  --bs-table-striped-bg: #d9e6c8;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #cddabd;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #d3e0c2;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-info {
  --bs-table-color: #000;
  --bs-table-bg: #ccedf9;
  --bs-table-border-color: #b8d5e0;
  --bs-table-striped-bg: #c2e1ed;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #b8d5e0;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #bddbe6;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-warning {
  --bs-table-color: #000;
  --bs-table-bg: #ffe5d6;
  --bs-table-border-color: #e6cec1;
  --bs-table-striped-bg: #f2dacb;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #e6cec1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #ecd4c6;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-danger {
  --bs-table-color: #000;
  --bs-table-bg: #f5d5d5;
  --bs-table-border-color: #ddc0c0;
  --bs-table-striped-bg: #e9caca;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #ddc0c0;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #e3c5c5;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-light {
  --bs-table-color: #000;
  --bs-table-bg: #e8e8e8;
  --bs-table-border-color: #d1d1d1;
  --bs-table-striped-bg: gainsboro;
  --bs-table-striped-color: #000;
  --bs-table-active-bg: #d1d1d1;
  --bs-table-active-color: #000;
  --bs-table-hover-bg: #d7d7d7;
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-dark {
  --bs-table-color: #ffffff;
  --bs-table-bg: #191919;
  --bs-table-border-color: #303030;
  --bs-table-striped-bg: #252525;
  --bs-table-striped-color: #ffffff;
  --bs-table-active-bg: #303030;
  --bs-table-active-color: #ffffff;
  --bs-table-hover-bg: #2a2a2a;
  --bs-table-hover-color: #ffffff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1259.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  margin-bottom: 0.5rem;
}

.col-form-label {
  padding-top: calc(0.5rem + var(--bs-border-width));
  padding-bottom: calc(0.5rem + var(--bs-border-width));
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}

.col-form-label-lg {
  padding-top: calc(0.5rem + var(--bs-border-width));
  padding-bottom: calc(0.5rem + var(--bs-border-width));
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.5rem + var(--bs-border-width));
  padding-bottom: calc(0.5rem + var(--bs-border-width));
  font-size: 0.875rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: var(--bs-secondary-color);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem 0.5rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  background-clip: padding-box;
  border: var(--bs-border-width) solid var(--bs-border-color);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  border-color: #c68ea0;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.form-control::-webkit-date-and-time-value {
  min-width: 85px;
  height: 1.5em;
  margin: 0;
}
.form-control::-webkit-datetime-edit {
  display: block;
  padding: 0;
}
.form-control::-moz-placeholder {
  color: #747474;
  opacity: 1;
}
.form-control::placeholder {
  color: #747474;
  opacity: 1;
}
.form-control:disabled {
  background-color: var(--bs-secondary-bg);
  opacity: 1;
}
.form-control::file-selector-button {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
  color: var(--bs-body-color);
  background-color: var(--bs-tertiary-bg);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: var(--bs-border-width);
  border-radius: 0;
  transition: 0.03s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: var(--bs-secondary-bg);
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.5rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: var(--bs-body-color);
  background-color: transparent;
  border: solid transparent;
  border-width: var(--bs-border-width) 0;
}
.form-control-plaintext:focus {
  outline: 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: auto;
  padding: 0.5rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0;
}
.form-control-sm::file-selector-button {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: auto;
  padding: 0.5rem 0.5rem;
  font-size: 1.25rem;
  border-radius: 0;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 0.5rem;
  margin: -0.5rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}

textarea.form-control {
  min-height: auto;
}
textarea.form-control-sm {
  min-height: auto;
}
textarea.form-control-lg {
  min-height: auto;
}

.form-control-color {
  width: 3rem;
  height: auto;
  padding: 0.5rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: 0;
}
.form-control-color::-webkit-color-swatch {
  border: 0 !important;
  border-radius: 0;
}
.form-control-color.form-control-sm {
  height: auto;
}
.form-control-color.form-control-lg {
  height: auto;
}

.form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  display: block;
  width: 100%;
  padding: 0.5rem 1.5rem 0.5rem 0.5rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--bs-body-color);
  background-color: var(--bs-body-bg);
  background-image: var(--bs-form-select-bg-img), var(--bs-form-select-bg-icon, none);
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  background-size: 16px 12px;
  border: var(--bs-border-width) solid var(--bs-border-color);
  border-radius: 0;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: #c68ea0;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  padding-right: 0.5rem;
  background-image: none;
}
.form-select:disabled {
  background-color: var(--bs-secondary-bg);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--bs-body-color);
}

.form-select-sm {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0;
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 0.5rem;
  font-size: 1.25rem;
  border-radius: 0;
}

[data-bs-theme=dark] .form-select {
  --bs-form-select-bg-img: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23adb5bd' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
}

.form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}

.form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right;
}
.form-check-reverse .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}

.form-check-input {
  --bs-form-check-bg: var(--bs-body-bg);
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: var(--bs-form-check-bg);
  background-image: var(--bs-form-check-bg-image);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: var(--bs-border-width) solid var(--bs-border-color);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #c68ea0;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.form-check-input:checked {
  background-color: #8c1d40;
  border-color: #8c1d40;
}
.form-check-input:checked[type=checkbox] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}
.form-check-input:checked[type=radio] {
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #8c1d40;
  border-color: #8c1d40;
  --bs-form-check-bg-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}

.form-switch {
  padding-left: 2.5em;
}
.form-switch .form-check-input {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  width: 2em;
  margin-left: -2.5em;
  background-image: var(--bs-form-switch-bg);
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23c68ea0'/%3e%3c/svg%3e");
}
.form-switch .form-check-input:checked {
  background-position: right center;
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-switch.form-check-reverse {
  padding-right: 2.5em;
  padding-left: 0;
}
.form-switch.form-check-reverse .form-check-input {
  margin-right: -2.5em;
  margin-left: 0;
}

.form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check[disabled] + .uds-modal-close-btn, .btn-check:disabled + .btn, .btn-check:disabled + .uds-modal-close-btn {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}

[data-bs-theme=dark] .form-switch .form-check-input:not(:checked):not(:focus) {
  --bs-form-switch-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.25%29'/%3e%3c/svg%3e");
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #ffffff, 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #8c1d40;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: #ddbbc6;
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--bs-tertiary-bg);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #8c1d40;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: #ddbbc6;
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--bs-tertiary-bg);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: var(--bs-secondary-color);
}
.form-range:disabled::-moz-range-thumb {
  background-color: var(--bs-secondary-color);
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext,
.form-floating > .form-select {
  height: calc(3.5rem + calc(var(--bs-border-width) * 2));
  min-height: calc(3.5rem + calc(var(--bs-border-width) * 2));
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  padding: 1rem 0.5rem;
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: var(--bs-border-width) solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control,
.form-floating > .form-control-plaintext {
  padding: 1rem 0.5rem;
}
.form-floating > .form-control::-moz-placeholder, .form-floating > .form-control-plaintext::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder,
.form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown), .form-floating > .form-control-plaintext:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown),
.form-floating > .form-control-plaintext:focus,
.form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill,
.form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-control-plaintext ~ label,
.form-floating > .form-select ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label::after {
  position: absolute;
  inset: 1rem 0.25rem;
  z-index: -1;
  height: 1.5em;
  content: "";
  background-color: var(--bs-body-bg);
  border-radius: 0;
}
.form-floating > .form-control:focus ~ label::after,
.form-floating > .form-control:not(:placeholder-shown) ~ label::after,
.form-floating > .form-control-plaintext ~ label::after,
.form-floating > .form-select ~ label::after {
  position: absolute;
  inset: 1rem 0.25rem;
  z-index: -1;
  height: 1.5em;
  content: "";
  background-color: var(--bs-body-bg);
  border-radius: 0;
}
.form-floating > .form-control:-webkit-autofill ~ label {
  color: rgba(var(--bs-body-color-rgb), 0.65);
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control-plaintext ~ label {
  border-width: var(--bs-border-width) 0;
}
.form-floating > :disabled ~ label {
  color: #6c757d;
}
.form-floating > :disabled ~ label::after {
  background-color: var(--bs-secondary-bg);
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select,
.input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus,
.input-group > .form-floating:focus-within {
  z-index: 5;
}
.input-group .btn, .input-group .uds-modal-close-btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus, .input-group .uds-modal-close-btn:focus {
  z-index: 5;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.5rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--bs-body-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-tertiary-bg);
  border: var(--bs-border-width) solid var(--bs-border-color);
  border-radius: 0;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn,
.input-group-lg > .uds-modal-close-btn {
  padding: 0.5rem 0.5rem;
  font-size: 1.25rem;
  border-radius: 0;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn,
.input-group-sm > .uds-modal-close-btn {
  padding: 0.5rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0;
}

.input-group-lg > .form-select,
.input-group-sm > .form-select {
  padding-right: 2rem;
}

.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: calc(var(--bs-border-width) * -1);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.input-group > .form-floating:not(:first-child) > .form-control,
.input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #446d12;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #ffffff;
  background-color: rgba(68, 109, 18, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #446d12;
  padding-right: calc(1.5em + 1rem);
  background-image: none;
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.25rem) center;
  background-size: calc(0.75em + 0.5rem) calc(0.75em + 0.5rem);
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #446d12;
  box-shadow: 0 0 0 0.25rem rgba(68, 109, 18, 0.25);
}

.was-validated textarea.form-control:valid, textarea.form-control.is-valid {
  padding-right: calc(1.5em + 1rem);
  background-position: top calc(0.375em + 0.25rem) right calc(0.375em + 0.25rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #446d12;
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: none;
  padding-right: 2.75rem;
  background-position: right 0.5rem center, center right 1.5rem;
  background-size: 16px 12px, calc(0.75em + 0.5rem) calc(0.75em + 0.5rem);
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #446d12;
  box-shadow: 0 0 0 0.25rem rgba(68, 109, 18, 0.25);
}

.was-validated .form-control-color:valid, .form-control-color.is-valid {
  width: calc(3rem + calc(1.5em + 1rem));
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #446d12;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #446d12;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(68, 109, 18, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #446d12;
}

.form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):valid, .input-group > .form-control:not(:focus).is-valid,
.was-validated .input-group > .form-select:not(:focus):valid,
.input-group > .form-select:not(:focus).is-valid,
.was-validated .input-group > .form-floating:not(:focus-within):valid,
.input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #b72a2a;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #ffffff;
  background-color: rgba(183, 42, 42, 0.9);
  border-radius: var(--bs-border-radius);
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #b72a2a;
  padding-right: calc(1.5em + 1rem);
  background-image: none;
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.25rem) center;
  background-size: calc(0.75em + 0.5rem) calc(0.75em + 0.5rem);
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #b72a2a;
  box-shadow: 0 0 0 0.25rem rgba(183, 42, 42, 0.25);
}

.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 1rem);
  background-position: top calc(0.375em + 0.25rem) right calc(0.375em + 0.25rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #b72a2a;
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  --bs-form-select-bg-icon: none;
  padding-right: 2.75rem;
  background-position: right 0.5rem center, center right 1.5rem;
  background-size: 16px 12px, calc(0.75em + 0.5rem) calc(0.75em + 0.5rem);
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #b72a2a;
  box-shadow: 0 0 0 0.25rem rgba(183, 42, 42, 0.25);
}

.was-validated .form-control-color:invalid, .form-control-color.is-invalid {
  width: calc(3rem + calc(1.5em + 1rem));
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #b72a2a;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #b72a2a;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(183, 42, 42, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #b72a2a;
}

.form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}

.was-validated .input-group > .form-control:not(:focus):invalid, .input-group > .form-control:not(:focus).is-invalid,
.was-validated .input-group > .form-select:not(:focus):invalid,
.input-group > .form-select:not(:focus).is-invalid,
.was-validated .input-group > .form-floating:not(:focus-within):invalid,
.input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}

.btn, .uds-modal-close-btn {
  --bs-btn-padding-x: 2rem;
  --bs-btn-padding-y: 1rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 1rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1rem;
  --bs-btn-color: var(--bs-body-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-width: var(--bs-border-width);
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 400rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.5;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: 0.03s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn, .uds-modal-close-btn {
    transition: none;
  }
}
.btn:hover, .uds-modal-close-btn:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn-check + .btn:hover, .btn-check + .uds-modal-close-btn:hover {
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
.btn:focus-visible, .uds-modal-close-btn:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:focus-visible + .btn, .btn-check:focus-visible + .uds-modal-close-btn {
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked + .btn, .btn-check:checked + .uds-modal-close-btn, :not(.btn-check) + .btn:active, :not(.btn-check) + .uds-modal-close-btn:active, .btn:first-child:active, .uds-modal-close-btn:first-child:active, .btn.active, .active.uds-modal-close-btn, .btn.show, .show.uds-modal-close-btn {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
}
.btn-check:checked + .btn:focus-visible, .btn-check:checked + .uds-modal-close-btn:focus-visible, :not(.btn-check) + .btn:active:focus-visible, :not(.btn-check) + .uds-modal-close-btn:active:focus-visible, .btn:first-child:active:focus-visible, .uds-modal-close-btn:first-child:active:focus-visible, .btn.active:focus-visible, .active.uds-modal-close-btn:focus-visible, .btn.show:focus-visible, .show.uds-modal-close-btn:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn:disabled, .uds-modal-close-btn:disabled, .btn.disabled, .disabled.uds-modal-close-btn, fieldset:disabled .btn, fieldset:disabled .uds-modal-close-btn {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
}

.btn-gold {
  --bs-btn-color: #000;
  --bs-btn-bg: #ffc627;
  --bs-btn-border-color: #ffc627;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffcf47;
  --bs-btn-hover-border-color: #ffcc3d;
  --bs-btn-focus-shadow-rgb: 217, 168, 33;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffd152;
  --bs-btn-active-border-color: #ffcc3d;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ffc627;
  --bs-btn-disabled-border-color: #ffc627;
}

.btn-maroon {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #8c1d40;
  --bs-btn-border-color: #8c1d40;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #771936;
  --bs-btn-hover-border-color: #701733;
  --bs-btn-focus-shadow-rgb: 157, 63, 93;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #701733;
  --bs-btn-active-border-color: #691630;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #8c1d40;
  --bs-btn-disabled-border-color: #8c1d40;
}

.btn-success {
  --bs-btn-color: #000;
  --bs-btn-bg: #78be20;
  --bs-btn-border-color: #78be20;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #8cc841;
  --bs-btn-hover-border-color: #86c536;
  --bs-btn-focus-shadow-rgb: 102, 162, 27;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #93cb4d;
  --bs-btn-active-border-color: #86c536;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #78be20;
  --bs-btn-disabled-border-color: #78be20;
}

.btn-info {
  --bs-btn-color: #000;
  --bs-btn-bg: #00a3e0;
  --bs-btn-border-color: #00a3e0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #26b1e5;
  --bs-btn-hover-border-color: #1aace3;
  --bs-btn-focus-shadow-rgb: 0, 139, 190;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #33b5e6;
  --bs-btn-active-border-color: #1aace3;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #00a3e0;
  --bs-btn-disabled-border-color: #00a3e0;
}

.btn-warning {
  --bs-btn-color: #000;
  --bs-btn-bg: #ff7f32;
  --bs-btn-border-color: #ff7f32;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ff9251;
  --bs-btn-hover-border-color: #ff8c47;
  --bs-btn-focus-shadow-rgb: 217, 108, 43;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ff995b;
  --bs-btn-active-border-color: #ff8c47;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ff7f32;
  --bs-btn-disabled-border-color: #ff7f32;
}

.btn-danger {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #cc2f2f;
  --bs-btn-border-color: #cc2f2f;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #ad2828;
  --bs-btn-hover-border-color: #a32626;
  --bs-btn-focus-shadow-rgb: 212, 78, 78;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #a32626;
  --bs-btn-active-border-color: #992323;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #cc2f2f;
  --bs-btn-disabled-border-color: #cc2f2f;
}

.btn-light {
  --bs-btn-color: #000;
  --bs-btn-bg: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #c5c5c5;
  --bs-btn-hover-border-color: #bababa;
  --bs-btn-focus-shadow-rgb: 197, 197, 197;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #bababa;
  --bs-btn-active-border-color: #aeaeae;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #e8e8e8;
  --bs-btn-disabled-border-color: #e8e8e8;
}

.btn-gray {
  --bs-btn-color: #000;
  --bs-btn-bg: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #c9c9c9;
  --bs-btn-hover-border-color: #c5c5c5;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #cccccc;
  --bs-btn-active-border-color: #c5c5c5;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #bfbfbf;
  --bs-btn-disabled-border-color: #bfbfbf;
}

.btn-dark {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #3c3c3c;
  --bs-btn-hover-border-color: #303030;
  --bs-btn-focus-shadow-rgb: 60, 60, 60;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #474747;
  --bs-btn-active-border-color: #303030;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #191919;
  --bs-btn-disabled-border-color: #191919;
}

.btn-gray-1 {
  --bs-btn-color: #000;
  --bs-btn-bg: #fafafa;
  --bs-btn-border-color: #fafafa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #fbfbfb;
  --bs-btn-hover-border-color: #fbfbfb;
  --bs-btn-focus-shadow-rgb: 213, 213, 213;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #fbfbfb;
  --bs-btn-active-border-color: #fbfbfb;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fafafa;
  --bs-btn-disabled-border-color: #fafafa;
}

.btn-gray-2 {
  --bs-btn-color: #000;
  --bs-btn-bg: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ebebeb;
  --bs-btn-hover-border-color: #eaeaea;
  --bs-btn-focus-shadow-rgb: 197, 197, 197;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ededed;
  --bs-btn-active-border-color: #eaeaea;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #e8e8e8;
  --bs-btn-disabled-border-color: #e8e8e8;
}

.btn-gray-3 {
  --bs-btn-color: #000;
  --bs-btn-bg: #d0d0d0;
  --bs-btn-border-color: #d0d0d0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #d7d7d7;
  --bs-btn-hover-border-color: #d5d5d5;
  --bs-btn-focus-shadow-rgb: 177, 177, 177;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #d9d9d9;
  --bs-btn-active-border-color: #d5d5d5;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #d0d0d0;
  --bs-btn-disabled-border-color: #d0d0d0;
}

.btn-gray-4 {
  --bs-btn-color: #000;
  --bs-btn-bg: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #c9c9c9;
  --bs-btn-hover-border-color: #c5c5c5;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #cccccc;
  --bs-btn-active-border-color: #c5c5c5;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #bfbfbf;
  --bs-btn-disabled-border-color: #bfbfbf;
}

.btn-gray-5 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #747474;
  --bs-btn-border-color: #747474;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #636363;
  --bs-btn-hover-border-color: #5d5d5d;
  --bs-btn-focus-shadow-rgb: 137, 137, 137;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #5d5d5d;
  --bs-btn-active-border-color: #575757;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #747474;
  --bs-btn-disabled-border-color: #747474;
}

.btn-gray-6 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #484848;
  --bs-btn-border-color: #484848;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #3d3d3d;
  --bs-btn-hover-border-color: #3a3a3a;
  --bs-btn-focus-shadow-rgb: 99, 99, 99;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #3a3a3a;
  --bs-btn-active-border-color: #363636;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #484848;
  --bs-btn-disabled-border-color: #484848;
}

.btn-gray-7 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #151515;
  --bs-btn-hover-border-color: #141414;
  --bs-btn-focus-shadow-rgb: 60, 60, 60;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #141414;
  --bs-btn-active-border-color: #131313;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #191919;
  --bs-btn-disabled-border-color: #191919;
}

.btn-outline-gold {
  --bs-btn-color: #ffc627;
  --bs-btn-border-color: #ffc627;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffc627;
  --bs-btn-hover-border-color: #ffc627;
  --bs-btn-focus-shadow-rgb: 255, 198, 39;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffc627;
  --bs-btn-active-border-color: #ffc627;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffc627;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffc627;
  --bs-gradient: none;
}

.btn-outline-maroon {
  --bs-btn-color: #8c1d40;
  --bs-btn-border-color: #8c1d40;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #8c1d40;
  --bs-btn-hover-border-color: #8c1d40;
  --bs-btn-focus-shadow-rgb: 140, 29, 64;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #8c1d40;
  --bs-btn-active-border-color: #8c1d40;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #8c1d40;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #8c1d40;
  --bs-gradient: none;
}

.btn-outline-success {
  --bs-btn-color: #78be20;
  --bs-btn-border-color: #78be20;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #78be20;
  --bs-btn-hover-border-color: #78be20;
  --bs-btn-focus-shadow-rgb: 120, 190, 32;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #78be20;
  --bs-btn-active-border-color: #78be20;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #78be20;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #78be20;
  --bs-gradient: none;
}

.btn-outline-info {
  --bs-btn-color: #00a3e0;
  --bs-btn-border-color: #00a3e0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #00a3e0;
  --bs-btn-hover-border-color: #00a3e0;
  --bs-btn-focus-shadow-rgb: 0, 163, 224;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #00a3e0;
  --bs-btn-active-border-color: #00a3e0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #00a3e0;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #00a3e0;
  --bs-gradient: none;
}

.btn-outline-warning {
  --bs-btn-color: #ff7f32;
  --bs-btn-border-color: #ff7f32;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ff7f32;
  --bs-btn-hover-border-color: #ff7f32;
  --bs-btn-focus-shadow-rgb: 255, 127, 50;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ff7f32;
  --bs-btn-active-border-color: #ff7f32;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ff7f32;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ff7f32;
  --bs-gradient: none;
}

.btn-outline-danger {
  --bs-btn-color: #cc2f2f;
  --bs-btn-border-color: #cc2f2f;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #cc2f2f;
  --bs-btn-hover-border-color: #cc2f2f;
  --bs-btn-focus-shadow-rgb: 204, 47, 47;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #cc2f2f;
  --bs-btn-active-border-color: #cc2f2f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #cc2f2f;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #cc2f2f;
  --bs-gradient: none;
}

.btn-outline-light {
  --bs-btn-color: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #e8e8e8;
  --bs-btn-hover-border-color: #e8e8e8;
  --bs-btn-focus-shadow-rgb: 232, 232, 232;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #e8e8e8;
  --bs-btn-active-border-color: #e8e8e8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #e8e8e8;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #e8e8e8;
  --bs-gradient: none;
}

.btn-outline-gray {
  --bs-btn-color: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #bfbfbf;
  --bs-btn-hover-border-color: #bfbfbf;
  --bs-btn-focus-shadow-rgb: 191, 191, 191;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #bfbfbf;
  --bs-btn-active-border-color: #bfbfbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #bfbfbf;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #bfbfbf;
  --bs-gradient: none;
}

.btn-outline-dark {
  --bs-btn-color: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #191919;
  --bs-btn-hover-border-color: #191919;
  --bs-btn-focus-shadow-rgb: 25, 25, 25;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #191919;
  --bs-btn-active-border-color: #191919;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #191919;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #191919;
  --bs-gradient: none;
}

.btn-outline-gray-1 {
  --bs-btn-color: #fafafa;
  --bs-btn-border-color: #fafafa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #fafafa;
  --bs-btn-hover-border-color: #fafafa;
  --bs-btn-focus-shadow-rgb: 250, 250, 250;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #fafafa;
  --bs-btn-active-border-color: #fafafa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fafafa;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #fafafa;
  --bs-gradient: none;
}

.btn-outline-gray-2 {
  --bs-btn-color: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #e8e8e8;
  --bs-btn-hover-border-color: #e8e8e8;
  --bs-btn-focus-shadow-rgb: 232, 232, 232;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #e8e8e8;
  --bs-btn-active-border-color: #e8e8e8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #e8e8e8;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #e8e8e8;
  --bs-gradient: none;
}

.btn-outline-gray-3 {
  --bs-btn-color: #d0d0d0;
  --bs-btn-border-color: #d0d0d0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #d0d0d0;
  --bs-btn-hover-border-color: #d0d0d0;
  --bs-btn-focus-shadow-rgb: 208, 208, 208;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #d0d0d0;
  --bs-btn-active-border-color: #d0d0d0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #d0d0d0;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #d0d0d0;
  --bs-gradient: none;
}

.btn-outline-gray-4 {
  --bs-btn-color: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #bfbfbf;
  --bs-btn-hover-border-color: #bfbfbf;
  --bs-btn-focus-shadow-rgb: 191, 191, 191;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #bfbfbf;
  --bs-btn-active-border-color: #bfbfbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #bfbfbf;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #bfbfbf;
  --bs-gradient: none;
}

.btn-outline-gray-5 {
  --bs-btn-color: #747474;
  --bs-btn-border-color: #747474;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #747474;
  --bs-btn-hover-border-color: #747474;
  --bs-btn-focus-shadow-rgb: 116, 116, 116;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #747474;
  --bs-btn-active-border-color: #747474;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #747474;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #747474;
  --bs-gradient: none;
}

.btn-outline-gray-6 {
  --bs-btn-color: #484848;
  --bs-btn-border-color: #484848;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #484848;
  --bs-btn-hover-border-color: #484848;
  --bs-btn-focus-shadow-rgb: 72, 72, 72;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #484848;
  --bs-btn-active-border-color: #484848;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #484848;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #484848;
  --bs-gradient: none;
}

.btn-outline-gray-7 {
  --bs-btn-color: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #191919;
  --bs-btn-hover-border-color: #191919;
  --bs-btn-focus-shadow-rgb: 25, 25, 25;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #191919;
  --bs-btn-active-border-color: #191919;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #191919;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #191919;
  --bs-gradient: none;
}

.btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: 0 0 0 #000;
  --bs-btn-focus-shadow-rgb: 157, 63, 93;
  text-decoration: underline;
}
.btn-link:hover, .btn-link:focus-visible {
  text-decoration: none;
}
.btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.btn-link:hover {
  color: var(--bs-btn-hover-color);
}

.btn-lg, .btn-group-lg > .btn, .btn-group-lg > .uds-modal-close-btn {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size: 1.25rem;
  --bs-btn-border-radius: 400rem;
}

.btn-sm, .btn-group-sm > .btn, .btn-group-sm > .uds-modal-close-btn {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-border-radius: 400rem;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart,
.dropup-center,
.dropdown-center {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: var(--bs-body-color);
  --bs-dropdown-bg: var(--bs-body-bg);
  --bs-dropdown-border-color: #e8e8e8;
  --bs-dropdown-border-radius: 0;
  --bs-dropdown-border-width: 1px;
  --bs-dropdown-inner-border-radius: calc(0 - 1px);
  --bs-dropdown-divider-bg: #e8e8e8;
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-dropdown-link-color: #191919;
  --bs-dropdown-link-hover-color: #8c1d40;
  --bs-dropdown-link-hover-bg: transparent;
  --bs-dropdown-link-active-color: #191919;
  --bs-dropdown-link-active-bg: transparent;
  --bs-dropdown-link-disabled-color: #747474;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 0.5rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 1rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
}
.dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
}

.dropdown-menu-start {
  --bs-position: start;
}
.dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --bs-position: end;
}
.dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --bs-position: start;
  }
  .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-md-end {
    --bs-position: end;
  }
  .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1260px) {
  .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer);
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}

.dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer);
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer);
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  border-radius: var(--bs-dropdown-item-border-radius, 0);
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color);
}

.dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: #e8e8e8;
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #ffffff;
  --bs-dropdown-divider-bg: #e8e8e8;
  --bs-dropdown-link-hover-bg: rgba(255, 255, 255, 0.15);
  --bs-dropdown-link-active-color: #191919;
  --bs-dropdown-link-active-bg: transparent;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn, .btn-group > .uds-modal-close-btn,
.btn-group-vertical > .btn,
.btn-group-vertical > .uds-modal-close-btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:checked + .uds-modal-close-btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn-check:focus + .uds-modal-close-btn,
.btn-group > .btn:hover,
.btn-group > .uds-modal-close-btn:hover,
.btn-group > .btn:focus,
.btn-group > .uds-modal-close-btn:focus,
.btn-group > .btn:active,
.btn-group > .uds-modal-close-btn:active,
.btn-group > .btn.active,
.btn-group > .active.uds-modal-close-btn,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:checked + .uds-modal-close-btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn-check:focus + .uds-modal-close-btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .uds-modal-close-btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .uds-modal-close-btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .uds-modal-close-btn:active,
.btn-group-vertical > .btn.active,
.btn-group-vertical > .active.uds-modal-close-btn {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

.btn-group {
  border-radius: 400rem;
}
.btn-group > :not(.btn-check:first-child) + .btn, .btn-group > :not(.btn-check:first-child) + .uds-modal-close-btn,
.btn-group > .btn-group:not(:first-child) {
  margin-left: calc(var(--bs-border-width) * -1);
}
.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .uds-modal-close-btn:not(:last-child):not(.dropdown-toggle),
.btn-group > .btn.dropdown-toggle-split:first-child,
.btn-group > .dropdown-toggle-split.uds-modal-close-btn:first-child,
.btn-group > .btn-group:not(:last-child) > .btn,
.btn-group > .btn-group:not(:last-child) > .uds-modal-close-btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.btn-group > .btn:nth-child(n+3), .btn-group > .uds-modal-close-btn:nth-child(n+3),
.btn-group > :not(.btn-check) + .btn,
.btn-group > :not(.btn-check) + .uds-modal-close-btn,
.btn-group > .btn-group:not(:first-child) > .btn,
.btn-group > .btn-group:not(:first-child) > .uds-modal-close-btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}
.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split, .btn-group-sm > .uds-modal-close-btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split, .btn-group-lg > .uds-modal-close-btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn, .btn-group-vertical > .uds-modal-close-btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .uds-modal-close-btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: calc(var(--bs-border-width) * -1);
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .uds-modal-close-btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn,
.btn-group-vertical > .btn-group:not(:last-child) > .uds-modal-close-btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn ~ .btn, .btn-group-vertical > .uds-modal-close-btn ~ .btn, .btn-group-vertical > .btn ~ .uds-modal-close-btn, .btn-group-vertical > .uds-modal-close-btn ~ .uds-modal-close-btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .uds-modal-close-btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: var(--bs-secondary-color);
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}

.nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  text-decoration: none;
  background: none;
  border: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.nav-link:focus-visible {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
}
.nav-link.disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  --bs-nav-tabs-border-width: 0;
  --bs-nav-tabs-border-color: #000;
  --bs-nav-tabs-border-radius: 0;
  --bs-nav-tabs-link-hover-border-color: var(--bs-secondary-bg) var(--bs-secondary-bg) #000;
  --bs-nav-tabs-link-active-color: #8c1d40;
  --bs-nav-tabs-link-active-bg: var(--bs-body-bg);
  --bs-nav-tabs-link-active-border-color: #8c1d40;
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color);
}
.nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--bs-nav-tabs-link-hover-border-color);
}
.nav-tabs .nav-link.disabled, .nav-tabs .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
}
.nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills {
  --bs-nav-pills-border-radius: var(--bs-border-radius);
  --bs-nav-pills-link-active-color: #ffffff;
  --bs-nav-pills-link-active-bg: #8c1d40;
}
.nav-pills .nav-link {
  border-radius: var(--bs-nav-pills-border-radius);
}
.nav-pills .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--bs-nav-pills-link-active-color);
  background-color: var(--bs-nav-pills-link-active-bg);
}

.nav-underline {
  --bs-nav-underline-gap: 1rem;
  --bs-nav-underline-border-width: 0.125rem;
  --bs-nav-underline-link-active-color: var(--bs-emphasis-color);
  gap: var(--bs-nav-underline-gap);
}
.nav-underline .nav-link {
  padding-right: 0;
  padding-left: 0;
  border-bottom: var(--bs-nav-underline-border-width) solid transparent;
}
.nav-underline .nav-link:hover, .nav-underline .nav-link:focus {
  border-bottom-color: currentcolor;
}
.nav-underline .nav-link.active,
.nav-underline .show > .nav-link {
  font-weight: 700;
  color: var(--bs-nav-underline-link-active-color);
  border-bottom-color: currentcolor;
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(var(--bs-emphasis-color-rgb), 0.65);
  --bs-navbar-hover-color: rgba(var(--bs-emphasis-color-rgb), 0.8);
  --bs-navbar-disabled-color: rgba(var(--bs-emphasis-color-rgb), 0.3);
  --bs-navbar-active-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-padding-y: 0.3125rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-brand-hover-color: rgba(var(--bs-emphasis-color-rgb), 1);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2825, 25, 25, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(var(--bs-emphasis-color-rgb), 0.15);
  --bs-navbar-toggler-border-radius: 400rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  text-decoration: none;
  white-space: nowrap;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--bs-navbar-brand-hover-color);
}

.navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
  color: var(--bs-navbar-active-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--bs-navbar-active-color);
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1260px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: var(--bs-navbar-nav-link-padding-x);
  padding-left: var(--bs-navbar-nav-link-padding-x);
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  transform: none !important;
  transition: none;
}
.navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-dark,
.navbar[data-bs-theme=dark] {
  --bs-navbar-color: rgba(255, 255, 255, 0.55);
  --bs-navbar-hover-color: rgba(255, 255, 255, 0.75);
  --bs-navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --bs-navbar-active-color: #ffffff;
  --bs-navbar-brand-color: #ffffff;
  --bs-navbar-brand-hover-color: #ffffff;
  --bs-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

[data-bs-theme=dark] .navbar-toggler-icon {
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-title-color: ;
  --bs-card-subtitle-color: ;
  --bs-card-border-width: var(--bs-border-width);
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: 0;
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: calc(0 - (var(--bs-border-width)));
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(255, 255, 255, 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: var(--bs-body-bg);
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  color: var(--bs-body-color);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color: var(--bs-card-color);
}

.card-title {
  margin-bottom: var(--bs-card-title-spacer-y);
  color: var(--bs-card-title-color);
}

.card-subtitle {
  margin-top: calc(-0.5 * var(--bs-card-title-spacer-y));
  margin-bottom: 0;
  color: var(--bs-card-subtitle-color);
}

.card-text:last-child {
  margin-bottom: 0;
}

.card-link + .card-link {
  margin-left: var(--bs-card-spacer-x);
}

.card-header {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  margin-bottom: 0;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-header:first-child {
  border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0;
}

.card-footer {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.card-footer:last-child {
  border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius);
}

.card-header-tabs {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-bottom: calc(-1 * var(--bs-card-cap-padding-y));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
  border-bottom: 0;
}
.card-header-tabs .nav-link.active {
  background-color: var(--bs-card-bg);
  border-bottom-color: var(--bs-card-bg);
}

.card-header-pills {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--bs-card-img-overlay-padding);
  border-radius: var(--bs-card-inner-border-radius);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}

.card-group > .card {
  margin-bottom: var(--bs-card-group-margin);
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
  .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
  .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
  .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
  .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.accordion {
  --bs-accordion-color: var(--bs-body-color);
  --bs-accordion-bg: var(--bs-body-bg);
  --bs-accordion-transition: 0.03s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: var(--bs-border-width);
  --bs-accordion-border-radius: 0;
  --bs-accordion-inner-border-radius: calc(0 - (var(--bs-border-width)));
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: var(--bs-body-color);
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23191919'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23380c1a'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-border-color: #c68ea0;
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: var(--bs-primary-text-emphasis);
  --bs-accordion-active-bg: var(--bs-primary-bg-subtle);
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.accordion-button::after {
  flex-shrink: 0;
  width: var(--bs-accordion-btn-icon-width);
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  content: "";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: var(--bs-accordion-btn-focus-border-color);
  outline: 0;
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
}
.accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}

.accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button, .accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}

[data-bs-theme=dark] .accordion-button::after {
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ba778c'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ba778c'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.breadcrumb {
  --bs-breadcrumb-padding-x: 1rem;
  --bs-breadcrumb-padding-y: 0.75rem;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-font-size: 0.875rem;
  --bs-breadcrumb-bg: transparent;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: #bfbfbf;
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: #191919;
  display: flex;
  flex-wrap: wrap;
  padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
  margin-bottom: var(--bs-breadcrumb-margin-bottom);
  font-size: var(--bs-breadcrumb-font-size);
  list-style: none;
  background-color: var(--bs-breadcrumb-bg);
  border-radius: var(--bs-breadcrumb-border-radius);
}

.breadcrumb-item + .breadcrumb-item {
  padding-left: var(--bs-breadcrumb-item-padding-x);
}
.breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: var(--bs-breadcrumb-item-padding-x);
  color: var(--bs-breadcrumb-divider-color);
  content: var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */;
}
.breadcrumb-item.active {
  color: var(--bs-breadcrumb-item-active-color);
}

.pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: #191919;
  --bs-pagination-bg: var(--bs-body-bg);
  --bs-pagination-border-width: 0rem;
  --bs-pagination-border-color: rgba(0, 0, 0, 0);
  --bs-pagination-border-radius: var(--bs-border-radius);
  --bs-pagination-hover-color: var(--bs-link-hover-color);
  --bs-pagination-hover-bg: var(--bs-tertiary-bg);
  --bs-pagination-hover-border-color: var(--bs-border-color);
  --bs-pagination-focus-color: var(--bs-link-hover-color);
  --bs-pagination-focus-bg: var(--bs-secondary-bg);
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
  --bs-pagination-active-color: #ffffff;
  --bs-pagination-active-bg: #8c1d40;
  --bs-pagination-active-border-color: rgba(0, 0, 0, 0);
  --bs-pagination-disabled-color: var(--bs-secondary-color);
  --bs-pagination-disabled-bg: var(--bs-secondary-bg);
  --bs-pagination-disabled-border-color: var(--bs-border-color);
  display: flex;
  padding-left: 0;
  list-style: none;
}

.page-link {
  position: relative;
  display: block;
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  font-size: var(--bs-pagination-font-size);
  color: var(--bs-pagination-color);
  text-decoration: none;
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--bs-pagination-hover-color);
  background-color: var(--bs-pagination-hover-bg);
  border-color: var(--bs-pagination-hover-border-color);
}
.page-link:focus {
  z-index: 3;
  color: var(--bs-pagination-focus-color);
  background-color: var(--bs-pagination-focus-bg);
  outline: 0;
  box-shadow: var(--bs-pagination-focus-box-shadow);
}
.page-link.active, .active > .page-link {
  z-index: 3;
  color: var(--bs-pagination-active-color);
  background-color: var(--bs-pagination-active-bg);
  border-color: var(--bs-pagination-active-border-color);
}
.page-link.disabled, .disabled > .page-link {
  color: var(--bs-pagination-disabled-color);
  pointer-events: none;
  background-color: var(--bs-pagination-disabled-bg);
  border-color: var(--bs-pagination-disabled-border-color);
}

.page-item:not(:first-child) .page-link {
  margin-left: calc(0rem * -1);
}
.page-item:first-child .page-link {
  border-top-left-radius: var(--bs-pagination-border-radius);
  border-bottom-left-radius: var(--bs-pagination-border-radius);
}
.page-item:last-child .page-link {
  border-top-right-radius: var(--bs-pagination-border-radius);
  border-bottom-right-radius: var(--bs-pagination-border-radius);
}

.pagination-lg {
  --bs-pagination-padding-x: 1.5rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1.25rem;
  --bs-pagination-border-radius: var(--bs-border-radius-lg);
}

.pagination-sm {
  --bs-pagination-padding-x: 0.5rem;
  --bs-pagination-padding-y: 0.25rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-border-radius: var(--bs-border-radius-sm);
}

.badge {
  --bs-badge-padding-x: 0.65em;
  --bs-badge-padding-y: 0.35em;
  --bs-badge-font-size: 0.75em;
  --bs-badge-font-weight: 700;
  --bs-badge-color: #ffffff;
  --bs-badge-border-radius: 400rem;
  display: inline-block;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  color: var(--bs-badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--bs-badge-border-radius);
}
.badge:empty {
  display: none;
}

.btn .badge, .uds-modal-close-btn .badge {
  position: relative;
  top: -1px;
}

.alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: var(--bs-border-width) solid var(--bs-alert-border-color);
  --bs-alert-border-radius: var(--bs-border-radius);
  --bs-alert-link-color: inherit;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
  color: var(--bs-alert-link-color);
}

.alert-dismissible {
  padding-right: 3rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}

.alert-gold {
  --bs-alert-color: var(--bs-gold-text-emphasis);
  --bs-alert-bg: var(--bs-gold-bg-subtle);
  --bs-alert-border-color: var(--bs-gold-border-subtle);
  --bs-alert-link-color: var(--bs-gold-text-emphasis);
}

.alert-maroon {
  --bs-alert-color: var(--bs-maroon-text-emphasis);
  --bs-alert-bg: var(--bs-maroon-bg-subtle);
  --bs-alert-border-color: var(--bs-maroon-border-subtle);
  --bs-alert-link-color: var(--bs-maroon-text-emphasis);
}

.alert-success {
  --bs-alert-color: var(--bs-success-text-emphasis);
  --bs-alert-bg: var(--bs-success-bg-subtle);
  --bs-alert-border-color: var(--bs-success-border-subtle);
  --bs-alert-link-color: var(--bs-success-text-emphasis);
}

.alert-info {
  --bs-alert-color: var(--bs-info-text-emphasis);
  --bs-alert-bg: var(--bs-info-bg-subtle);
  --bs-alert-border-color: var(--bs-info-border-subtle);
  --bs-alert-link-color: var(--bs-info-text-emphasis);
}

.alert-warning {
  --bs-alert-color: var(--bs-warning-text-emphasis);
  --bs-alert-bg: var(--bs-warning-bg-subtle);
  --bs-alert-border-color: var(--bs-warning-border-subtle);
  --bs-alert-link-color: var(--bs-warning-text-emphasis);
}

.alert-danger {
  --bs-alert-color: var(--bs-danger-text-emphasis);
  --bs-alert-bg: var(--bs-danger-bg-subtle);
  --bs-alert-border-color: var(--bs-danger-border-subtle);
  --bs-alert-link-color: var(--bs-danger-text-emphasis);
}

.alert-light {
  --bs-alert-color: var(--bs-light-text-emphasis);
  --bs-alert-bg: var(--bs-light-bg-subtle);
  --bs-alert-border-color: var(--bs-light-border-subtle);
  --bs-alert-link-color: var(--bs-light-text-emphasis);
}

.alert-gray {
  --bs-alert-color: var(--bs-gray-text-emphasis);
  --bs-alert-bg: var(--bs-gray-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-border-subtle);
  --bs-alert-link-color: var(--bs-gray-text-emphasis);
}

.alert-dark {
  --bs-alert-color: var(--bs-dark-text-emphasis);
  --bs-alert-bg: var(--bs-dark-bg-subtle);
  --bs-alert-border-color: var(--bs-dark-border-subtle);
  --bs-alert-link-color: var(--bs-dark-text-emphasis);
}

.alert-gray-1 {
  --bs-alert-color: var(--bs-gray-1-text-emphasis);
  --bs-alert-bg: var(--bs-gray-1-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-1-border-subtle);
  --bs-alert-link-color: var(--bs-gray-1-text-emphasis);
}

.alert-gray-2 {
  --bs-alert-color: var(--bs-gray-2-text-emphasis);
  --bs-alert-bg: var(--bs-gray-2-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-2-border-subtle);
  --bs-alert-link-color: var(--bs-gray-2-text-emphasis);
}

.alert-gray-3 {
  --bs-alert-color: var(--bs-gray-3-text-emphasis);
  --bs-alert-bg: var(--bs-gray-3-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-3-border-subtle);
  --bs-alert-link-color: var(--bs-gray-3-text-emphasis);
}

.alert-gray-4 {
  --bs-alert-color: var(--bs-gray-4-text-emphasis);
  --bs-alert-bg: var(--bs-gray-4-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-4-border-subtle);
  --bs-alert-link-color: var(--bs-gray-4-text-emphasis);
}

.alert-gray-5 {
  --bs-alert-color: var(--bs-gray-5-text-emphasis);
  --bs-alert-bg: var(--bs-gray-5-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-5-border-subtle);
  --bs-alert-link-color: var(--bs-gray-5-text-emphasis);
}

.alert-gray-6 {
  --bs-alert-color: var(--bs-gray-6-text-emphasis);
  --bs-alert-bg: var(--bs-gray-6-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-6-border-subtle);
  --bs-alert-link-color: var(--bs-gray-6-text-emphasis);
}

.alert-gray-7 {
  --bs-alert-color: var(--bs-gray-7-text-emphasis);
  --bs-alert-bg: var(--bs-gray-7-bg-subtle);
  --bs-alert-border-color: var(--bs-gray-7-border-subtle);
  --bs-alert-link-color: var(--bs-gray-7-text-emphasis);
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress,
.progress-stacked {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: var(--bs-secondary-bg);
  --bs-progress-border-radius: var(--bs-border-radius);
  --bs-progress-box-shadow: var(--bs-box-shadow-inset);
  --bs-progress-bar-color: #ffffff;
  --bs-progress-bar-bg: #8c1d40;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--bs-progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-progress-bar-bg);
  transition: var(--bs-progress-bar-transition);
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--bs-progress-height) var(--bs-progress-height);
}

.progress-stacked > .progress {
  overflow: visible;
}

.progress-stacked > .progress > .progress-bar {
  width: 100%;
}

.progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    animation: none;
  }
}

.list-group {
  --bs-list-group-color: var(--bs-body-color);
  --bs-list-group-bg: var(--bs-body-bg);
  --bs-list-group-border-color: var(--bs-border-color);
  --bs-list-group-border-width: var(--bs-border-width);
  --bs-list-group-border-radius: var(--bs-border-radius);
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: var(--bs-secondary-color);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-tertiary-bg);
  --bs-list-group-action-active-color: var(--bs-body-color);
  --bs-list-group-action-active-bg: var(--bs-secondary-bg);
  --bs-list-group-disabled-color: var(--bs-secondary-color);
  --bs-list-group-disabled-bg: var(--bs-body-bg);
  --bs-list-group-active-color: #ffffff;
  --bs-list-group-active-bg: #8c1d40;
  --bs-list-group-active-border-color: #8c1d40;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius);
}

.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > .list-group-item::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}

.list-group-item-action {
  width: 100%;
  color: var(--bs-list-group-action-color);
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: var(--bs-list-group-action-hover-color);
  text-decoration: none;
  background-color: var(--bs-list-group-action-hover-bg);
}
.list-group-item-action:active {
  color: var(--bs-list-group-action-active-color);
  background-color: var(--bs-list-group-action-active-bg);
}

.list-group-item {
  position: relative;
  display: block;
  padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
  color: var(--bs-list-group-color);
  text-decoration: none;
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color);
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: var(--bs-list-group-disabled-color);
  pointer-events: none;
  background-color: var(--bs-list-group-disabled-bg);
}
.list-group-item.active {
  z-index: 2;
  color: var(--bs-list-group-active-color);
  background-color: var(--bs-list-group-active-bg);
  border-color: var(--bs-list-group-active-border-color);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: calc(-1 * var(--bs-list-group-border-width));
  border-top-width: var(--bs-list-group-border-width);
}

.list-group-horizontal {
  flex-direction: row;
}
.list-group-horizontal > .list-group-item:first-child:not(:last-child) {
  border-bottom-left-radius: var(--bs-list-group-border-radius);
  border-top-right-radius: 0;
}
.list-group-horizontal > .list-group-item:last-child:not(:first-child) {
  border-top-right-radius: var(--bs-list-group-border-radius);
  border-bottom-left-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: var(--bs-list-group-border-width);
  border-left-width: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: calc(-1 * var(--bs-list-group-border-width));
  border-left-width: var(--bs-list-group-border-width);
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 1260px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 var(--bs-list-group-border-width);
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-gold {
  --bs-list-group-color: var(--bs-gold-text-emphasis);
  --bs-list-group-bg: var(--bs-gold-bg-subtle);
  --bs-list-group-border-color: var(--bs-gold-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gold-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gold-border-subtle);
  --bs-list-group-active-color: var(--bs-gold-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gold-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gold-text-emphasis);
}

.list-group-item-maroon {
  --bs-list-group-color: var(--bs-maroon-text-emphasis);
  --bs-list-group-bg: var(--bs-maroon-bg-subtle);
  --bs-list-group-border-color: var(--bs-maroon-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-maroon-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-maroon-border-subtle);
  --bs-list-group-active-color: var(--bs-maroon-bg-subtle);
  --bs-list-group-active-bg: var(--bs-maroon-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-maroon-text-emphasis);
}

.list-group-item-success {
  --bs-list-group-color: var(--bs-success-text-emphasis);
  --bs-list-group-bg: var(--bs-success-bg-subtle);
  --bs-list-group-border-color: var(--bs-success-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-success-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-success-border-subtle);
  --bs-list-group-active-color: var(--bs-success-bg-subtle);
  --bs-list-group-active-bg: var(--bs-success-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-success-text-emphasis);
}

.list-group-item-info {
  --bs-list-group-color: var(--bs-info-text-emphasis);
  --bs-list-group-bg: var(--bs-info-bg-subtle);
  --bs-list-group-border-color: var(--bs-info-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-info-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-info-border-subtle);
  --bs-list-group-active-color: var(--bs-info-bg-subtle);
  --bs-list-group-active-bg: var(--bs-info-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-info-text-emphasis);
}

.list-group-item-warning {
  --bs-list-group-color: var(--bs-warning-text-emphasis);
  --bs-list-group-bg: var(--bs-warning-bg-subtle);
  --bs-list-group-border-color: var(--bs-warning-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-warning-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-warning-border-subtle);
  --bs-list-group-active-color: var(--bs-warning-bg-subtle);
  --bs-list-group-active-bg: var(--bs-warning-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-warning-text-emphasis);
}

.list-group-item-danger {
  --bs-list-group-color: var(--bs-danger-text-emphasis);
  --bs-list-group-bg: var(--bs-danger-bg-subtle);
  --bs-list-group-border-color: var(--bs-danger-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-danger-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-danger-border-subtle);
  --bs-list-group-active-color: var(--bs-danger-bg-subtle);
  --bs-list-group-active-bg: var(--bs-danger-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-danger-text-emphasis);
}

.list-group-item-light {
  --bs-list-group-color: var(--bs-light-text-emphasis);
  --bs-list-group-bg: var(--bs-light-bg-subtle);
  --bs-list-group-border-color: var(--bs-light-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-light-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-light-border-subtle);
  --bs-list-group-active-color: var(--bs-light-bg-subtle);
  --bs-list-group-active-bg: var(--bs-light-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-light-text-emphasis);
}

.list-group-item-gray {
  --bs-list-group-color: var(--bs-gray-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-text-emphasis);
}

.list-group-item-dark {
  --bs-list-group-color: var(--bs-dark-text-emphasis);
  --bs-list-group-bg: var(--bs-dark-bg-subtle);
  --bs-list-group-border-color: var(--bs-dark-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-dark-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-dark-border-subtle);
  --bs-list-group-active-color: var(--bs-dark-bg-subtle);
  --bs-list-group-active-bg: var(--bs-dark-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-dark-text-emphasis);
}

.list-group-item-gray-1 {
  --bs-list-group-color: var(--bs-gray-1-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-1-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-1-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-1-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-1-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-1-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-1-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-1-text-emphasis);
}

.list-group-item-gray-2 {
  --bs-list-group-color: var(--bs-gray-2-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-2-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-2-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-2-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-2-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-2-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-2-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-2-text-emphasis);
}

.list-group-item-gray-3 {
  --bs-list-group-color: var(--bs-gray-3-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-3-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-3-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-3-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-3-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-3-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-3-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-3-text-emphasis);
}

.list-group-item-gray-4 {
  --bs-list-group-color: var(--bs-gray-4-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-4-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-4-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-4-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-4-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-4-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-4-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-4-text-emphasis);
}

.list-group-item-gray-5 {
  --bs-list-group-color: var(--bs-gray-5-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-5-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-5-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-5-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-5-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-5-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-5-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-5-text-emphasis);
}

.list-group-item-gray-6 {
  --bs-list-group-color: var(--bs-gray-6-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-6-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-6-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-6-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-6-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-6-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-6-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-6-text-emphasis);
}

.list-group-item-gray-7 {
  --bs-list-group-color: var(--bs-gray-7-text-emphasis);
  --bs-list-group-bg: var(--bs-gray-7-bg-subtle);
  --bs-list-group-border-color: var(--bs-gray-7-border-subtle);
  --bs-list-group-action-hover-color: var(--bs-emphasis-color);
  --bs-list-group-action-hover-bg: var(--bs-gray-7-border-subtle);
  --bs-list-group-action-active-color: var(--bs-emphasis-color);
  --bs-list-group-action-active-bg: var(--bs-gray-7-border-subtle);
  --bs-list-group-active-color: var(--bs-gray-7-bg-subtle);
  --bs-list-group-active-bg: var(--bs-gray-7-text-emphasis);
  --bs-list-group-active-border-color: var(--bs-gray-7-text-emphasis);
}

.btn-close {
  --bs-btn-close-color: #000;
  --bs-btn-close-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e");
  --bs-btn-close-opacity: 0.5;
  --bs-btn-close-hover-opacity: 0.75;
  --bs-btn-close-focus-shadow: 0 0 0 0.25rem rgba(140, 29, 64, 0.25);
  --bs-btn-close-focus-opacity: 1;
  --bs-btn-close-disabled-opacity: 0.25;
  --bs-btn-close-white-filter: invert(1) grayscale(100%) brightness(200%);
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: var(--bs-btn-close-color);
  background: transparent var(--bs-btn-close-bg) center/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: var(--bs-btn-close-opacity);
}
.btn-close:hover {
  color: var(--bs-btn-close-color);
  text-decoration: none;
  opacity: var(--bs-btn-close-hover-opacity);
}
.btn-close:focus {
  outline: 0;
  box-shadow: var(--bs-btn-close-focus-shadow);
  opacity: var(--bs-btn-close-focus-opacity);
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: var(--bs-btn-close-disabled-opacity);
}

.btn-close-white {
  filter: var(--bs-btn-close-white-filter);
}

[data-bs-theme=dark] .btn-close {
  filter: var(--bs-btn-close-white-filter);
}

.toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 24px;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: ;
  --bs-toast-bg: rgba(var(--bs-body-bg-rgb), 0.85);
  --bs-toast-border-width: var(--bs-border-width);
  --bs-toast-border-color: var(--bs-border-color-translucent);
  --bs-toast-border-radius: var(--bs-border-radius);
  --bs-toast-box-shadow: var(--bs-box-shadow);
  --bs-toast-header-color: var(--bs-secondary-color);
  --bs-toast-header-bg: rgba(var(--bs-body-bg-rgb), 0.85);
  --bs-toast-header-border-color: var(--bs-border-color-translucent);
  width: var(--bs-toast-max-width);
  max-width: 100%;
  font-size: var(--bs-toast-font-size);
  color: var(--bs-toast-color);
  pointer-events: auto;
  background-color: var(--bs-toast-bg);
  background-clip: padding-box;
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  box-shadow: var(--bs-toast-box-shadow);
  border-radius: var(--bs-toast-border-radius);
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.toast-container {
  --bs-toast-zindex: 1090;
  position: absolute;
  z-index: var(--bs-toast-zindex);
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: var(--bs-toast-spacing);
}

.toast-header {
  display: flex;
  align-items: center;
  padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x);
  color: var(--bs-toast-header-color);
  background-color: var(--bs-toast-header-bg);
  background-clip: padding-box;
  border-bottom: var(--bs-toast-border-width) solid var(--bs-toast-header-border-color);
  border-top-left-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
  border-top-right-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
}
.toast-header .btn-close {
  margin-right: calc(-0.5 * var(--bs-toast-padding-x));
  margin-left: var(--bs-toast-padding-x);
}

.toast-body {
  padding: var(--bs-toast-padding-x);
  word-wrap: break-word;
}

.modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 1rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: var(--bs-body-bg);
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: var(--bs-border-width);
  --bs-modal-border-radius: var(--bs-border-radius-lg);
  --bs-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-modal-inner-border-radius: calc(var(--bs-border-radius-lg) - (var(--bs-border-width)));
  --bs-modal-header-padding-x: 1rem;
  --bs-modal-header-padding-y: 1rem;
  --bs-modal-header-padding: 1rem 1rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: var(--bs-border-width);
  --bs-modal-title-line-height: 1.5;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: var(--bs-border-width);
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
}

.modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000;
  --bs-backdrop-opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * 0.5) calc(var(--bs-modal-header-padding-x) * 0.5);
  margin: calc(-0.5 * var(--bs-modal-header-padding-y)) calc(-0.5 * var(--bs-modal-header-padding-x)) calc(-0.5 * var(--bs-modal-header-padding-y)) auto;
}

.modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * 0.5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
}
.modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * 0.5);
}

@media (min-width: 576px) {
  .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }
  .modal-sm {
    --bs-modal-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
  .modal-xl {
    --bs-modal-width: 800px;
  }
}
@media (min-width: 1260px) {
  .modal-xl {
    --bs-modal-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header,
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header,
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header,
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1259.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header,
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
.tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 0.5rem;
  --bs-tooltip-padding-y: 0.25rem;
  --bs-tooltip-margin: ;
  --bs-tooltip-font-size: 0.875rem;
  --bs-tooltip-color: var(--bs-body-bg);
  --bs-tooltip-bg: var(--bs-emphasis-color);
  --bs-tooltip-border-radius: var(--bs-border-radius);
  --bs-tooltip-opacity: 0.9;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  z-index: var(--bs-tooltip-zindex);
  display: block;
  margin: var(--bs-tooltip-margin);
  font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans, sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-tooltip-font-size);
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: var(--bs-tooltip-opacity);
}
.tooltip .tooltip-arrow {
  display: block;
  width: var(--bs-tooltip-arrow-width);
  height: var(--bs-tooltip-arrow-height);
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: calc(-1 * var(--bs-tooltip-arrow-height));
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-top-color: var(--bs-tooltip-bg);
}

/* rtl:begin:ignore */
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: calc(-1 * var(--bs-tooltip-arrow-height));
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-right-color: var(--bs-tooltip-bg);
}

/* rtl:end:ignore */
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: calc(-1 * var(--bs-tooltip-arrow-height));
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-bottom-color: var(--bs-tooltip-bg);
}

/* rtl:begin:ignore */
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: calc(-1 * var(--bs-tooltip-arrow-height));
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-left-color: var(--bs-tooltip-bg);
}

/* rtl:end:ignore */
.tooltip-inner {
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  color: var(--bs-tooltip-color);
  text-align: center;
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius);
}

.popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 0.875rem;
  --bs-popover-bg: var(--bs-body-bg);
  --bs-popover-border-width: var(--bs-border-width);
  --bs-popover-border-color: var(--bs-border-color-translucent);
  --bs-popover-border-radius: var(--bs-border-radius-lg);
  --bs-popover-inner-border-radius: calc(var(--bs-border-radius-lg) - var(--bs-border-width));
  --bs-popover-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-popover-header-padding-x: 1rem;
  --bs-popover-header-padding-y: 0.5rem;
  --bs-popover-header-font-size: 1rem;
  --bs-popover-header-color: inherit;
  --bs-popover-header-bg: var(--bs-secondary-bg);
  --bs-popover-body-padding-x: 1rem;
  --bs-popover-body-padding-y: 1rem;
  --bs-popover-body-color: var(--bs-body-color);
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  z-index: var(--bs-popover-zindex);
  display: block;
  max-width: var(--bs-popover-max-width);
  font-family: Arial, Helvetica, "Nimbus Sans L", "Liberation Sans", FreeSans, sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-popover-font-size);
  word-wrap: break-word;
  background-color: var(--bs-popover-bg);
  background-clip: padding-box;
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius);
}
.popover .popover-arrow {
  display: block;
  width: var(--bs-popover-arrow-width);
  height: var(--bs-popover-arrow-height);
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before, .bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  border-width: var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-top-color: var(--bs-popover-arrow-border);
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: var(--bs-popover-border-width);
  border-top-color: var(--bs-popover-bg);
}

/* rtl:begin:ignore */
.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before, .bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-right-color: var(--bs-popover-arrow-border);
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: var(--bs-popover-border-width);
  border-right-color: var(--bs-popover-bg);
}

/* rtl:end:ignore */
.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before, .bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  border-width: 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-bottom-color: var(--bs-popover-arrow-border);
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: var(--bs-popover-border-width);
  border-bottom-color: var(--bs-popover-bg);
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: var(--bs-popover-arrow-width);
  margin-left: calc(-0.5 * var(--bs-popover-arrow-width));
  content: "";
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-header-bg);
}

/* rtl:begin:ignore */
.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before, .bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-left-color: var(--bs-popover-arrow-border);
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: var(--bs-popover-border-width);
  border-left-color: var(--bs-popover-bg);
}

/* rtl:end:ignore */
.popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--bs-popover-header-font-size);
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
  color: var(--bs-popover-body-color);
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%);
}

.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
  .carousel-fade .active.carousel-item-end {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #ffffff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
  .carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23ffffff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
}
.carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #ffffff;
  text-align: center;
}

.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.carousel-dark .carousel-caption {
  color: #000;
}

[data-bs-theme=dark] .carousel .carousel-control-prev-icon,
[data-bs-theme=dark] .carousel .carousel-control-next-icon, [data-bs-theme=dark].carousel .carousel-control-prev-icon,
[data-bs-theme=dark].carousel .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
[data-bs-theme=dark] .carousel .carousel-indicators [data-bs-target], [data-bs-theme=dark].carousel .carousel-indicators [data-bs-target] {
  background-color: #000;
}
[data-bs-theme=dark] .carousel .carousel-caption, [data-bs-theme=dark].carousel .carousel-caption {
  color: #000;
}

.spinner-grow,
.spinner-border {
  display: inline-block;
  width: var(--bs-spinner-width);
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  border-radius: 50%;
  animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name);
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.25em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-border;
  border: var(--bs-spinner-border-width) solid currentcolor;
  border-right-color: transparent;
}

.spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.2em;
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0;
}

.spinner-grow-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
  .spinner-grow {
    --bs-spinner-animation-speed: 1.5s;
  }
}
.offcanvas, .offcanvas-xl, .offcanvas-lg, .offcanvas-md, .offcanvas-sm {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: var(--bs-body-color);
  --bs-offcanvas-bg: var(--bs-body-bg);
  --bs-offcanvas-border-width: var(--bs-border-width);
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-offcanvas-transition: transform 0.3s ease-in-out;
  --bs-offcanvas-title-line-height: 1.5;
}

@media (max-width: 575.98px) {
  .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .offcanvas-sm.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-sm.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-sm.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-sm.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-sm.showing, .offcanvas-sm.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-sm.showing, .offcanvas-sm.hiding, .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .offcanvas-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 767.98px) {
  .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .offcanvas-md.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-md.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-md.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-md.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-md.showing, .offcanvas-md.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-md.showing, .offcanvas-md.hiding, .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-md .offcanvas-header {
    display: none;
  }
  .offcanvas-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 991.98px) {
  .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .offcanvas-lg.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-lg.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-lg.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-lg.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-lg.showing, .offcanvas-lg.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-lg.showing, .offcanvas-lg.hiding, .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .offcanvas-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

@media (max-width: 1259.98px) {
  .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: var(--bs-offcanvas-transition);
  }
}
@media (max-width: 1259.98px) and (prefers-reduced-motion: reduce) {
  .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1259.98px) {
  .offcanvas-xl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .offcanvas-xl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .offcanvas-xl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .offcanvas-xl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .offcanvas-xl.showing, .offcanvas-xl.show:not(.hiding) {
    transform: none;
  }
  .offcanvas-xl.showing, .offcanvas-xl.hiding, .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1260px) {
  .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .offcanvas-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}

.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: var(--bs-offcanvas-transition);
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}
.offcanvas.offcanvas-start {
  top: 0;
  left: 0;
  width: var(--bs-offcanvas-width);
  border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(-100%);
}
.offcanvas.offcanvas-end {
  top: 0;
  right: 0;
  width: var(--bs-offcanvas-width);
  border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(100%);
}
.offcanvas.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(-100%);
}
.offcanvas.offcanvas-bottom {
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.offcanvas.showing, .offcanvas.show:not(.hiding) {
  transform: none;
}
.offcanvas.showing, .offcanvas.hiding, .offcanvas.show {
  visibility: visible;
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.offcanvas-header .btn-close {
  padding: calc(var(--bs-offcanvas-padding-y) * 0.5) calc(var(--bs-offcanvas-padding-x) * 0.5);
  margin-top: calc(-0.5 * var(--bs-offcanvas-padding-y));
  margin-right: calc(-0.5 * var(--bs-offcanvas-padding-x));
  margin-bottom: calc(-0.5 * var(--bs-offcanvas-padding-y));
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: var(--bs-offcanvas-title-line-height);
}

.offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto;
}

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5;
}
.placeholder.btn::before, .placeholder.uds-modal-close-btn::before {
  display: inline-block;
  content: "";
}

.placeholder-xs {
  min-height: 0.6em;
}

.placeholder-sm {
  min-height: 0.8em;
}

.placeholder-lg {
  min-height: 1.2em;
}

.placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite;
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  -webkit-mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
          mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
  -webkit-mask-size: 200% 100%;
          mask-size: 200% 100%;
  animation: placeholder-wave 2s linear infinite;
}

@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}
.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.text-bg-gold {
  color: #000 !important;
  background-color: RGBA(255, 198, 39, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-maroon {
  color: #ffffff !important;
  background-color: RGBA(140, 29, 64, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-success {
  color: #000 !important;
  background-color: RGBA(120, 190, 32, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-info {
  color: #000 !important;
  background-color: RGBA(0, 163, 224, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-warning {
  color: #000 !important;
  background-color: RGBA(255, 127, 50, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-danger {
  color: #ffffff !important;
  background-color: RGBA(204, 47, 47, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-light {
  color: #000 !important;
  background-color: RGBA(232, 232, 232, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray {
  color: #000 !important;
  background-color: RGBA(191, 191, 191, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-dark {
  color: #ffffff !important;
  background-color: RGBA(25, 25, 25, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-1 {
  color: #000 !important;
  background-color: RGBA(250, 250, 250, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-2 {
  color: #000 !important;
  background-color: RGBA(232, 232, 232, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-3 {
  color: #000 !important;
  background-color: RGBA(208, 208, 208, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-4 {
  color: #000 !important;
  background-color: RGBA(191, 191, 191, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-5 {
  color: #ffffff !important;
  background-color: RGBA(116, 116, 116, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-6 {
  color: #ffffff !important;
  background-color: RGBA(72, 72, 72, var(--bs-bg-opacity, 1)) !important;
}

.text-bg-gray-7 {
  color: #ffffff !important;
  background-color: RGBA(25, 25, 25, var(--bs-bg-opacity, 1)) !important;
}

.link-gold {
  color: RGBA(var(--bs-gold-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gold-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gold:hover, .link-gold:focus {
  color: RGBA(255, 209, 82, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(255, 209, 82, var(--bs-link-underline-opacity, 1)) !important;
}

.link-maroon {
  color: RGBA(var(--bs-maroon-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-maroon-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-maroon:hover, .link-maroon:focus {
  color: RGBA(112, 23, 51, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(112, 23, 51, var(--bs-link-underline-opacity, 1)) !important;
}

.link-success {
  color: RGBA(var(--bs-success-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-success-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-success:hover, .link-success:focus {
  color: RGBA(147, 203, 77, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(147, 203, 77, var(--bs-link-underline-opacity, 1)) !important;
}

.link-info {
  color: RGBA(var(--bs-info-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-info-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-info:hover, .link-info:focus {
  color: RGBA(51, 181, 230, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(51, 181, 230, var(--bs-link-underline-opacity, 1)) !important;
}

.link-warning {
  color: RGBA(var(--bs-warning-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-warning-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-warning:hover, .link-warning:focus {
  color: RGBA(255, 153, 91, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(255, 153, 91, var(--bs-link-underline-opacity, 1)) !important;
}

.link-danger {
  color: RGBA(var(--bs-danger-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-danger-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-danger:hover, .link-danger:focus {
  color: RGBA(163, 38, 38, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(163, 38, 38, var(--bs-link-underline-opacity, 1)) !important;
}

.link-light {
  color: RGBA(var(--bs-light-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-light-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-light:hover, .link-light:focus {
  color: RGBA(237, 237, 237, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(237, 237, 237, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray {
  color: RGBA(var(--bs-gray-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray:hover, .link-gray:focus {
  color: RGBA(204, 204, 204, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(204, 204, 204, var(--bs-link-underline-opacity, 1)) !important;
}

.link-dark {
  color: RGBA(var(--bs-dark-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-dark-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-dark:hover, .link-dark:focus {
  color: RGBA(20, 20, 20, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(20, 20, 20, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-1 {
  color: RGBA(var(--bs-gray-1-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-1-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-1:hover, .link-gray-1:focus {
  color: RGBA(251, 251, 251, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(251, 251, 251, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-2 {
  color: RGBA(var(--bs-gray-2-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-2-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-2:hover, .link-gray-2:focus {
  color: RGBA(237, 237, 237, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(237, 237, 237, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-3 {
  color: RGBA(var(--bs-gray-3-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-3-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-3:hover, .link-gray-3:focus {
  color: RGBA(217, 217, 217, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(217, 217, 217, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-4 {
  color: RGBA(var(--bs-gray-4-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-4-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-4:hover, .link-gray-4:focus {
  color: RGBA(204, 204, 204, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(204, 204, 204, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-5 {
  color: RGBA(var(--bs-gray-5-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-5-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-5:hover, .link-gray-5:focus {
  color: RGBA(93, 93, 93, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(93, 93, 93, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-6 {
  color: RGBA(var(--bs-gray-6-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-6-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-6:hover, .link-gray-6:focus {
  color: RGBA(58, 58, 58, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(58, 58, 58, var(--bs-link-underline-opacity, 1)) !important;
}

.link-gray-7 {
  color: RGBA(var(--bs-gray-7-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-gray-7-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-gray-7:hover, .link-gray-7:focus {
  color: RGBA(20, 20, 20, var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(20, 20, 20, var(--bs-link-underline-opacity, 1)) !important;
}

.link-body-emphasis {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 1)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}
.link-body-emphasis:hover, .link-body-emphasis:focus {
  color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-opacity, 0.75)) !important;
  text-decoration-color: RGBA(var(--bs-emphasis-color-rgb), var(--bs-link-underline-opacity, 0.75)) !important;
}

.focus-ring:focus {
  outline: 0;
  box-shadow: var(--bs-focus-ring-x, 0) var(--bs-focus-ring-y, 0) var(--bs-focus-ring-blur, 0) var(--bs-focus-ring-width) var(--bs-focus-ring-color);
}

.icon-link {
  display: inline-flex;
  gap: 0.375rem;
  align-items: center;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 0.5));
  text-underline-offset: 0.25em;
  backface-visibility: hidden;
}
.icon-link > .bi {
  flex-shrink: 0;
  width: 1em;
  height: 1em;
  fill: currentcolor;
  transition: 0.2s ease-in-out transform;
}
@media (prefers-reduced-motion: reduce) {
  .icon-link > .bi {
    transition: none;
  }
}

.icon-link-hover:hover > .bi, .icon-link-hover:focus-visible > .bi {
  transform: var(--bs-icon-link-transform, translate3d(0.25em, 0, 0));
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.ratio-1x1 {
  --bs-aspect-ratio: 100%;
}

.ratio-4x3 {
  --bs-aspect-ratio: 75%;
}

.ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1260px) {
  .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.visually-hidden:not(caption),
.visually-hidden-focusable:not(:focus):not(:focus-within):not(caption) {
  position: absolute !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

.float-start {
  float: left !important;
}

.float-end {
  float: right !important;
}

.float-none {
  float: none !important;
}

.object-fit-contain {
  -o-object-fit: contain !important;
     object-fit: contain !important;
}

.object-fit-cover {
  -o-object-fit: cover !important;
     object-fit: cover !important;
}

.object-fit-fill {
  -o-object-fit: fill !important;
     object-fit: fill !important;
}

.object-fit-scale {
  -o-object-fit: scale-down !important;
     object-fit: scale-down !important;
}

.object-fit-none {
  -o-object-fit: none !important;
     object-fit: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.overflow-x-auto {
  overflow-x: auto !important;
}

.overflow-x-hidden {
  overflow-x: hidden !important;
}

.overflow-x-visible {
  overflow-x: visible !important;
}

.overflow-x-scroll {
  overflow-x: scroll !important;
}

.overflow-y-auto {
  overflow-y: auto !important;
}

.overflow-y-hidden {
  overflow-y: hidden !important;
}

.overflow-y-visible {
  overflow-y: visible !important;
}

.overflow-y-scroll {
  overflow-y: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-inline-grid {
  display: inline-grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.focus-ring-gold {
  --bs-focus-ring-color: rgba(var(--bs-gold-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-maroon {
  --bs-focus-ring-color: rgba(var(--bs-maroon-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-success {
  --bs-focus-ring-color: rgba(var(--bs-success-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-info {
  --bs-focus-ring-color: rgba(var(--bs-info-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-warning {
  --bs-focus-ring-color: rgba(var(--bs-warning-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-danger {
  --bs-focus-ring-color: rgba(var(--bs-danger-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-light {
  --bs-focus-ring-color: rgba(var(--bs-light-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray {
  --bs-focus-ring-color: rgba(var(--bs-gray-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-dark {
  --bs-focus-ring-color: rgba(var(--bs-dark-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-1 {
  --bs-focus-ring-color: rgba(var(--bs-gray-1-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-2 {
  --bs-focus-ring-color: rgba(var(--bs-gray-2-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-3 {
  --bs-focus-ring-color: rgba(var(--bs-gray-3-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-4 {
  --bs-focus-ring-color: rgba(var(--bs-gray-4-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-5 {
  --bs-focus-ring-color: rgba(var(--bs-gray-5-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-6 {
  --bs-focus-ring-color: rgba(var(--bs-gray-6-rgb), var(--bs-focus-ring-opacity));
}

.focus-ring-gray-7 {
  --bs-focus-ring-color: rgba(var(--bs-gray-7-rgb), var(--bs-focus-ring-opacity));
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

.border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-end-0 {
  border-right: 0 !important;
}

.border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

.border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}

.border-start-0 {
  border-left: 0 !important;
}

.border-gold {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gold-rgb), var(--bs-border-opacity)) !important;
}

.border-maroon {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-maroon-rgb), var(--bs-border-opacity)) !important;
}

.border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}

.border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}

.border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}

.border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}

.border-light {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
}

.border-gray {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-rgb), var(--bs-border-opacity)) !important;
}

.border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-1 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-1-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-2 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-2-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-3 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-3-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-4 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-4-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-5 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-5-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-6 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-6-rgb), var(--bs-border-opacity)) !important;
}

.border-gray-7 {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-gray-7-rgb), var(--bs-border-opacity)) !important;
}

.border-black {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-black-rgb), var(--bs-border-opacity)) !important;
}

.border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}

.border-primary-subtle {
  border-color: var(--bs-primary-border-subtle) !important;
}

.border-secondary-subtle {
  border-color: var(--bs-secondary-border-subtle) !important;
}

.border-success-subtle {
  border-color: var(--bs-success-border-subtle) !important;
}

.border-info-subtle {
  border-color: var(--bs-info-border-subtle) !important;
}

.border-warning-subtle {
  border-color: var(--bs-warning-border-subtle) !important;
}

.border-danger-subtle {
  border-color: var(--bs-danger-border-subtle) !important;
}

.border-light-subtle {
  border-color: var(--bs-light-border-subtle) !important;
}

.border-dark-subtle {
  border-color: var(--bs-dark-border-subtle) !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-opacity-10 {
  --bs-border-opacity: 0.1;
}

.border-opacity-25 {
  --bs-border-opacity: 0.25;
}

.border-opacity-50 {
  --bs-border-opacity: 0.5;
}

.border-opacity-75 {
  --bs-border-opacity: 0.75;
}

.border-opacity-100 {
  --bs-border-opacity: 1;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0rem !important;
}

.m-1 {
  margin: 0.5rem !important;
}

.m-2 {
  margin: 1rem !important;
}

.m-3 {
  margin: 1.5rem !important;
}

.m-4 {
  margin: 2rem !important;
}

.m-5 {
  margin: 2.5rem !important;
}

.m-6 {
  margin: 3rem !important;
}

.m-7 {
  margin: 3.5rem !important;
}

.m-8 {
  margin: 4rem !important;
}

.m-9 {
  margin: 4.5rem !important;
}

.m-10 {
  margin: 5rem !important;
}

.m-12 {
  margin: 6rem !important;
}

.m-14 {
  margin: 7rem !important;
}

.m-16 {
  margin: 8rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0rem !important;
  margin-left: 0rem !important;
}

.mx-1 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-2 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-3 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-4 {
  margin-right: 2rem !important;
  margin-left: 2rem !important;
}

.mx-5 {
  margin-right: 2.5rem !important;
  margin-left: 2.5rem !important;
}

.mx-6 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-7 {
  margin-right: 3.5rem !important;
  margin-left: 3.5rem !important;
}

.mx-8 {
  margin-right: 4rem !important;
  margin-left: 4rem !important;
}

.mx-9 {
  margin-right: 4.5rem !important;
  margin-left: 4.5rem !important;
}

.mx-10 {
  margin-right: 5rem !important;
  margin-left: 5rem !important;
}

.mx-12 {
  margin-right: 6rem !important;
  margin-left: 6rem !important;
}

.mx-14 {
  margin-right: 7rem !important;
  margin-left: 7rem !important;
}

.mx-16 {
  margin-right: 8rem !important;
  margin-left: 8rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}

.my-1 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-2 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-3 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-4 {
  margin-top: 2rem !important;
  margin-bottom: 2rem !important;
}

.my-5 {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.my-6 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-7 {
  margin-top: 3.5rem !important;
  margin-bottom: 3.5rem !important;
}

.my-8 {
  margin-top: 4rem !important;
  margin-bottom: 4rem !important;
}

.my-9 {
  margin-top: 4.5rem !important;
  margin-bottom: 4.5rem !important;
}

.my-10 {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important;
}

.my-12 {
  margin-top: 6rem !important;
  margin-bottom: 6rem !important;
}

.my-14 {
  margin-top: 7rem !important;
  margin-bottom: 7rem !important;
}

.my-16 {
  margin-top: 8rem !important;
  margin-bottom: 8rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0rem !important;
}

.mt-1 {
  margin-top: 0.5rem !important;
}

.mt-2 {
  margin-top: 1rem !important;
}

.mt-3 {
  margin-top: 1.5rem !important;
}

.mt-4 {
  margin-top: 2rem !important;
}

.mt-5 {
  margin-top: 2.5rem !important;
}

.mt-6 {
  margin-top: 3rem !important;
}

.mt-7 {
  margin-top: 3.5rem !important;
}

.mt-8 {
  margin-top: 4rem !important;
}

.mt-9 {
  margin-top: 4.5rem !important;
}

.mt-10 {
  margin-top: 5rem !important;
}

.mt-12 {
  margin-top: 6rem !important;
}

.mt-14 {
  margin-top: 7rem !important;
}

.mt-16 {
  margin-top: 8rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

.me-0 {
  margin-right: 0rem !important;
}

.me-1 {
  margin-right: 0.5rem !important;
}

.me-2 {
  margin-right: 1rem !important;
}

.me-3 {
  margin-right: 1.5rem !important;
}

.me-4 {
  margin-right: 2rem !important;
}

.me-5 {
  margin-right: 2.5rem !important;
}

.me-6 {
  margin-right: 3rem !important;
}

.me-7 {
  margin-right: 3.5rem !important;
}

.me-8 {
  margin-right: 4rem !important;
}

.me-9 {
  margin-right: 4.5rem !important;
}

.me-10 {
  margin-right: 5rem !important;
}

.me-12 {
  margin-right: 6rem !important;
}

.me-14 {
  margin-right: 7rem !important;
}

.me-16 {
  margin-right: 8rem !important;
}

.me-auto {
  margin-right: auto !important;
}

.mb-0 {
  margin-bottom: 0rem !important;
}

.mb-1 {
  margin-bottom: 0.5rem !important;
}

.mb-2 {
  margin-bottom: 1rem !important;
}

.mb-3 {
  margin-bottom: 1.5rem !important;
}

.mb-4 {
  margin-bottom: 2rem !important;
}

.mb-5 {
  margin-bottom: 2.5rem !important;
}

.mb-6 {
  margin-bottom: 3rem !important;
}

.mb-7 {
  margin-bottom: 3.5rem !important;
}

.mb-8 {
  margin-bottom: 4rem !important;
}

.mb-9 {
  margin-bottom: 4.5rem !important;
}

.mb-10 {
  margin-bottom: 5rem !important;
}

.mb-12 {
  margin-bottom: 6rem !important;
}

.mb-14 {
  margin-bottom: 7rem !important;
}

.mb-16 {
  margin-bottom: 8rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

.ms-0 {
  margin-left: 0rem !important;
}

.ms-1 {
  margin-left: 0.5rem !important;
}

.ms-2 {
  margin-left: 1rem !important;
}

.ms-3 {
  margin-left: 1.5rem !important;
}

.ms-4 {
  margin-left: 2rem !important;
}

.ms-5 {
  margin-left: 2.5rem !important;
}

.ms-6 {
  margin-left: 3rem !important;
}

.ms-7 {
  margin-left: 3.5rem !important;
}

.ms-8 {
  margin-left: 4rem !important;
}

.ms-9 {
  margin-left: 4.5rem !important;
}

.ms-10 {
  margin-left: 5rem !important;
}

.ms-12 {
  margin-left: 6rem !important;
}

.ms-14 {
  margin-left: 7rem !important;
}

.ms-16 {
  margin-left: 8rem !important;
}

.ms-auto {
  margin-left: auto !important;
}

.p-0 {
  padding: 0rem !important;
}

.p-1 {
  padding: 0.5rem !important;
}

.p-2 {
  padding: 1rem !important;
}

.p-3 {
  padding: 1.5rem !important;
}

.p-4 {
  padding: 2rem !important;
}

.p-5 {
  padding: 2.5rem !important;
}

.p-6 {
  padding: 3rem !important;
}

.p-7 {
  padding: 3.5rem !important;
}

.p-8 {
  padding: 4rem !important;
}

.p-9 {
  padding: 4.5rem !important;
}

.p-10 {
  padding: 5rem !important;
}

.p-12 {
  padding: 6rem !important;
}

.p-14 {
  padding: 7rem !important;
}

.p-16 {
  padding: 8rem !important;
}

.px-0 {
  padding-right: 0rem !important;
  padding-left: 0rem !important;
}

.px-1 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-2 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-3 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-4 {
  padding-right: 2rem !important;
  padding-left: 2rem !important;
}

.px-5 {
  padding-right: 2.5rem !important;
  padding-left: 2.5rem !important;
}

.px-6 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.px-7 {
  padding-right: 3.5rem !important;
  padding-left: 3.5rem !important;
}

.px-8 {
  padding-right: 4rem !important;
  padding-left: 4rem !important;
}

.px-9 {
  padding-right: 4.5rem !important;
  padding-left: 4.5rem !important;
}

.px-10 {
  padding-right: 5rem !important;
  padding-left: 5rem !important;
}

.px-12 {
  padding-right: 6rem !important;
  padding-left: 6rem !important;
}

.px-14 {
  padding-right: 7rem !important;
  padding-left: 7rem !important;
}

.px-16 {
  padding-right: 8rem !important;
  padding-left: 8rem !important;
}

.py-0 {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}

.py-1 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-2 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-3 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-4 {
  padding-top: 2rem !important;
  padding-bottom: 2rem !important;
}

.py-5 {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.py-6 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.py-7 {
  padding-top: 3.5rem !important;
  padding-bottom: 3.5rem !important;
}

.py-8 {
  padding-top: 4rem !important;
  padding-bottom: 4rem !important;
}

.py-9 {
  padding-top: 4.5rem !important;
  padding-bottom: 4.5rem !important;
}

.py-10 {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.py-12 {
  padding-top: 6rem !important;
  padding-bottom: 6rem !important;
}

.py-14 {
  padding-top: 7rem !important;
  padding-bottom: 7rem !important;
}

.py-16 {
  padding-top: 8rem !important;
  padding-bottom: 8rem !important;
}

.pt-0 {
  padding-top: 0rem !important;
}

.pt-1 {
  padding-top: 0.5rem !important;
}

.pt-2 {
  padding-top: 1rem !important;
}

.pt-3 {
  padding-top: 1.5rem !important;
}

.pt-4 {
  padding-top: 2rem !important;
}

.pt-5 {
  padding-top: 2.5rem !important;
}

.pt-6 {
  padding-top: 3rem !important;
}

.pt-7 {
  padding-top: 3.5rem !important;
}

.pt-8 {
  padding-top: 4rem !important;
}

.pt-9 {
  padding-top: 4.5rem !important;
}

.pt-10 {
  padding-top: 5rem !important;
}

.pt-12 {
  padding-top: 6rem !important;
}

.pt-14 {
  padding-top: 7rem !important;
}

.pt-16 {
  padding-top: 8rem !important;
}

.pe-0 {
  padding-right: 0rem !important;
}

.pe-1 {
  padding-right: 0.5rem !important;
}

.pe-2 {
  padding-right: 1rem !important;
}

.pe-3 {
  padding-right: 1.5rem !important;
}

.pe-4 {
  padding-right: 2rem !important;
}

.pe-5 {
  padding-right: 2.5rem !important;
}

.pe-6 {
  padding-right: 3rem !important;
}

.pe-7 {
  padding-right: 3.5rem !important;
}

.pe-8 {
  padding-right: 4rem !important;
}

.pe-9 {
  padding-right: 4.5rem !important;
}

.pe-10 {
  padding-right: 5rem !important;
}

.pe-12 {
  padding-right: 6rem !important;
}

.pe-14 {
  padding-right: 7rem !important;
}

.pe-16 {
  padding-right: 8rem !important;
}

.pb-0 {
  padding-bottom: 0rem !important;
}

.pb-1 {
  padding-bottom: 0.5rem !important;
}

.pb-2 {
  padding-bottom: 1rem !important;
}

.pb-3 {
  padding-bottom: 1.5rem !important;
}

.pb-4 {
  padding-bottom: 2rem !important;
}

.pb-5 {
  padding-bottom: 2.5rem !important;
}

.pb-6 {
  padding-bottom: 3rem !important;
}

.pb-7 {
  padding-bottom: 3.5rem !important;
}

.pb-8 {
  padding-bottom: 4rem !important;
}

.pb-9 {
  padding-bottom: 4.5rem !important;
}

.pb-10 {
  padding-bottom: 5rem !important;
}

.pb-12 {
  padding-bottom: 6rem !important;
}

.pb-14 {
  padding-bottom: 7rem !important;
}

.pb-16 {
  padding-bottom: 8rem !important;
}

.ps-0 {
  padding-left: 0rem !important;
}

.ps-1 {
  padding-left: 0.5rem !important;
}

.ps-2 {
  padding-left: 1rem !important;
}

.ps-3 {
  padding-left: 1.5rem !important;
}

.ps-4 {
  padding-left: 2rem !important;
}

.ps-5 {
  padding-left: 2.5rem !important;
}

.ps-6 {
  padding-left: 3rem !important;
}

.ps-7 {
  padding-left: 3.5rem !important;
}

.ps-8 {
  padding-left: 4rem !important;
}

.ps-9 {
  padding-left: 4.5rem !important;
}

.ps-10 {
  padding-left: 5rem !important;
}

.ps-12 {
  padding-left: 6rem !important;
}

.ps-14 {
  padding-left: 7rem !important;
}

.ps-16 {
  padding-left: 8rem !important;
}

.gap-0 {
  gap: 0rem !important;
}

.gap-1 {
  gap: 0.5rem !important;
}

.gap-2 {
  gap: 1rem !important;
}

.gap-3 {
  gap: 1.5rem !important;
}

.gap-4 {
  gap: 2rem !important;
}

.gap-5 {
  gap: 2.5rem !important;
}

.gap-6 {
  gap: 3rem !important;
}

.gap-7 {
  gap: 3.5rem !important;
}

.gap-8 {
  gap: 4rem !important;
}

.gap-9 {
  gap: 4.5rem !important;
}

.gap-10 {
  gap: 5rem !important;
}

.gap-12 {
  gap: 6rem !important;
}

.gap-14 {
  gap: 7rem !important;
}

.gap-16 {
  gap: 8rem !important;
}

.row-gap-0 {
  row-gap: 0rem !important;
}

.row-gap-1 {
  row-gap: 0.5rem !important;
}

.row-gap-2 {
  row-gap: 1rem !important;
}

.row-gap-3 {
  row-gap: 1.5rem !important;
}

.row-gap-4 {
  row-gap: 2rem !important;
}

.row-gap-5 {
  row-gap: 2.5rem !important;
}

.row-gap-6 {
  row-gap: 3rem !important;
}

.row-gap-7 {
  row-gap: 3.5rem !important;
}

.row-gap-8 {
  row-gap: 4rem !important;
}

.row-gap-9 {
  row-gap: 4.5rem !important;
}

.row-gap-10 {
  row-gap: 5rem !important;
}

.row-gap-12 {
  row-gap: 6rem !important;
}

.row-gap-14 {
  row-gap: 7rem !important;
}

.row-gap-16 {
  row-gap: 8rem !important;
}

.column-gap-0 {
  -moz-column-gap: 0rem !important;
       column-gap: 0rem !important;
}

.column-gap-1 {
  -moz-column-gap: 0.5rem !important;
       column-gap: 0.5rem !important;
}

.column-gap-2 {
  -moz-column-gap: 1rem !important;
       column-gap: 1rem !important;
}

.column-gap-3 {
  -moz-column-gap: 1.5rem !important;
       column-gap: 1.5rem !important;
}

.column-gap-4 {
  -moz-column-gap: 2rem !important;
       column-gap: 2rem !important;
}

.column-gap-5 {
  -moz-column-gap: 2.5rem !important;
       column-gap: 2.5rem !important;
}

.column-gap-6 {
  -moz-column-gap: 3rem !important;
       column-gap: 3rem !important;
}

.column-gap-7 {
  -moz-column-gap: 3.5rem !important;
       column-gap: 3.5rem !important;
}

.column-gap-8 {
  -moz-column-gap: 4rem !important;
       column-gap: 4rem !important;
}

.column-gap-9 {
  -moz-column-gap: 4.5rem !important;
       column-gap: 4.5rem !important;
}

.column-gap-10 {
  -moz-column-gap: 5rem !important;
       column-gap: 5rem !important;
}

.column-gap-12 {
  -moz-column-gap: 6rem !important;
       column-gap: 6rem !important;
}

.column-gap-14 {
  -moz-column-gap: 7rem !important;
       column-gap: 7rem !important;
}

.column-gap-16 {
  -moz-column-gap: 8rem !important;
       column-gap: 8rem !important;
}

.font-monospace {
  font-family: var(--bs-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-lighter {
  font-weight: 100 !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: 900 !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.5 !important;
}

.lh-lg {
  line-height: 2 !important;
}

.text-start {
  text-align: left !important;
}

.text-end {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-gold {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gold-rgb), var(--bs-text-opacity)) !important;
}

.text-maroon {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-maroon-rgb), var(--bs-text-opacity)) !important;
}

.text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}

.text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}

.text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}

.text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}

.text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}

.text-gray {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-rgb), var(--bs-text-opacity)) !important;
}

.text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-1 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-1-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-2 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-2-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-3 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-3-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-4 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-4-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-5 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-5-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-6 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-6-rgb), var(--bs-text-opacity)) !important;
}

.text-gray-7 {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-gray-7-rgb), var(--bs-text-opacity)) !important;
}

.text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}

.text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}

.text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}

.text-muted {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-body-secondary {
  --bs-text-opacity: 1;
  color: var(--bs-secondary-color) !important;
}

.text-body-tertiary {
  --bs-text-opacity: 1;
  color: var(--bs-tertiary-color) !important;
}

.text-body-emphasis {
  --bs-text-opacity: 1;
  color: var(--bs-emphasis-color) !important;
}

.text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}

.text-opacity-25 {
  --bs-text-opacity: 0.25;
}

.text-opacity-50 {
  --bs-text-opacity: 0.5;
}

.text-opacity-75 {
  --bs-text-opacity: 0.75;
}

.text-opacity-100 {
  --bs-text-opacity: 1;
}

.text-primary-emphasis {
  color: var(--bs-primary-text-emphasis) !important;
}

.text-secondary-emphasis {
  color: var(--bs-secondary-text-emphasis) !important;
}

.text-success-emphasis {
  color: var(--bs-success-text-emphasis) !important;
}

.text-info-emphasis {
  color: var(--bs-info-text-emphasis) !important;
}

.text-warning-emphasis {
  color: var(--bs-warning-text-emphasis) !important;
}

.text-danger-emphasis {
  color: var(--bs-danger-text-emphasis) !important;
}

.text-light-emphasis {
  color: var(--bs-light-text-emphasis) !important;
}

.text-dark-emphasis {
  color: var(--bs-dark-text-emphasis) !important;
}

.link-opacity-10 {
  --bs-link-opacity: 0.1;
}

.link-opacity-10-hover:hover {
  --bs-link-opacity: 0.1;
}

.link-opacity-25 {
  --bs-link-opacity: 0.25;
}

.link-opacity-25-hover:hover {
  --bs-link-opacity: 0.25;
}

.link-opacity-50 {
  --bs-link-opacity: 0.5;
}

.link-opacity-50-hover:hover {
  --bs-link-opacity: 0.5;
}

.link-opacity-75 {
  --bs-link-opacity: 0.75;
}

.link-opacity-75-hover:hover {
  --bs-link-opacity: 0.75;
}

.link-opacity-100 {
  --bs-link-opacity: 1;
}

.link-opacity-100-hover:hover {
  --bs-link-opacity: 1;
}

.link-offset-1 {
  text-underline-offset: 0.125em !important;
}

.link-offset-1-hover:hover {
  text-underline-offset: 0.125em !important;
}

.link-offset-2 {
  text-underline-offset: 0.25em !important;
}

.link-offset-2-hover:hover {
  text-underline-offset: 0.25em !important;
}

.link-offset-3 {
  text-underline-offset: 0.375em !important;
}

.link-offset-3-hover:hover {
  text-underline-offset: 0.375em !important;
}

.link-underline-gold {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gold-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-maroon {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-maroon-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-success {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-success-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-info {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-info-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-warning {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-warning-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-danger {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-danger-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-light {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-light-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-dark {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-dark-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-1 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-1-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-2 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-2-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-3 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-3-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-4 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-4-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-5 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-5-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-6 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-6-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline-gray-7 {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-gray-7-rgb), var(--bs-link-underline-opacity)) !important;
}

.link-underline {
  --bs-link-underline-opacity: 1;
  text-decoration-color: rgba(var(--bs-link-color-rgb), var(--bs-link-underline-opacity, 1)) !important;
}

.link-underline-opacity-0 {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-0-hover:hover {
  --bs-link-underline-opacity: 0;
}

.link-underline-opacity-10 {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-10-hover:hover {
  --bs-link-underline-opacity: 0.1;
}

.link-underline-opacity-25 {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-25-hover:hover {
  --bs-link-underline-opacity: 0.25;
}

.link-underline-opacity-50 {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-50-hover:hover {
  --bs-link-underline-opacity: 0.5;
}

.link-underline-opacity-75 {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-75-hover:hover {
  --bs-link-underline-opacity: 0.75;
}

.link-underline-opacity-100 {
  --bs-link-underline-opacity: 1;
}

.link-underline-opacity-100-hover:hover {
  --bs-link-underline-opacity: 1;
}

.bg-gold {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gold-rgb), var(--bs-bg-opacity)) !important;
}

.bg-maroon {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-maroon-rgb), var(--bs-bg-opacity)) !important;
}

.bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}

.bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}

.bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}

.bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}

.bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-rgb), var(--bs-bg-opacity)) !important;
}

.bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-1 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-1-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-2 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-2-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-3 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-3-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-4 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-4-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-5 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-5-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-6 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-6-rgb), var(--bs-bg-opacity)) !important;
}

.bg-gray-7 {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-gray-7-rgb), var(--bs-bg-opacity)) !important;
}

.bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}

.bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}

.bg-body-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-body-tertiary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-tertiary-bg-rgb), var(--bs-bg-opacity)) !important;
}

.bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --bs-bg-opacity: 1;
}

.bg-primary-subtle {
  background-color: var(--bs-primary-bg-subtle) !important;
}

.bg-secondary-subtle {
  background-color: var(--bs-secondary-bg-subtle) !important;
}

.bg-success-subtle {
  background-color: var(--bs-success-bg-subtle) !important;
}

.bg-info-subtle {
  background-color: var(--bs-info-bg-subtle) !important;
}

.bg-warning-subtle {
  background-color: var(--bs-warning-bg-subtle) !important;
}

.bg-danger-subtle {
  background-color: var(--bs-danger-bg-subtle) !important;
}

.bg-light-subtle {
  background-color: var(--bs-light-bg-subtle) !important;
}

.bg-dark-subtle {
  background-color: var(--bs-dark-bg-subtle) !important;
}

.bg-gradient {
  background-image: var(--bs-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}

.rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}

.rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}

.rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}

.rounded-5 {
  border-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}

.rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-0 {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.rounded-top-1 {
  border-top-left-radius: var(--bs-border-radius-sm) !important;
  border-top-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-top-2 {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}

.rounded-top-3 {
  border-top-left-radius: var(--bs-border-radius-lg) !important;
  border-top-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-top-4 {
  border-top-left-radius: var(--bs-border-radius-xl) !important;
  border-top-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-top-5 {
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-top-circle {
  border-top-left-radius: 50% !important;
  border-top-right-radius: 50% !important;
}

.rounded-top-pill {
  border-top-left-radius: var(--bs-border-radius-pill) !important;
  border-top-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-0 {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.rounded-end-1 {
  border-top-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
}

.rounded-end-2 {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.rounded-end-3 {
  border-top-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
}

.rounded-end-4 {
  border-top-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
}

.rounded-end-5 {
  border-top-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-end-circle {
  border-top-right-radius: 50% !important;
  border-bottom-right-radius: 50% !important;
}

.rounded-end-pill {
  border-top-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
}

.rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-0 {
  border-bottom-right-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.rounded-bottom-1 {
  border-bottom-right-radius: var(--bs-border-radius-sm) !important;
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-bottom-2 {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}

.rounded-bottom-3 {
  border-bottom-right-radius: var(--bs-border-radius-lg) !important;
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-bottom-4 {
  border-bottom-right-radius: var(--bs-border-radius-xl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-bottom-5 {
  border-bottom-right-radius: var(--bs-border-radius-xxl) !important;
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-bottom-circle {
  border-bottom-right-radius: 50% !important;
  border-bottom-left-radius: 50% !important;
}

.rounded-bottom-pill {
  border-bottom-right-radius: var(--bs-border-radius-pill) !important;
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
}

.rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-0 {
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}

.rounded-start-1 {
  border-bottom-left-radius: var(--bs-border-radius-sm) !important;
  border-top-left-radius: var(--bs-border-radius-sm) !important;
}

.rounded-start-2 {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}

.rounded-start-3 {
  border-bottom-left-radius: var(--bs-border-radius-lg) !important;
  border-top-left-radius: var(--bs-border-radius-lg) !important;
}

.rounded-start-4 {
  border-bottom-left-radius: var(--bs-border-radius-xl) !important;
  border-top-left-radius: var(--bs-border-radius-xl) !important;
}

.rounded-start-5 {
  border-bottom-left-radius: var(--bs-border-radius-xxl) !important;
  border-top-left-radius: var(--bs-border-radius-xxl) !important;
}

.rounded-start-circle {
  border-bottom-left-radius: 50% !important;
  border-top-left-radius: 50% !important;
}

.rounded-start-pill {
  border-bottom-left-radius: var(--bs-border-radius-pill) !important;
  border-top-left-radius: var(--bs-border-radius-pill) !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.z-n1 {
  z-index: -1 !important;
}

.z-0 {
  z-index: 0 !important;
}

.z-1 {
  z-index: 1 !important;
}

.z-2 {
  z-index: 2 !important;
}

.z-3 {
  z-index: 3 !important;
}

@media (min-width: 576px) {
  .float-sm-start {
    float: left !important;
  }
  .float-sm-end {
    float: right !important;
  }
  .float-sm-none {
    float: none !important;
  }
  .object-fit-sm-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important;
  }
  .object-fit-sm-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
  .object-fit-sm-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important;
  }
  .object-fit-sm-scale {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important;
  }
  .object-fit-sm-none {
    -o-object-fit: none !important;
       object-fit: none !important;
  }
  .d-sm-inline {
    display: inline !important;
  }
  .d-sm-inline-block {
    display: inline-block !important;
  }
  .d-sm-block {
    display: block !important;
  }
  .d-sm-grid {
    display: grid !important;
  }
  .d-sm-inline-grid {
    display: inline-grid !important;
  }
  .d-sm-table {
    display: table !important;
  }
  .d-sm-table-row {
    display: table-row !important;
  }
  .d-sm-table-cell {
    display: table-cell !important;
  }
  .d-sm-flex {
    display: flex !important;
  }
  .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .d-sm-none {
    display: none !important;
  }
  .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .flex-sm-row {
    flex-direction: row !important;
  }
  .flex-sm-column {
    flex-direction: column !important;
  }
  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .justify-content-sm-center {
    justify-content: center !important;
  }
  .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-sm-start {
    align-items: flex-start !important;
  }
  .align-items-sm-end {
    align-items: flex-end !important;
  }
  .align-items-sm-center {
    align-items: center !important;
  }
  .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .align-content-sm-start {
    align-content: flex-start !important;
  }
  .align-content-sm-end {
    align-content: flex-end !important;
  }
  .align-content-sm-center {
    align-content: center !important;
  }
  .align-content-sm-between {
    align-content: space-between !important;
  }
  .align-content-sm-around {
    align-content: space-around !important;
  }
  .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .align-self-sm-auto {
    align-self: auto !important;
  }
  .align-self-sm-start {
    align-self: flex-start !important;
  }
  .align-self-sm-end {
    align-self: flex-end !important;
  }
  .align-self-sm-center {
    align-self: center !important;
  }
  .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .order-sm-first {
    order: -1 !important;
  }
  .order-sm-0 {
    order: 0 !important;
  }
  .order-sm-1 {
    order: 1 !important;
  }
  .order-sm-2 {
    order: 2 !important;
  }
  .order-sm-3 {
    order: 3 !important;
  }
  .order-sm-4 {
    order: 4 !important;
  }
  .order-sm-5 {
    order: 5 !important;
  }
  .order-sm-last {
    order: 6 !important;
  }
  .m-sm-0 {
    margin: 0rem !important;
  }
  .m-sm-1 {
    margin: 0.5rem !important;
  }
  .m-sm-2 {
    margin: 1rem !important;
  }
  .m-sm-3 {
    margin: 1.5rem !important;
  }
  .m-sm-4 {
    margin: 2rem !important;
  }
  .m-sm-5 {
    margin: 2.5rem !important;
  }
  .m-sm-6 {
    margin: 3rem !important;
  }
  .m-sm-7 {
    margin: 3.5rem !important;
  }
  .m-sm-8 {
    margin: 4rem !important;
  }
  .m-sm-9 {
    margin: 4.5rem !important;
  }
  .m-sm-10 {
    margin: 5rem !important;
  }
  .m-sm-12 {
    margin: 6rem !important;
  }
  .m-sm-14 {
    margin: 7rem !important;
  }
  .m-sm-16 {
    margin: 8rem !important;
  }
  .m-sm-auto {
    margin: auto !important;
  }
  .mx-sm-0 {
    margin-right: 0rem !important;
    margin-left: 0rem !important;
  }
  .mx-sm-1 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-sm-2 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-sm-3 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-sm-4 {
    margin-right: 2rem !important;
    margin-left: 2rem !important;
  }
  .mx-sm-5 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-sm-6 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-sm-7 {
    margin-right: 3.5rem !important;
    margin-left: 3.5rem !important;
  }
  .mx-sm-8 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }
  .mx-sm-9 {
    margin-right: 4.5rem !important;
    margin-left: 4.5rem !important;
  }
  .mx-sm-10 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }
  .mx-sm-12 {
    margin-right: 6rem !important;
    margin-left: 6rem !important;
  }
  .mx-sm-14 {
    margin-right: 7rem !important;
    margin-left: 7rem !important;
  }
  .mx-sm-16 {
    margin-right: 8rem !important;
    margin-left: 8rem !important;
  }
  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-sm-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
  .my-sm-1 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-sm-2 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-sm-3 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-sm-4 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
  .my-sm-5 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-sm-6 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-sm-7 {
    margin-top: 3.5rem !important;
    margin-bottom: 3.5rem !important;
  }
  .my-sm-8 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
  .my-sm-9 {
    margin-top: 4.5rem !important;
    margin-bottom: 4.5rem !important;
  }
  .my-sm-10 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
  .my-sm-12 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
  .my-sm-14 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
  .my-sm-16 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-sm-0 {
    margin-top: 0rem !important;
  }
  .mt-sm-1 {
    margin-top: 0.5rem !important;
  }
  .mt-sm-2 {
    margin-top: 1rem !important;
  }
  .mt-sm-3 {
    margin-top: 1.5rem !important;
  }
  .mt-sm-4 {
    margin-top: 2rem !important;
  }
  .mt-sm-5 {
    margin-top: 2.5rem !important;
  }
  .mt-sm-6 {
    margin-top: 3rem !important;
  }
  .mt-sm-7 {
    margin-top: 3.5rem !important;
  }
  .mt-sm-8 {
    margin-top: 4rem !important;
  }
  .mt-sm-9 {
    margin-top: 4.5rem !important;
  }
  .mt-sm-10 {
    margin-top: 5rem !important;
  }
  .mt-sm-12 {
    margin-top: 6rem !important;
  }
  .mt-sm-14 {
    margin-top: 7rem !important;
  }
  .mt-sm-16 {
    margin-top: 8rem !important;
  }
  .mt-sm-auto {
    margin-top: auto !important;
  }
  .me-sm-0 {
    margin-right: 0rem !important;
  }
  .me-sm-1 {
    margin-right: 0.5rem !important;
  }
  .me-sm-2 {
    margin-right: 1rem !important;
  }
  .me-sm-3 {
    margin-right: 1.5rem !important;
  }
  .me-sm-4 {
    margin-right: 2rem !important;
  }
  .me-sm-5 {
    margin-right: 2.5rem !important;
  }
  .me-sm-6 {
    margin-right: 3rem !important;
  }
  .me-sm-7 {
    margin-right: 3.5rem !important;
  }
  .me-sm-8 {
    margin-right: 4rem !important;
  }
  .me-sm-9 {
    margin-right: 4.5rem !important;
  }
  .me-sm-10 {
    margin-right: 5rem !important;
  }
  .me-sm-12 {
    margin-right: 6rem !important;
  }
  .me-sm-14 {
    margin-right: 7rem !important;
  }
  .me-sm-16 {
    margin-right: 8rem !important;
  }
  .me-sm-auto {
    margin-right: auto !important;
  }
  .mb-sm-0 {
    margin-bottom: 0rem !important;
  }
  .mb-sm-1 {
    margin-bottom: 0.5rem !important;
  }
  .mb-sm-2 {
    margin-bottom: 1rem !important;
  }
  .mb-sm-3 {
    margin-bottom: 1.5rem !important;
  }
  .mb-sm-4 {
    margin-bottom: 2rem !important;
  }
  .mb-sm-5 {
    margin-bottom: 2.5rem !important;
  }
  .mb-sm-6 {
    margin-bottom: 3rem !important;
  }
  .mb-sm-7 {
    margin-bottom: 3.5rem !important;
  }
  .mb-sm-8 {
    margin-bottom: 4rem !important;
  }
  .mb-sm-9 {
    margin-bottom: 4.5rem !important;
  }
  .mb-sm-10 {
    margin-bottom: 5rem !important;
  }
  .mb-sm-12 {
    margin-bottom: 6rem !important;
  }
  .mb-sm-14 {
    margin-bottom: 7rem !important;
  }
  .mb-sm-16 {
    margin-bottom: 8rem !important;
  }
  .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .ms-sm-0 {
    margin-left: 0rem !important;
  }
  .ms-sm-1 {
    margin-left: 0.5rem !important;
  }
  .ms-sm-2 {
    margin-left: 1rem !important;
  }
  .ms-sm-3 {
    margin-left: 1.5rem !important;
  }
  .ms-sm-4 {
    margin-left: 2rem !important;
  }
  .ms-sm-5 {
    margin-left: 2.5rem !important;
  }
  .ms-sm-6 {
    margin-left: 3rem !important;
  }
  .ms-sm-7 {
    margin-left: 3.5rem !important;
  }
  .ms-sm-8 {
    margin-left: 4rem !important;
  }
  .ms-sm-9 {
    margin-left: 4.5rem !important;
  }
  .ms-sm-10 {
    margin-left: 5rem !important;
  }
  .ms-sm-12 {
    margin-left: 6rem !important;
  }
  .ms-sm-14 {
    margin-left: 7rem !important;
  }
  .ms-sm-16 {
    margin-left: 8rem !important;
  }
  .ms-sm-auto {
    margin-left: auto !important;
  }
  .p-sm-0 {
    padding: 0rem !important;
  }
  .p-sm-1 {
    padding: 0.5rem !important;
  }
  .p-sm-2 {
    padding: 1rem !important;
  }
  .p-sm-3 {
    padding: 1.5rem !important;
  }
  .p-sm-4 {
    padding: 2rem !important;
  }
  .p-sm-5 {
    padding: 2.5rem !important;
  }
  .p-sm-6 {
    padding: 3rem !important;
  }
  .p-sm-7 {
    padding: 3.5rem !important;
  }
  .p-sm-8 {
    padding: 4rem !important;
  }
  .p-sm-9 {
    padding: 4.5rem !important;
  }
  .p-sm-10 {
    padding: 5rem !important;
  }
  .p-sm-12 {
    padding: 6rem !important;
  }
  .p-sm-14 {
    padding: 7rem !important;
  }
  .p-sm-16 {
    padding: 8rem !important;
  }
  .px-sm-0 {
    padding-right: 0rem !important;
    padding-left: 0rem !important;
  }
  .px-sm-1 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-sm-2 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-sm-3 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-sm-4 {
    padding-right: 2rem !important;
    padding-left: 2rem !important;
  }
  .px-sm-5 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .px-sm-6 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-sm-7 {
    padding-right: 3.5rem !important;
    padding-left: 3.5rem !important;
  }
  .px-sm-8 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }
  .px-sm-9 {
    padding-right: 4.5rem !important;
    padding-left: 4.5rem !important;
  }
  .px-sm-10 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }
  .px-sm-12 {
    padding-right: 6rem !important;
    padding-left: 6rem !important;
  }
  .px-sm-14 {
    padding-right: 7rem !important;
    padding-left: 7rem !important;
  }
  .px-sm-16 {
    padding-right: 8rem !important;
    padding-left: 8rem !important;
  }
  .py-sm-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
  .py-sm-1 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-sm-2 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-sm-3 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-sm-4 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  .py-sm-5 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .py-sm-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-sm-7 {
    padding-top: 3.5rem !important;
    padding-bottom: 3.5rem !important;
  }
  .py-sm-8 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
  .py-sm-9 {
    padding-top: 4.5rem !important;
    padding-bottom: 4.5rem !important;
  }
  .py-sm-10 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
  .py-sm-12 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
  .py-sm-14 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
  .py-sm-16 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
  .pt-sm-0 {
    padding-top: 0rem !important;
  }
  .pt-sm-1 {
    padding-top: 0.5rem !important;
  }
  .pt-sm-2 {
    padding-top: 1rem !important;
  }
  .pt-sm-3 {
    padding-top: 1.5rem !important;
  }
  .pt-sm-4 {
    padding-top: 2rem !important;
  }
  .pt-sm-5 {
    padding-top: 2.5rem !important;
  }
  .pt-sm-6 {
    padding-top: 3rem !important;
  }
  .pt-sm-7 {
    padding-top: 3.5rem !important;
  }
  .pt-sm-8 {
    padding-top: 4rem !important;
  }
  .pt-sm-9 {
    padding-top: 4.5rem !important;
  }
  .pt-sm-10 {
    padding-top: 5rem !important;
  }
  .pt-sm-12 {
    padding-top: 6rem !important;
  }
  .pt-sm-14 {
    padding-top: 7rem !important;
  }
  .pt-sm-16 {
    padding-top: 8rem !important;
  }
  .pe-sm-0 {
    padding-right: 0rem !important;
  }
  .pe-sm-1 {
    padding-right: 0.5rem !important;
  }
  .pe-sm-2 {
    padding-right: 1rem !important;
  }
  .pe-sm-3 {
    padding-right: 1.5rem !important;
  }
  .pe-sm-4 {
    padding-right: 2rem !important;
  }
  .pe-sm-5 {
    padding-right: 2.5rem !important;
  }
  .pe-sm-6 {
    padding-right: 3rem !important;
  }
  .pe-sm-7 {
    padding-right: 3.5rem !important;
  }
  .pe-sm-8 {
    padding-right: 4rem !important;
  }
  .pe-sm-9 {
    padding-right: 4.5rem !important;
  }
  .pe-sm-10 {
    padding-right: 5rem !important;
  }
  .pe-sm-12 {
    padding-right: 6rem !important;
  }
  .pe-sm-14 {
    padding-right: 7rem !important;
  }
  .pe-sm-16 {
    padding-right: 8rem !important;
  }
  .pb-sm-0 {
    padding-bottom: 0rem !important;
  }
  .pb-sm-1 {
    padding-bottom: 0.5rem !important;
  }
  .pb-sm-2 {
    padding-bottom: 1rem !important;
  }
  .pb-sm-3 {
    padding-bottom: 1.5rem !important;
  }
  .pb-sm-4 {
    padding-bottom: 2rem !important;
  }
  .pb-sm-5 {
    padding-bottom: 2.5rem !important;
  }
  .pb-sm-6 {
    padding-bottom: 3rem !important;
  }
  .pb-sm-7 {
    padding-bottom: 3.5rem !important;
  }
  .pb-sm-8 {
    padding-bottom: 4rem !important;
  }
  .pb-sm-9 {
    padding-bottom: 4.5rem !important;
  }
  .pb-sm-10 {
    padding-bottom: 5rem !important;
  }
  .pb-sm-12 {
    padding-bottom: 6rem !important;
  }
  .pb-sm-14 {
    padding-bottom: 7rem !important;
  }
  .pb-sm-16 {
    padding-bottom: 8rem !important;
  }
  .ps-sm-0 {
    padding-left: 0rem !important;
  }
  .ps-sm-1 {
    padding-left: 0.5rem !important;
  }
  .ps-sm-2 {
    padding-left: 1rem !important;
  }
  .ps-sm-3 {
    padding-left: 1.5rem !important;
  }
  .ps-sm-4 {
    padding-left: 2rem !important;
  }
  .ps-sm-5 {
    padding-left: 2.5rem !important;
  }
  .ps-sm-6 {
    padding-left: 3rem !important;
  }
  .ps-sm-7 {
    padding-left: 3.5rem !important;
  }
  .ps-sm-8 {
    padding-left: 4rem !important;
  }
  .ps-sm-9 {
    padding-left: 4.5rem !important;
  }
  .ps-sm-10 {
    padding-left: 5rem !important;
  }
  .ps-sm-12 {
    padding-left: 6rem !important;
  }
  .ps-sm-14 {
    padding-left: 7rem !important;
  }
  .ps-sm-16 {
    padding-left: 8rem !important;
  }
  .gap-sm-0 {
    gap: 0rem !important;
  }
  .gap-sm-1 {
    gap: 0.5rem !important;
  }
  .gap-sm-2 {
    gap: 1rem !important;
  }
  .gap-sm-3 {
    gap: 1.5rem !important;
  }
  .gap-sm-4 {
    gap: 2rem !important;
  }
  .gap-sm-5 {
    gap: 2.5rem !important;
  }
  .gap-sm-6 {
    gap: 3rem !important;
  }
  .gap-sm-7 {
    gap: 3.5rem !important;
  }
  .gap-sm-8 {
    gap: 4rem !important;
  }
  .gap-sm-9 {
    gap: 4.5rem !important;
  }
  .gap-sm-10 {
    gap: 5rem !important;
  }
  .gap-sm-12 {
    gap: 6rem !important;
  }
  .gap-sm-14 {
    gap: 7rem !important;
  }
  .gap-sm-16 {
    gap: 8rem !important;
  }
  .row-gap-sm-0 {
    row-gap: 0rem !important;
  }
  .row-gap-sm-1 {
    row-gap: 0.5rem !important;
  }
  .row-gap-sm-2 {
    row-gap: 1rem !important;
  }
  .row-gap-sm-3 {
    row-gap: 1.5rem !important;
  }
  .row-gap-sm-4 {
    row-gap: 2rem !important;
  }
  .row-gap-sm-5 {
    row-gap: 2.5rem !important;
  }
  .row-gap-sm-6 {
    row-gap: 3rem !important;
  }
  .row-gap-sm-7 {
    row-gap: 3.5rem !important;
  }
  .row-gap-sm-8 {
    row-gap: 4rem !important;
  }
  .row-gap-sm-9 {
    row-gap: 4.5rem !important;
  }
  .row-gap-sm-10 {
    row-gap: 5rem !important;
  }
  .row-gap-sm-12 {
    row-gap: 6rem !important;
  }
  .row-gap-sm-14 {
    row-gap: 7rem !important;
  }
  .row-gap-sm-16 {
    row-gap: 8rem !important;
  }
  .column-gap-sm-0 {
    -moz-column-gap: 0rem !important;
         column-gap: 0rem !important;
  }
  .column-gap-sm-1 {
    -moz-column-gap: 0.5rem !important;
         column-gap: 0.5rem !important;
  }
  .column-gap-sm-2 {
    -moz-column-gap: 1rem !important;
         column-gap: 1rem !important;
  }
  .column-gap-sm-3 {
    -moz-column-gap: 1.5rem !important;
         column-gap: 1.5rem !important;
  }
  .column-gap-sm-4 {
    -moz-column-gap: 2rem !important;
         column-gap: 2rem !important;
  }
  .column-gap-sm-5 {
    -moz-column-gap: 2.5rem !important;
         column-gap: 2.5rem !important;
  }
  .column-gap-sm-6 {
    -moz-column-gap: 3rem !important;
         column-gap: 3rem !important;
  }
  .column-gap-sm-7 {
    -moz-column-gap: 3.5rem !important;
         column-gap: 3.5rem !important;
  }
  .column-gap-sm-8 {
    -moz-column-gap: 4rem !important;
         column-gap: 4rem !important;
  }
  .column-gap-sm-9 {
    -moz-column-gap: 4.5rem !important;
         column-gap: 4.5rem !important;
  }
  .column-gap-sm-10 {
    -moz-column-gap: 5rem !important;
         column-gap: 5rem !important;
  }
  .column-gap-sm-12 {
    -moz-column-gap: 6rem !important;
         column-gap: 6rem !important;
  }
  .column-gap-sm-14 {
    -moz-column-gap: 7rem !important;
         column-gap: 7rem !important;
  }
  .column-gap-sm-16 {
    -moz-column-gap: 8rem !important;
         column-gap: 8rem !important;
  }
  .text-sm-start {
    text-align: left !important;
  }
  .text-sm-end {
    text-align: right !important;
  }
  .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .float-md-start {
    float: left !important;
  }
  .float-md-end {
    float: right !important;
  }
  .float-md-none {
    float: none !important;
  }
  .object-fit-md-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important;
  }
  .object-fit-md-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
  .object-fit-md-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important;
  }
  .object-fit-md-scale {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important;
  }
  .object-fit-md-none {
    -o-object-fit: none !important;
       object-fit: none !important;
  }
  .d-md-inline {
    display: inline !important;
  }
  .d-md-inline-block {
    display: inline-block !important;
  }
  .d-md-block {
    display: block !important;
  }
  .d-md-grid {
    display: grid !important;
  }
  .d-md-inline-grid {
    display: inline-grid !important;
  }
  .d-md-table {
    display: table !important;
  }
  .d-md-table-row {
    display: table-row !important;
  }
  .d-md-table-cell {
    display: table-cell !important;
  }
  .d-md-flex {
    display: flex !important;
  }
  .d-md-inline-flex {
    display: inline-flex !important;
  }
  .d-md-none {
    display: none !important;
  }
  .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .flex-md-row {
    flex-direction: row !important;
  }
  .flex-md-column {
    flex-direction: column !important;
  }
  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .justify-content-md-center {
    justify-content: center !important;
  }
  .justify-content-md-between {
    justify-content: space-between !important;
  }
  .justify-content-md-around {
    justify-content: space-around !important;
  }
  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-md-start {
    align-items: flex-start !important;
  }
  .align-items-md-end {
    align-items: flex-end !important;
  }
  .align-items-md-center {
    align-items: center !important;
  }
  .align-items-md-baseline {
    align-items: baseline !important;
  }
  .align-items-md-stretch {
    align-items: stretch !important;
  }
  .align-content-md-start {
    align-content: flex-start !important;
  }
  .align-content-md-end {
    align-content: flex-end !important;
  }
  .align-content-md-center {
    align-content: center !important;
  }
  .align-content-md-between {
    align-content: space-between !important;
  }
  .align-content-md-around {
    align-content: space-around !important;
  }
  .align-content-md-stretch {
    align-content: stretch !important;
  }
  .align-self-md-auto {
    align-self: auto !important;
  }
  .align-self-md-start {
    align-self: flex-start !important;
  }
  .align-self-md-end {
    align-self: flex-end !important;
  }
  .align-self-md-center {
    align-self: center !important;
  }
  .align-self-md-baseline {
    align-self: baseline !important;
  }
  .align-self-md-stretch {
    align-self: stretch !important;
  }
  .order-md-first {
    order: -1 !important;
  }
  .order-md-0 {
    order: 0 !important;
  }
  .order-md-1 {
    order: 1 !important;
  }
  .order-md-2 {
    order: 2 !important;
  }
  .order-md-3 {
    order: 3 !important;
  }
  .order-md-4 {
    order: 4 !important;
  }
  .order-md-5 {
    order: 5 !important;
  }
  .order-md-last {
    order: 6 !important;
  }
  .m-md-0 {
    margin: 0rem !important;
  }
  .m-md-1 {
    margin: 0.5rem !important;
  }
  .m-md-2 {
    margin: 1rem !important;
  }
  .m-md-3 {
    margin: 1.5rem !important;
  }
  .m-md-4 {
    margin: 2rem !important;
  }
  .m-md-5 {
    margin: 2.5rem !important;
  }
  .m-md-6 {
    margin: 3rem !important;
  }
  .m-md-7 {
    margin: 3.5rem !important;
  }
  .m-md-8 {
    margin: 4rem !important;
  }
  .m-md-9 {
    margin: 4.5rem !important;
  }
  .m-md-10 {
    margin: 5rem !important;
  }
  .m-md-12 {
    margin: 6rem !important;
  }
  .m-md-14 {
    margin: 7rem !important;
  }
  .m-md-16 {
    margin: 8rem !important;
  }
  .m-md-auto {
    margin: auto !important;
  }
  .mx-md-0 {
    margin-right: 0rem !important;
    margin-left: 0rem !important;
  }
  .mx-md-1 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-md-2 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-md-3 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-md-4 {
    margin-right: 2rem !important;
    margin-left: 2rem !important;
  }
  .mx-md-5 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-md-6 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-md-7 {
    margin-right: 3.5rem !important;
    margin-left: 3.5rem !important;
  }
  .mx-md-8 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }
  .mx-md-9 {
    margin-right: 4.5rem !important;
    margin-left: 4.5rem !important;
  }
  .mx-md-10 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }
  .mx-md-12 {
    margin-right: 6rem !important;
    margin-left: 6rem !important;
  }
  .mx-md-14 {
    margin-right: 7rem !important;
    margin-left: 7rem !important;
  }
  .mx-md-16 {
    margin-right: 8rem !important;
    margin-left: 8rem !important;
  }
  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-md-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
  .my-md-1 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-md-2 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-md-3 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-md-4 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
  .my-md-5 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-md-6 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-md-7 {
    margin-top: 3.5rem !important;
    margin-bottom: 3.5rem !important;
  }
  .my-md-8 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
  .my-md-9 {
    margin-top: 4.5rem !important;
    margin-bottom: 4.5rem !important;
  }
  .my-md-10 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
  .my-md-12 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
  .my-md-14 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
  .my-md-16 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-md-0 {
    margin-top: 0rem !important;
  }
  .mt-md-1 {
    margin-top: 0.5rem !important;
  }
  .mt-md-2 {
    margin-top: 1rem !important;
  }
  .mt-md-3 {
    margin-top: 1.5rem !important;
  }
  .mt-md-4 {
    margin-top: 2rem !important;
  }
  .mt-md-5 {
    margin-top: 2.5rem !important;
  }
  .mt-md-6 {
    margin-top: 3rem !important;
  }
  .mt-md-7 {
    margin-top: 3.5rem !important;
  }
  .mt-md-8 {
    margin-top: 4rem !important;
  }
  .mt-md-9 {
    margin-top: 4.5rem !important;
  }
  .mt-md-10 {
    margin-top: 5rem !important;
  }
  .mt-md-12 {
    margin-top: 6rem !important;
  }
  .mt-md-14 {
    margin-top: 7rem !important;
  }
  .mt-md-16 {
    margin-top: 8rem !important;
  }
  .mt-md-auto {
    margin-top: auto !important;
  }
  .me-md-0 {
    margin-right: 0rem !important;
  }
  .me-md-1 {
    margin-right: 0.5rem !important;
  }
  .me-md-2 {
    margin-right: 1rem !important;
  }
  .me-md-3 {
    margin-right: 1.5rem !important;
  }
  .me-md-4 {
    margin-right: 2rem !important;
  }
  .me-md-5 {
    margin-right: 2.5rem !important;
  }
  .me-md-6 {
    margin-right: 3rem !important;
  }
  .me-md-7 {
    margin-right: 3.5rem !important;
  }
  .me-md-8 {
    margin-right: 4rem !important;
  }
  .me-md-9 {
    margin-right: 4.5rem !important;
  }
  .me-md-10 {
    margin-right: 5rem !important;
  }
  .me-md-12 {
    margin-right: 6rem !important;
  }
  .me-md-14 {
    margin-right: 7rem !important;
  }
  .me-md-16 {
    margin-right: 8rem !important;
  }
  .me-md-auto {
    margin-right: auto !important;
  }
  .mb-md-0 {
    margin-bottom: 0rem !important;
  }
  .mb-md-1 {
    margin-bottom: 0.5rem !important;
  }
  .mb-md-2 {
    margin-bottom: 1rem !important;
  }
  .mb-md-3 {
    margin-bottom: 1.5rem !important;
  }
  .mb-md-4 {
    margin-bottom: 2rem !important;
  }
  .mb-md-5 {
    margin-bottom: 2.5rem !important;
  }
  .mb-md-6 {
    margin-bottom: 3rem !important;
  }
  .mb-md-7 {
    margin-bottom: 3.5rem !important;
  }
  .mb-md-8 {
    margin-bottom: 4rem !important;
  }
  .mb-md-9 {
    margin-bottom: 4.5rem !important;
  }
  .mb-md-10 {
    margin-bottom: 5rem !important;
  }
  .mb-md-12 {
    margin-bottom: 6rem !important;
  }
  .mb-md-14 {
    margin-bottom: 7rem !important;
  }
  .mb-md-16 {
    margin-bottom: 8rem !important;
  }
  .mb-md-auto {
    margin-bottom: auto !important;
  }
  .ms-md-0 {
    margin-left: 0rem !important;
  }
  .ms-md-1 {
    margin-left: 0.5rem !important;
  }
  .ms-md-2 {
    margin-left: 1rem !important;
  }
  .ms-md-3 {
    margin-left: 1.5rem !important;
  }
  .ms-md-4 {
    margin-left: 2rem !important;
  }
  .ms-md-5 {
    margin-left: 2.5rem !important;
  }
  .ms-md-6 {
    margin-left: 3rem !important;
  }
  .ms-md-7 {
    margin-left: 3.5rem !important;
  }
  .ms-md-8 {
    margin-left: 4rem !important;
  }
  .ms-md-9 {
    margin-left: 4.5rem !important;
  }
  .ms-md-10 {
    margin-left: 5rem !important;
  }
  .ms-md-12 {
    margin-left: 6rem !important;
  }
  .ms-md-14 {
    margin-left: 7rem !important;
  }
  .ms-md-16 {
    margin-left: 8rem !important;
  }
  .ms-md-auto {
    margin-left: auto !important;
  }
  .p-md-0 {
    padding: 0rem !important;
  }
  .p-md-1 {
    padding: 0.5rem !important;
  }
  .p-md-2 {
    padding: 1rem !important;
  }
  .p-md-3 {
    padding: 1.5rem !important;
  }
  .p-md-4 {
    padding: 2rem !important;
  }
  .p-md-5 {
    padding: 2.5rem !important;
  }
  .p-md-6 {
    padding: 3rem !important;
  }
  .p-md-7 {
    padding: 3.5rem !important;
  }
  .p-md-8 {
    padding: 4rem !important;
  }
  .p-md-9 {
    padding: 4.5rem !important;
  }
  .p-md-10 {
    padding: 5rem !important;
  }
  .p-md-12 {
    padding: 6rem !important;
  }
  .p-md-14 {
    padding: 7rem !important;
  }
  .p-md-16 {
    padding: 8rem !important;
  }
  .px-md-0 {
    padding-right: 0rem !important;
    padding-left: 0rem !important;
  }
  .px-md-1 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-md-2 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-md-3 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-md-4 {
    padding-right: 2rem !important;
    padding-left: 2rem !important;
  }
  .px-md-5 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .px-md-6 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-md-7 {
    padding-right: 3.5rem !important;
    padding-left: 3.5rem !important;
  }
  .px-md-8 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }
  .px-md-9 {
    padding-right: 4.5rem !important;
    padding-left: 4.5rem !important;
  }
  .px-md-10 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }
  .px-md-12 {
    padding-right: 6rem !important;
    padding-left: 6rem !important;
  }
  .px-md-14 {
    padding-right: 7rem !important;
    padding-left: 7rem !important;
  }
  .px-md-16 {
    padding-right: 8rem !important;
    padding-left: 8rem !important;
  }
  .py-md-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
  .py-md-1 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-md-2 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-md-3 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-md-4 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  .py-md-5 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .py-md-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-md-7 {
    padding-top: 3.5rem !important;
    padding-bottom: 3.5rem !important;
  }
  .py-md-8 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
  .py-md-9 {
    padding-top: 4.5rem !important;
    padding-bottom: 4.5rem !important;
  }
  .py-md-10 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
  .py-md-12 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
  .py-md-14 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
  .py-md-16 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
  .pt-md-0 {
    padding-top: 0rem !important;
  }
  .pt-md-1 {
    padding-top: 0.5rem !important;
  }
  .pt-md-2 {
    padding-top: 1rem !important;
  }
  .pt-md-3 {
    padding-top: 1.5rem !important;
  }
  .pt-md-4 {
    padding-top: 2rem !important;
  }
  .pt-md-5 {
    padding-top: 2.5rem !important;
  }
  .pt-md-6 {
    padding-top: 3rem !important;
  }
  .pt-md-7 {
    padding-top: 3.5rem !important;
  }
  .pt-md-8 {
    padding-top: 4rem !important;
  }
  .pt-md-9 {
    padding-top: 4.5rem !important;
  }
  .pt-md-10 {
    padding-top: 5rem !important;
  }
  .pt-md-12 {
    padding-top: 6rem !important;
  }
  .pt-md-14 {
    padding-top: 7rem !important;
  }
  .pt-md-16 {
    padding-top: 8rem !important;
  }
  .pe-md-0 {
    padding-right: 0rem !important;
  }
  .pe-md-1 {
    padding-right: 0.5rem !important;
  }
  .pe-md-2 {
    padding-right: 1rem !important;
  }
  .pe-md-3 {
    padding-right: 1.5rem !important;
  }
  .pe-md-4 {
    padding-right: 2rem !important;
  }
  .pe-md-5 {
    padding-right: 2.5rem !important;
  }
  .pe-md-6 {
    padding-right: 3rem !important;
  }
  .pe-md-7 {
    padding-right: 3.5rem !important;
  }
  .pe-md-8 {
    padding-right: 4rem !important;
  }
  .pe-md-9 {
    padding-right: 4.5rem !important;
  }
  .pe-md-10 {
    padding-right: 5rem !important;
  }
  .pe-md-12 {
    padding-right: 6rem !important;
  }
  .pe-md-14 {
    padding-right: 7rem !important;
  }
  .pe-md-16 {
    padding-right: 8rem !important;
  }
  .pb-md-0 {
    padding-bottom: 0rem !important;
  }
  .pb-md-1 {
    padding-bottom: 0.5rem !important;
  }
  .pb-md-2 {
    padding-bottom: 1rem !important;
  }
  .pb-md-3 {
    padding-bottom: 1.5rem !important;
  }
  .pb-md-4 {
    padding-bottom: 2rem !important;
  }
  .pb-md-5 {
    padding-bottom: 2.5rem !important;
  }
  .pb-md-6 {
    padding-bottom: 3rem !important;
  }
  .pb-md-7 {
    padding-bottom: 3.5rem !important;
  }
  .pb-md-8 {
    padding-bottom: 4rem !important;
  }
  .pb-md-9 {
    padding-bottom: 4.5rem !important;
  }
  .pb-md-10 {
    padding-bottom: 5rem !important;
  }
  .pb-md-12 {
    padding-bottom: 6rem !important;
  }
  .pb-md-14 {
    padding-bottom: 7rem !important;
  }
  .pb-md-16 {
    padding-bottom: 8rem !important;
  }
  .ps-md-0 {
    padding-left: 0rem !important;
  }
  .ps-md-1 {
    padding-left: 0.5rem !important;
  }
  .ps-md-2 {
    padding-left: 1rem !important;
  }
  .ps-md-3 {
    padding-left: 1.5rem !important;
  }
  .ps-md-4 {
    padding-left: 2rem !important;
  }
  .ps-md-5 {
    padding-left: 2.5rem !important;
  }
  .ps-md-6 {
    padding-left: 3rem !important;
  }
  .ps-md-7 {
    padding-left: 3.5rem !important;
  }
  .ps-md-8 {
    padding-left: 4rem !important;
  }
  .ps-md-9 {
    padding-left: 4.5rem !important;
  }
  .ps-md-10 {
    padding-left: 5rem !important;
  }
  .ps-md-12 {
    padding-left: 6rem !important;
  }
  .ps-md-14 {
    padding-left: 7rem !important;
  }
  .ps-md-16 {
    padding-left: 8rem !important;
  }
  .gap-md-0 {
    gap: 0rem !important;
  }
  .gap-md-1 {
    gap: 0.5rem !important;
  }
  .gap-md-2 {
    gap: 1rem !important;
  }
  .gap-md-3 {
    gap: 1.5rem !important;
  }
  .gap-md-4 {
    gap: 2rem !important;
  }
  .gap-md-5 {
    gap: 2.5rem !important;
  }
  .gap-md-6 {
    gap: 3rem !important;
  }
  .gap-md-7 {
    gap: 3.5rem !important;
  }
  .gap-md-8 {
    gap: 4rem !important;
  }
  .gap-md-9 {
    gap: 4.5rem !important;
  }
  .gap-md-10 {
    gap: 5rem !important;
  }
  .gap-md-12 {
    gap: 6rem !important;
  }
  .gap-md-14 {
    gap: 7rem !important;
  }
  .gap-md-16 {
    gap: 8rem !important;
  }
  .row-gap-md-0 {
    row-gap: 0rem !important;
  }
  .row-gap-md-1 {
    row-gap: 0.5rem !important;
  }
  .row-gap-md-2 {
    row-gap: 1rem !important;
  }
  .row-gap-md-3 {
    row-gap: 1.5rem !important;
  }
  .row-gap-md-4 {
    row-gap: 2rem !important;
  }
  .row-gap-md-5 {
    row-gap: 2.5rem !important;
  }
  .row-gap-md-6 {
    row-gap: 3rem !important;
  }
  .row-gap-md-7 {
    row-gap: 3.5rem !important;
  }
  .row-gap-md-8 {
    row-gap: 4rem !important;
  }
  .row-gap-md-9 {
    row-gap: 4.5rem !important;
  }
  .row-gap-md-10 {
    row-gap: 5rem !important;
  }
  .row-gap-md-12 {
    row-gap: 6rem !important;
  }
  .row-gap-md-14 {
    row-gap: 7rem !important;
  }
  .row-gap-md-16 {
    row-gap: 8rem !important;
  }
  .column-gap-md-0 {
    -moz-column-gap: 0rem !important;
         column-gap: 0rem !important;
  }
  .column-gap-md-1 {
    -moz-column-gap: 0.5rem !important;
         column-gap: 0.5rem !important;
  }
  .column-gap-md-2 {
    -moz-column-gap: 1rem !important;
         column-gap: 1rem !important;
  }
  .column-gap-md-3 {
    -moz-column-gap: 1.5rem !important;
         column-gap: 1.5rem !important;
  }
  .column-gap-md-4 {
    -moz-column-gap: 2rem !important;
         column-gap: 2rem !important;
  }
  .column-gap-md-5 {
    -moz-column-gap: 2.5rem !important;
         column-gap: 2.5rem !important;
  }
  .column-gap-md-6 {
    -moz-column-gap: 3rem !important;
         column-gap: 3rem !important;
  }
  .column-gap-md-7 {
    -moz-column-gap: 3.5rem !important;
         column-gap: 3.5rem !important;
  }
  .column-gap-md-8 {
    -moz-column-gap: 4rem !important;
         column-gap: 4rem !important;
  }
  .column-gap-md-9 {
    -moz-column-gap: 4.5rem !important;
         column-gap: 4.5rem !important;
  }
  .column-gap-md-10 {
    -moz-column-gap: 5rem !important;
         column-gap: 5rem !important;
  }
  .column-gap-md-12 {
    -moz-column-gap: 6rem !important;
         column-gap: 6rem !important;
  }
  .column-gap-md-14 {
    -moz-column-gap: 7rem !important;
         column-gap: 7rem !important;
  }
  .column-gap-md-16 {
    -moz-column-gap: 8rem !important;
         column-gap: 8rem !important;
  }
  .text-md-start {
    text-align: left !important;
  }
  .text-md-end {
    text-align: right !important;
  }
  .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .float-lg-start {
    float: left !important;
  }
  .float-lg-end {
    float: right !important;
  }
  .float-lg-none {
    float: none !important;
  }
  .object-fit-lg-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important;
  }
  .object-fit-lg-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
  .object-fit-lg-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important;
  }
  .object-fit-lg-scale {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important;
  }
  .object-fit-lg-none {
    -o-object-fit: none !important;
       object-fit: none !important;
  }
  .d-lg-inline {
    display: inline !important;
  }
  .d-lg-inline-block {
    display: inline-block !important;
  }
  .d-lg-block {
    display: block !important;
  }
  .d-lg-grid {
    display: grid !important;
  }
  .d-lg-inline-grid {
    display: inline-grid !important;
  }
  .d-lg-table {
    display: table !important;
  }
  .d-lg-table-row {
    display: table-row !important;
  }
  .d-lg-table-cell {
    display: table-cell !important;
  }
  .d-lg-flex {
    display: flex !important;
  }
  .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .d-lg-none {
    display: none !important;
  }
  .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .flex-lg-row {
    flex-direction: row !important;
  }
  .flex-lg-column {
    flex-direction: column !important;
  }
  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .justify-content-lg-center {
    justify-content: center !important;
  }
  .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-lg-start {
    align-items: flex-start !important;
  }
  .align-items-lg-end {
    align-items: flex-end !important;
  }
  .align-items-lg-center {
    align-items: center !important;
  }
  .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .align-content-lg-start {
    align-content: flex-start !important;
  }
  .align-content-lg-end {
    align-content: flex-end !important;
  }
  .align-content-lg-center {
    align-content: center !important;
  }
  .align-content-lg-between {
    align-content: space-between !important;
  }
  .align-content-lg-around {
    align-content: space-around !important;
  }
  .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .align-self-lg-auto {
    align-self: auto !important;
  }
  .align-self-lg-start {
    align-self: flex-start !important;
  }
  .align-self-lg-end {
    align-self: flex-end !important;
  }
  .align-self-lg-center {
    align-self: center !important;
  }
  .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .order-lg-first {
    order: -1 !important;
  }
  .order-lg-0 {
    order: 0 !important;
  }
  .order-lg-1 {
    order: 1 !important;
  }
  .order-lg-2 {
    order: 2 !important;
  }
  .order-lg-3 {
    order: 3 !important;
  }
  .order-lg-4 {
    order: 4 !important;
  }
  .order-lg-5 {
    order: 5 !important;
  }
  .order-lg-last {
    order: 6 !important;
  }
  .m-lg-0 {
    margin: 0rem !important;
  }
  .m-lg-1 {
    margin: 0.5rem !important;
  }
  .m-lg-2 {
    margin: 1rem !important;
  }
  .m-lg-3 {
    margin: 1.5rem !important;
  }
  .m-lg-4 {
    margin: 2rem !important;
  }
  .m-lg-5 {
    margin: 2.5rem !important;
  }
  .m-lg-6 {
    margin: 3rem !important;
  }
  .m-lg-7 {
    margin: 3.5rem !important;
  }
  .m-lg-8 {
    margin: 4rem !important;
  }
  .m-lg-9 {
    margin: 4.5rem !important;
  }
  .m-lg-10 {
    margin: 5rem !important;
  }
  .m-lg-12 {
    margin: 6rem !important;
  }
  .m-lg-14 {
    margin: 7rem !important;
  }
  .m-lg-16 {
    margin: 8rem !important;
  }
  .m-lg-auto {
    margin: auto !important;
  }
  .mx-lg-0 {
    margin-right: 0rem !important;
    margin-left: 0rem !important;
  }
  .mx-lg-1 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-lg-2 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-lg-3 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-lg-4 {
    margin-right: 2rem !important;
    margin-left: 2rem !important;
  }
  .mx-lg-5 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-lg-6 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-lg-7 {
    margin-right: 3.5rem !important;
    margin-left: 3.5rem !important;
  }
  .mx-lg-8 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }
  .mx-lg-9 {
    margin-right: 4.5rem !important;
    margin-left: 4.5rem !important;
  }
  .mx-lg-10 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }
  .mx-lg-12 {
    margin-right: 6rem !important;
    margin-left: 6rem !important;
  }
  .mx-lg-14 {
    margin-right: 7rem !important;
    margin-left: 7rem !important;
  }
  .mx-lg-16 {
    margin-right: 8rem !important;
    margin-left: 8rem !important;
  }
  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-lg-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
  .my-lg-1 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-lg-2 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-lg-3 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-lg-4 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
  .my-lg-5 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-lg-6 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-lg-7 {
    margin-top: 3.5rem !important;
    margin-bottom: 3.5rem !important;
  }
  .my-lg-8 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
  .my-lg-9 {
    margin-top: 4.5rem !important;
    margin-bottom: 4.5rem !important;
  }
  .my-lg-10 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
  .my-lg-12 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
  .my-lg-14 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
  .my-lg-16 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-lg-0 {
    margin-top: 0rem !important;
  }
  .mt-lg-1 {
    margin-top: 0.5rem !important;
  }
  .mt-lg-2 {
    margin-top: 1rem !important;
  }
  .mt-lg-3 {
    margin-top: 1.5rem !important;
  }
  .mt-lg-4 {
    margin-top: 2rem !important;
  }
  .mt-lg-5 {
    margin-top: 2.5rem !important;
  }
  .mt-lg-6 {
    margin-top: 3rem !important;
  }
  .mt-lg-7 {
    margin-top: 3.5rem !important;
  }
  .mt-lg-8 {
    margin-top: 4rem !important;
  }
  .mt-lg-9 {
    margin-top: 4.5rem !important;
  }
  .mt-lg-10 {
    margin-top: 5rem !important;
  }
  .mt-lg-12 {
    margin-top: 6rem !important;
  }
  .mt-lg-14 {
    margin-top: 7rem !important;
  }
  .mt-lg-16 {
    margin-top: 8rem !important;
  }
  .mt-lg-auto {
    margin-top: auto !important;
  }
  .me-lg-0 {
    margin-right: 0rem !important;
  }
  .me-lg-1 {
    margin-right: 0.5rem !important;
  }
  .me-lg-2 {
    margin-right: 1rem !important;
  }
  .me-lg-3 {
    margin-right: 1.5rem !important;
  }
  .me-lg-4 {
    margin-right: 2rem !important;
  }
  .me-lg-5 {
    margin-right: 2.5rem !important;
  }
  .me-lg-6 {
    margin-right: 3rem !important;
  }
  .me-lg-7 {
    margin-right: 3.5rem !important;
  }
  .me-lg-8 {
    margin-right: 4rem !important;
  }
  .me-lg-9 {
    margin-right: 4.5rem !important;
  }
  .me-lg-10 {
    margin-right: 5rem !important;
  }
  .me-lg-12 {
    margin-right: 6rem !important;
  }
  .me-lg-14 {
    margin-right: 7rem !important;
  }
  .me-lg-16 {
    margin-right: 8rem !important;
  }
  .me-lg-auto {
    margin-right: auto !important;
  }
  .mb-lg-0 {
    margin-bottom: 0rem !important;
  }
  .mb-lg-1 {
    margin-bottom: 0.5rem !important;
  }
  .mb-lg-2 {
    margin-bottom: 1rem !important;
  }
  .mb-lg-3 {
    margin-bottom: 1.5rem !important;
  }
  .mb-lg-4 {
    margin-bottom: 2rem !important;
  }
  .mb-lg-5 {
    margin-bottom: 2.5rem !important;
  }
  .mb-lg-6 {
    margin-bottom: 3rem !important;
  }
  .mb-lg-7 {
    margin-bottom: 3.5rem !important;
  }
  .mb-lg-8 {
    margin-bottom: 4rem !important;
  }
  .mb-lg-9 {
    margin-bottom: 4.5rem !important;
  }
  .mb-lg-10 {
    margin-bottom: 5rem !important;
  }
  .mb-lg-12 {
    margin-bottom: 6rem !important;
  }
  .mb-lg-14 {
    margin-bottom: 7rem !important;
  }
  .mb-lg-16 {
    margin-bottom: 8rem !important;
  }
  .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .ms-lg-0 {
    margin-left: 0rem !important;
  }
  .ms-lg-1 {
    margin-left: 0.5rem !important;
  }
  .ms-lg-2 {
    margin-left: 1rem !important;
  }
  .ms-lg-3 {
    margin-left: 1.5rem !important;
  }
  .ms-lg-4 {
    margin-left: 2rem !important;
  }
  .ms-lg-5 {
    margin-left: 2.5rem !important;
  }
  .ms-lg-6 {
    margin-left: 3rem !important;
  }
  .ms-lg-7 {
    margin-left: 3.5rem !important;
  }
  .ms-lg-8 {
    margin-left: 4rem !important;
  }
  .ms-lg-9 {
    margin-left: 4.5rem !important;
  }
  .ms-lg-10 {
    margin-left: 5rem !important;
  }
  .ms-lg-12 {
    margin-left: 6rem !important;
  }
  .ms-lg-14 {
    margin-left: 7rem !important;
  }
  .ms-lg-16 {
    margin-left: 8rem !important;
  }
  .ms-lg-auto {
    margin-left: auto !important;
  }
  .p-lg-0 {
    padding: 0rem !important;
  }
  .p-lg-1 {
    padding: 0.5rem !important;
  }
  .p-lg-2 {
    padding: 1rem !important;
  }
  .p-lg-3 {
    padding: 1.5rem !important;
  }
  .p-lg-4 {
    padding: 2rem !important;
  }
  .p-lg-5 {
    padding: 2.5rem !important;
  }
  .p-lg-6 {
    padding: 3rem !important;
  }
  .p-lg-7 {
    padding: 3.5rem !important;
  }
  .p-lg-8 {
    padding: 4rem !important;
  }
  .p-lg-9 {
    padding: 4.5rem !important;
  }
  .p-lg-10 {
    padding: 5rem !important;
  }
  .p-lg-12 {
    padding: 6rem !important;
  }
  .p-lg-14 {
    padding: 7rem !important;
  }
  .p-lg-16 {
    padding: 8rem !important;
  }
  .px-lg-0 {
    padding-right: 0rem !important;
    padding-left: 0rem !important;
  }
  .px-lg-1 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-lg-2 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-lg-3 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-lg-4 {
    padding-right: 2rem !important;
    padding-left: 2rem !important;
  }
  .px-lg-5 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .px-lg-6 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-lg-7 {
    padding-right: 3.5rem !important;
    padding-left: 3.5rem !important;
  }
  .px-lg-8 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }
  .px-lg-9 {
    padding-right: 4.5rem !important;
    padding-left: 4.5rem !important;
  }
  .px-lg-10 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }
  .px-lg-12 {
    padding-right: 6rem !important;
    padding-left: 6rem !important;
  }
  .px-lg-14 {
    padding-right: 7rem !important;
    padding-left: 7rem !important;
  }
  .px-lg-16 {
    padding-right: 8rem !important;
    padding-left: 8rem !important;
  }
  .py-lg-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
  .py-lg-1 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-lg-2 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-lg-3 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-lg-4 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  .py-lg-5 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .py-lg-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-lg-7 {
    padding-top: 3.5rem !important;
    padding-bottom: 3.5rem !important;
  }
  .py-lg-8 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
  .py-lg-9 {
    padding-top: 4.5rem !important;
    padding-bottom: 4.5rem !important;
  }
  .py-lg-10 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
  .py-lg-12 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
  .py-lg-14 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
  .py-lg-16 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
  .pt-lg-0 {
    padding-top: 0rem !important;
  }
  .pt-lg-1 {
    padding-top: 0.5rem !important;
  }
  .pt-lg-2 {
    padding-top: 1rem !important;
  }
  .pt-lg-3 {
    padding-top: 1.5rem !important;
  }
  .pt-lg-4 {
    padding-top: 2rem !important;
  }
  .pt-lg-5 {
    padding-top: 2.5rem !important;
  }
  .pt-lg-6 {
    padding-top: 3rem !important;
  }
  .pt-lg-7 {
    padding-top: 3.5rem !important;
  }
  .pt-lg-8 {
    padding-top: 4rem !important;
  }
  .pt-lg-9 {
    padding-top: 4.5rem !important;
  }
  .pt-lg-10 {
    padding-top: 5rem !important;
  }
  .pt-lg-12 {
    padding-top: 6rem !important;
  }
  .pt-lg-14 {
    padding-top: 7rem !important;
  }
  .pt-lg-16 {
    padding-top: 8rem !important;
  }
  .pe-lg-0 {
    padding-right: 0rem !important;
  }
  .pe-lg-1 {
    padding-right: 0.5rem !important;
  }
  .pe-lg-2 {
    padding-right: 1rem !important;
  }
  .pe-lg-3 {
    padding-right: 1.5rem !important;
  }
  .pe-lg-4 {
    padding-right: 2rem !important;
  }
  .pe-lg-5 {
    padding-right: 2.5rem !important;
  }
  .pe-lg-6 {
    padding-right: 3rem !important;
  }
  .pe-lg-7 {
    padding-right: 3.5rem !important;
  }
  .pe-lg-8 {
    padding-right: 4rem !important;
  }
  .pe-lg-9 {
    padding-right: 4.5rem !important;
  }
  .pe-lg-10 {
    padding-right: 5rem !important;
  }
  .pe-lg-12 {
    padding-right: 6rem !important;
  }
  .pe-lg-14 {
    padding-right: 7rem !important;
  }
  .pe-lg-16 {
    padding-right: 8rem !important;
  }
  .pb-lg-0 {
    padding-bottom: 0rem !important;
  }
  .pb-lg-1 {
    padding-bottom: 0.5rem !important;
  }
  .pb-lg-2 {
    padding-bottom: 1rem !important;
  }
  .pb-lg-3 {
    padding-bottom: 1.5rem !important;
  }
  .pb-lg-4 {
    padding-bottom: 2rem !important;
  }
  .pb-lg-5 {
    padding-bottom: 2.5rem !important;
  }
  .pb-lg-6 {
    padding-bottom: 3rem !important;
  }
  .pb-lg-7 {
    padding-bottom: 3.5rem !important;
  }
  .pb-lg-8 {
    padding-bottom: 4rem !important;
  }
  .pb-lg-9 {
    padding-bottom: 4.5rem !important;
  }
  .pb-lg-10 {
    padding-bottom: 5rem !important;
  }
  .pb-lg-12 {
    padding-bottom: 6rem !important;
  }
  .pb-lg-14 {
    padding-bottom: 7rem !important;
  }
  .pb-lg-16 {
    padding-bottom: 8rem !important;
  }
  .ps-lg-0 {
    padding-left: 0rem !important;
  }
  .ps-lg-1 {
    padding-left: 0.5rem !important;
  }
  .ps-lg-2 {
    padding-left: 1rem !important;
  }
  .ps-lg-3 {
    padding-left: 1.5rem !important;
  }
  .ps-lg-4 {
    padding-left: 2rem !important;
  }
  .ps-lg-5 {
    padding-left: 2.5rem !important;
  }
  .ps-lg-6 {
    padding-left: 3rem !important;
  }
  .ps-lg-7 {
    padding-left: 3.5rem !important;
  }
  .ps-lg-8 {
    padding-left: 4rem !important;
  }
  .ps-lg-9 {
    padding-left: 4.5rem !important;
  }
  .ps-lg-10 {
    padding-left: 5rem !important;
  }
  .ps-lg-12 {
    padding-left: 6rem !important;
  }
  .ps-lg-14 {
    padding-left: 7rem !important;
  }
  .ps-lg-16 {
    padding-left: 8rem !important;
  }
  .gap-lg-0 {
    gap: 0rem !important;
  }
  .gap-lg-1 {
    gap: 0.5rem !important;
  }
  .gap-lg-2 {
    gap: 1rem !important;
  }
  .gap-lg-3 {
    gap: 1.5rem !important;
  }
  .gap-lg-4 {
    gap: 2rem !important;
  }
  .gap-lg-5 {
    gap: 2.5rem !important;
  }
  .gap-lg-6 {
    gap: 3rem !important;
  }
  .gap-lg-7 {
    gap: 3.5rem !important;
  }
  .gap-lg-8 {
    gap: 4rem !important;
  }
  .gap-lg-9 {
    gap: 4.5rem !important;
  }
  .gap-lg-10 {
    gap: 5rem !important;
  }
  .gap-lg-12 {
    gap: 6rem !important;
  }
  .gap-lg-14 {
    gap: 7rem !important;
  }
  .gap-lg-16 {
    gap: 8rem !important;
  }
  .row-gap-lg-0 {
    row-gap: 0rem !important;
  }
  .row-gap-lg-1 {
    row-gap: 0.5rem !important;
  }
  .row-gap-lg-2 {
    row-gap: 1rem !important;
  }
  .row-gap-lg-3 {
    row-gap: 1.5rem !important;
  }
  .row-gap-lg-4 {
    row-gap: 2rem !important;
  }
  .row-gap-lg-5 {
    row-gap: 2.5rem !important;
  }
  .row-gap-lg-6 {
    row-gap: 3rem !important;
  }
  .row-gap-lg-7 {
    row-gap: 3.5rem !important;
  }
  .row-gap-lg-8 {
    row-gap: 4rem !important;
  }
  .row-gap-lg-9 {
    row-gap: 4.5rem !important;
  }
  .row-gap-lg-10 {
    row-gap: 5rem !important;
  }
  .row-gap-lg-12 {
    row-gap: 6rem !important;
  }
  .row-gap-lg-14 {
    row-gap: 7rem !important;
  }
  .row-gap-lg-16 {
    row-gap: 8rem !important;
  }
  .column-gap-lg-0 {
    -moz-column-gap: 0rem !important;
         column-gap: 0rem !important;
  }
  .column-gap-lg-1 {
    -moz-column-gap: 0.5rem !important;
         column-gap: 0.5rem !important;
  }
  .column-gap-lg-2 {
    -moz-column-gap: 1rem !important;
         column-gap: 1rem !important;
  }
  .column-gap-lg-3 {
    -moz-column-gap: 1.5rem !important;
         column-gap: 1.5rem !important;
  }
  .column-gap-lg-4 {
    -moz-column-gap: 2rem !important;
         column-gap: 2rem !important;
  }
  .column-gap-lg-5 {
    -moz-column-gap: 2.5rem !important;
         column-gap: 2.5rem !important;
  }
  .column-gap-lg-6 {
    -moz-column-gap: 3rem !important;
         column-gap: 3rem !important;
  }
  .column-gap-lg-7 {
    -moz-column-gap: 3.5rem !important;
         column-gap: 3.5rem !important;
  }
  .column-gap-lg-8 {
    -moz-column-gap: 4rem !important;
         column-gap: 4rem !important;
  }
  .column-gap-lg-9 {
    -moz-column-gap: 4.5rem !important;
         column-gap: 4.5rem !important;
  }
  .column-gap-lg-10 {
    -moz-column-gap: 5rem !important;
         column-gap: 5rem !important;
  }
  .column-gap-lg-12 {
    -moz-column-gap: 6rem !important;
         column-gap: 6rem !important;
  }
  .column-gap-lg-14 {
    -moz-column-gap: 7rem !important;
         column-gap: 7rem !important;
  }
  .column-gap-lg-16 {
    -moz-column-gap: 8rem !important;
         column-gap: 8rem !important;
  }
  .text-lg-start {
    text-align: left !important;
  }
  .text-lg-end {
    text-align: right !important;
  }
  .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1260px) {
  .float-xl-start {
    float: left !important;
  }
  .float-xl-end {
    float: right !important;
  }
  .float-xl-none {
    float: none !important;
  }
  .object-fit-xl-contain {
    -o-object-fit: contain !important;
       object-fit: contain !important;
  }
  .object-fit-xl-cover {
    -o-object-fit: cover !important;
       object-fit: cover !important;
  }
  .object-fit-xl-fill {
    -o-object-fit: fill !important;
       object-fit: fill !important;
  }
  .object-fit-xl-scale {
    -o-object-fit: scale-down !important;
       object-fit: scale-down !important;
  }
  .object-fit-xl-none {
    -o-object-fit: none !important;
       object-fit: none !important;
  }
  .d-xl-inline {
    display: inline !important;
  }
  .d-xl-inline-block {
    display: inline-block !important;
  }
  .d-xl-block {
    display: block !important;
  }
  .d-xl-grid {
    display: grid !important;
  }
  .d-xl-inline-grid {
    display: inline-grid !important;
  }
  .d-xl-table {
    display: table !important;
  }
  .d-xl-table-row {
    display: table-row !important;
  }
  .d-xl-table-cell {
    display: table-cell !important;
  }
  .d-xl-flex {
    display: flex !important;
  }
  .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .d-xl-none {
    display: none !important;
  }
  .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .flex-xl-row {
    flex-direction: row !important;
  }
  .flex-xl-column {
    flex-direction: column !important;
  }
  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .justify-content-xl-center {
    justify-content: center !important;
  }
  .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .align-items-xl-start {
    align-items: flex-start !important;
  }
  .align-items-xl-end {
    align-items: flex-end !important;
  }
  .align-items-xl-center {
    align-items: center !important;
  }
  .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .align-content-xl-start {
    align-content: flex-start !important;
  }
  .align-content-xl-end {
    align-content: flex-end !important;
  }
  .align-content-xl-center {
    align-content: center !important;
  }
  .align-content-xl-between {
    align-content: space-between !important;
  }
  .align-content-xl-around {
    align-content: space-around !important;
  }
  .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .align-self-xl-auto {
    align-self: auto !important;
  }
  .align-self-xl-start {
    align-self: flex-start !important;
  }
  .align-self-xl-end {
    align-self: flex-end !important;
  }
  .align-self-xl-center {
    align-self: center !important;
  }
  .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .order-xl-first {
    order: -1 !important;
  }
  .order-xl-0 {
    order: 0 !important;
  }
  .order-xl-1 {
    order: 1 !important;
  }
  .order-xl-2 {
    order: 2 !important;
  }
  .order-xl-3 {
    order: 3 !important;
  }
  .order-xl-4 {
    order: 4 !important;
  }
  .order-xl-5 {
    order: 5 !important;
  }
  .order-xl-last {
    order: 6 !important;
  }
  .m-xl-0 {
    margin: 0rem !important;
  }
  .m-xl-1 {
    margin: 0.5rem !important;
  }
  .m-xl-2 {
    margin: 1rem !important;
  }
  .m-xl-3 {
    margin: 1.5rem !important;
  }
  .m-xl-4 {
    margin: 2rem !important;
  }
  .m-xl-5 {
    margin: 2.5rem !important;
  }
  .m-xl-6 {
    margin: 3rem !important;
  }
  .m-xl-7 {
    margin: 3.5rem !important;
  }
  .m-xl-8 {
    margin: 4rem !important;
  }
  .m-xl-9 {
    margin: 4.5rem !important;
  }
  .m-xl-10 {
    margin: 5rem !important;
  }
  .m-xl-12 {
    margin: 6rem !important;
  }
  .m-xl-14 {
    margin: 7rem !important;
  }
  .m-xl-16 {
    margin: 8rem !important;
  }
  .m-xl-auto {
    margin: auto !important;
  }
  .mx-xl-0 {
    margin-right: 0rem !important;
    margin-left: 0rem !important;
  }
  .mx-xl-1 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .mx-xl-2 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .mx-xl-3 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .mx-xl-4 {
    margin-right: 2rem !important;
    margin-left: 2rem !important;
  }
  .mx-xl-5 {
    margin-right: 2.5rem !important;
    margin-left: 2.5rem !important;
  }
  .mx-xl-6 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .mx-xl-7 {
    margin-right: 3.5rem !important;
    margin-left: 3.5rem !important;
  }
  .mx-xl-8 {
    margin-right: 4rem !important;
    margin-left: 4rem !important;
  }
  .mx-xl-9 {
    margin-right: 4.5rem !important;
    margin-left: 4.5rem !important;
  }
  .mx-xl-10 {
    margin-right: 5rem !important;
    margin-left: 5rem !important;
  }
  .mx-xl-12 {
    margin-right: 6rem !important;
    margin-left: 6rem !important;
  }
  .mx-xl-14 {
    margin-right: 7rem !important;
    margin-left: 7rem !important;
  }
  .mx-xl-16 {
    margin-right: 8rem !important;
    margin-left: 8rem !important;
  }
  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .my-xl-0 {
    margin-top: 0rem !important;
    margin-bottom: 0rem !important;
  }
  .my-xl-1 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .my-xl-2 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .my-xl-3 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .my-xl-4 {
    margin-top: 2rem !important;
    margin-bottom: 2rem !important;
  }
  .my-xl-5 {
    margin-top: 2.5rem !important;
    margin-bottom: 2.5rem !important;
  }
  .my-xl-6 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .my-xl-7 {
    margin-top: 3.5rem !important;
    margin-bottom: 3.5rem !important;
  }
  .my-xl-8 {
    margin-top: 4rem !important;
    margin-bottom: 4rem !important;
  }
  .my-xl-9 {
    margin-top: 4.5rem !important;
    margin-bottom: 4.5rem !important;
  }
  .my-xl-10 {
    margin-top: 5rem !important;
    margin-bottom: 5rem !important;
  }
  .my-xl-12 {
    margin-top: 6rem !important;
    margin-bottom: 6rem !important;
  }
  .my-xl-14 {
    margin-top: 7rem !important;
    margin-bottom: 7rem !important;
  }
  .my-xl-16 {
    margin-top: 8rem !important;
    margin-bottom: 8rem !important;
  }
  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .mt-xl-0 {
    margin-top: 0rem !important;
  }
  .mt-xl-1 {
    margin-top: 0.5rem !important;
  }
  .mt-xl-2 {
    margin-top: 1rem !important;
  }
  .mt-xl-3 {
    margin-top: 1.5rem !important;
  }
  .mt-xl-4 {
    margin-top: 2rem !important;
  }
  .mt-xl-5 {
    margin-top: 2.5rem !important;
  }
  .mt-xl-6 {
    margin-top: 3rem !important;
  }
  .mt-xl-7 {
    margin-top: 3.5rem !important;
  }
  .mt-xl-8 {
    margin-top: 4rem !important;
  }
  .mt-xl-9 {
    margin-top: 4.5rem !important;
  }
  .mt-xl-10 {
    margin-top: 5rem !important;
  }
  .mt-xl-12 {
    margin-top: 6rem !important;
  }
  .mt-xl-14 {
    margin-top: 7rem !important;
  }
  .mt-xl-16 {
    margin-top: 8rem !important;
  }
  .mt-xl-auto {
    margin-top: auto !important;
  }
  .me-xl-0 {
    margin-right: 0rem !important;
  }
  .me-xl-1 {
    margin-right: 0.5rem !important;
  }
  .me-xl-2 {
    margin-right: 1rem !important;
  }
  .me-xl-3 {
    margin-right: 1.5rem !important;
  }
  .me-xl-4 {
    margin-right: 2rem !important;
  }
  .me-xl-5 {
    margin-right: 2.5rem !important;
  }
  .me-xl-6 {
    margin-right: 3rem !important;
  }
  .me-xl-7 {
    margin-right: 3.5rem !important;
  }
  .me-xl-8 {
    margin-right: 4rem !important;
  }
  .me-xl-9 {
    margin-right: 4.5rem !important;
  }
  .me-xl-10 {
    margin-right: 5rem !important;
  }
  .me-xl-12 {
    margin-right: 6rem !important;
  }
  .me-xl-14 {
    margin-right: 7rem !important;
  }
  .me-xl-16 {
    margin-right: 8rem !important;
  }
  .me-xl-auto {
    margin-right: auto !important;
  }
  .mb-xl-0 {
    margin-bottom: 0rem !important;
  }
  .mb-xl-1 {
    margin-bottom: 0.5rem !important;
  }
  .mb-xl-2 {
    margin-bottom: 1rem !important;
  }
  .mb-xl-3 {
    margin-bottom: 1.5rem !important;
  }
  .mb-xl-4 {
    margin-bottom: 2rem !important;
  }
  .mb-xl-5 {
    margin-bottom: 2.5rem !important;
  }
  .mb-xl-6 {
    margin-bottom: 3rem !important;
  }
  .mb-xl-7 {
    margin-bottom: 3.5rem !important;
  }
  .mb-xl-8 {
    margin-bottom: 4rem !important;
  }
  .mb-xl-9 {
    margin-bottom: 4.5rem !important;
  }
  .mb-xl-10 {
    margin-bottom: 5rem !important;
  }
  .mb-xl-12 {
    margin-bottom: 6rem !important;
  }
  .mb-xl-14 {
    margin-bottom: 7rem !important;
  }
  .mb-xl-16 {
    margin-bottom: 8rem !important;
  }
  .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .ms-xl-0 {
    margin-left: 0rem !important;
  }
  .ms-xl-1 {
    margin-left: 0.5rem !important;
  }
  .ms-xl-2 {
    margin-left: 1rem !important;
  }
  .ms-xl-3 {
    margin-left: 1.5rem !important;
  }
  .ms-xl-4 {
    margin-left: 2rem !important;
  }
  .ms-xl-5 {
    margin-left: 2.5rem !important;
  }
  .ms-xl-6 {
    margin-left: 3rem !important;
  }
  .ms-xl-7 {
    margin-left: 3.5rem !important;
  }
  .ms-xl-8 {
    margin-left: 4rem !important;
  }
  .ms-xl-9 {
    margin-left: 4.5rem !important;
  }
  .ms-xl-10 {
    margin-left: 5rem !important;
  }
  .ms-xl-12 {
    margin-left: 6rem !important;
  }
  .ms-xl-14 {
    margin-left: 7rem !important;
  }
  .ms-xl-16 {
    margin-left: 8rem !important;
  }
  .ms-xl-auto {
    margin-left: auto !important;
  }
  .p-xl-0 {
    padding: 0rem !important;
  }
  .p-xl-1 {
    padding: 0.5rem !important;
  }
  .p-xl-2 {
    padding: 1rem !important;
  }
  .p-xl-3 {
    padding: 1.5rem !important;
  }
  .p-xl-4 {
    padding: 2rem !important;
  }
  .p-xl-5 {
    padding: 2.5rem !important;
  }
  .p-xl-6 {
    padding: 3rem !important;
  }
  .p-xl-7 {
    padding: 3.5rem !important;
  }
  .p-xl-8 {
    padding: 4rem !important;
  }
  .p-xl-9 {
    padding: 4.5rem !important;
  }
  .p-xl-10 {
    padding: 5rem !important;
  }
  .p-xl-12 {
    padding: 6rem !important;
  }
  .p-xl-14 {
    padding: 7rem !important;
  }
  .p-xl-16 {
    padding: 8rem !important;
  }
  .px-xl-0 {
    padding-right: 0rem !important;
    padding-left: 0rem !important;
  }
  .px-xl-1 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .px-xl-2 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .px-xl-3 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .px-xl-4 {
    padding-right: 2rem !important;
    padding-left: 2rem !important;
  }
  .px-xl-5 {
    padding-right: 2.5rem !important;
    padding-left: 2.5rem !important;
  }
  .px-xl-6 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .px-xl-7 {
    padding-right: 3.5rem !important;
    padding-left: 3.5rem !important;
  }
  .px-xl-8 {
    padding-right: 4rem !important;
    padding-left: 4rem !important;
  }
  .px-xl-9 {
    padding-right: 4.5rem !important;
    padding-left: 4.5rem !important;
  }
  .px-xl-10 {
    padding-right: 5rem !important;
    padding-left: 5rem !important;
  }
  .px-xl-12 {
    padding-right: 6rem !important;
    padding-left: 6rem !important;
  }
  .px-xl-14 {
    padding-right: 7rem !important;
    padding-left: 7rem !important;
  }
  .px-xl-16 {
    padding-right: 8rem !important;
    padding-left: 8rem !important;
  }
  .py-xl-0 {
    padding-top: 0rem !important;
    padding-bottom: 0rem !important;
  }
  .py-xl-1 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .py-xl-2 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .py-xl-3 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .py-xl-4 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }
  .py-xl-5 {
    padding-top: 2.5rem !important;
    padding-bottom: 2.5rem !important;
  }
  .py-xl-6 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .py-xl-7 {
    padding-top: 3.5rem !important;
    padding-bottom: 3.5rem !important;
  }
  .py-xl-8 {
    padding-top: 4rem !important;
    padding-bottom: 4rem !important;
  }
  .py-xl-9 {
    padding-top: 4.5rem !important;
    padding-bottom: 4.5rem !important;
  }
  .py-xl-10 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
  }
  .py-xl-12 {
    padding-top: 6rem !important;
    padding-bottom: 6rem !important;
  }
  .py-xl-14 {
    padding-top: 7rem !important;
    padding-bottom: 7rem !important;
  }
  .py-xl-16 {
    padding-top: 8rem !important;
    padding-bottom: 8rem !important;
  }
  .pt-xl-0 {
    padding-top: 0rem !important;
  }
  .pt-xl-1 {
    padding-top: 0.5rem !important;
  }
  .pt-xl-2 {
    padding-top: 1rem !important;
  }
  .pt-xl-3 {
    padding-top: 1.5rem !important;
  }
  .pt-xl-4 {
    padding-top: 2rem !important;
  }
  .pt-xl-5 {
    padding-top: 2.5rem !important;
  }
  .pt-xl-6 {
    padding-top: 3rem !important;
  }
  .pt-xl-7 {
    padding-top: 3.5rem !important;
  }
  .pt-xl-8 {
    padding-top: 4rem !important;
  }
  .pt-xl-9 {
    padding-top: 4.5rem !important;
  }
  .pt-xl-10 {
    padding-top: 5rem !important;
  }
  .pt-xl-12 {
    padding-top: 6rem !important;
  }
  .pt-xl-14 {
    padding-top: 7rem !important;
  }
  .pt-xl-16 {
    padding-top: 8rem !important;
  }
  .pe-xl-0 {
    padding-right: 0rem !important;
  }
  .pe-xl-1 {
    padding-right: 0.5rem !important;
  }
  .pe-xl-2 {
    padding-right: 1rem !important;
  }
  .pe-xl-3 {
    padding-right: 1.5rem !important;
  }
  .pe-xl-4 {
    padding-right: 2rem !important;
  }
  .pe-xl-5 {
    padding-right: 2.5rem !important;
  }
  .pe-xl-6 {
    padding-right: 3rem !important;
  }
  .pe-xl-7 {
    padding-right: 3.5rem !important;
  }
  .pe-xl-8 {
    padding-right: 4rem !important;
  }
  .pe-xl-9 {
    padding-right: 4.5rem !important;
  }
  .pe-xl-10 {
    padding-right: 5rem !important;
  }
  .pe-xl-12 {
    padding-right: 6rem !important;
  }
  .pe-xl-14 {
    padding-right: 7rem !important;
  }
  .pe-xl-16 {
    padding-right: 8rem !important;
  }
  .pb-xl-0 {
    padding-bottom: 0rem !important;
  }
  .pb-xl-1 {
    padding-bottom: 0.5rem !important;
  }
  .pb-xl-2 {
    padding-bottom: 1rem !important;
  }
  .pb-xl-3 {
    padding-bottom: 1.5rem !important;
  }
  .pb-xl-4 {
    padding-bottom: 2rem !important;
  }
  .pb-xl-5 {
    padding-bottom: 2.5rem !important;
  }
  .pb-xl-6 {
    padding-bottom: 3rem !important;
  }
  .pb-xl-7 {
    padding-bottom: 3.5rem !important;
  }
  .pb-xl-8 {
    padding-bottom: 4rem !important;
  }
  .pb-xl-9 {
    padding-bottom: 4.5rem !important;
  }
  .pb-xl-10 {
    padding-bottom: 5rem !important;
  }
  .pb-xl-12 {
    padding-bottom: 6rem !important;
  }
  .pb-xl-14 {
    padding-bottom: 7rem !important;
  }
  .pb-xl-16 {
    padding-bottom: 8rem !important;
  }
  .ps-xl-0 {
    padding-left: 0rem !important;
  }
  .ps-xl-1 {
    padding-left: 0.5rem !important;
  }
  .ps-xl-2 {
    padding-left: 1rem !important;
  }
  .ps-xl-3 {
    padding-left: 1.5rem !important;
  }
  .ps-xl-4 {
    padding-left: 2rem !important;
  }
  .ps-xl-5 {
    padding-left: 2.5rem !important;
  }
  .ps-xl-6 {
    padding-left: 3rem !important;
  }
  .ps-xl-7 {
    padding-left: 3.5rem !important;
  }
  .ps-xl-8 {
    padding-left: 4rem !important;
  }
  .ps-xl-9 {
    padding-left: 4.5rem !important;
  }
  .ps-xl-10 {
    padding-left: 5rem !important;
  }
  .ps-xl-12 {
    padding-left: 6rem !important;
  }
  .ps-xl-14 {
    padding-left: 7rem !important;
  }
  .ps-xl-16 {
    padding-left: 8rem !important;
  }
  .gap-xl-0 {
    gap: 0rem !important;
  }
  .gap-xl-1 {
    gap: 0.5rem !important;
  }
  .gap-xl-2 {
    gap: 1rem !important;
  }
  .gap-xl-3 {
    gap: 1.5rem !important;
  }
  .gap-xl-4 {
    gap: 2rem !important;
  }
  .gap-xl-5 {
    gap: 2.5rem !important;
  }
  .gap-xl-6 {
    gap: 3rem !important;
  }
  .gap-xl-7 {
    gap: 3.5rem !important;
  }
  .gap-xl-8 {
    gap: 4rem !important;
  }
  .gap-xl-9 {
    gap: 4.5rem !important;
  }
  .gap-xl-10 {
    gap: 5rem !important;
  }
  .gap-xl-12 {
    gap: 6rem !important;
  }
  .gap-xl-14 {
    gap: 7rem !important;
  }
  .gap-xl-16 {
    gap: 8rem !important;
  }
  .row-gap-xl-0 {
    row-gap: 0rem !important;
  }
  .row-gap-xl-1 {
    row-gap: 0.5rem !important;
  }
  .row-gap-xl-2 {
    row-gap: 1rem !important;
  }
  .row-gap-xl-3 {
    row-gap: 1.5rem !important;
  }
  .row-gap-xl-4 {
    row-gap: 2rem !important;
  }
  .row-gap-xl-5 {
    row-gap: 2.5rem !important;
  }
  .row-gap-xl-6 {
    row-gap: 3rem !important;
  }
  .row-gap-xl-7 {
    row-gap: 3.5rem !important;
  }
  .row-gap-xl-8 {
    row-gap: 4rem !important;
  }
  .row-gap-xl-9 {
    row-gap: 4.5rem !important;
  }
  .row-gap-xl-10 {
    row-gap: 5rem !important;
  }
  .row-gap-xl-12 {
    row-gap: 6rem !important;
  }
  .row-gap-xl-14 {
    row-gap: 7rem !important;
  }
  .row-gap-xl-16 {
    row-gap: 8rem !important;
  }
  .column-gap-xl-0 {
    -moz-column-gap: 0rem !important;
         column-gap: 0rem !important;
  }
  .column-gap-xl-1 {
    -moz-column-gap: 0.5rem !important;
         column-gap: 0.5rem !important;
  }
  .column-gap-xl-2 {
    -moz-column-gap: 1rem !important;
         column-gap: 1rem !important;
  }
  .column-gap-xl-3 {
    -moz-column-gap: 1.5rem !important;
         column-gap: 1.5rem !important;
  }
  .column-gap-xl-4 {
    -moz-column-gap: 2rem !important;
         column-gap: 2rem !important;
  }
  .column-gap-xl-5 {
    -moz-column-gap: 2.5rem !important;
         column-gap: 2.5rem !important;
  }
  .column-gap-xl-6 {
    -moz-column-gap: 3rem !important;
         column-gap: 3rem !important;
  }
  .column-gap-xl-7 {
    -moz-column-gap: 3.5rem !important;
         column-gap: 3.5rem !important;
  }
  .column-gap-xl-8 {
    -moz-column-gap: 4rem !important;
         column-gap: 4rem !important;
  }
  .column-gap-xl-9 {
    -moz-column-gap: 4.5rem !important;
         column-gap: 4.5rem !important;
  }
  .column-gap-xl-10 {
    -moz-column-gap: 5rem !important;
         column-gap: 5rem !important;
  }
  .column-gap-xl-12 {
    -moz-column-gap: 6rem !important;
         column-gap: 6rem !important;
  }
  .column-gap-xl-14 {
    -moz-column-gap: 7rem !important;
         column-gap: 7rem !important;
  }
  .column-gap-xl-16 {
    -moz-column-gap: 8rem !important;
         column-gap: 8rem !important;
  }
  .text-xl-start {
    text-align: left !important;
  }
  .text-xl-end {
    text-align: right !important;
  }
  .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important;
  }
  .fs-2 {
    font-size: 2rem !important;
  }
  .fs-3 {
    font-size: 1.75rem !important;
  }
  .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }
  .d-print-inline-block {
    display: inline-block !important;
  }
  .d-print-block {
    display: block !important;
  }
  .d-print-grid {
    display: grid !important;
  }
  .d-print-inline-grid {
    display: inline-grid !important;
  }
  .d-print-table {
    display: table !important;
  }
  .d-print-table-row {
    display: table-row !important;
  }
  .d-print-table-cell {
    display: table-cell !important;
  }
  .d-print-flex {
    display: flex !important;
  }
  .d-print-inline-flex {
    display: inline-flex !important;
  }
  .d-print-none {
    display: none !important;
  }
}
.img-background {
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: scroll;
}

.col.card {
  padding-right: 0;
  padding-left: 0;
}

.page-link {
  border-radius: 400px;
  text-decoration: none;
  font-weight: bold;
}

.page-item:last-child .page-link {
  border-radius: 400px;
}

.page-item:first-child .page-link {
  border-radius: 400px;
}
/* 
label {
  font-weight: bold;
  font-size: 0.85em;
}
 */
.card-img-top img {
  max-width: 100%;
  height: auto;
}

.card-header {
  border-bottom: 0;
}

.card-header .card-title {
  margin-bottom: 0;
}

.card-title a {
  color: #191919;
  text-decoration: none;
}

.card-title a:hover {
  text-decoration: underline;
}

.card-degree .card-header .card-title:after {
  content: "";
  width: 32px;
  height: 4px;
  display: block;
  background-color: #ffc627;
  margin-top: 16px;
}

.card-degree .card-footer {
  background-color: #ffffff;
}

.card-degree .card-footer a {
  color: #191919;
  text-decoration: none;
  font-weight: bold;
}

.card-degree .card-footer a:hover {
  text-decoration: underline;
}

.card-degree .card-footer a:after {
  float: right;
  height: 20px;
  width: 20px;
  content: url("data:image/svg+xml; utf8, <svg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='arrow-right' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' class='svg-inline--fa fa-arrow-right fa-w-14 fa-2x'><path fill='currentColor' d='M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z'></path></svg>");
}

.card-news .card-header,
.card-news .card-body {
  margin-right: 15px;
  margin-left: 15px;
  background-color: #ffffff;
}

.card-news .card-img-top {
  margin-bottom: -32px;
}

.card-event .card-header {
  border-top: solid 4px #ffc627;
}

.bg-dark a,
.bg-primary a,
.bg-black a {
  color: #e8e8e8;
}

.bg-light a,
.bg-secondary a,
.bg-white a {
  color: #8c1d40;
}

.visually-hidden:not(:focus):not(:active) {
  clip: rect(0 0 0 0);
  -webkit-clip-path: inset(100%);
          clip-path: inset(100%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

button:focus,
a:focus,
input:focus,
textarea:focus,
select:focus {
  outline: none !important;
  box-shadow: 0px 0px 0px 2px #ffffff, 0px 0px 0px 4px #191919 !important;
}

.alert {
  color: #191919;
  display: flex;
  border-radius: 0;
}
.alert .alert-icon {
  flex: 0 0 4rem;
}
.alert .alert-icon svg {
  font-size: 2rem;
}
.alert .alert-content {
  flex: 10;
  padding: 0.5rem 0rem;
}
.alert .alert-close {
  flex: 1;
}
.alert .alert-close .close {
  opacity: 1;
  font-size: 1rem;
}
.alert .alert-close .close:hover {
  opacity: 1;
}

.alert-warning {
  background-color: #ffeade; /* 33% opacity of official maroon */
  border-color: #ff7f32;
}

.alert-success {
  background-color: #e9f5db;
  border-color: #78be20;
}

.alert-info {
  background-color: #d6f0fa;
  border-color: #00a3e0;
}

.alert-danger {
  background-color: #f7dddd; /* 20% opacity of official maroon */
  border-color: #cc2f2f;
}

.alert:not([class*=alert-]),
div[class="alert alert-block"] {
  /* Catch-all for default alert class */
  background-color: #e8e8e8;
  border-color: #d0d0d0;
}

@media screen and (max-width: 576px) {
  .alert .alert-icon {
    flex: 0 0 3rem;
  }
  .alert .alert-icon svg {
    font-size: 2rem;
    margin-top: 1rem;
  }
  .alert .alert-close {
    margin-top: -0.5rem;
    margin-right: -0.75rem;
  }
}

.background-panel {
  height: 322px;
}

.gray-7-bg {
  background: #191919;
}

.gray-2-bg {
  background: #e8e8e8;
}

.gray-1-bg {
  background: #fafafa;
}

.white-bg {
  background: #ffffff;
  border: 1px solid #d0d0d0;
}

.image-bg {
  background: transparent linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.7882352941) 100%) 0% 0% no-repeat padding-box;
}

.section-line {
  border-bottom: 1px solid #707070;
  width: 100%;
}

.scaling-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.content-description {
  padding: 16px 0;
  font-weight: bold;
}

.max-size-container {
  max-width: 1920px;
}

.content-description-container {
  background-color: #fafafa;
  width: 100%;
  border: 1px solid #d0d0d0;
  margin-top: 16px;
}

.content-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px dashed #747474;
  height: 251px;
  margin: 20px 0 100px 0;
  font-size: 50px;
  color: #d0d0d0;
}

.bg {
  background: transparent #fff 0% 0% repeat padding-box;
  opacity: 1;
}
.bg.morse-code-white {
  background-image: url("../img/background-patterns/MorseCodeWhite.png");
}
.bg.morse-code-black {
  background-image: url("../img/background-patterns/MorseCodeBlack.png");
}
.bg.network-white {
  background-image: url("../img/background-patterns/NetworkWhite.png");
}
.bg.network-black {
  background-image: url("../img/background-patterns/NetworkBlack.png");
}
.bg.topo {
  opacity: 1;
}
.bg.topo-white {
  background-image: url("../img/background-patterns/TopoPatternWhite.png");
}
.bg.topo-black {
  background-image: url("../img/background-patterns/TopoPatternBlack.png");
}

.hidden-banner {
  display: none;
}

.banner {
  position: relative;
  display: flex;
  padding: 2rem 0;
}
.banner .banner-icon {
  text-align: left;
}
.banner .banner-icon svg {
  font-size: 2rem;
  margin: 0.25rem 2rem 0.25rem 0;
}
.banner .banner-content {
  flex: 5;
  margin-right: 2rem;
}
.banner .banner-content h3, .banner .banner-content .h3 {
  margin: 0.5rem 0rem;
}
.banner .banner-buttons {
  flex: 3;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
.banner .banner-buttons a,
.banner .banner-buttons button {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  margin-left: 0;
}
.banner .banner-close {
  flex: 2;
  margin-top: -1rem;
  margin-right: -1rem;
}
.banner .banner-close .close {
  opacity: 1;
  font-size: 1rem;
}
.banner .banner-close .close:hover {
  opacity: 1;
}

.banner-green {
  background: #78be20 0% 0% no-repeat padding-box;
}
.banner-green a:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}
.banner-green a:visited:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}

.banner-orange {
  background: #ff7f32 0% 0% no-repeat padding-box;
}
.banner-orange a:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}
.banner-orange a:visited:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}

.banner-blue {
  background: #00a3e0 0% 0% no-repeat padding-box;
}
.banner-blue a:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}
.banner-blue a:visited:not(.btn):not(.uds-modal-close-btn) {
  color: #191919;
}

.banner-gray {
  background: #e8e8e8 0% 0% no-repeat padding-box;
}

.banner-black {
  background: #191919 0% 0% no-repeat padding-box;
  color: #ffffff;
}
.banner-black a:not(.btn):not(.uds-modal-close-btn) {
  color: #ffc627;
}
.banner-black a:visited:not(.btn):not(.uds-modal-close-btn) {
  color: #7f6227;
}

@media screen and (max-width: 576px) {
  .banner {
    flex-direction: column;
  }
  .banner .banner-icon {
    flex: 1;
    margin-bottom: 0.5rem;
  }
  .banner .banner-icon svg {
    font-size: 2.5rem;
    margin: 0rem;
  }
  .banner .banner-content {
    margin-bottom: 1rem;
  }
  .banner .banner-content h3, .banner .banner-content .h3 {
    margin-bottom: 1rem;
  }
  .banner .banner-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    margin-top: initial;
    margin-right: initial;
  }
}

.btn, .uds-modal-close-btn {
  text-decoration: none;
  font-weight: bold;
  white-space: nowrap;
  width: -moz-max-content !important;
  width: max-content !important;
}
.btn::first-letter, .uds-modal-close-btn::first-letter {
  text-transform: uppercase;
}
.btn.btn-primary, .btn-primary.uds-modal-close-btn {
  color: #ffffff;
  background: #8c1d40;
}
.btn.btn-md, .btn-md.uds-modal-close-btn {
  font-size: 0.875rem;
  padding: 0.5rem 1rem;
}
.btn.btn-sm, .btn-sm.uds-modal-close-btn, .btn-group-sm > .btn, .btn-group-sm > .uds-modal-close-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
}
.btn.btn-tag, .btn-tag.uds-modal-close-btn {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  margin-top: 8px;
  background: #e8e8e8;
}
.btn.btn-tag.btn-tag-alt-white, .btn-tag.btn-tag-alt-white.uds-modal-close-btn {
  background: #e8e8e8;
  color: #191919;
}
.btn.btn-tag.btn-tag-alt-gray, .btn-tag.btn-tag-alt-gray.uds-modal-close-btn {
  background: #d0d0d0;
  color: #191919;
}
.btn.btn-tag.btn-tag-alt-black, .btn-tag.btn-tag-alt-black.uds-modal-close-btn {
  background: #bfbfbf;
  color: #191919;
}
.btn.btn-circle, .btn-circle.uds-modal-close-btn {
  padding: 0.25rem 0.25rem;
  width: 2rem !important;
  height: 2rem;
  border: solid 1px #d0d0d0;
}
.btn.btn-circle.btn-circle-alt-white, .btn-circle.btn-circle-alt-white.uds-modal-close-btn {
  background: #e8e8e8;
  color: #191919;
}
.btn.btn-circle.btn-circle-alt-gray, .btn-circle.btn-circle-alt-gray.uds-modal-close-btn {
  background: #ffffff;
  color: #191919;
}
.btn.btn-circle.btn-circle-alt-black, .btn-circle.btn-circle-alt-black.uds-modal-close-btn {
  background: #ffffff;
  color: #191919;
}
.btn.btn-circle.btn-circle-large, .btn-circle.btn-circle-large.uds-modal-close-btn {
  width: 4rem !important;
  height: 4rem;
  font-size: 1.5rem;
}
.btn.btn-circle.btn-circle-x-large, .btn-circle.btn-circle-x-large.uds-modal-close-btn {
  width: 6.5rem !important;
  height: 6.5rem;
  font-size: 3rem;
}
.btn:hover, .uds-modal-close-btn:hover {
  transform: none;
}
.btn:active, .uds-modal-close-btn:active {
  transform: scale(0.95);
}

.bg-light-gray {
  background-color: #fafafa;
}

.btn-gold {
  --bs-btn-color: #000;
  --bs-btn-bg: #ffc627;
  --bs-btn-border-color: #ffc627;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffc627;
  --bs-btn-hover-border-color: #ffc627;
  --bs-btn-focus-shadow-rgb: 217, 168, 33;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffc627;
  --bs-btn-active-border-color: #ffc627;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ffc627;
  --bs-btn-disabled-border-color: #ffc627;
}

.btn-maroon {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #8c1d40;
  --bs-btn-border-color: #8c1d40;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #8c1d40;
  --bs-btn-hover-border-color: #8c1d40;
  --bs-btn-focus-shadow-rgb: 157, 63, 93;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #8c1d40;
  --bs-btn-active-border-color: #8c1d40;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #8c1d40;
  --bs-btn-disabled-border-color: #8c1d40;
}

.btn-success {
  --bs-btn-color: #000;
  --bs-btn-bg: #78be20;
  --bs-btn-border-color: #78be20;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #78be20;
  --bs-btn-hover-border-color: #78be20;
  --bs-btn-focus-shadow-rgb: 102, 162, 27;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #78be20;
  --bs-btn-active-border-color: #78be20;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #78be20;
  --bs-btn-disabled-border-color: #78be20;
}

.btn-info {
  --bs-btn-color: #000;
  --bs-btn-bg: #00a3e0;
  --bs-btn-border-color: #00a3e0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #00a3e0;
  --bs-btn-hover-border-color: #00a3e0;
  --bs-btn-focus-shadow-rgb: 0, 139, 190;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #00a3e0;
  --bs-btn-active-border-color: #00a3e0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #00a3e0;
  --bs-btn-disabled-border-color: #00a3e0;
}

.btn-warning {
  --bs-btn-color: #000;
  --bs-btn-bg: #ff7f32;
  --bs-btn-border-color: #ff7f32;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ff7f32;
  --bs-btn-hover-border-color: #ff7f32;
  --bs-btn-focus-shadow-rgb: 217, 108, 43;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ff7f32;
  --bs-btn-active-border-color: #ff7f32;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ff7f32;
  --bs-btn-disabled-border-color: #ff7f32;
}

.btn-danger {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #cc2f2f;
  --bs-btn-border-color: #cc2f2f;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #cc2f2f;
  --bs-btn-hover-border-color: #cc2f2f;
  --bs-btn-focus-shadow-rgb: 212, 78, 78;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #cc2f2f;
  --bs-btn-active-border-color: #cc2f2f;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #cc2f2f;
  --bs-btn-disabled-border-color: #cc2f2f;
}

.btn-light {
  --bs-btn-color: #000;
  --bs-btn-bg: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #e8e8e8;
  --bs-btn-hover-border-color: #e8e8e8;
  --bs-btn-focus-shadow-rgb: 197, 197, 197;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #e8e8e8;
  --bs-btn-active-border-color: #e8e8e8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #e8e8e8;
  --bs-btn-disabled-border-color: #e8e8e8;
}

.btn-gray {
  --bs-btn-color: #000;
  --bs-btn-bg: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #bfbfbf;
  --bs-btn-hover-border-color: #bfbfbf;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #bfbfbf;
  --bs-btn-active-border-color: #bfbfbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #bfbfbf;
  --bs-btn-disabled-border-color: #bfbfbf;
}

.btn-dark {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #191919;
  --bs-btn-hover-border-color: #191919;
  --bs-btn-focus-shadow-rgb: 60, 60, 60;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #191919;
  --bs-btn-active-border-color: #191919;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #191919;
  --bs-btn-disabled-border-color: #191919;
}

.btn-gray-1 {
  --bs-btn-color: #000;
  --bs-btn-bg: #fafafa;
  --bs-btn-border-color: #fafafa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #fafafa;
  --bs-btn-hover-border-color: #fafafa;
  --bs-btn-focus-shadow-rgb: 213, 213, 213;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #fafafa;
  --bs-btn-active-border-color: #fafafa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #fafafa;
  --bs-btn-disabled-border-color: #fafafa;
}

.btn-gray-2 {
  --bs-btn-color: #000;
  --bs-btn-bg: #e8e8e8;
  --bs-btn-border-color: #e8e8e8;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #e8e8e8;
  --bs-btn-hover-border-color: #e8e8e8;
  --bs-btn-focus-shadow-rgb: 197, 197, 197;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #e8e8e8;
  --bs-btn-active-border-color: #e8e8e8;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #e8e8e8;
  --bs-btn-disabled-border-color: #e8e8e8;
}

.btn-gray-3 {
  --bs-btn-color: #000;
  --bs-btn-bg: #d0d0d0;
  --bs-btn-border-color: #d0d0d0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #d0d0d0;
  --bs-btn-hover-border-color: #d0d0d0;
  --bs-btn-focus-shadow-rgb: 177, 177, 177;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #d0d0d0;
  --bs-btn-active-border-color: #d0d0d0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #d0d0d0;
  --bs-btn-disabled-border-color: #d0d0d0;
}

.btn-gray-4 {
  --bs-btn-color: #000;
  --bs-btn-bg: #bfbfbf;
  --bs-btn-border-color: #bfbfbf;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #bfbfbf;
  --bs-btn-hover-border-color: #bfbfbf;
  --bs-btn-focus-shadow-rgb: 162, 162, 162;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #bfbfbf;
  --bs-btn-active-border-color: #bfbfbf;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #bfbfbf;
  --bs-btn-disabled-border-color: #bfbfbf;
}

.btn-gray-5 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #747474;
  --bs-btn-border-color: #747474;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #747474;
  --bs-btn-hover-border-color: #747474;
  --bs-btn-focus-shadow-rgb: 137, 137, 137;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #747474;
  --bs-btn-active-border-color: #747474;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #747474;
  --bs-btn-disabled-border-color: #747474;
}

.btn-gray-6 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #484848;
  --bs-btn-border-color: #484848;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #484848;
  --bs-btn-hover-border-color: #484848;
  --bs-btn-focus-shadow-rgb: 99, 99, 99;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #484848;
  --bs-btn-active-border-color: #484848;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #484848;
  --bs-btn-disabled-border-color: #484848;
}

.btn-gray-7 {
  --bs-btn-color: #ffffff;
  --bs-btn-bg: #191919;
  --bs-btn-border-color: #191919;
  --bs-btn-hover-color: #ffffff;
  --bs-btn-hover-bg: #191919;
  --bs-btn-hover-border-color: #191919;
  --bs-btn-focus-shadow-rgb: 60, 60, 60;
  --bs-btn-active-color: #ffffff;
  --bs-btn-active-bg: #191919;
  --bs-btn-active-border-color: #191919;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffffff;
  --bs-btn-disabled-bg: #191919;
  --bs-btn-disabled-border-color: #191919;
}

a.text-gold:hover,
a.text-gold:focus {
  color: #ffc627 !important;
}

a.text-gold:visited:not(.btn):not(.uds-modal-close-btn) {
  color: #daa000 !important;
}

/*--------------------------------------------------------------
0 <USER> <GROUP> breakpoint included
--------------------------------------------------------------*/
@media (max-width: 575.98px) {
  .btn.btn-responsive, .btn-responsive.uds-modal-close-btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}
/*--------------------------------------------------------------
LG and up
--------------------------------------------------------------*/
@media (min-width: 992px) {
  .btn:hover, .uds-modal-close-btn:hover {
    transform: scale(1.05);
  }
  .btn:active, .uds-modal-close-btn:active {
    transform: scale(1);
  }
}
#calendar .calendar-grid {
  margin: 1.5rem 0;
  text-align: left;
  font-weight: 700;
}
#calendar .calendar-grid p {
  margin-bottom: 0;
}
#calendar .calendar-grid .heading {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 0.25rem;
}
#calendar .calendar-grid .heading.mobile {
  display: none;
}
#calendar .calendar-grid .body {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  border-top: 1px solid #747474;
  border-left: 1px solid #747474;
}
#calendar .calendar-grid .body .calendar-item {
  font-size: 1rem;
  padding: 4rem 0.5rem 0.5rem;
  cursor: pointer;
  border-bottom: 1px solid #747474;
  border-right: 1px solid #747474;
  margin: 0;
}
#calendar .calendar-grid .body .today {
  border-radius: 400rem;
  background-color: #8c1d40;
  color: #ffffff;
  padding: 0.25rem 0.5rem;
}
#calendar .calendar-grid .body .prev,
#calendar .calendar-grid .body .next {
  color: #747474;
}
#calendar .calendar-nav {
  text-align: center;
}
#calendar .calendar-nav button {
  border: 1px solid #d0d0d0;
  border-radius: 400rem;
  background-color: #e8e8e8;
  padding: 0.75rem 1.1666666667rem;
}
#calendar .calendar-nav button svg {
  font-size: 1.5rem;
  vertical-align: middle;
  pointer-events: none;
}

/*--------------------------------------------------------------
Mobile
--------------------------------------------------------------*/
@media screen and (max-width: 992px) {
  #calendar .calendar-grid {
    padding-left: 0;
    padding-right: 0;
    text-align: center;
  }
  #calendar .calendar-grid .heading {
    border-bottom: 1px solid #747474;
    padding-bottom: 0.5rem;
  }
  #calendar .calendar-grid .heading.desktop {
    display: none;
  }
  #calendar .calendar-grid .heading.mobile {
    display: grid;
  }
  #calendar .calendar-grid .body {
    border: unset;
  }
  #calendar .calendar-grid .body .calendar-item {
    padding: 1rem 0.5rem 0.5rem;
    border: unset;
  }
}
/*------------------------------------------------------------------
Cards - Table of Contents

1. Basic badge styles
2. Badges within cards
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
1. Basic Cards
--------------------------------------------------------------------*/
.card .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  margin-top: 8px;
  line-height: 1.5;
}

.icon-small {
  font-size: 1.5rem;
}

.icon-base {
  font-size: 2rem;
}

.icon-large {
  font-size: 2.5rem;
}

.icon-xl {
  font-size: 3rem;
}

.icon-xxl {
  font-size: 5rem;
}

.uds-img {
  opacity: 1;
  margin-bottom: 0rem;
  max-width: 900px;
}
.uds-img img {
  border: 1px solid #d0d0d0;
  width: 100%;
}
.uds-img.borderless img {
  border-color: transparent;
}
.uds-img.borderless .uds-figure .uds-figure-caption {
  border-color: transparent;
}
.uds-img .uds-figure {
  width: 100%;
}
.uds-img .uds-figure img.img-original {
  width: initial;
}
.uds-img .uds-figure .uds-figure-caption {
  border: 1px solid #d0d0d0;
  border-top: none;
  opacity: 1;
  background: #ffffff 0% 0% no-repeat padding-box;
  padding: 0.75rem;
  font-size: 0.75rem;
}
.uds-img .uds-figure .uds-caption-text {
  display: block;
  max-width: 700px;
  color: #747474;
}
.uds-img.uds-img-drop-shadow {
  box-shadow: 0rem 0.5rem 1rem rgba(25, 25, 25, 0.2);
}
.uds-img.uds-img-drop-shadow .uds-figure {
  margin-bottom: 0rem;
}

.uds-image-text-block-container {
  width: 100%;
  max-width: 1920px;
  border: 1px solid #d0d0d0;
  background: #ffffff 0% 0% no-repeat padding-box;
  display: flex;
  flex-direction: row;
  margin: 0 auto;
}
.uds-image-text-block-container-right {
  flex-direction: row-reverse;
}
@media (min-width: 768px) {
  .uds-image-text-block-container {
    max-height: 540px;
  }
}
@media screen and (max-width: 576px) {
  .uds-image-text-block-container {
    flex-direction: column;
  }
}
.uds-image-text-block-image-container {
  width: 100%;
  display: flex;
  flex: 1;
}
.uds-image-text-block-image-container img {
  width: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
@media screen and (max-width: 576px) {
  .uds-image-text-block-image-container {
    display: block;
    -o-object-fit: cover;
       object-fit: cover;
    order: 1;
  }
}
.uds-image-text-block-text-container {
  flex: 1;
  /*
  * Set a max width of 50% here because padding/margins are added in addition to the space given for flex, therefore
  * the width of the text container is actually bigger than of the image container otherwise.
  */
  max-width: 50%;
  padding: 48px 96px;
}
.uds-image-text-block-text-container.gray-7-bg {
  color: #ffffff;
}
@media screen and (max-width: 992px) {
  .uds-image-text-block-text-container {
    padding: 48px;
  }
}
@media screen and (max-width: 768px) {
  .uds-image-text-block-text-container {
    padding: 36px;
  }
}
@media screen and (max-width: 576px) {
  .uds-image-text-block-text-container {
    max-width: 100%;
    order: 2;
  }
  .uds-image-text-block-text-container .btn, .uds-image-text-block-text-container .uds-modal-close-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    line-height: 1rem;
  }
}

/*--------------------------------------------------------------
# Content Image Overlap

1. Mobile
2. Desktop lg
3. Desktop xl
--------------------------------------------------------------*/
.uds-image-overlap {
  position: relative;
  padding-top: 1.5rem;
  width: 100%;
}
.uds-image-overlap:before {
  content: "";
  height: 1.5rem;
  width: calc(100% - 4rem);
  background-color: #ffc627;
  position: absolute;
  left: 2rem;
  top: 0;
}
.uds-image-overlap .content-wrapper {
  background-color: #ffffff;
  padding: 32px;
  overflow: hidden;
}
.uds-image-overlap .content-wrapper h1, .uds-image-overlap .content-wrapper .h1, .uds-image-overlap .content-wrapper h2, .uds-image-overlap .content-wrapper .h2, .uds-image-overlap .content-wrapper h3, .uds-image-overlap .content-wrapper .h3, .uds-image-overlap .content-wrapper h4, .uds-image-overlap .content-wrapper .h4, .uds-image-overlap .content-wrapper h5, .uds-image-overlap .content-wrapper .h5 {
  margin-top: 0;
}

/*------------------------------------------------------------------
2. Desktop, large
------------------------------------------------------------------- */
@media (min-width: 992px) {
  .uds-image-overlap {
    max-height: 100%;
    display: grid;
    grid-template-columns: 1.5rem 1fr 20rem 10rem 1.5rem;
    grid-template-rows: 4.5rem 1fr 4.5rem;
    gap: 0px 0px;
  }
  .uds-image-overlap:before {
    display: none;
  }
  .uds-image-overlap img {
    width: 100%;
    height: 100%;
    -o-object-fit: cover;
       object-fit: cover;
    grid-column: 2/span 2;
    grid-row: 1/span 3;
  }
  .uds-image-overlap .content-wrapper {
    padding: 40px;
    grid-column: 3/span 2;
    grid-row: 2/span 1;
  }
  .uds-image-overlap:after {
    content: "";
    width: 1.5rem;
    background-color: #ffc627;
    grid-column: 1/2;
    grid-row: 2/span 1;
  }
  .uds-image-overlap.content-left {
    grid-template-columns: 1.5rem 10rem 20rem 1fr 1.5rem;
  }
  .uds-image-overlap.content-left img {
    grid-column: 3/span 2;
    grid-row: 1/span 3;
  }
  .uds-image-overlap.content-left .content-wrapper {
    grid-column: 2/span 2;
    grid-row: 2/span 1;
  }
  .uds-image-overlap.content-left:after {
    grid-column: 5/6;
    grid-row: 2/span 1;
  }
}
.uds-inset-box-container {
  display: flex;
  justify-content: center;
  padding: 3rem;
  width: 100%;
}
.uds-inset-box-container.image-background {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
@media screen and (max-width: 576px) {
  .uds-inset-box-container {
    padding: 1.5rem;
  }
}
.uds-inset-box-content {
  background-color: #ffffff;
  border: 1px solid #d0d0d0;
  max-width: 1920px;
  padding: 2rem 4rem;
  width: 50%;
}
.uds-inset-box-content .uds-inset-box-buttons {
  margin-bottom: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}
.uds-inset-box-content .uds-inset-box-buttons .btn, .uds-inset-box-content .uds-inset-box-buttons .uds-modal-close-btn {
  display: inline;
  padding-right: 0;
  font-size: 0.75rem;
  padding: 0.25rem 0.75rem;
  line-height: 1.5;
  margin: 0;
}
@media screen and (max-width: 992px) {
  .uds-inset-box-content {
    padding: 2rem;
  }
}
@media screen and (max-width: 576px) {
  .uds-inset-box-content {
    padding: 1rem;
    width: 100%;
  }
  .uds-inset-box-content .btn, .uds-inset-box-content .uds-modal-close-btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    line-height: 1rem;
  }
}

/*------------------------------------------------------------------
Cards - Table of Contents

1. Basic Cards
2. Degree Cards
3. Story Cards
4. Event Cards
5. Foldable Cards
6. Foldable - Desktop Disable
7. Accordion
8. Checkbox Stacked Cards
9. Checkbox Inline Cards
10. Checkbox Inline Cards
-------------------------------------------------------------------*/
.row-spaced {
  margin-bottom: -32px;
}

.row-spaced > .col,
.row-spaced > div {
  margin-bottom: 32px;
}

/*------------------------------------------------------------------
1. Basic Cards
--------------------------------------------------------------------*/
.card {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.card.borderless {
  border-color: transparent;
}

.card-hover:hover {
  transform: scale(1.05);
  cursor: pointer;
  box-shadow: 0px 8px 16px rgba(25, 25, 25, 0.2);
}

.card-hover:active {
  transform: scale(0.95);
  cursor: pointer;
}

.card-hover:focus {
  outline: 0;
  box-shadow: 0 0 8px #00baff !important;
}

.card-hover > button {
  border: none;
  background: transparent;
  text-align: inherit;
  margin: 0;
  padding: 0;
}

.card-hover > button:focus {
  outline: 0;
  box-shadow: 0 0 8px #00baff !important;
  border: 0;
}

.card-icon-top {
  width: 2rem;
  height: 2rem;
  margin: 2rem 2rem 0 2rem;
}

.card-image-content {
  position: relative;
}

.card-image-gradient {
  max-width: 100%;
  height: 100%;
  width: 100%;
  margin: auto;
  position: relative;
}

.card-image-gradient::after {
  display: block;
  position: absolute;
  background: transparent linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.7882352941) 120%) 0% 0% no-repeat padding-box;
  height: 100%;
  width: 100%;
  top: 0;
  content: "";
}

.card-img-top img,
.card-img-top {
  max-width: 100%;
  height: 12.5rem;
  -o-object-fit: cover;
     object-fit: cover;
}

.card-header {
  padding: 24px 32px 16px 32px;
  flex-grow: 1;
}
.card-header .card-icon {
  margin-bottom: 1rem;
}

.card-body, .accordion-body {
  padding: 0 32px 24px 32px;
  flex-grow: 100;
}

.card:not(.card.card-foldable) > div:first-of-type:not(.card-image-content) {
  flex-grow: 1;
}

.card-link {
  padding: 0 32px 24px 32px;
}

.card-footer {
  padding: 0 32px 24px 32px;
  border-top: 0;
}

.card-tags {
  padding: 0 32px 24px 32px;
  border-top: 0;
  margin-top: -8px;
}
.card-tags .btn-tag,
.card-tags .badge {
  margin-top: 8px;
  margin-left: 0;
  margin-right: 1rem;
}

.card-button {
  margin-top: auto;
  padding: 0 32px 24px 32px;
  display: flex;
  flex-wrap: wrap;
  row-gap: 1rem;
  align-items: center;
}

.card > div:last-child {
  padding-bottom: 32px;
}

@media (max-width: 767.98px) {
  .card-icon-top {
    width: 2rem;
    height: 2rem;
    margin: 2rem 2rem 0 2rem;
  }
  .card-img-top img,
  .card-img-top {
    height: 160px;
  }
  .card-header {
    padding: 24px 24px 16px 24px;
  }
  .card-body {
    padding: 0 24px 24px 24px;
  }
  .card-link {
    padding: 0 24px 24px 24px;
  }
  .card-footer {
    padding: 0 24px 24px 24px;
    border-top: 0;
  }
  .card-tags {
    padding: 0 24px 24px 24px;
    border-top: 0;
  }
  .card-button {
    margin-top: auto;
    padding: 0 24px 24px 24px;
  }
}
@media (max-width: 767.98px) and (max-width: 767.98px) {
  .card-buttons {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }
  .card-buttons .card-button {
    display: inline;
    padding-right: 0;
  }
}
@media (max-width: 767.98px) {
  .card > div:last-child {
    padding-bottom: 24px;
  }
}
@media (min-width: 992px) {
  .col-lg-6 .card .card-img-top img,
  .col-lg-6 .card .card-img-top {
    height: 240px;
  }
  .col-lg-6 .card-horizontal .card-img-top {
    height: auto;
    max-width: 40%;
  }
}
.card-sm .card-body {
  padding-right: 24px;
  padding-left: 24px;
}
.card-sm .card-header {
  padding: 24px 24px 16px 24px;
}
.card-sm .card > div:first-of-type {
  padding-top: 24px;
  flex-grow: 1;
}
.card-sm .card-image-gradient::after {
  height: 160px;
}
.card-sm .card-img-top img,
.card-sm .card-img-top {
  height: 160px;
}
.card-sm .card-footer-link {
  padding-right: 24px;
  padding-left: 24px;
}
.card-sm .card-button {
  padding-right: 24px;
  padding-left: 24px;
}
.card-sm .card-link {
  padding-right: 24px;
  padding-left: 24px;
}
.card-sm .card-tags {
  padding-right: 24px;
  padding-left: 24px;
}
.card-sm .card-event-details {
  padding-right: 24px;
  padding-left: 24px;
}

.card-lg .card-img-top img,
.card-lg .card-img-top {
  height: 15rem;
}
.card-lg .card-image-gradient::after {
  height: 15rem;
}

.card-header .card-title {
  margin: 0;
}

.card-title a,
.card-title a:visited {
  color: #191919;
  text-decoration: none;
}

.card-title a:hover {
  text-decoration: underline;
}

/*------------------------------------------------------------------
2. Degree Cards
--------------------------------------------------------------------*/
.card-degree .card-header .card-title:after {
  content: "";
  width: 2rem;
  height: 0.25rem;
  display: block;
  background-color: #ffc627;
  margin-top: 1rem;
}

.card-degree .card-footer {
  background-color: #ffffff;
}

.card-footer-link a,
.card-footer-link a:visited {
  color: #191919;
  text-decoration: none;
  font-weight: bold;
  display: block;
}

.card-footer-link a:hover {
  text-decoration: underline;
}

.card-footer-link a:after {
  float: right;
  height: 20px;
  width: 20px;
  content: url("data:image/svg+xml; utf8, <svg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='arrow-right' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' class='svg-inline--fa fa-arrow-right fa-w-14 fa-2x'><path fill='currentColor' d='M190.5 66.9l22.2-22.2c9.4-9.4 24.6-9.4 33.9 0L441 239c9.4 9.4 9.4 24.6 0 33.9L246.6 467.3c-9.4 9.4-24.6 9.4-33.9 0l-22.2-22.2c-9.5-9.5-9.3-25 .4-34.3L311.4 296H24c-13.3 0-24-10.7-24-24v-32c0-13.3 10.7-24 24-24h287.4L190.9 101.2c-9.8-9.3-10-24.8-.4-34.3z'></path></svg>");
}

/*------------------------------------------------------------------
3. Story Cards
--------------------------------------------------------------------*/
.card-story .card-header,
.card-story .card-body,
.card-story .card-button,
.card-story .card-footer,
.card-story .card-tags,
.card-story .card-footer,
.card-story .card-link {
  margin-left: 24px;
  margin-right: 24px;
  background-color: #ffffff;
  padding-left: 16px;
  padding-right: 16px;
}

.card-story:not(.card.card-foldable) > div:first-of-type:not(.card-image-content) {
  padding-top: 16px;
}

.card-story > div:first-of-type:not(.card-image-content) {
  padding: 16px;
  flex-grow: 1;
}

.card-story .card-footer {
  padding: 1rem 2rem 1rem 2rem;
  padding-left: 24px;
  padding-right: 24px;
}

.card-story .card-img-top {
  margin-bottom: -48px;
}

.card-story.card-sm .card-body {
  margin: 0 1rem 0 1rem;
}
.card-story.card-sm .card-header {
  margin: 0 1rem 0 1rem;
}
.card-story.card-sm .card-button {
  margin: 0 1rem 0 1rem;
}

.card-story.card-horizontal .card-img-top {
  margin-right: -32px;
  position: relative;
  z-index: 0;
}

.card-story.card-horizontal .card-content-wrapper {
  margin-top: 16px;
  margin-bottom: 16px;
  position: relative;
  z-index: 100;
  background-color: white;
}

.card-story.card-horizontal .card-header,
.card-story.card-horizontal .card-body,
.card-story.card-horizontal .card-button,
.card-story.card-horizontal .card-footer,
.card-story.card-horizontal .card-tags,
.card-story.card-horizontal .card-footer,
.card-story.card-horizontal .card-link {
  margin-left: 0;
  margin-right: 0;
  background-color: #ffffff;
  padding-left: 24px;
  padding-right: 24px;
}

/*------------------------------------------------------------------
4. Event Cards
--------------------------------------------------------------------*/
.card-event .card-header {
  border-top: solid 8px #ffc627;
}

.card-event-details {
  display: flex;
  flex-grow: 100000;
  padding: 0 2rem 2rem 2rem;
  padding: 0 32px 24px 32px;
  font-size: 14px;
}
.card-event-details > * {
  flex: 50%;
}

.card-event-details + .card-event-details {
  flex-grow: 999900000;
}

.card-event-details > div:first-child {
  margin-right: 16px;
}

.card-event-icons {
  display: flex;
}

.card-event-icons > div:first-child {
  width: 16px;
  margin-right: 8px;
}

.card-horizontal {
  flex-direction: row;
}

@media (max-width: 767.98px) {
  .card-horizontal {
    flex-direction: column;
  }
  .card-horizontal .card-img-top {
    height: 160px !important;
    max-width: 100% !important;
    width: 100% !important;
  }
  .card-event.card-horizontal .card-content-wrapper {
    border-top: solid 8px #ffc627;
    border-left: 0 !important;
  }
  .card-story.card-horizontal .card-content-wrapper {
    margin-right: 16px;
    margin-left: 16px;
    margin-top: -24px;
    margin-bottom: 0;
  }
}
.card-content-wrapper {
  padding-bottom: 0 !important;
}

.card-story.card-horizontal .card-img-top {
  margin-bottom: 0;
}

.card-horizontal .card-img-top {
  height: auto;
  max-width: 40%;
}

.card-event.card-horizontal .card-header {
  border-top: 0;
}

.card-event.card-horizontal .card-content-wrapper {
  border-left: solid 8px #ffc627;
}

/*------------------------------------------------------------------
5. Foldable Cards
--------------------------------------------------------------------*/
.accordion-item {
  border-color: #d0d0d0;
  border-left: 0.5rem solid #ffc627;
  height: auto;
}
.accordion-item.accordion-item-maroon {
  border-left-color: #8c1d40;
}
.accordion-item.accordion-item-gray {
  border-left-color: #bfbfbf;
}
.accordion-item.accordion-item-dark {
  border-left-color: #191919;
}
.accordion-item .accordion-header {
  padding: 0.5rem;
  overflow: hidden;
}
.accordion-item .accordion-header.accordion-header-icon .accordion-icon {
  display: flex;
  margin-left: -0.75rem;
  align-items: flex-start;
}
.accordion-item .accordion-header.accordion-header-icon .accordion-icon svg {
  margin-top: 2px;
}
.accordion-item .accordion-header:hover {
  background-color: #e8e8e8;
}
.accordion-item .accordion-header h3, .accordion-item .accordion-header .h3 {
  font-size: 1.25rem;
}
.accordion-item .accordion-header h3, .accordion-item .accordion-header .h3,
.accordion-item .accordion-header h4,
.accordion-item .accordion-header .h4,
.accordion-item .accordion-header h5,
.accordion-item .accordion-header .h5 {
  margin: 0;
}
.accordion-item .accordion-header h3 a, .accordion-item .accordion-header .h3 a,
.accordion-item .accordion-header h4 a,
.accordion-item .accordion-header .h4 a,
.accordion-item .accordion-header h5 a,
.accordion-item .accordion-header .h5 a {
  padding: 0.5rem 1.5rem;
  color: #191919;
  text-decoration: none;
  display: flex;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
}
.accordion-item .accordion-header h3 a svg.fa-chevron-up, .accordion-item .accordion-header .h3 a svg.fa-chevron-up,
.accordion-item .accordion-header h4 a svg.fa-chevron-up,
.accordion-item .accordion-header .h4 a svg.fa-chevron-up,
.accordion-item .accordion-header h5 a svg.fa-chevron-up,
.accordion-item .accordion-header .h5 a svg.fa-chevron-up {
  margin-left: 1.5rem;
  transition-duration: 0.4s;
  align-self: flex-start;
}
.accordion-item .accordion-header h3 a.collapsed svg.fa-chevron-up, .accordion-item .accordion-header .h3 a.collapsed svg.fa-chevron-up, .accordion-item .accordion-header h3 a.collapsed i.fa-chevron-up, .accordion-item .accordion-header .h3 a.collapsed i.fa-chevron-up,
.accordion-item .accordion-header h4 a.collapsed svg.fa-chevron-up,
.accordion-item .accordion-header .h4 a.collapsed svg.fa-chevron-up,
.accordion-item .accordion-header h4 a.collapsed i.fa-chevron-up,
.accordion-item .accordion-header .h4 a.collapsed i.fa-chevron-up,
.accordion-item .accordion-header h5 a.collapsed svg.fa-chevron-up,
.accordion-item .accordion-header .h5 a.collapsed svg.fa-chevron-up,
.accordion-item .accordion-header h5 a.collapsed i.fa-chevron-up,
.accordion-item .accordion-header .h5 a.collapsed i.fa-chevron-up {
  transform: rotate(180deg);
}
.accordion-item .accordion-header + .accordion-body {
  border-top: 1px solid #d0d0d0;
}
.accordion-item .accordion-body {
  background-color: #fafafa;
}
.accordion-item .accordion-body > p:first-child {
  margin-top: 1rem;
}
.accordion-item .accordion-body > p:last-child {
  margin-bottom: 0;
}

/*------------------------------------------------------------------
6. Foldable - Desktop Disable
--------------------------------------------------------------------*/
@media (min-width: 1260px) {
  .accordion-item.desktop-disable-xl {
    border-left: 1px solid #d0d0d0;
  }
  .accordion-item.desktop-disable-xl .accordion-header h4 a, .accordion-item.desktop-disable-xl .accordion-header .h4 a {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .accordion-item.desktop-disable-xl .accordion-header h4 a:hover, .accordion-item.desktop-disable-xl .accordion-header .h4 a:hover {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-xl .accordion-header svg {
    display: none;
  }
  .accordion-item.desktop-disable-xl .accordion-header span {
    display: none;
  }
  .accordion-item.desktop-disable-xl .accordion-header + .accordion-body {
    border-top: 0;
  }
  .accordion-item.desktop-disable-xl .accordion-body {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-xl .accordion-body > p:first-child {
    margin-top: 0;
  }
  .accordion-item.desktop-disable-xl .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .accordion-item.desktop-disable-xl .collapse:not(.show) {
    display: block;
  }
  .accordion-item.desktop-disable-xl .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
@media (min-width: 992px) {
  .accordion-item.desktop-disable-lg {
    border-left: 1px solid #d0d0d0;
  }
  .accordion-item.desktop-disable-lg .accordion-header h4 a, .accordion-item.desktop-disable-lg .accordion-header .h4 a {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .accordion-item.desktop-disable-lg .accordion-header h4 a:hover, .accordion-item.desktop-disable-lg .accordion-header .h4 a:hover {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-lg .accordion-header svg {
    display: none;
  }
  .accordion-item.desktop-disable-lg .accordion-header span {
    display: none;
  }
  .accordion-item.desktop-disable-lg .accordion-header + .accordion-body {
    border-top: 0;
  }
  .accordion-item.desktop-disable-lg .accordion-body {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-lg .accordion-body > p:first-child {
    margin-top: 0;
  }
  .accordion-item.desktop-disable-lg .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .accordion-item.desktop-disable-lg .collapse:not(.show) {
    display: block;
  }
  .accordion-item.desktop-disable-lg .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
@media (min-width: 768px) {
  .accordion-item.desktop-disable-md {
    border-left: 1px solid #d0d0d0;
  }
  .accordion-item.desktop-disable-md .accordion-header h4 a, .accordion-item.desktop-disable-md .accordion-header .h4 a {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .accordion-item.desktop-disable-md .accordion-header h4 a:hover, .accordion-item.desktop-disable-md .accordion-header .h4 a:hover {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-md .accordion-header svg {
    display: none;
  }
  .accordion-item.desktop-disable-md .accordion-header span {
    display: none;
  }
  .accordion-item.desktop-disable-md .accordion-header + .accordion-body {
    border-top: 0;
  }
  .accordion-item.desktop-disable-md .accordion-body {
    background-color: transparent;
  }
  .accordion-item.desktop-disable-md .accordion-body > p:first-child {
    margin-top: 0;
  }
  .accordion-item.desktop-disable-md .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .accordion-item.desktop-disable-md .collapse:not(.show) {
    display: block;
  }
  .accordion-item.desktop-disable-md .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
/*------------------------------------------------------------------
7. Accordion
--------------------------------------------------------------------*/
.accordion .accordion-item {
  border-bottom: 1px solid #d0d0d0;
  border-top: 1px solid #d0d0d0;
}
.accordion .accordion-header .accordion-icon {
  display: flex;
  align-items: center;
  margin-bottom: 0;
}

/*------------------------------------------------------------------
8. Checkbox Stacked Cards
--------------------------------------------------------------------*/
.card-checkbox-stacked .card-header {
  position: relative;
}

.card-checkbox-stacked .card-footer {
  background-color: #ffffff;
}

/*------------------------------------------------------------------
9. Checkbox Inline Cards
--------------------------------------------------------------------*/
.card-checkbox-inline .card-header {
  position: relative;
}

.card-checkbox-inline .card-footer {
  background-color: #ffffff;
}

/*------------------------------------------------------------------
10. Checkbox Inline Cards
--------------------------------------------------------------------*/
.media-type-overlay {
  background-color: white;
  border: 1px solid #d0d0d0;
  border-radius: 32px;
  width: 64px;
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/*------------------------------------------------------------------
11. Generic Header Image Overlay
--------------------------------------------------------------------*/
.card-image-overlay-content {
  position: absolute;
  bottom: 0;
  color: #ffffff;
}
.card-image-overlay-content .fa-video {
  color: #191919;
  font-size: 2rem;
}

.icon-example {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

ul.uds-list {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
}
ul.uds-list li {
  margin-bottom: 1rem;
}
ul.uds-list li:last-of-type {
  margin-bottom: 0rem;
}
ul.uds-list li:before,
ul.uds-list ul ul li:before,
ul.uds-list ul ul ul ul li:before,
ul.uds-list ul ul ul ul ul ul li:before,
ul.uds-list ul ul ul ul ul ul ul ul li:before,
ul.uds-list ul ul ul ul ul ul ul ul ul ul li:before {
  content: "•";
  font-size: 2rem;
  vertical-align: middle;
  line-height: 1.5rem;
  padding-right: 1.25rem;
  margin-left: -2rem;
}
ul.uds-list ul li:before,
ul.uds-list ul ul ul li:before,
ul.uds-list ul ul ul ul ul li:before,
ul.uds-list ul ul ul ul ul ul ul li:before,
ul.uds-list ul ul ul ul ul ul ul ul ul li:before {
  content: "◦";
}
ul.uds-list ol,
ul.uds-list ul {
  padding: 1rem 1.5rem 0rem;
}

ul.uds-list.maroon li:before,
ol.uds-list.maroon li:before {
  color: #8c1d40;
}
ul.uds-list.darkmode,
ol.uds-list.darkmode {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
  margin-left: 0rem;
  margin-bottom: 0rem;
  background-color: #191919;
  color: #e8e8e8;
}
ul.uds-list.darkmode li,
ol.uds-list.darkmode li {
  margin-bottom: 1rem;
}
ul.uds-list.darkmode li:last-of-type,
ol.uds-list.darkmode li:last-of-type {
  margin-bottom: 0rem;
}
ul.uds-list.darkmode li:before,
ol.uds-list.darkmode li:before {
  color: #e8e8e8;
}
ul.uds-list.darkmode.gold li:before,
ol.uds-list.darkmode.gold li:before {
  color: #ffc627;
}
ul.uds-list.darkmode.gold li .fa-li,
ol.uds-list.darkmode.gold li .fa-li {
  color: #ffc627;
}
ul.uds-list.darkmode.uds-steplist li:before,
ol.uds-list.darkmode.uds-steplist li:before {
  background-color: #e8e8e8;
  color: #191919;
}
ul.uds-list.smokemode,
ol.uds-list.smokemode {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
  margin-left: 0rem;
  margin-bottom: 0rem;
  background-color: #e8e8e8;
}
ul.uds-list.smokemode li,
ol.uds-list.smokemode li {
  margin-bottom: 1rem;
}
ul.uds-list.smokemode li:last-of-type,
ol.uds-list.smokemode li:last-of-type {
  margin-bottom: 0rem;
}
ul.uds-list.light-smokemode,
ol.uds-list.light-smokemode {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
  margin-left: 0rem;
  margin-bottom: 0rem;
  background-color: #fafafa;
}
ul.uds-list.light-smokemode li,
ol.uds-list.light-smokemode li {
  margin-bottom: 1rem;
}
ul.uds-list.light-smokemode li:last-of-type,
ol.uds-list.light-smokemode li:last-of-type {
  margin-bottom: 0rem;
}

ul.uds-list.fa-ul {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
  margin-left: 0rem;
  margin-bottom: 0rem;
  padding-left: 2.25rem;
}
ul.uds-list.fa-ul li {
  margin-bottom: 1rem;
}
ul.uds-list.fa-ul li:last-of-type {
  margin-bottom: 0rem;
}
ul.uds-list.fa-ul li .fa-li {
  left: -2.5rem;
}
ul.uds-list.fa-ul li:before {
  content: none;
  font-size: 2rem;
  vertical-align: middle;
  line-height: 1.5rem;
  padding-right: 1rem;
  margin-left: -1.5rem;
}
ul.uds-list.fa-ul.maroon li .fa-li {
  color: #8c1d40;
}

ol.uds-list {
  max-width: 43.75rem;
  padding: 0 0 3rem 2rem;
  list-style: none;
  padding-left: 3rem;
  counter-reset: listcounter;
}
ol.uds-list li {
  margin-bottom: 1rem;
}
ol.uds-list li:last-of-type {
  margin-bottom: 0rem;
}
ol.uds-list.darkmode {
  padding-left: 3rem;
}
ol.uds-list.smokemode {
  padding-left: 3rem;
}
ol.uds-list.light-smokemode {
  padding-left: 3rem;
}
ol.uds-list li ol {
  padding: 1rem 1.5rem 0rem;
}
ol.uds-list li:before {
  line-height: 1.5rem;
  padding-right: 1rem;
  margin-left: -1.9rem;
}
ol.uds-list li:before,
ol.uds-list ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol ol ol ol li:before {
  content: counter(listcounter) ". ";
  counter-increment: listcounter;
}
ol.uds-list ol li:before,
ol.uds-list ol ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol ol ol ol ol li:before {
  content: counter(listcounter, lower-alpha) ". ";
  counter-increment: listcounter;
}
ol.uds-list ol ol li:before,
ol.uds-list ol ol ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol ol ol li:before,
ol.uds-list ol ol ol ol ol ol ol ol ol ol ol li:before {
  content: counter(listcounter, lower-roman) ". ";
  counter-increment: listcounter;
}
ol.uds-list li:nth-of-type(9) ~ li:before {
  margin-left: -2.4rem;
}
ol.uds-list li:nth-of-type(99) ~ li:before {
  margin-left: -2.9rem;
}
ol.uds-list.uds-steplist {
  padding-left: 1.5rem;
  max-width: 75ch;
  padding-right: 0rem;
}
ol.uds-list.uds-steplist li {
  padding-bottom: 2rem;
  padding-left: 3rem;
  margin-bottom: 3rem;
  border-bottom: 1px solid #bfbfbf;
  font-weight: bold;
}
ol.uds-list.uds-steplist li span {
  padding-left: 0;
  display: block;
  margin-top: 1rem;
  font-weight: normal;
}
ol.uds-list.uds-steplist li:before {
  border-radius: 50rem;
  background-color: #191919;
  color: #fafafa;
  padding: 0.5rem 0.8rem;
  margin-right: 2rem;
  margin-left: -4.5rem;
  font-size: 1.25rem;
  font-weight: bold;
  content: counter(listcounter);
}
ol.uds-list.uds-steplist.uds-steplist-gold li:before {
  background-color: #ffc627;
  color: #191919;
}
ol.uds-list.uds-steplist.uds-steplist-maroon li:before {
  background-color: #8c1d40;
}
/* 
h1,
.h1,
h2,
h3,
h4,
h5,
.h2,
.h3,
.h4,
.h5 {
  font-weight: 700;
  text-align: left;
  opacity: 1;
  margin: 1rem 0;
  line-height: calc(100% + 0.12em);
}

p + h1,
p + h2,
p + h3,
p + h4,
p + h5,
p + h6,
p + .h1,
p + .h2,
p + .h3,
p + .h4,
p + .h5,
p + .h6 {
  margin-top: 2rem;
}

p:last-of-type + h1,
p:last-of-type + h2,
p:last-of-type + h3,
p:last-of-type + h4,
p:last-of-type + h5,
p:last-of-type + h6,
p:last-of-type + .h1,
p:last-of-type + .h2,
p:last-of-type + .h3,
p:last-of-type + .h4,
p:last-of-type + .h5,
p:last-of-type + .h6 {
  margin-top: 1rem;
}

h1, .h1 {
  font-size: 4rem;
  line-height: 4.25rem;
  letter-spacing: -0.14rem;
}
h1.article, .h1.article {
  font-size: 3rem;
  line-height: 3.25rem;
  letter-spacing: -0.105rem;
}
@media (max-width: 577px) {
  h1, .h1, h1.article, .h1.article {
    font-size: 2.25rem;
    line-height: 2.5rem;
    letter-spacing: -0.078rem;
  }
}

h2, .h2 {
  font-size: 2.5rem;
  line-height: 2.75rem;
  letter-spacing: -0.0875rem;
}
@media (max-width: 577px) {
  h2, .h2 {
    font-size: 2rem;
    line-height: 2.125rem;
    letter-spacing: -0.07rem;
  }
}

h3, .h3 {
  font-size: 1.5rem;
  line-height: 1.75rem;
  letter-spacing: -0.0525rem;
}

h4, .h4 {
  font-size: 1.25rem;
  line-height: 1.625rem;
  letter-spacing: -0.01875rem;
}

h5, .h5 {
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: -0.015rem;
}

h1 span[class^=highlight-],
.h1 span[class^=highlight-],
h2 span[class^=highlight-],
.h2 span[class^=highlight-],
h3 span[class^=highlight-],
.h3 span[class^=highlight-],
h4 span[class^=highlight-],
.h4 span[class^=highlight-] {
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

h1 span.highlight-gold,
.h1 span.highlight-gold,
h2 span.highlight-gold,
.h2 span.highlight-gold {
  box-shadow: -0.15em 0 0 #ffc627, 0.15em 0 0 #ffc627;
  background: #ffc627;
  color: #191919;
}
h1 span.highlight-black,
.h1 span.highlight-black,
h2 span.highlight-black,
.h2 span.highlight-black {
  box-shadow: -0.15em 0 0 #191919, 0.15em 0 0 #191919;
  background: #191919;
  color: #fafafa;
}
h1 span.highlight-white,
.h1 span.highlight-white,
h2 span.highlight-white,
.h2 span.highlight-white {
  box-shadow: -0.15em 0 0 #ffffff, 0.15em 0 0 #ffffff;
  background: #ffffff;
  color: #191919;
}

h3 span.highlight-gold,
.h3 span.highlight-gold,
h4 span.highlight-gold,
.h4 span.highlight-gold {
  box-shadow: -0.15em 0 0 #ffc627, 0.15em 0 0 #ffc627;
  background: #ffc627;
  color: #191919;
}
h3 span.highlight-black,
.h3 span.highlight-black,
h4 span.highlight-black,
.h4 span.highlight-black {
  box-shadow: -0.15em 0 0 #191919, 0.15em 0 0 #191919;
  background: #191919;
  color: #fafafa;
}
h3 span.highlight-white,
.h3 span.highlight-white,
h4 span.highlight-white,
.h4 span.highlight-white {
  box-shadow: -0.15em 0 0 #ffffff, 0.15em 0 0 #ffffff;
  background: #ffffff;
  color: #191919;
}

hr {
  border-top: 0;
  height: 1px;
  margin: 3rem 0;
  background-color: #d0d0d0;
  opacity: 1;
} 
*/
hr.copy-divider {
  height: 0.5rem;
  background-color: #ffc627;
  max-width: 16rem;
}

a.page-link,
a.page-link:visited {
  border-radius: 400rem;
  text-decoration: none;
  font-weight: bold;
  color: #191919;
  transition: 0.1s ease-out;
  font-size: 14px;
}

a.page-link:hover {
  color: #191919;
  text-decoration: none;
}

span.page-link {
  padding-left: 0;
  padding-right: 0;
}
span.page-link:hover {
  background-color: inherit;
  color: inherit;
}

.page-item:last-child .page-link {
  border-radius: 400rem;
}

.page-item:first-child .page-link {
  border-radius: 400rem;
}

.page-item:last-child .page-link-icon:after {
  display: inline-block;
  font-size: inherit;
  content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='currentColor' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'></path></svg>");
  transform: rotate(270deg);
  float: right;
  height: 1rem;
  width: 1rem;
  margin-left: 0.25rem;
}

.page-item:first-child .page-link-icon:before {
  display: inline-block;
  font-size: inherit;
  content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='currentColor' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'></path></svg>");
  transform: rotate(90deg);
  float: left;
  height: 1rem;
  width: 1rem;
  margin-right: 0.25rem;
}

.disabled .page-link-icon:before,
.disabled .page-link-icon:after {
  opacity: 0.5;
}

.pagination {
  overflow-x: auto;
}
.pagination.uds-bg-gray1 {
  background-color: #fafafa;
}
.pagination.uds-bg-gray1 :not(.active) .page-link {
  background-color: #fafafa;
}
.pagination.uds-bg-gray1 :not(.active) .page-link:hover {
  background-color: #bfbfbf;
  color: #191919;
}
.pagination.uds-bg-gray {
  background-color: #e8e8e8;
}
.pagination.uds-bg-gray :not(.active) .page-link {
  background-color: #e8e8e8;
}
.pagination.uds-bg-gray :not(.active) .page-link:hover {
  background-color: #bfbfbf;
  color: #191919;
}
.pagination.uds-bg-dark {
  background-color: #191919;
}
.pagination.uds-bg-dark .page-item:last-child .page-link-icon::after {
  content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='%23fafafa' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'></path></svg>");
}
.pagination.uds-bg-dark .page-item:first-child .page-link-icon::before {
  content: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='%23fafafa' d='M207.029 381.476L12.686 187.132c-9.373-9.373-9.373-24.569 0-33.941l22.667-22.667c9.357-9.357 24.522-9.375 33.901-.04L224 284.505l154.745-154.021c9.379-9.335 24.544-9.317 33.901.04l22.667 22.667c9.373 9.373 9.373 24.569 0 33.941L240.971 381.476c-9.373 9.372-24.569 9.372-33.942 0z'></path></svg>");
}
.pagination.uds-bg-dark .page-link {
  color: #fafafa;
}
.pagination.uds-bg-dark .active .page-link {
  background-color: #ffc627;
  color: #191919;
}
.pagination.uds-bg-dark :not(.active) .page-link {
  background-color: #191919;
}
.pagination.uds-bg-dark :not(.active) .page-link:hover {
  background-color: #747474;
  color: #fafafa;
}

@media (min-width: 768px) {
  a.page-link {
    font-size: 16px;
  }
}
@media (max-width: 767.98px) {
  .page-item {
    margin: 0 0.2rem;
  }
  a.page-link {
    font-size: 14px;
  }
}
.uds-tabbed-panels {
  box-shadow: inset 0px -2px 0px 0px #bfbfbf;
  flex-wrap: inherit;
  overflow: hidden;
  position: relative;
  -webkit-overflow-scrolling: touch;
  white-space: nowrap;
}
.uds-tabbed-panels .scroll::-webkit-scrollbar {
  display: none;
}
@media screen and (min-width: 992px) {
  .uds-tabbed-panels:hover span.carousel-control-prev-icon, .uds-tabbed-panels:hover span.carousel-control-next-icon {
    opacity: 1;
  }
}
.uds-tabbed-panels .nav-tabs {
  display: flex;
  flex-wrap: inherit;
  left: 0px;
  position: relative;
  transition: all 0.25s ease 0s;
  white-space: nowrap;
  overflow-x: auto;
  scroll-behavior: smooth;
  -ms-overflow-style: none;
  scrollbar-width: none;
}
.uds-tabbed-panels .nav-tabs::-webkit-scrollbar {
  display: none;
}
.uds-tabbed-panels .nav-tabs .nav-link {
  color: #000;
  padding: 0.2em 0.8em;
  border-right: 0;
  border-left: 0;
  border-top: 0;
  text-decoration: none;
  font-weight: bold;
  font-size: 24px;
  margin: 0.2em 0.2em 0 0.2em;
}
.uds-tabbed-panels .nav-tabs .nav-link.active {
  background-color: transparent;
  border-bottom: 8px solid #8c1d40;
  color: var(--bs-nav-tabs-link-active-color);
}
.uds-tabbed-panels .nav-tabs .nav-link:hover,
.uds-tabbed-panels .nav-tabs .nav-link:focus {
  color: #8c1d40;
}
.uds-tabbed-panels-dark .nav-tabs .nav-link {
  color: #fafafa;
}
.uds-tabbed-panels-dark .nav-tabs .nav-link.active {
  color: #ffc627;
  background-color: transparent;
  border-bottom: 8px solid #ffc627;
}
.uds-tabbed-panels-dark .nav-tabs .nav-link:hover,
.uds-tabbed-panels-dark .nav-tabs .nav-link:focus {
  color: #ffc627;
}
@media screen and (min-width: 992px) {
  .uds-tabbed-panels-dark:hover span.carousel-control-prev-icon, .uds-tabbed-panels-dark:hover span.carousel-control-next-icon {
    opacity: 1;
  }
}

.scroll-control-prev {
  outline: none;
  border: none;
  width: 80px;
  position: absolute;
  height: 100%;
  top: 0;
  background: linear-gradient(90deg, rgba(25, 25, 25, 0.25) 0%, rgba(25, 25, 25, 0) 100%);
  left: 0;
}
.scroll-control-prev span.carousel-control-prev-icon {
  margin: 0 42px 0 12px;
}
.scroll-control-next {
  width: 80px;
  position: absolute;
  height: 100%;
  top: 0;
  right: 0;
  background: linear-gradient(90deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.25) 100%);
}
.scroll-control-next span.carousel-control-next-icon {
  margin: 0 12px 0 42px;
}
.scroll-control-prev span.carousel-control-prev-icon, .scroll-control-next span.carousel-control-next-icon {
  background-size: 60% 60%;
  display: block;
  opacity: 0;
  padding: 12px;
  position: relative;
  top: 50%;
  left: 0;
  transform: translate(0, -50%);
  background-color: #fafafa;
  border: solid 1px #d0d0d0;
  border-radius: 100%;
  color: #000;
}
@media screen and (max-width: 768px) {
  .scroll-control-prev, .scroll-control-next {
    width: 48px;
  }
}

.tab-content {
  overflow-x: hidden;
  /* word-break: break-word; */
  padding-top: 2rem;
  padding-bottom: 2rem;
  margin-bottom: 2rem;
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='currentColor' d='M285.476 272.971L91.132 467.314c-9.373 9.373-24.569 9.373-33.941 0l-22.667-22.667c-9.357-9.357-9.375-24.522-.04-33.901L188.505 256 34.484 101.255c-9.335-9.379-9.317-24.544.04-33.901l22.667-22.667c9.373-9.373 24.569-9.373 33.941 0L285.475 239.03c9.373 9.372 9.373 24.568.001 33.941z'></path></svg>");
  background-position: 80% 50%;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml; utf8, <svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 448 512' data-fa-i2svg=''><path fill='currentColor' d='M34.52 239.03L228.87 44.69c9.37-9.37 24.57-9.37 33.94 0l22.67 22.67c9.36 9.36 9.37 24.52.04 33.9L131.49 256l154.02 154.75c9.34 9.38 9.32 24.54-.04 33.9l-22.67 22.67c-9.37 9.37-24.57 9.37-33.94 0L34.52 272.97c-9.37-9.37-9.37-24.57 0-33.94z'></path></svg>");
  background-position: 60% 50%;
}

/*--------------------------------------------------------------
# Heroes

0. Local variables
1. Mixins
2. Hero, Mobile first
3. Hero, Tablet
4. Hero, Desktop
5. Story Hero, Mobile
6. Story Hero, Desktop
7. Hero, Degree Pages
--------------------------------------------------------------*/
/*--------------------------------------------------------------
0. Local variables
--------------------------------------------------------------*/
/*--------------------------------------------------------------
1. Mixins
--------------------------------------------------------------*/
/*--------------------------------------------------------------
2. Hero, Mobile first
--------------------------------------------------------------*/
@media (max-width: 575.98px) {
  div[class^=uds-hero] .btn, div[class^=uds-hero] .uds-modal-close-btn {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}
div[class^=uds-hero] {
  display: grid;
  grid-template-columns: 2rem 1fr 2rem;
  grid-template-rows: 1fr repeat(3, auto) 3rem;
  gap: 0px 0px;
  max-width: 1920px;
  margin: 0 auto;
  position: relative;
  align-content: flex-end;
  margin-bottom: 4rem;
}
div[class^=uds-hero] h1, div[class^=uds-hero] .h1,
div[class^=uds-hero] [role=doc-subtitle],
div[class^=uds-hero] a.btn,
div[class^=uds-hero] a.uds-modal-close-btn,
div[class^=uds-hero] .content,
div[class^=uds-hero] .btn-row {
  grid-column: 2;
  z-index: 30;
}
div[class^=uds-hero] img.hero {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  -o-object-fit: cover;
     object-fit: cover;
}
div[class^=uds-hero] [role=doc-subtitle] {
  font-size: 1.5rem;
  letter-spacing: -0.0525rem;
  font-weight: 700;
  display: inline-block;
  grid-row: 2;
  line-height: 1;
  height: -moz-min-content;
  height: min-content;
}
div[class^=uds-hero] [role=doc-subtitle] .highlight-white {
  box-shadow: -0.15em 0 0 #ffffff, 0.15em 0 0 #ffffff;
  background: #ffffff;
  color: #191919;
}
div[class^=uds-hero] [role=doc-subtitle] .highlight-black {
  box-shadow: -0.15em 0 0 #191919, 0.15em 0 0 #191919;
  background: #191919;
  color: #fafafa;
}
div[class^=uds-hero] [role=doc-subtitle] .highlight-gold {
  box-shadow: -0.15em 0 0 #ffc627, 0.15em 0 0 #ffc627;
  background: #ffc627;
  color: #191919;
}
div[class^=uds-hero] [role=doc-subtitle] span {
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
  margin-left: 0.15em;
}
div[class^=uds-hero] h1, div[class^=uds-hero] .h1 {
  grid-row: 3;
  margin: 0;
}
div[class^=uds-hero] h1 span, div[class^=uds-hero] .h1 span {
  margin-left: 0.15em;
}
div[class^=uds-hero] > a.btn, div[class^=uds-hero] > a.uds-modal-close-btn {
  position: absolute;
}
div[class^=uds-hero] .content {
  grid-row: 4;
  font-size: 1.5rem;
  letter-spacing: -0.0525rem;
  line-height: 1.75rem;
  font-weight: 700;
}
div[class^=uds-hero].has-btn-row {
  grid-template-rows: 1fr repeat(3, auto) 3rem;
}
div[class^=uds-hero].has-btn-row h1, div[class^=uds-hero].has-btn-row .h1 {
  grid-row: 3;
}
div[class^=uds-hero].has-btn-row .btn-row {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  position: absolute;
}
div[class^=uds-hero].has-btn-row .btn-row a.btn, div[class^=uds-hero].has-btn-row .btn-row a.uds-modal-close-btn {
  white-space: normal;
}
div[class^=uds-hero].has-btn-row .content {
  grid-row: 4;
}
div[class^=uds-hero].hide-content h1, div[class^=uds-hero].hide-content .h1 {
  margin-bottom: 2rem;
}
div[class^=uds-hero].hide-content .content {
  display: none;
}
div[class^=uds-hero] .hero-overlay {
  content: "";
  height: 100%;
  width: 100%;
  background-image: linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.7882352941) 100%);
  z-index: 20;
  position: absolute;
  top: 0;
  left: 0;
}

div.uds-hero-sm {
  height: 16rem;
  grid-template-rows: 1fr auto auto 3rem;
}
div.uds-hero-sm h1, div.uds-hero-sm .h1,
div.uds-hero-sm a.btn,
div.uds-hero-sm a.uds-modal-close-btn,
div.uds-hero-sm .btn-row {
  z-index: 30;
}
div.uds-hero-sm .content {
  display: none;
}
div.uds-hero-sm.has-btn-row h1, div.uds-hero-sm.has-btn-row .h1 {
  align-self: center;
}
div.uds-hero-sm.has-btn-row .content {
  display: none;
}
div.uds-hero-sm.has-btn-row .btn-row {
  align-self: flex-start;
  flex-wrap: wrap;
  top: 15rem;
}
div.uds-hero-sm.has-btn-row .btn-row a.btn, div.uds-hero-sm.has-btn-row .btn-row a.uds-modal-close-btn {
  margin-top: 0;
}
div.uds-hero-sm.has-btn-row .hero-overlay {
  height: 16rem;
}
div.uds-hero-sm > a.btn, div.uds-hero-sm > a.uds-modal-close-btn {
  top: 15rem;
}

div.uds-hero-md {
  height: 21rem;
  grid-template-rows: 1fr auto auto 3rem;
}
div.uds-hero-md .content {
  display: none;
}
div.uds-hero-md.has-btn-row .btn-row {
  flex-wrap: wrap;
  align-self: flex-start;
  top: 20rem;
}
div.uds-hero-md.has-btn-row .hero-overlay {
  height: 21rem;
}
div.uds-hero-md > a.btn, div.uds-hero-md > a.uds-modal-close-btn {
  top: 20rem;
}

div.uds-hero-lg {
  height: 32rem;
}
div.uds-hero-lg h1, div.uds-hero-lg .h1 {
  margin-bottom: 1rem;
}
div.uds-hero-lg.has-btn-row {
  grid-template-rows: 1fr repeat(3, auto) 1.5rem;
}
div.uds-hero-lg.has-btn-row .content p {
  overflow: hidden;
  text-overflow: ellipsis;
}
div.uds-hero-lg.has-btn-row .btn-row {
  align-self: flex-start;
  top: 31rem;
}
div.uds-hero-lg.has-btn-row .hero-overlay {
  height: 32rem;
}
div.uds-hero-lg.uds-video-hero video, div.uds-hero-lg.uds-video-hero .video-hero-controls {
  display: none;
}
div.uds-hero-lg > a.btn, div.uds-hero-lg > a.uds-modal-close-btn {
  top: 31rem;
}

/*--------------------------------------------------------------
3. Hero, Tablet
--------------------------------------------------------------*/
@media (min-width: 768px) {
  div[class^=uds-hero] {
    grid-template-columns: 1fr minmax(0, 700px) 1fr;
    margin-bottom: 0;
  }
  div[class^=uds-hero] h1, div[class^=uds-hero] .h1,
  div[class^=uds-hero] [role=doc-subtitle],
  div[class^=uds-hero] a.btn,
  div[class^=uds-hero] a.uds-modal-close-btn,
  div[class^=uds-hero] .content,
  div[class^=uds-hero] .btn-row {
    grid-column: 2;
    max-width: 700px;
    z-index: 30;
  }
  div[class^=uds-hero] .btn-row {
    align-items: center;
  }
  div[class^=uds-hero] .content {
    grid-row: 4;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1.75rem;
    margin-bottom: 2rem;
  }
  div[class^=uds-hero] .content p:last-child {
    margin-bottom: 0;
  }
  div[class^=uds-hero] h1, div[class^=uds-hero] .h1 {
    margin-bottom: 0;
  }
  div[class^=uds-hero].hide-content .content {
    display: block;
  }
  div[class^=uds-hero] > a.btn, div[class^=uds-hero] > a.uds-modal-close-btn {
    position: static;
    grid-row: 5;
  }
  div.uds-hero-sm {
    grid-template-rows: 1fr auto auto 3.5rem;
  }
  div.uds-hero-sm.has-btn-row .btn-row {
    top: 14.5rem;
  }
  div.uds-hero-sm > a.btn, div.uds-hero-sm > a.uds-modal-close-btn {
    grid-row: unset;
    position: absolute;
    top: 14.5rem;
  }
  div.uds-hero-md {
    height: 32rem;
    grid-template-rows: 1fr repeat(4, auto) 3rem;
  }
  div.uds-hero-md h1, div.uds-hero-md .h1 {
    margin-bottom: 2rem;
  }
  div.uds-hero-md .content {
    display: block;
  }
  div.uds-hero-md.has-btn-row {
    grid-template-rows: 1fr repeat(4, auto) 3rem;
  }
  div.uds-hero-md.has-btn-row h1, div.uds-hero-md.has-btn-row .h1 {
    grid-row: 3;
  }
  div.uds-hero-md.has-btn-row .content {
    grid-row: 4;
  }
  div.uds-hero-md.has-btn-row .btn-row {
    grid-row: 5;
    position: static;
  }
  div.uds-hero-md.has-btn-row .btn-row a.btn, div.uds-hero-md.has-btn-row .btn-row a.uds-modal-close-btn {
    margin-top: 0;
  }
  div.uds-hero-md.has-btn-row .hero-overlay {
    height: 32rem;
  }
  div.uds-hero-lg {
    height: 42.75rem;
    grid-template-rows: 1fr repeat(4, auto) 3rem;
  }
  div.uds-hero-lg.has-btn-row {
    grid-template-rows: 1fr repeat(4, auto) 3rem;
  }
  div.uds-hero-lg.has-btn-row .content {
    grid-row: 4;
  }
  div.uds-hero-lg.has-btn-row .btn-row {
    position: static;
    grid-row: 5;
  }
  div.uds-hero-lg.has-btn-row .btn-row a.btn, div.uds-hero-lg.has-btn-row .btn-row a.uds-modal-close-btn {
    margin-top: 0;
  }
  div.uds-hero-lg.has-btn-row .hero-overlay {
    height: 42.75rem;
  }
  div.uds-hero-lg h1, div.uds-hero-lg .h1 {
    margin-bottom: 2rem;
  }
}
/*--------------------------------------------------------------
4. Hero, Desktop
--------------------------------------------------------------*/
@media (min-width: 992px) {
  div[class^=uds-hero] {
    grid-template-columns: 1fr minmax(0, 940px) 1fr;
  }
  div[class^=uds-hero] h1, div[class^=uds-hero] .h1,
  div[class^=uds-hero] [role=doc-subtitle],
  div[class^=uds-hero] a.btn,
  div[class^=uds-hero] a.uds-modal-close-btn,
  div[class^=uds-hero] .content,
  div[class^=uds-hero] .btn-row {
    max-width: calc(940px - 30%);
  }
}
@media (min-width: 1260px) {
  div[class^=uds-hero] {
    grid-template-columns: 1fr minmax(0, 1200px) 1fr;
  }
  div[class^=uds-hero] h1, div[class^=uds-hero] .h1,
  div[class^=uds-hero] [role=doc-subtitle],
  div[class^=uds-hero] a.btn,
  div[class^=uds-hero] a.uds-modal-close-btn,
  div[class^=uds-hero] .content,
  div[class^=uds-hero] .btn-row {
    max-width: calc(1200px - 34%);
  }
  div.uds-hero-lg {
    height: 42.75rem;
  }
  div.uds-hero-lg.uds-video-hero {
    position: relative;
    overflow: hidden;
  }
  div.uds-hero-lg.uds-video-hero:after {
    display: none;
  }
  div.uds-hero-lg.uds-video-hero .hero {
    display: none;
  }
  div.uds-hero-lg.uds-video-hero.uds-video-hero video {
    display: block;
    height: auto;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
  }
  div.uds-hero-lg.uds-video-hero .video-hero-controls {
    display: block;
    position: relative;
    z-index: 40;
    grid-column: 2;
    grid-row: 6;
    align-self: end;
    justify-self: flex-end;
    top: -3rem;
  }
  div.uds-hero-lg.uds-video-hero .video-hero-controls .btn-circle-large {
    width: 56px !important;
    height: 56px !important;
    font-size: 16px !important;
    opacity: 0.3 !important;
  }
  div.uds-hero-lg.uds-video-hero .video-hero-controls .btn-circle-large:hover {
    opacity: 1 !important;
    cursor: pointer;
    transform: none;
  }
  div.uds-hero-lg.uds-video-hero .video-hero-controls .btn-circle-large .svg-inline--fa.fa-w-14 {
    margin-left: 0 !important;
  }
  div.uds-hero-lg.uds-video-hero .video-hero-controls #playHeroVid {
    display: none;
  }
}
/*--------------------------------------------------------------
5. Story Hero, Mobile
--------------------------------------------------------------*/
.uds-story-hero {
  display: grid;
  grid-template-columns: 1rem 1fr 1rem;
  grid-template-rows: calc(16rem - 4.5rem) 4.5rem auto;
  gap: 0px 0px;
  max-width: 1920px;
  margin: 0 auto;
}
.uds-story-hero .hero {
  grid-column: 1/span 3;
  grid-row: 1/span 2;
  -o-object-fit: cover;
     object-fit: cover;
  height: 100%;
  max-width: 100%;
}
.uds-story-hero .content {
  grid-column: 2/span 1;
  grid-row: 2/span 2;
  background-color: #ffffff;
  padding: 0 1rem;
}
.uds-story-hero .content .breadcrumb,
.uds-story-hero .content p.meta {
  padding: 0.75rem 0;
  margin: 0.25rem 0;
}
.uds-story-hero .content h1, .uds-story-hero .content .h1 {
  margin-top: 0;
}

/*--------------------------------------------------------------
6. Story Hero, Desktop
--------------------------------------------------------------*/
@media (min-width: 950px) {
  .uds-story-hero {
    grid-template-columns: 1.5rem 1fr 1.5rem;
    grid-template-rows: calc(32rem - 8rem) 8rem 1fr;
  }
  .uds-story-hero.uds-story-hero-lg {
    grid-template-rows: calc(42.75rem - 8rem) 8rem 1fr;
  }
  .uds-story-hero .content {
    padding: 0 calc(8.34% + 12px);
  }
  .uds-story-hero .content .breadcrumb,
  .uds-story-hero .content p.meta {
    margin: 1.25rem 0 2.25rem;
  }
}
@media (min-width: 1260px) {
  .uds-story-hero {
    grid-template-columns: 1fr 1224px 1fr;
  }
}
/*--------------------------------------------------------------
7. Hero, Degree Pages
--------------------------------------------------------------*/
#degreeListingPageContainer div[class^=uds-hero],
#degreeDetailPageContainer div[class^=uds-hero] {
  margin-bottom: 0;
}

.breadcrumb {
  padding-left: 0;
}
.breadcrumb.bg-gray-7 li.active {
  color: #fafafa;
}

@media screen and (max-width: 576px) {
  .breadcrumb {
    display: none;
  }
}
/*------------------------------------------------------------------
Sidebar Navigation

1. Component Mixins
2. Sidebar
-------------------------------------------------------------------*/
/*------------------------------------------------------------------
2. Sidebar
--------------------------------------------------------------------*/
.sidebar-toggler {
  border: 1px solid #d0d0d0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1rem;
}
.sidebar-toggler p {
  margin: 0;
}
.sidebar-toggler svg {
  transition: all 0.3s;
}
.sidebar-toggler[aria-expanded=false] svg {
  transform: rotate(-180deg);
}
@media (min-width: 768px) {
  .sidebar-toggler {
    display: none;
  }
}

.sidebar {
  border: 1px solid #d0d0d0;
}
.sidebar a:first-child {
  border-top: 0;
}
.sidebar .nav-text {
  font-size: 1rem;
  line-height: 1rem;
  letter-spacing: -0.015em;
  color: #191919;
  padding: 1rem;
  border: 1px solid #d0d0d0;
}
.sidebar .nav-text:not(:last-child) {
  border-bottom: 0;
}
.sidebar .nav-link {
  position: relative;
  padding: 0 0.5rem;
  color: #191919;
  font-size: 1rem;
  line-height: 1.5rem;
  letter-spacing: 0;
}
.sidebar .nav-link:not(.is-active) {
  display: flex;
  justify-content: space-between;
}
.sidebar .nav-link:hover, .sidebar .nav-link:focus {
  text-decoration: underline;
}
.sidebar .nav-link:visited {
  color: #191919;
}
.sidebar .nav-link.is-active {
  text-decoration: none;
}
.sidebar .nav-link.is-active:after {
  content: "";
  position: absolute;
  display: block;
  height: 0.5rem;
  width: 100%;
  background-color: #ffc627;
  text-decoration: none;
  bottom: -0.76rem;
  left: 50%;
  transform: translateX(-50%);
}
.sidebar .nav-link[aria-expanded=true] svg {
  transform: rotate(180deg);
}
.sidebar > .nav-link-container {
  padding: 0.7rem 0.5rem;
  min-height: 3rem;
  overflow: hidden;
  color: #191919;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.sidebar > .nav-link-container:not(:first-child) {
  border-top: 1px solid #d0d0d0;
}
.sidebar .card-foldable {
  border: 0;
  border-top: 1px solid #d0d0d0;
}
.sidebar .card-foldable .card-header,
.sidebar .card-foldable .card-body {
  background-color: transparent;
}
.sidebar .card-foldable .card-header {
  padding: 0.7rem 0.5rem;
  min-height: 3rem;
  min-height: calc(3rem - 1px);
}
.sidebar .card-foldable .card-header h1, .sidebar .card-foldable .card-header .h1,
.sidebar .card-foldable .card-header h2,
.sidebar .card-foldable .card-header .h2,
.sidebar .card-foldable .card-header h3,
.sidebar .card-foldable .card-header .h3,
.sidebar .card-foldable .card-header h4,
.sidebar .card-foldable .card-header .h4,
.sidebar .card-foldable .card-header h5,
.sidebar .card-foldable .card-header .h5 {
  font-weight: 400;
}
.sidebar .card-foldable .card-header a {
  align-items: center;
  display: flex;
  justify-content: space-between;
}
.sidebar .card-foldable .card-header ~ .card-body {
  border-top: 0;
}
.sidebar .card-foldable .card-body {
  padding: 0 1rem 1rem 1rem;
}
.sidebar .card-foldable .card-body > .nav-link {
  padding: 1rem 2.5rem 0 1.5rem;
}
.sidebar .card-foldable .card-body > .nav-link.is-active:after {
  bottom: -1rem;
  width: calc(100% - 4rem);
  transform: translateX(calc(-50% - 0.5rem));
}
.sidebar .card-foldable .card-body > .nav-link + .nav-link {
  padding-top: 1rem;
}
@media (min-width: 768px) {
  .sidebar.collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .sidebar.collapse:not(.show) {
    display: block;
  }
}
@media (max-width: 767.98px) {
  .sidebar {
    border-top: 0;
  }
}

/*--------------------------------------------------------------
# Blockquotes Alt

1. Reset of various elements.
2. New Blockquote, mobile
3. New Blockquote, desktop
--------------------------------------------------------------*/
blockquote {
  border-left: none;
  font-weight: inherit;
  padding-left: 0;
  max-width: none;
}

blockquote:before {
  display: none;
}

/*------------------------------------------------------------------
2. Blockquote, mobile
------------------------------------------------------------------- */
.uds-blockquote {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 1.5rem;
  font-size: 1.25rem;
  font-weight: 700;
  padding: 2rem;
}
@media (max-width: 575.98px) {
  .uds-blockquote {
    padding: 2rem;
  }
}
.uds-blockquote svg {
  grid-column: 1;
  fill: #191919;
  width: 2.5rem;
  height: auto;
}
.uds-blockquote blockquote {
  grid-column: 2;
  margin-top: 0;
  margin-bottom: 0;
}
.uds-blockquote blockquote p:last-child {
  margin-bottom: 0;
}
.uds-blockquote blockquote p:last-of-type:after {
  content: "”";
}
.uds-blockquote .citation {
  display: flex;
}
.uds-blockquote .citation .citation-content {
  display: flex;
  flex-direction: column;
}
.uds-blockquote .citation cite {
  display: block;
  font-size: 1rem;
  font-style: normal;
}
.uds-blockquote .citation cite.name {
  font-weight: 700;
}
.uds-blockquote .citation cite.description {
  font-weight: 400;
}
.uds-blockquote .citation:before {
  padding-right: 0.5em;
  margin-top: -5px;
  content: "—";
}
.uds-blockquote.with-image img {
  -o-object-fit: cover;
     object-fit: cover;
  border-radius: 100%;
  width: 72px;
  height: 72px;
}
.uds-blockquote.with-image blockquote p:first-of-type:before {
  content: "“";
}
.uds-blockquote.no-citation blockquote {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.uds-blockquote.no-citation blockquote p:first-of-type:before {
  content: "“";
}
.uds-blockquote.no-citation blockquote h3, .uds-blockquote.no-citation blockquote .h3 {
  margin-bottom: 0;
}
.uds-blockquote.no-citation blockquote h3 + p, .uds-blockquote.no-citation blockquote .h3 + p {
  margin-top: 1.5rem;
}
.uds-blockquote.accent-gold svg {
  fill: #ffc627;
}
.uds-blockquote.accent-maroon svg {
  fill: #8c1d40;
}

/*------------------------------------------------------------------
3. Blockquote, mobile only
------------------------------------------------------------------- */
@media (max-width: 575.98px) {
  .uds-blockquote.no-cite.with-image {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .uds-blockquote.no-cite.with-image blockquote,
  .uds-blockquote.no-cite.with-image h3,
  .uds-blockquote.no-cite.with-image .h3 {
    text-align: center;
  }
}
/*------------------------------------------------------------------
4. Blockquote, desktop
------------------------------------------------------------------- */
@media (min-width: 576px) {
  .uds-blockquote.no-cite {
    align-items: center;
  }
  .uds-blockquote.with-image {
    gap: 2rem;
  }
  .uds-blockquote.with-image img {
    width: 180px;
    height: 180px;
  }
  .uds-blockquote.with-image.reversed img {
    grid-column: 2;
  }
  .uds-blockquote.with-image.reversed blockquote {
    grid-column: 1;
    grid-row: 1;
  }
}
/*------------------------------------------------------------------
5. Alt Citation.
-  Produces a gold bar above the cite elements.
------------------------------------------------------------------- */
.alt-citation .citation,
.uds-testimonial .citation {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.alt-citation .citation:before,
.uds-testimonial .citation:before {
  content: "";
  display: block;
  background-color: #191919;
  height: 0.25rem;
  width: 4.5rem;
  margin: 1rem 0;
}
.alt-citation .citation cite.name:before,
.uds-testimonial .citation cite.name:before {
  content: none;
  display: flex;
}
.alt-citation .citation cite.name,
.uds-testimonial .citation cite.name {
  display: flex;
  justify-content: flex-start;
}
.alt-citation .citation cite.description,
.uds-testimonial .citation cite.description {
  font-weight: 400;
  padding-left: 0;
  display: flex;
  justify-content: flex-start;
}
.alt-citation.accent-gold .citation:before,
.uds-testimonial.accent-gold .citation:before {
  background-color: #ffc627;
}
.alt-citation.accent-maroon .citation:before,
.uds-testimonial.accent-maroon .citation:before {
  background-color: #8c1d40;
}

/*------------------------------------------------------------------
5. Testimonial
-  Vertically centered alignment of a standard blockquote.
------------------------------------------------------------------- */
.uds-blockquote.uds-testimonial {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.uds-blockquote.uds-testimonial blockquote {
  text-align: center;
}
.uds-blockquote.uds-testimonial blockquote p:first-of-type:before {
  content: "“";
}
.uds-blockquote.uds-testimonial blockquote p:last-of-type:after {
  content: "”";
}
.uds-blockquote.uds-testimonial blockquote p:last-of-type {
  margin-bottom: 0;
}
.uds-blockquote.uds-testimonial .citation:before {
  display: inline-flex;
  margin: 1rem auto;
}
.uds-blockquote.uds-testimonial cite.name,
.uds-blockquote.uds-testimonial cite.description {
  display: flex;
  justify-content: center;
}
.uds-blockquote.uds-testimonial.with-image img {
  width: 180px;
  height: 180px;
}

form.uds-form {
  /* Labels */
  /* Helper text */
  /* Input text */
  /* Input focus */
  /* Radios and Checkboxes - For more flexible control, this is a departure
    from Bootstrap 4 custom Checkboxes and Radios as described in:
    https://getbootstrap.com/docs/4.0/components/forms/#checkboxes-and-radios-1
    and has us instead managing these ourselves. */
  /* ERROR/SUCCESS VALIDATION - Client-side, using :valid and :invalid pseudo
    classes ala Bootstrap 4:
    https://getbootstrap.com/docs/4.0/components/forms/#validation */
  /* ERROR/SUCCESS VALIDATION - SERVER-SIDE
    using Bootstrap 4 .is-valid and .is-invalid classes. */
  /* Errors */
  /* checks and radios */
  /* Error for group follows legend and doesn't need margin adjustment. */
  /* Successes */
  /* checks and radios */
  /* Success for group follows legend and doesn't need margin adjustment. */
  /* TRAILING ICON INPUT */
  /* VARIOUS BACKGROUNDS, OVERRIDES */
}
form.uds-form .form-group {
  margin: 0 0 2rem 0;
}
form.uds-form label,
form.uds-form legend {
  font-size: 1rem;
  margin-bottom: 0.5rem;
}
form.uds-form label svg.uds-field-required,
form.uds-form legend svg.uds-field-required {
  font-size: 0.5rem;
  margin-right: 0.25rem;
  margin-bottom: 0.125rem;
  color: #8c1d40;
}
form.uds-form label.uds-form-label-disabled,
form.uds-form legend.uds-form-label-disabled {
  color: #484848;
}
form.uds-form .form-control {
  color: #191919;
  border: 1px solid #747474;
  /* Disabled input */
}
form.uds-form .form-control.is-invalid::-moz-placeholder {
  color: #191919;
}
form.uds-form .form-control.is-invalid::placeholder {
  color: #191919;
}
form.uds-form .form-control:disabled, form.uds-form .form-control[readonly] {
  color: #bfbfbf;
  background: #e8e8e8;
}
form.uds-form .form-control:disabled::-moz-placeholder, form.uds-form .form-control[readonly]::-moz-placeholder {
  color: #484848;
}
form.uds-form .form-control:disabled::placeholder, form.uds-form .form-control[readonly]::placeholder {
  color: #484848;
}
form.uds-form input,
form.uds-form textarea,
form.uds-form select {
  padding-left: 1rem !important;
}
form.uds-form input:focus,
form.uds-form textarea:focus,
form.uds-form select:focus {
  outline: none !important;
  box-shadow: none !important;
  border: 2px solid #191919 !important;
  border-radius: none;
}
form.uds-form fieldset {
  margin: 0 0 2rem 0;
}
form.uds-form fieldset .form-check {
  margin: 1rem 0;
}
form.uds-form .card-image-fieldset {
  margin-bottom: 1rem;
}
form.uds-form .card-image-fieldset.inline {
  display: flex;
}
form.uds-form .card-image-fieldset.inline .form-check:not(:first-child) {
  margin-left: 2rem;
}
form.uds-form .form-check {
  margin: 0 0 2rem 0;
}
form.uds-form .form-check input[type=radio],
form.uds-form .form-check input[type=checkbox] {
  opacity: 0;
}
form.uds-form .form-check input[type=radio] + label,
form.uds-form .form-check input[type=checkbox] + label {
  font-weight: bold;
  position: relative;
  display: inline-block;
  cursor: pointer;
  margin-left: 1rem;
  margin-bottom: 0;
}
form.uds-form .form-check input[type=radio] + label::before,
form.uds-form .form-check input[type=checkbox] + label::before {
  content: "";
  position: absolute;
  display: inline-block;
  left: -2.25rem;
  border: 1px solid #747474;
  width: 1.5rem;
  height: 1.5rem;
  background: white;
}
form.uds-form .form-check input[type=radio] + label::after,
form.uds-form .form-check input[type=checkbox] + label::after {
  content: "";
  position: absolute;
  display: inline-block;
  left: -1.875rem;
  top: 0.375rem;
  width: 0.75rem;
  height: 0.75rem;
}
form.uds-form .form-check input[type=radio]:checked + label::after,
form.uds-form .form-check input[type=checkbox]:checked + label::after {
  background: black;
}
form.uds-form .form-check input[type=radio]:focus + label::before,
form.uds-form .form-check input[type=checkbox]:focus + label::before {
  border: 2px solid #191919;
}
form.uds-form .form-check input[type=radio]:disabled + label::before,
form.uds-form .form-check input[type=checkbox]:disabled + label::before {
  background: #e8e8e8;
}
form.uds-form .form-check input[type=radio]:disabled + label,
form.uds-form .form-check input[type=checkbox]:disabled + label {
  color: #747474;
}
form.uds-form .form-check input[type=radio] + label::before {
  border-radius: 50%;
}
form.uds-form .form-check input[type=radio] + label::after {
  border-radius: 50%;
}
form.uds-form .form-check input[type=checkbox] + label::before {
  left: -2.25rem;
  top: 0.1rem;
}
form.uds-form .form-check input[type=checkbox] + label::after {
  left: -2rem;
}
form.uds-form .form-check input[type=checkbox]:checked + label::after {
  background: transparent;
  background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='check' class='svg-inline--fa fa-check fa-w-16' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='currentColor' d='M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z'%3E%3C/path%3E%3C/svg%3E");
  height: 1rem;
  width: 1rem;
}
form.uds-form.was-validated .form-control:invalid {
  border-style: solid;
  border: 1px solid #b72a2a;
  border-bottom: 8px solid #b72a2a;
}
form.uds-form.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #191919;
}
form.uds-form.was-validated .form-control:valid {
  border-style: solid;
  border: 1px solid #446d12;
  border-bottom: 8px solid #446d12;
}
form.uds-form.was-validated .form-check-input:valid ~ .form-check-label {
  color: #191919;
}
form.uds-form.needs-validation .invalid-feedback {
  display: none;
}
form.uds-form.needs-validation .valid-feedback {
  display: none;
}
form.uds-form input.is-invalid,
form.uds-form textarea.is-invalid,
form.uds-form select.is-invalid {
  border-style: solid;
  border: 1px solid #b72a2a;
  border-bottom: 8px solid #b72a2a;
}
form.uds-form small.is-invalid, form.uds-form .is-invalid.small,
form.uds-form div.is-invalid {
  margin-left: -1rem;
  margin-top: 1rem;
}
form.uds-form legend ~ small.is-invalid, form.uds-form legend ~ .is-invalid.small,
form.uds-form legend ~ div.is-invalid,
form.uds-form select ~ small.is-invalid,
form.uds-form select ~ .is-invalid.small,
form.uds-form select ~ div.is-invalid {
  margin-left: inherit;
  margin-top: inherit;
  color: #b72a2a;
}
form.uds-form .invalid-feedback {
  display: inline-block;
  font-weight: bold;
}
form.uds-form .invalid-feedback svg {
  color: #b72a2a;
  margin-right: 0.5rem;
}
form.uds-form input.is-valid,
form.uds-form textarea.is-valid,
form.uds-form select.is-valid {
  border-style: solid;
  border: 1px solid #446d12;
  border-bottom: 8px solid #446d12;
}
form.uds-form small.is-valid, form.uds-form .is-valid.small,
form.uds-form div.is-valid {
  margin-left: -1rem;
  margin-top: 1rem;
}
form.uds-form legend ~ small.is-valid, form.uds-form legend ~ .is-valid.small,
form.uds-form legend ~ div.is-valid,
form.uds-form select ~ small.is-valid,
form.uds-form select ~ .is-valid.small,
form.uds-form select ~ div.is-valid {
  margin-left: inherit;
  margin-top: inherit;
  color: #446d12;
}
form.uds-form .valid-feedback {
  display: inline-block;
  font-weight: bold;
}
form.uds-form .valid-feedback svg {
  color: #446d12;
  margin-right: 0.5rem;
}
form.uds-form .input-group-trailing-icon svg {
  position: absolute;
  right: 0.75rem;
  top: 0.75rem;
  color: #191919;
}
form.uds-form.uds-form-white {
  background-color: white;
}
form.uds-form.uds-form-gray1 {
  background-color: #fafafa;
}
form.uds-form.uds-form-gray2 {
  background-color: #e8e8e8;
  /* Dark Radios and Checkboxes */
}
form.uds-form.uds-form-gray2 .form-check input[type=radio]:disabled + label::before,
form.uds-form.uds-form-gray2 .form-check input[type=checkbox]:disabled + label::before {
  background: #bfbfbf;
  border-color: #747474;
}
form.uds-form.uds-form-gray2 .form-check input[type=radio]:disabled + label,
form.uds-form.uds-form-gray2 .form-check input[type=checkbox]:disabled + label {
  color: #484848;
}
form.uds-form.uds-form-gray7 {
  background-color: #191919;
  /* Dark Labels */
  /* Dark Labels, checks, radios and selects, client side, tweaks */
  /* TODO Likely we'll find more work to do with client side validation rules
    in order to take advantage of having both valid/invalid markup present.
    Just hastn't been implemented in stories yet for the sake of drawing a
    line somewhere. */
  /* Dark Input focus */
  /* Dark Radios and Checkboxes */
  /* Dark error input */
  /* Dark success input */
}
form.uds-form.uds-form-gray7 .form-control {
  /* Disabled input */
}
form.uds-form.uds-form-gray7 .form-control::-moz-placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 .form-control::placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 .form-control.is-invalid::-moz-placeholder {
  color: #fafafa;
}
form.uds-form.uds-form-gray7 .form-control.is-invalid::placeholder {
  color: #fafafa;
}
form.uds-form.uds-form-gray7 .form-control:disabled, form.uds-form.uds-form-gray7 .form-control[readonly] {
  color: #747474;
  background: #484848;
  border: 1px solid #747474;
}
form.uds-form.uds-form-gray7 .form-control:disabled::-moz-placeholder, form.uds-form.uds-form-gray7 .form-control[readonly]::-moz-placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 .form-control:disabled::placeholder, form.uds-form.uds-form-gray7 .form-control[readonly]::placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 label,
form.uds-form.uds-form-gray7 legend {
  color: #fafafa;
}
form.uds-form.uds-form-gray7 label.uds-form-label-disabled,
form.uds-form.uds-form-gray7 legend.uds-form-label-disabled {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7.was-validated .form-check-input:valid ~ .form-check-label {
  color: #fafafa;
}
form.uds-form.uds-form-gray7.was-validated select ~ small.is-valid, form.uds-form.uds-form-gray7.was-validated select ~ .is-valid.small, form.uds-form.uds-form-gray7.was-validated select ~ div.is-valid {
  color: #78be20;
}
form.uds-form.uds-form-gray7.was-validated .form-check-input:invalid ~ .form-check-label {
  color: #fafafa;
}
form.uds-form.uds-form-gray7.was-validated select ~ small.is-invalid, form.uds-form.uds-form-gray7.was-validated select ~ .is-invalid.small, form.uds-form.uds-form-gray7.was-validated select ~ div.is-invalid {
  color: #cc2f2f;
}
form.uds-form.uds-form-gray7 input,
form.uds-form.uds-form-gray7 textarea,
form.uds-form.uds-form-gray7 select {
  background-color: #191919;
  color: #fafafa;
}
form.uds-form.uds-form-gray7 input::-moz-placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 input::placeholder {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 input:focus,
form.uds-form.uds-form-gray7 textarea:focus,
form.uds-form.uds-form-gray7 select:focus {
  background-color: #191919;
  border: 2px solid #fafafa !important;
  color: #fafafa;
}
form.uds-form.uds-form-gray7 .form-check input[type=radio] + label::before,
form.uds-form.uds-form-gray7 .form-check input[type=checkbox] + label::before {
  border: 1px solid #707070;
  background: #191919;
}
form.uds-form.uds-form-gray7 .form-check input[type=radio]:checked + label::after,
form.uds-form.uds-form-gray7 .form-check input[type=checkbox]:checked + label::after {
  background: #fafafa;
}
form.uds-form.uds-form-gray7 .form-check input[type=radio]:focus + label::before,
form.uds-form.uds-form-gray7 .form-check input[type=checkbox]:focus + label::before {
  border: 2px solid #fafafa;
}
form.uds-form.uds-form-gray7 .form-check input[type=radio]:disabled + label::before,
form.uds-form.uds-form-gray7 .form-check input[type=checkbox]:disabled + label::before {
  background: #484848;
  border-color: #747474;
}
form.uds-form.uds-form-gray7 .form-check input[type=radio]:disabled + label,
form.uds-form.uds-form-gray7 .form-check input[type=checkbox]:disabled + label {
  color: #bfbfbf;
}
form.uds-form.uds-form-gray7 .form-check input[type=checkbox]:checked + label::after {
  background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' data-prefix='fas' data-icon='check' class='svg-inline--fa fa-check fa-w-16' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'%3E%3Cpath fill='currentColor' d='M173.898 439.404l-166.4-166.4c-9.997-9.997-9.997-26.206 0-36.204l36.203-36.204c9.997-9.998 26.207-9.998 36.204 0L192 312.69 432.095 72.596c9.997-9.997 26.207-9.997 36.204 0l36.203 36.204c9.997 9.997 9.997 26.206 0 36.204l-294.4 294.401c-9.998 9.997-26.207 9.997-36.204-.001z'%3E%3C/path%3E%3C/svg%3E");
  filter: invert(100%) sepia(28%) saturate(2502%) hue-rotate(222deg) brightness(144%) contrast(94%);
}
form.uds-form.uds-form-gray7 input.is-invalid,
form.uds-form.uds-form-gray7 textarea.is-invalid,
form.uds-form.uds-form-gray7 select.is-invalid {
  border-style: solid;
  border: 1px solid #cc2f2f;
  border-bottom: 8px solid #cc2f2f;
}
form.uds-form.uds-form-gray7 .invalid-feedback {
  font-weight: bold;
  color: #cc2f2f;
}
form.uds-form.uds-form-gray7 .invalid-feedback svg {
  color: #cc2f2f;
  margin-right: 0.5rem;
}
form.uds-form.uds-form-gray7 input.is-valid,
form.uds-form.uds-form-gray7 textarea.is-valid,
form.uds-form.uds-form-gray7 select.is-valid {
  border-style: solid;
  border: 1px solid #78be20;
  border-bottom: 8px solid #78be20;
}
form.uds-form.uds-form-gray7 .valid-feedback {
  font-weight: bold;
  color: #78be20;
}
form.uds-form.uds-form-gray7 .valid-feedback svg {
  color: #78be20;
  margin-right: 0.5rem;
}
form.uds-form.uds-form-gray7 .input-group-trailing-icon svg {
  color: #fafafa;
}

.uds-quote-image-background {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  align-items: flex-end;
  color: #ffffff !important;
  display: flex;
  flex-direction: row;
  height: auto;
  justify-content: center;
  max-width: 1920px;
  padding: 2rem;
  width: 100%;
}
@media screen and (max-width: 576px) {
  .uds-quote-image-background {
    min-height: 512px;
    padding: 24px;
    width: 100%;
    margin: 0;
  }
  .uds-quote-image-background .container {
    padding-left: 8px;
    padding-right: 8px;
  }
}
.uds-quote-image-background-container {
  max-width: 384px;
}
@media screen and (max-width: 576px) {
  .uds-quote-image-background-container {
    width: 100%;
  }
  .uds-quote-image-background-container .uds-blockquote {
    font-size: 1rem;
  }
}

div.uds-tooltip-container {
  display: inline-block;
  position: relative;
}

button.uds-tooltip {
  background: none;
  color: inherit;
  border: none;
  padding: 0;
  font: inherit;
  cursor: pointer;
  outline: inherit;
}
button.uds-tooltip .fa-circle {
  color: #d0d0d0;
}
button.uds-tooltip .fa-info {
  color: #ffffff;
}
button.uds-tooltip .fa-stack > * {
  font-size: 0.75rem;
}
button.uds-tooltip i {
  vertical-align: middle;
}
button.uds-tooltip:focus + div[role=tooltip].uds-tooltip-description {
  visibility: visible;
}
button.uds-tooltip:focus .fa-circle {
  color: #00b0f3;
}
@media (min-width: 576px) {
  button.uds-tooltip:hover + div[role=tooltip].uds-tooltip-description {
    visibility: visible;
  }
  button.uds-tooltip:hover .fa-circle {
    color: #00b0f3;
  }
}

button.uds-tooltip-gray-1 .fa-circle {
  color: #bfbfbf;
}
button.uds-tooltip-gray-1 .fa-info {
  color: #fafafa;
}

button.uds-tooltip-gray .fa-circle {
  color: #bfbfbf;
}
button.uds-tooltip-gray .fa-info {
  color: #e8e8e8;
}

button.uds-tooltip-dark .fa-circle {
  color: #747474;
}
button.uds-tooltip-dark .fa-info {
  color: #191919;
}

div[role=tooltip].uds-tooltip-description {
  background: #191919 0% 0% no-repeat padding-box;
  color: #fafafa;
  font: normal normal normal 1rem Arial;
  line-height: 1.5rem;
  margin: 0px 5px;
  max-width: 353px;
  min-width: 300px;
  padding: 2rem;
  position: absolute;
  left: 40px;
  top: 0;
  visibility: hidden;
  z-index: 1;
}
div[role=tooltip].uds-tooltip-description > span.uds-tooltip-heading {
  color: #fafafa;
  display: block;
  font: normal normal bold 1rem Arial;
  letter-spacing: 0px;
  margin-bottom: 1rem;
  text-align: left;
}
@media (max-width: 390px) {
  div[role=tooltip].uds-tooltip-description {
    min-width: 230px;
  }
}

span.uds-tooltip-visually-hidden {
  -webkit-clip-path: inset(100%);
          clip-path: inset(100%);
  clip: rect(1px, 1px, 1px, 1px);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

.uds-tooltip-bg-white {
  background-color: #ffffff;
}

.uds-tooltip-bg-base-gray {
  background-color: #fafafa;
}

.uds-tooltip-bg-gray {
  background-color: #e8e8e8;
}

.uds-tooltip-bg-dark {
  background-color: #191919;
}

.uds-video-with-caption {
  border: 1px solid #d0d0d0;
}
.uds-video-with-caption figure {
  margin: 0;
  display: flex;
  align-items: center;
}
.uds-video-with-caption figure figcaption {
  color: #747474;
  font-size: 0.75rem;
  margin: 0.5rem 1rem 1rem 1rem;
}
.uds-video-btn-play.btn.btn-circle.btn-circle-large, .uds-video-btn-play.btn-circle.btn-circle-large.uds-modal-close-btn {
  background-color: white !important;
  z-index: 2;
  opacity: 0.7;
  width: 104px !important;
  height: 104px !important;
  font-size: 2.5rem !important;
}
.uds-video-btn-play.btn.btn-circle.btn-circle-large:hover, .uds-video-btn-play.btn-circle.btn-circle-large.uds-modal-close-btn:hover {
  opacity: 1;
  transform: scale(1.15);
}
.uds-video-btn-play.btn.btn-circle.btn-circle-large .svg-inline--fa.fa-w-14, .uds-video-btn-play.btn-circle.btn-circle-large.uds-modal-close-btn .svg-inline--fa.fa-w-14 {
  margin-left: 5px;
}
@media (max-width: 576px) {
  .uds-video-btn-play.btn.btn-circle.btn-circle-large, .uds-video-btn-play.btn-circle.btn-circle-large.uds-modal-close-btn {
    width: 64px !important;
    height: 64px !important;
    font-size: 2rem !important;
  }
}
.uds-video-container video {
  height: auto;
  width: 100%;
  z-index: 0;
}
.uds-video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
}
.uds-video-player {
  position: relative;
  height: -moz-fit-content;
  height: fit-content;
}
.uds-video-player.youtube-video {
  padding-bottom: 56.25%; /* 16:9 */
}
.uds-video-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background: transparent linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.7882352941) 100%) 0% 0% no-repeat padding-box;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0.5rem;
  z-index: 1;
}
.uds-video-overlay:hover button.uds-video-btn-play {
  opacity: 1;
  transform: scale(1.15);
}

.uds-card-and-image {
  aspect-ratio: 16/9;
  background-position: center;
  background-size: cover;
  display: flex;
  align-items: center;
  flex-direction: row;
  height: 466px;
  max-width: 1920px;
  padding: 3rem 6rem;
  width: 100%;
}
.uds-card-and-image-right {
  flex-direction: row-reverse;
}
.uds-card-and-image-right .card-and-image-align {
  display: flex;
  flex-direction: row-reverse;
}
@media screen and (max-width: 576px) {
  .uds-card-and-image {
    flex-direction: column;
    height: auto;
    justify-content: flex-end;
    min-height: 720px;
    padding: 24px;
    width: 100%;
    margin: 0;
  }
  .uds-card-and-image .container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}
.uds-card-and-image-container {
  max-width: 384px;
}
.uds-card-and-image-container .card .card-header {
  padding-bottom: 1rem !important;
}
.uds-card-and-image-container .card {
  max-height: calc(466px - 4rem);
}
.uds-card-and-image-container .card .card-icon-top {
  margin: 1.5rem auto -0.5rem 1.5rem;
}
.uds-card-and-image-container .card .card-body {
  overflow: hidden;
  text-overflow: ellipsis;
  padding-bottom: 0;
  margin-bottom: 1.5rem;
  display: -webkit-box;
  -webkit-line-clamp: 6;
  -webkit-box-orient: vertical;
}
.uds-card-and-image-container .card .card-body p {
  margin-bottom: 0;
}
.uds-card-and-image-container .card .card-buttons .card-button {
  padding-bottom: 0;
}
@media screen and (max-width: 576px) {
  .uds-card-and-image-container {
    width: 100%;
  }
}

/*--------------------------------------------------------------
# UDS Grid Links

1. Mobile
2. Desktop modifiers
--------------------------------------------------------------*/
/*--------------------------------------------------------------
1. Mobile
--------------------------------------------------------------*/
.uds-grid-links {
  grid-template-columns: 1fr;
  display: grid;
  grid-template-rows: auto;
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
  row-gap: 1.5rem;
}
.uds-grid-links a {
  display: flex;
  font-size: 1.25rem;
  letter-spacing: -0.01875rem;
  font-weight: 700;
  border: 1px solid #d0d0d0;
  color: #191919;
  background-color: #ffffff;
  padding: 1.5rem;
  text-decoration: none;
}
.uds-grid-links a svg {
  margin: 0.25rem 0.5rem 0 0;
}
.uds-grid-links a:hover {
  text-decoration: underline;
}
.uds-grid-links.text-gold a {
  color: #ffc627;
  background-color: #191919;
}
.uds-grid-links.text-white a {
  color: #ffffff;
  background-color: #191919;
}

/*--------------------------------------------------------------
2. Desktop modifiers
--------------------------------------------------------------*/
@media (min-width: 768px) {
  .uds-grid-links.two-columns,
  .uds-grid-links.three-columns,
  .uds-grid-links.four-columns {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 992px) {
  .uds-grid-links.three-columns,
  .uds-grid-links.four-columns {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (min-width: 1260px) {
  .uds-grid-links.four-columns {
    grid-template-columns: repeat(4, 1fr);
  }
}
ul.uds-display-list {
  color: #191919;
  font-size: 1rem;
}
ul.uds-display-list li span {
  color: #747474;
  display: block;
  font-size: 0.875rem;
}

.uds-charts-and-graphs-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  max-width: 350px;
}

.uds-charts-and-graphs-overlay {
  display: flex;
  align-items: center;
  flex-direction: column;
  position: absolute;
  text-align: center;
}
.uds-charts-and-graphs-overlay h4, .uds-charts-and-graphs-overlay .h4 {
  margin: 0.5rem;
  font: normal normal bold 2.5rem Arial;
}
.uds-charts-and-graphs-overlay span {
  font: normal normal bold 1.125rem Arial;
}
@media (max-width: 767.98px) {
  .uds-charts-and-graphs-overlay h4, .uds-charts-and-graphs-overlay .h4 {
    font-size: 1.5rem;
  }
  .uds-charts-and-graphs-overlay span {
    font-size: 1rem;
  }
}

canvas {
  width: 100%;
  height: auto;
  position: relative;
}

.uds-anchor-menu {
  background-color: white;
  border-bottom: 1px solid #d0d0d0;
  z-index: 100;
}
.uds-anchor-menu-wrapper {
  display: flex;
  flex-direction: column;
}
.uds-anchor-menu-sticky {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
}
.uds-anchor-menu h4, .uds-anchor-menu .h4 {
  font-size: 1rem;
  padding: 0 !important;
}
.uds-anchor-menu h4:hover, .uds-anchor-menu .h4:hover {
  transform: none;
}
.uds-anchor-menu h4 svg, .uds-anchor-menu .h4 svg {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
  margin-top: 0.35rem;
  transform: rotate(0deg);
}
.uds-anchor-menu h4[aria-expanded=true] svg, .uds-anchor-menu [aria-expanded=true].h4 svg,
.uds-anchor-menu h4[aria-expanded=true] i,
.uds-anchor-menu [aria-expanded=true].h4 i {
  transform: rotate(180deg);
}
.uds-anchor-menu .card-body {
  padding-top: 1.5rem;
}
.uds-anchor-menu nav {
  flex-direction: column;
}
.uds-anchor-menu .nav-link {
  color: #191919;
  padding: 1.5rem 0;
  padding-left: 0.5rem;
  text-align: start;
  font-weight: 400;
}
.uds-anchor-menu .nav-link:not(:last-child) {
  border-bottom: 1px solid #d0d0d0;
}
.uds-anchor-menu .nav-link:hover, .uds-anchor-menu .nav-link.active {
  margin-bottom: 0;
  border-bottom: 0.5rem solid #ffc627;
}
.uds-anchor-menu .nav-link:visited {
  color: #191919;
}
.uds-anchor-menu .nav-link svg {
  width: 2rem !important;
  margin-right: 0.5rem;
  margin-left: -0.5rem;
}

@media (min-width: 576px) {
  .uds-anchor-menu.uds-anchor-menu-expanded-sm {
    border-bottom: 1px solid #d0d0d0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .uds-anchor-menu-wrapper {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm h4, .uds-anchor-menu.uds-anchor-menu-expanded-sm .h4 {
    margin: auto 0;
    font-size: 1.25rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm h4 svg, .uds-anchor-menu.uds-anchor-menu-expanded-sm .h4 svg {
    display: none;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm nav {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .nav-link {
    border-bottom: 0.5rem solid transparent;
    padding: 1.5rem 1rem 1rem 1rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .nav-link:hover, .uds-anchor-menu.uds-anchor-menu-expanded-sm .nav-link.active {
    margin-bottom: 0;
    border-bottom: 0.5rem solid #ffc627;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .card {
    border: 0;
    padding: 0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .collapse:not(.show) {
    display: block;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-sm .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
@media (min-width: 768px) {
  .uds-anchor-menu.uds-anchor-menu-expanded-md {
    border-bottom: 1px solid #d0d0d0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .uds-anchor-menu-wrapper {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md h4, .uds-anchor-menu.uds-anchor-menu-expanded-md .h4 {
    margin: auto 0;
    font-size: 1.25rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md h4 svg, .uds-anchor-menu.uds-anchor-menu-expanded-md .h4 svg {
    display: none;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md nav {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .nav-link {
    border-bottom: 0.5rem solid transparent;
    padding: 1.5rem 1rem 1rem 1rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .nav-link:hover, .uds-anchor-menu.uds-anchor-menu-expanded-md .nav-link.active {
    margin-bottom: 0;
    border-bottom: 0.5rem solid #ffc627;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .card {
    border: 0;
    padding: 0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .collapse:not(.show) {
    display: block;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-md .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
@media (min-width: 992px) {
  .uds-anchor-menu.uds-anchor-menu-expanded-lg {
    border-bottom: 1px solid #d0d0d0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .uds-anchor-menu-wrapper {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg h4, .uds-anchor-menu.uds-anchor-menu-expanded-lg .h4 {
    margin: auto 0;
    font-size: 1.25rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg h4 svg, .uds-anchor-menu.uds-anchor-menu-expanded-lg .h4 svg {
    display: none;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg nav {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .nav-link {
    border-bottom: 0.5rem solid transparent;
    padding: 1.5rem 1rem 1rem 1rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .nav-link:hover, .uds-anchor-menu.uds-anchor-menu-expanded-lg .nav-link.active {
    margin-bottom: 0;
    border-bottom: 0.5rem solid #ffc627;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .card {
    border: 0;
    padding: 0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .collapse:not(.show) {
    display: block;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-lg .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
@media (max-width: 991.98px) {
  .uds-anchor-menu h4, .uds-anchor-menu .h4 {
    display: flex;
    justify-content: space-between;
  }
}
@media (min-width: 1260px) {
  .uds-anchor-menu.uds-anchor-menu-expanded-xl {
    border-bottom: 1px solid #d0d0d0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .uds-anchor-menu-wrapper {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl h4, .uds-anchor-menu.uds-anchor-menu-expanded-xl .h4 {
    margin: auto 0;
    font-size: 1.25rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl h4 svg, .uds-anchor-menu.uds-anchor-menu-expanded-xl .h4 svg {
    display: none;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl nav {
    flex-direction: row;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .nav-link {
    border-bottom: 0.5rem solid transparent;
    padding: 1.5rem 1rem 1rem 1rem;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .nav-link:hover, .uds-anchor-menu.uds-anchor-menu-expanded-xl .nav-link.active {
    margin-bottom: 0;
    border-bottom: 0.5rem solid #ffc627;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .card {
    border: 0;
    padding: 0;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .collapse {
    display: block;
    height: auto !important;
    visibility: visible;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .collapse:not(.show) {
    display: block;
  }
  .uds-anchor-menu.uds-anchor-menu-expanded-xl .collapsing {
    position: relative;
    height: unset !important;
    overflow: hidden;
  }
}
.uds-card-image-and-content {
  display: flex;
  flex-direction: column;
}
.uds-card-image-and-content-image-container {
  background-size: cover;
  background-position: top;
  display: flex;
  height: 512px;
  position: relative;
  width: 100%;
}
.uds-card-image-and-content-content-container {
  display: flex;
  flex-direction: row;
  position: relative;
}
.uds-card-image-and-content-content-container > .uds-card-image-and-content-content-container-headline {
  left: 0px;
  top: -175px;
  font: normal normal bold 3rem Arial;
  padding: 2rem;
  position: absolute;
  max-width: 50%;
  color: #ffffff;
}
.uds-card-image-and-content-content-container .content {
  width: 55%;
  padding: 2rem;
}
.uds-card-image-and-content-content-container .card {
  width: 35%;
  margin: -201px 5% 0 10%;
  height: unset;
}

@media only screen and (max-width: 992px) {
  .uds-card-image-and-content-content-container .card {
    width: 40%;
    margin: -161px 2% 0 8%;
  }
}
@media only screen and (max-width: 768px) {
  .uds-card-image-and-content-content-container {
    flex-direction: column;
  }
  .uds-card-image-and-content-content-container > .uds-card-image-and-content-content-container-headline {
    max-width: 100%;
  }
  .uds-card-image-and-content-content-container .content {
    width: 100%;
  }
  .uds-card-image-and-content-content-container .card {
    width: auto;
    margin: 0 2rem;
  }
}
@media only screen and (max-width: 576px) {
  .uds-card-image-and-content-image-container {
    height: 16rem;
  }
  .uds-card-image-and-content-content-container > .uds-card-image-and-content-content-container-headline {
    font: normal normal bold 2rem Arial;
    padding: 2rem;
  }
}
.uds-card-arrangement {
  display: flex;
  flex-direction: column;
  max-width: 1400px;
}
.uds-card-arrangement > .uds-card-arrangement-content-container {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.uds-card-arrangement > .uds-card-arrangement-content-container > h2, .uds-card-arrangement > .uds-card-arrangement-content-container > .h2 {
  font: normal normal bold 2.5rem Arial;
  padding-right: 3rem;
}
.uds-card-arrangement > .uds-card-arrangement-content-container > button {
  margin-left: auto;
}
@media screen and (max-width: 768px) {
  .uds-card-arrangement > .uds-card-arrangement-content-container {
    flex-direction: column;
    align-items: flex-start;
  }
  .uds-card-arrangement > .uds-card-arrangement-content-container > h2, .uds-card-arrangement > .uds-card-arrangement-content-container > .h2 {
    padding-right: 0;
  }
  .uds-card-arrangement > .uds-card-arrangement-content-container > button {
    margin-left: unset;
    margin-right: auto;
  }
}
.uds-card-arrangement > .uds-card-arrangement-card-container {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
  display: grid;
  grid-auto-flow: column;
  grid-template-columns: repeat(auto-fill, 1fr);
  margin-top: 1.5rem;
}
.uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement {
  grid-template-columns: repeat(2, 1fr);
  grid-auto-flow: row;
  row-gap: 1.5rem;
}
.uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.three-columns {
  grid-template-columns: repeat(3, 1fr);
}
.uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.four-columns {
  grid-template-columns: repeat(4, 1fr);
}
@media screen and (max-width: 576px) {
  .uds-card-arrangement > .uds-card-arrangement-card-container {
    grid-auto-flow: row;
    row-gap: 1rem;
  }
}
.uds-card-arrangement-vertical {
  flex-direction: row;
}
.uds-card-arrangement-vertical > .uds-card-arrangement-content-container {
  align-items: flex-start;
  justify-content: start;
  flex: 1;
  flex-direction: column;
}
.uds-card-arrangement-vertical > .uds-card-arrangement-content-container > button {
  margin: initial;
}
.uds-card-arrangement-vertical > .uds-card-arrangement-card-container {
  flex: 2;
  row-gap: 1.5rem;
  display: grid;
  grid-auto-flow: row;
  grid-template-rows: repeat(auto-fill, 1fr);
  margin-left: 1.5rem;
}
@media screen and (max-width: 1260px) {
  .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.four-columns {
    grid-template-columns: repeat(3, 1fr);
  }
}
@media screen and (max-width: 992px) {
  .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.four-columns, .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.three-columns {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media screen and (max-width: 576px) {
  .uds-card-arrangement {
    flex-direction: column;
  }
  .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement {
    grid-template-columns: 1fr;
  }
  .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.four-columns, .uds-card-arrangement > .uds-card-arrangement-card-container.auto-arrangement.three-columns {
    grid-template-columns: 1fr;
  }
  .uds-card-arrangement-vertical > .uds-card-arrangement-card-container {
    margin-left: 0;
  }
}

.page-item {
  margin: 0 0.5rem;
  white-space: nowrap;
}
@media (max-width: 576px) {
  .page-item {
    margin: 0 0.25rem;
    max-width: 34px;
  }
}
.page-item.elipses {
  max-width: 24px;
  margin: 0 0.15rem;
}

.uds-modal {
  align-items: center;
  background-color: rgba(25, 25, 25, 0.75);
  display: none;
  justify-content: center;
  left: 0;
  opacity: 0;
  padding: 4rem 2rem;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1030;
  min-height: 100vh;
}
.uds-modal.open {
  animation: fadeIn 0.4s ease-out forwards;
  display: flex;
}
.uds-modal-container {
  background-color: #ffffff;
  flex: 1;
  height: 530px;
  max-width: 1200px;
  opacity: 1;
  padding: 32px;
  position: relative;
  z-index: 1000;
}
.uds-modal-close-btn {
  background-color: #bfbfbf;
  border: 1px solid transparent;
  height: 2rem;
  opacity: 0.5;
  padding: 0.25rem;
  position: absolute;
  right: 0;
  text-decoration: none;
  top: -3.5rem;
  width: 2rem !important;
}
.uds-modal-close-btn .fa-times {
  color: #191919;
}
.uds-modal-close-btn:hover {
  opacity: 1;
}

@keyframes fadeIn {
  0% {
    display: none;
    opacity: 0;
  }
  1% {
    display: flex;
    opacity: 0;
  }
  100% {
    display: flex;
    opacity: 1;
  }
}
@media screen and (max-width: 576px) {
  .uds-modal-container {
    height: auto;
    min-height: 256px;
  }
}
.uds-image-background-with-cta {
  align-items: center;
  display: flex;
  height: 512px;
  justify-content: center;
  width: 100%;
  max-width: 1920px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  margin: auto;
}
.uds-image-background-with-cta-container {
  align-items: baseline;
  display: inline-flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  margin: 0 4rem;
}
.uds-image-background-with-cta-container > span {
  color: #fafafa;
  font: normal normal bold 2.5rem Arial;
  flex: 1;
  max-width: 784px;
}

@media screen and (max-width: 1260px) {
  .uds-image-background-with-cta {
    padding: 3rem;
  }
}
@media screen and (max-width: 992px) {
  .uds-image-background-with-cta {
    height: 434px;
    padding: 3rem 2rem;
    align-items: flex-end;
  }
  .uds-image-background-with-cta-container {
    align-items: flex-start;
    flex-direction: column;
    justify-content: flex-end;
  }
  .uds-image-background-with-cta-container > span {
    font-size: 2rem;
    margin-bottom: 2rem;
    max-width: 512px;
  }
}
@media screen and (max-width: 576px) {
  .uds-image-background-with-cta {
    margin: 0;
  }
  .uds-image-background-with-cta .container {
    padding: 0;
  }
}
.uds-table {
  border: 1px solid #e8e8e8;
  overflow-x: auto;
}
.uds-table > table {
  width: 100%;
}
.uds-table > table th,
.uds-table > table td {
  box-sizing: border-box;
  font-size: 1rem;
  text-align: left;
  padding: 1rem;
  max-width: 700px;
}
.uds-table > table tr :nth-child(n+1) {
  box-sizing: border-box;
}
.uds-table > table tr :first-child {
  box-sizing: border-box;
}
.uds-table > table > thead > tr:first-child th {
  background-color: #d0d0d0;
}
.uds-table > table tbody tr th,
.uds-table > table tbody tr td {
  background-color: white;
}
.uds-table > table tbody tr th p,
.uds-table > table tbody tr td p {
  margin-bottom: 0;
}
.uds-table > table tbody tr:nth-child(2n) th,
.uds-table > table tbody tr:nth-child(2n) td {
  background-color: #fafafa;
}
.uds-table > table tbody tr:hover th,
.uds-table > table tbody tr:hover td, .uds-table > table tbody tr:focus th,
.uds-table > table tbody tr:focus td {
  background-color: #e8e8e8;
}
.uds-table > table tbody tr > th.normal {
  font-weight: normal;
}
.uds-table > table tbody tr > th.indent {
  font-weight: normal;
  padding-left: 4rem;
}
@media screen and (max-width: 576px) {
  .uds-table > table tbody tr > th.indent {
    padding-left: 2rem;
  }
}

.uds-table-fixed {
  overflow-x: auto;
  border: 1px solid #e8e8e8;
  scroll-behavior: smooth;
}
.uds-table-fixed-wrapper .scroll-control {
  display: none;
}
.uds-table-fixed-wrapper:hover .scroll-control {
  display: block;
}
.uds-table-fixed-wrapper {
  position: relative;
}
.uds-table-fixed-wrapper .scroll-control {
  display: none;
  position: absolute;
  height: 100%;
  pointer-events: none;
  top: 0;
  z-index: 100;
}
.uds-table-fixed-wrapper .scroll-control.previous {
  background: linear-gradient(90deg, rgba(25, 25, 25, 0.25) 0%, rgba(25, 25, 25, 0) 100%);
  left: 315px;
}
.uds-table-fixed-wrapper .scroll-control.next {
  background: linear-gradient(90deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.25) 100%);
  right: 0;
}
.uds-table-fixed-wrapper .scroll-control button {
  pointer-events: all;
  margin: 0 8px;
  position: relative;
  top: 50%;
}
.uds-table-fixed > table {
  border: none;
}
.uds-table-fixed > table tr > *:first-child {
  left: 0;
  position: sticky;
}
.uds-table-fixed > table tr > *:nth-child(n+2),
.uds-table-fixed > table td {
  box-sizing: border-box;
}

@media screen and (max-width: 768px) {
  .uds-table-fixed-wrapper {
    overflow-x: hidden;
  }
  .uds-table-fixed-wrapper .scroll-control {
    pointer-events: none;
    display: block;
  }
  .uds-table-fixed-wrapper .scroll-control.previous button, .uds-table-fixed-wrapper .scroll-control.next button {
    visibility: hidden;
  }
}
@media screen and (max-width: 576px) {
  .uds-table-fixed-wrapper .scroll-control.previous {
    left: 128px;
  }
  .uds-table-fixed > table tr > *:first-child {
    min-width: auto;
  }
}
/*--------------------------------------------------------------
Desktop styles
--------------------------------------------------------------*/
.uds-person-profile {
  --max-width: 800px;
  --pic-size: 180px;
  --person-profession-bottom-margin: 16px;
  --person-name-size: 24px;
  --name-top-margin: 8px;
  display: flex;
  max-width: var(--max-width);
}
.uds-person-profile.small {
  --pic-size: 120px;
}
.uds-person-profile.micro {
  --max-width: 384px;
  --pic-size: 78px;
  --person-profession-bottom-margin: 8px;
  --person-name-size: 20px;
  --name-top-margin: 0;
}
.uds-person-profile.micro .person-profession h4:not(:first-child), .uds-person-profile.micro .person-profession .h4:not(:first-child) {
  display: none;
}
.uds-person-profile.fill {
  background-color: #fafafa;
  border: 1px solid #d0d0d0;
  padding: 32px;
}
.uds-person-profile .profile-img-container {
  width: var(--pic-size);
  height: 100%;
}
.uds-person-profile .profile-img-container .profile-img-placeholder {
  background-size: 100%;
  background-size: cover;
  width: var(--pic-size);
  height: var(--pic-size);
}
.uds-person-profile .profile-img-container .profile-img-placeholder .profile-img {
  width: var(--pic-size);
  height: var(--pic-size);
  border-radius: 100%;
}
.uds-person-profile .person {
  margin-left: 1.5rem;
}
.uds-person-profile .person-name {
  line-height: 1;
  font-size: var(--person-name-size);
  margin-top: var(--name-top-margin);
  margin-bottom: 0.5rem;
}
.uds-person-profile .person-profession {
  margin: 0;
  line-height: 1;
  margin-bottom: var(--person-profession-bottom-margin);
}
.uds-person-profile .person-profession h4, .uds-person-profile .person-profession .h4 {
  margin: 0;
  font-size: 1rem;
}
.uds-person-profile .person .more-link {
  line-height: 1;
}
.uds-person-profile .person ul {
  list-style-type: none;
  display: flex;
  padding-left: 0;
}
.uds-person-profile .person ul li:not(:first-child) {
  margin-left: 1.5rem;
}
.uds-person-profile .person ul.person-contact-info {
  margin-bottom: 1rem;
}
.uds-person-profile .person ul.person-contact-info .person-address {
  display: flex;
  flex-direction: column;
  margin: 0;
}
.uds-person-profile .person ul.person-social-medias a {
  color: #191919;
  font-size: 1.75rem;
}

/*--------------------------------------------------------------
Mobile Styles
--------------------------------------------------------------*/
@media (max-width: 767.98px) {
  .uds-person-profile {
    max-width: 300px;
    flex-direction: column;
  }
  .uds-person-profile .person {
    margin: 0;
  }
  .uds-person-profile .person-name {
    margin-top: 1.5rem;
  }
  .uds-person-profile .person-description {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
  .uds-person-profile .person ul.person-contact-info {
    flex-direction: column;
  }
  .uds-person-profile .person ul.person-contact-info li {
    margin-top: 0.5rem;
    margin-left: 0;
  }
}
.parallax-container {
  position: relative;
  overflow: hidden;
}
.parallax-container img {
  position: absolute;
  width: 100%;
}
.parallax-container .parallax-container-content {
  width: 600px;
  position: absolute;
  top: 150px;
  background-color: white;
  padding: 3rem;
}
.parallax-container .parallax-container-content.uds-card-and-image {
  background-color: transparent;
}
.parallax-container .parallax-container-content h2, .parallax-container .parallax-container-content .h2 {
  margin: 0;
}
.parallax-container .parallax-container-content p {
  margin: 2rem 0 1.5rem 0;
}
@media screen and (max-width: 768px) {
  .parallax-container .parallax-container-content {
    padding: 1rem;
    width: 450px;
  }
  .parallax-container .parallax-container-content p {
    margin: 1rem 0 0.5rem 0;
  }
}
.parallax-container .parallax-container-content .btn-maroon {
  margin-right: 1rem;
}

.carve-your-path {
  height: 700px;
}
.carve-your-path .parallax-container-content {
  height: 400px;
  left: 75px;
}
@media screen and (max-width: 768px) {
  .carve-your-path .parallax-container-content {
    left: 25px;
    height: 450px;
  }
}
.carve-your-path .parallax-container-content .btn-gold {
  margin-top: 1rem;
}
.carve-your-path .parallax-container-content .btn-maroon {
  display: block;
  margin-top: 1rem;
}
@media screen and (min-width: 768px) {
  .carve-your-path .parallax-container-content .btn-maroon {
    display: inline-block;
    margin-right: 1rem;
    margin-top: 0;
  }
}

.asu-for-you {
  height: 800px;
  background-size: cover;
  background-position: center;
  background-image: url("https://ux-annual-report.ws.asu.edu/sites/all/libraries/Prototype/images/asuForYouBG.png");
}
.asu-for-you .parallax-container-content {
  height: 500px;
  right: 75px;
}
@media screen and (max-width: 768px) {
  .asu-for-you .parallax-container-content {
    right: 25px;
    height: 450px;
  }
}

.uds-image-based-card {
  border: none;
  position: relative;
  overflow: hidden;
}
.uds-image-based-card .card-title {
  margin-bottom: 1.5rem;
}
.uds-image-based-card img {
  -o-object-fit: cover;
     object-fit: cover;
  -o-object-position: center;
     object-position: center;
  height: inherit;
  width: inherit;
}
.uds-image-based-card .card-img-overlay {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  background-image: linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.7882352941) 100%);
  z-index: 20;
  padding: 1.5rem;
}
.uds-image-based-card.portrait-sm, .uds-image-based-card.portrait-md {
  width: 100%;
}
.uds-image-based-card.portrait-sm img, .uds-image-based-card.portrait-md img {
  aspect-ratio: 9/16;
}
.uds-image-based-card.portrait-sm h2, .uds-image-based-card.portrait-sm .h2, .uds-image-based-card.portrait-md h2, .uds-image-based-card.portrait-md .h2 {
  font-size: 2.25rem;
}
.uds-image-based-card.portrait-sm {
  height: 450px;
}
@media (min-width: 768px) {
  .uds-image-based-card.portrait-sm {
    width: 255px;
  }
}
.uds-image-based-card.portrait-md {
  height: 620px;
}
@media (min-width: 768px) {
  .uds-image-based-card.portrait-md {
    width: 350px;
  }
}
.uds-image-based-card.portrait-md .card-img-overlay {
  padding-bottom: 3rem;
}
.uds-image-based-card.landscape-sm, .uds-image-based-card.landscape-md, .uds-image-based-card.landscape-lg {
  width: 100%;
}
.uds-image-based-card.landscape-sm img, .uds-image-based-card.landscape-md img, .uds-image-based-card.landscape-lg img {
  aspect-ratio: 16/9;
}
.uds-image-based-card.landscape-sm {
  height: 144px;
}
.uds-image-based-card.landscape-sm h2, .uds-image-based-card.landscape-sm .h2 {
  font-size: 1.5rem;
}
@media (min-width: 768px) {
  .uds-image-based-card.landscape-sm {
    width: 255px;
  }
}
.uds-image-based-card.landscape-md {
  height: 197px;
}
.uds-image-based-card.landscape-md h2, .uds-image-based-card.landscape-md .h2 {
  font-size: 1.5rem;
}
@media (min-width: 768px) {
  .uds-image-based-card.landscape-md {
    width: 350px;
  }
}
.uds-image-based-card.landscape-md .card-img-overlay {
  padding-bottom: 2rem;
}
.uds-image-based-card.landscape-lg {
  height: 304px;
}
@media (min-width: 768px) {
  .uds-image-based-card.landscape-lg {
    width: 540px;
  }
}
.uds-image-based-card.landscape-lg h2, .uds-image-based-card.landscape-lg .h2 {
  font-size: 36px;
}
.uds-image-based-card.landscape-lg .card-img-overlay {
  padding: 2.5rem 2rem;
}
.uds-image-based-card.square-sm, .uds-image-based-card.square-md, .uds-image-based-card.square-lg {
  width: 100%;
}
.uds-image-based-card.square-sm img, .uds-image-based-card.square-md img, .uds-image-based-card.square-lg img {
  aspect-ratio: 1/1;
}
.uds-image-based-card.square-sm .card-img-overlay, .uds-image-based-card.square-md .card-img-overlay, .uds-image-based-card.square-lg .card-img-overlay {
  padding: 2rem;
}
.uds-image-based-card.square-sm h2, .uds-image-based-card.square-sm .h2, .uds-image-based-card.square-md h2, .uds-image-based-card.square-md .h2, .uds-image-based-card.square-lg h2, .uds-image-based-card.square-lg .h2 {
  font-size: 2.25rem;
}
.uds-image-based-card.square-sm {
  height: 255px;
}
@media (min-width: 768px) {
  .uds-image-based-card.square-sm {
    width: 255px;
  }
}
.uds-image-based-card.square-sm h2, .uds-image-based-card.square-sm .h2 {
  font-size: 1.5rem;
}
.uds-image-based-card.square-md {
  height: 350px;
}
@media (min-width: 768px) {
  .uds-image-based-card.square-md {
    width: 350px;
  }
}
.uds-image-based-card.square-lg {
  height: 540px;
}
@media (min-width: 768px) {
  .uds-image-based-card.square-lg {
    width: 540px;
  }
}
.uds-image-based-card.square-lg .card-img-overlay {
  padding: 3rem;
}

.content-section {
  height: 496px;
  width: 100%;
  position: relative;
}
.content-section .image-holder {
  overflow: hidden;
  z-index: 0;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.content-section .image-holder img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.content-section .image-holder:before {
  transition: all 1s cubic-bezier(0.19, 1, 0.19, 1);
  z-index: 10;
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-image: linear-gradient(180deg, rgba(25, 25, 25, 0.15) 0%, rgba(25, 25, 25, 0.25) 50%, rgb(25, 25, 25) 100%);
}
.content-section .content-holder {
  position: absolute;
  bottom: 2rem;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  height: auto;
  z-index: 20;
}
.content-section .content-holder .content-bg h3, .content-section .content-holder .content-bg .h3 {
  font-size: 2rem;
  line-height: 2.125rem;
  padding: 1.5rem 1.5rem 0.5rem;
  margin: 0;
}
.content-section .content-holder .content-bg .hidden-details .long-text {
  display: none;
  margin-bottom: 0;
}
.content-section .content-holder .content-bg .hidden-details a.btn, .content-section .content-holder .content-bg .hidden-details a.uds-modal-close-btn {
  margin: 1.5rem 1.5rem 0;
}
@media screen and (min-width: 768px) {
  .content-section {
    width: 384px;
    height: 600px;
  }
  .content-section .image-holder {
    transition: all 1s cubic-bezier(0.19, 1, 0.19, 1);
  }
  .content-section .content-holder {
    width: calc(100% - 4rem);
    left: 2rem;
  }
  .content-section .content-holder .content-bg {
    transition: all 1s cubic-bezier(0.19, 1, 0.19, 1);
  }
  .content-section .content-holder .content-bg h3, .content-section .content-holder .content-bg .h3 {
    font-size: 2.5rem;
    line-height: 2.75rem;
  }
  .content-section .content-holder .content-bg .hidden-details {
    transition: all 1s cubic-bezier(0.19, 1, 0.19, 1);
    transition-duration: 0.5s;
    max-height: 0;
    overflow: hidden;
    opacity: 0;
  }
  .content-section .content-holder .content-bg .hidden-details .long-text {
    display: block;
  }
  .content-section:hover .image-holder, .content-section:focus-within .image-holder {
    cursor: pointer;
    transform: scale(1.04);
  }
  .content-section:hover .image-holder::before, .content-section:focus-within .image-holder::before {
    background-image: linear-gradient(180deg, rgba(25, 25, 25, 0) 0%, rgba(25, 25, 25, 0.15) 50%, rgb(25, 25, 25) 100%);
  }
  .content-section:hover .image-holder:before, .content-section:focus-within .image-holder:before {
    transform: scale(1.04);
  }
  .content-section:hover .content-holder .content-bg, .content-section:focus-within .content-holder .content-bg {
    background-color: #ffffff;
  }
  .content-section:hover .content-holder .content-bg h3, .content-section:hover .content-holder .content-bg .h3, .content-section:focus-within .content-holder .content-bg h3, .content-section:focus-within .content-holder .content-bg .h3 {
    color: #191919;
  }
  .content-section:hover .content-holder .content-bg .hidden-details, .content-section:focus-within .content-holder .content-bg .hidden-details {
    max-height: 300px;
    opacity: 1;
  }
  .content-section:hover .content-holder .content-bg .hidden-details .long-text, .content-section:focus-within .content-holder .content-bg .hidden-details .long-text {
    color: #191919;
    padding: 1rem 1.5rem 1.5rem;
  }
  .content-section:hover .content-holder .content-bg .hidden-details a.btn, .content-section:hover .content-holder .content-bg .hidden-details a.uds-modal-close-btn, .content-section:focus-within .content-holder .content-bg .hidden-details a.btn, .content-section:focus-within .content-holder .content-bg .hidden-details a.uds-modal-close-btn {
    margin-top: 0;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    line-height: 1.5rem;
  }
}

.card-ranking {
  height: 543px;
  position: relative;
  border: 1px solid #d0d0d0;
  overflow: hidden;
}
.card-ranking .info-layer {
  display: flex;
  flex-direction: column;
  position: absolute;
  bottom: 0;
  left: 0;
  padding: 1.5rem 1.5rem 0;
  border-top: 0.5rem solid #ffc627;
  transition: all 0.3s ease-in-out;
  z-index: 10;
  background-color: white;
  height: 104px;
  cursor: pointer;
}
.card-ranking .info-layer .content {
  flex-grow: 1;
}
.card-ranking .info-layer .content .header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.card-ranking .info-layer .content .header .btn-expand {
  margin: 0 0 0 0.5rem;
  padding: 0;
}
.card-ranking .info-layer .content .header .btn-expand svg {
  font-size: 1.25rem;
  float: right;
}
.card-ranking .info-layer .content .header .btn-expand:focus {
  box-shadow: none !important;
}
.card-ranking .info-layer .content .header h4, .card-ranking .info-layer .content .header .h4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.card-ranking .info-layer .content .header svg.fa-chevron-up,
.card-ranking .info-layer .content .header i.fa-chevron-up {
  transition: all 0.3s ease-in-out;
}
.card-ranking .info-layer .content p {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 0.875rem;
  line-height: 1.25rem;
  -webkit-line-clamp: 3;
}
.card-ranking .info-layer .content a {
  font-size: 0.875rem;
  font-weight: 600;
}
.card-ranking .info-layer .content a .icon-small {
  margin-left: 5px;
  font-size: 0.875rem;
}
.card-ranking .info-layer.active {
  height: 100%;
}
.card-ranking .info-layer.active .content .header h4, .card-ranking .info-layer.active .content .header .h4 {
  display: block;
}
.card-ranking .info-layer.active .content .header svg.fa-chevron-up,
.card-ranking .info-layer.active .content .header i.fa-chevron-up {
  transform: rotate(180deg);
}
.card-ranking .info-layer.active .content p {
  -webkit-line-clamp: 12;
}
.card-ranking .info-layer .icons {
  display: flex;
  font-size: 1.5rem;
  gap: 2rem;
  margin-bottom: 0.5rem;
}
.card-ranking.small-image .image-wrapper {
  overflow: hidden;
  height: 160px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.card-ranking.small-image .image-wrapper img {
  max-width: 100%;
}
.card-ranking.small-image .citation {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.card-ranking.large-image > img {
  z-index: 0;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
     object-fit: cover;
}
.card-ranking.large-image .info-layer .content .header h4, .card-ranking.large-image .info-layer .content .header .h4 {
  min-height: 52px;
  max-width: 300px;
  margin: 0 0 1.5rem;
  -webkit-line-clamp: 2;
}
.card-ranking.large-image .info-layer .content p {
  -webkit-line-clamp: 12;
}
.card-ranking.large-image .info-layer.active .content .header h4, .card-ranking.large-image .info-layer.active .content .header .h4 {
  min-height: unset;
  max-height: unset;
}
@media screen and (min-width: 768px) {
  .card-ranking {
    width: 282px;
    height: 497px;
  }
}

/* -----------------------------------------------------
Container/ row / column padding adjustments for mobile.

Bootstrap doesn't natively provide a way to alter the behavior
its native grid elements based on a media query. This overrides
that behavior at screens <= 575px. (The small breakpoint.)

Should be included after @import scss/grid. Registered here for clarity.
------------------------------------------------------ */
@media (max-width: 767.98px) {
  .container,
  .container-fluid,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
