/*! Sortable 1.10.0-rc3 - MIT | git://github.com/SortableJS/Sortable.git | aabdolla fix for Android delay */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sortable=e()}(this,function(){"use strict";function t(e){return(t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(e)}function e(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function n(){return(n=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function o(t){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{},i=Object.keys(o);"function"==typeof Object.getOwnPropertySymbols&&(i=i.concat(Object.getOwnPropertySymbols(o).filter(function(t){return Object.getOwnPropertyDescriptor(o,t).enumerable}))),i.forEach(function(n){e(t,n,o[n])})}return t}function r(t,e){if(null==t)return{};var n,o,i=function(t,e){if(null==t)return{};var n,o,i={},r=Object.keys(t);for(o=0;o<r.length;o++)n=r[o],e.indexOf(n)>=0||(i[n]=t[n]);return i}(t,e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);for(o=0;o<r.length;o++)n=r[o],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(i[n]=t[n])}return i}function a(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function l(t){return!!navigator.userAgent.match(t)}var s=l(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),c=l(/Edge/i),u=l(/firefox/i),d=l(/safari/i)&&!l(/chrome/i)&&!l(/android/i),h=l(/iP(ad|od|hone)/i),f=l(/chrome/i)&&l(/android/i),p={capture:!1,passive:!1};function g(t,e,n){t.addEventListener(e,n,!s&&p)}function v(t,e,n){t.removeEventListener(e,n,!s&&p)}function m(t,e){if(e){if(">"===e[0]&&(e=e.substring(1)),t)try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(t){return!1}return!1}}function b(t){return t.host&&t!==document&&t.host.nodeType?t.host:t.parentNode}function w(t,e,n,o){if(t){n=n||document;do{if(null!=e&&(">"===e[0]?t.parentNode===n&&m(t,e):m(t,e))||o&&t===n)return t;if(t===n)break}while(t=b(t))}return null}var y,E=/\s+/g;function D(t,e,n){if(t&&e)if(t.classList)t.classList[n?"add":"remove"](e);else{var o=(" "+t.className+" ").replace(E," ").replace(" "+e+" "," ");t.className=(o+(n?" "+e:"")).replace(E," ")}}function _(t,e,n){var o=t&&t.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),void 0===e?n:n[e];e in o||-1!==e.indexOf("webkit")||(e="-webkit-"+e),o[e]=n+("string"==typeof n?"":"px")}}function S(t,e){var n="";do{var o=_(t,"transform");o&&"none"!==o&&(n=o+" "+n)}while(!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix;return i&&new i(n)}function C(t,e,n){if(t){var o=t.getElementsByTagName(e),i=0,r=o.length;if(n)for(;i<r;i++)n(o[i],i);return o}return[]}function T(){return s?document.documentElement:document.scrollingElement}function x(t,e,n,o,i){if(t.getBoundingClientRect||t===window){var r,a,l,c,u,d,h;if(t!==window&&t!==T()?(a=(r=t.getBoundingClientRect()).top,l=r.left,c=r.bottom,u=r.right,d=r.height,h=r.width):(a=0,l=0,c=window.innerHeight,u=window.innerWidth,d=window.innerHeight,h=window.innerWidth),(e||n)&&t!==window&&(i=i||t.parentNode,!s))do{if(i&&i.getBoundingClientRect&&("none"!==_(i,"transform")||n&&"static"!==_(i,"position"))){var f=i.getBoundingClientRect();a-=f.top+parseInt(_(i,"border-top-width")),l-=f.left+parseInt(_(i,"border-left-width")),c=a+r.height,u=l+r.width;break}}while(i=i.parentNode);if(o&&t!==window){var p=S(i||t),g=p&&p.a,v=p&&p.d;p&&(c=(a/=v)+(d/=v),u=(l/=g)+(h/=g))}return{top:a,left:l,bottom:c,right:u,width:h,height:d}}}function O(t,e,n,o){for(var i=k(t,!0),r=(e||x(t))[n];i;){var a=x(i)[o];if(!("top"===o||"left"===o?r>=a:r<=a))return i;if(i===T())break;i=k(i,!1)}return!1}function M(t,e,n){for(var o=0,i=0,r=t.children;i<r.length;){if("none"!==r[i].style.display&&r[i]!==kt.ghost&&r[i]!==kt.dragged&&w(r[i],n.draggable,t,!1)){if(o===e)return r[i];o++}i++}return null}function A(t,e){for(var n=t.lastElementChild;n&&(n===kt.ghost||"none"===_(n,"display")||e&&!m(n,e));)n=n.previousElementSibling;return n||null}function N(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===kt.clone||e&&!m(t,e)||n++;return n}function I(t){var e=0,n=0,o=T();if(t)do{var i=S(t),r=i.a,a=i.d;e+=t.scrollLeft*r,n+=t.scrollTop*a}while(t!==o&&(t=t.parentNode));return[e,n]}function k(t,e){if(!t||!t.getBoundingClientRect)return T();var n=t,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=_(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return T();if(o||e)return n;o=!0}}}while(n=n.parentNode);return T()}function P(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function R(t,e){return function(){if(!y){var n=arguments;1===n.length?t.call(this,n[0]):t.apply(this,n),y=setTimeout(function(){y=void 0},e)}}}function X(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function Y(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function B(t,e){_(t,"position","absolute"),_(t,"top",e.top),_(t,"left",e.left),_(t,"width",e.width),_(t,"height",e.height)}function F(t){_(t,"position",""),_(t,"top",""),_(t,"left",""),_(t,"width",""),_(t,"height","")}var H="Sortable"+(new Date).getTime();function L(){var t,e=[];return{captureAnimationState:function(){(e=[],this.options.animation)&&[].slice.call(this.el.children).forEach(function(t){if("none"!==_(t,"display")&&t!==kt.ghost){e.push({target:t,rect:x(t)});var n=x(t);if(t.thisAnimationDuration){var o=S(t,!0);o&&(n.top-=o.f,n.left-=o.e)}t.fromRect=n}})},addAnimationState:function(t){e.push(t)},removeAnimationState:function(t){e.splice(function(t,e){for(var n in t)if(t.hasOwnProperty(n))for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n);return-1}(e,{target:t}),1)},animateAll:function(n){var o=this;if(!this.options.animation)return clearTimeout(t),void("function"==typeof n&&n());var i=!1,r=0;e.forEach(function(t){var e=0,n=t.target,a=n.fromRect,l=x(n),s=n.prevFromRect,c=n.prevToRect,u=t.rect,d=S(n,!0);d&&(l.top-=d.f,l.left-=d.e),n.toRect=l,(O(n,l,"bottom","top")||O(n,l,"top","bottom")||O(n,l,"right","left")||O(n,l,"left","right"))&&(O(n,u,"bottom","top")||O(n,u,"top","bottom")||O(n,u,"right","left")||O(n,u,"left","right"))&&(O(n,a,"bottom","top")||O(n,a,"top","bottom")||O(n,a,"right","left")||O(n,a,"left","right"))||(n.thisAnimationDuration&&P(s,l)&&!P(a,l)&&(u.top-l.top)/(u.left-l.left)==(a.top-l.top)/(a.left-l.left)&&(e=function(t,e,n,o){return Math.sqrt(Math.pow(e.top-t.top,2)+Math.pow(e.left-t.left,2))/Math.sqrt(Math.pow(e.top-n.top,2)+Math.pow(e.left-n.left,2))*o.animation}(u,s,c,o.options)),P(l,a)||(n.prevFromRect=a,n.prevToRect=l,e||(e=o.options.animation),o.animate(n,u,e)),e&&(i=!0,r=Math.max(r,e),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout(function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null},e),n.thisAnimationDuration=e))}),clearTimeout(t),i?t=setTimeout(function(){"function"==typeof n&&n()},r):"function"==typeof n&&n(),e=[]},animate:function(t,e,n){if(n){_(t,"transition",""),_(t,"transform","");var o=x(t),i=S(this.el),r=i&&i.a,a=i&&i.d,l=(e.left-o.left)/(r||1),s=(e.top-o.top)/(a||1);t.animatingX=!!l,t.animatingY=!!s,_(t,"transform","translate3d("+l+"px,"+s+"px,0)"),function(t){t.offsetWidth}(t),_(t,"transition","transform "+n+"ms"+(this.options.easing?" "+this.options.easing:"")),_(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout(function(){_(t,"transition",""),_(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1},n)}}}}var j=[],K={initializeByDefault:!0},W={mount:function(t){for(var e in K)!K.hasOwnProperty(e)||e in t||(t[e]=K[e]);j.push(t)},pluginEvent:function(t,e,n){var i=this;this.eventCanceled=!1;var r=t+"Global";j.forEach(function(a){e[a.pluginName]&&(e[a.pluginName][r]&&(i.eventCanceled=!!e[a.pluginName][r](o({sortable:e},n))),e.options[a.pluginName]&&e[a.pluginName][t]&&(i.eventCanceled=i.eventCanceled||!!e[a.pluginName][t](o({sortable:e},n))))})},initializePlugins:function(t,e,o){for(var i in j.forEach(function(i){var r=i.pluginName;if(t.options[r]||i.initializeByDefault){var a=new i(t,e);a.sortable=t,t[r]=a,n(o,a.options)}}),t.options)if(t.options.hasOwnProperty(i)){var r=this.modifyOption(t,i,t.options[i]);void 0!==r&&(t.options[i]=r)}},getEventOptions:function(t,e){var o={};return j.forEach(function(i){"function"==typeof i.eventOptions&&n(o,i.eventOptions.call(e,t))}),o},modifyOption:function(t,e,n){var o;return j.forEach(function(i){t[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[e]&&(o=i.optionListeners[e].call(t[i.pluginName],n))}),o}};function z(t){var e,n=t.sortable,i=t.rootEl,r=t.name,a=t.targetEl,l=t.cloneEl,u=t.toEl,d=t.fromEl,h=t.oldIndex,f=t.newIndex,p=t.oldDraggableIndex,g=t.newDraggableIndex,v=t.originalEvent,m=t.putSortable,b=t.eventOptions,w=(n=n||i[H]).options,y="on"+r.charAt(0).toUpperCase()+r.substr(1);!window.CustomEvent||s||c?(e=document.createEvent("Event")).initEvent(r,!0,!0):e=new CustomEvent(r,{bubbles:!0,cancelable:!0}),e.to=u||i,e.from=d||i,e.item=a||i,e.clone=l,e.oldIndex=h,e.newIndex=f,e.oldDraggableIndex=p,e.newDraggableIndex=g,e.originalEvent=v,e.pullMode=m?m.lastPutMode:void 0;var E=o({},b,W.getEventOptions(r,n));for(var D in E)e[D]=E[D];i&&i.dispatchEvent(e),w[y]&&w[y].call(n,e)}var G=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=n.evt,a=r(n,["evt"]);W.pluginEvent.bind(kt)(t,e,o({dragEl:q,parentEl:V,ghostEl:Z,rootEl:$,nextEl:Q,lastDownEl:J,cloneEl:tt,cloneHidden:et,dragStarted:ut,putSortable:lt,activeSortable:kt.active,originalEvent:i,oldIndex:nt,oldDraggableIndex:it,newIndex:ot,newDraggableIndex:rt,hideGhostForTarget:Mt,unhideGhostForTarget:At,cloneNowHidden:function(){et=!0},cloneNowShown:function(){et=!1},dispatchSortableEvent:function(t){U({sortable:e,name:t,originalEvent:i})}},a))};function U(t){z(o({putSortable:lt,cloneEl:tt,targetEl:q,rootEl:$,oldIndex:nt,oldDraggableIndex:it,newIndex:ot,newDraggableIndex:rt},t))}if("undefined"==typeof window||!window.document)throw new Error("Sortable.js requires a window with a document");var q,V,Z,$,Q,J,tt,et,nt,ot,it,rt,at,lt,st,ct,ut,dt,ht,ft,pt,gt=!1,vt=!1,mt=[],bt=!1,wt=!1,yt=[],Et=!1,Dt=[],_t=h,St=c||s?"cssFloat":"float",Ct=!f&&!h&&"draggable"in document.createElement("div"),Tt=function(){if(s)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}(),xt=function(t,e){var n=_(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=M(t,0,e),r=M(t,1,e),a=i&&_(i),l=r&&_(r),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+x(i).width,c=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+x(r).width;if("flex"===n.display)return"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal";if("grid"===n.display)return n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal";if(i&&"none"!==a.float){var u="left"===a.float?"left":"right";return!r||"both"!==l.clear&&l.clear!==u?"horizontal":"vertical"}return i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||s>=o&&"none"===n[St]||r&&"none"===n[St]&&s+c>o)?"vertical":"horizontal"},Ot=function(e){function n(t,e){return function(o,i,r,a){var l=o.options.group.name&&i.options.group.name&&o.options.group.name===i.options.group.name;if(null==t&&(e||l))return!0;if(null==t||!1===t)return!1;if(e&&"clone"===t)return t;if("function"==typeof t)return n(t(o,i,r,a),e)(o,i,r,a);var s=(e?o:i).options.group.name;return!0===t||"string"==typeof t&&t===s||t.join&&t.indexOf(s)>-1}}var o={},i=e.group;i&&"object"==t(i)||(i={name:i}),o.name=i.name,o.checkPull=n(i.pull,!0),o.checkPut=n(i.put),o.revertClone=i.revertClone,e.group=o},Mt=function(){!Tt&&Z&&_(Z,"display","none")},At=function(){!Tt&&Z&&_(Z,"display","")};document.addEventListener("click",function(t){if(vt)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),vt=!1,!1},!0);var Nt=function(t){if(q){t=t.touches?t.touches[0]:t;var e=(i=t.clientX,r=t.clientY,mt.some(function(t){if(!A(t)){var e=x(t),n=t[H].options.emptyInsertThreshold,o=i>=e.left-n&&i<=e.right+n,l=r>=e.top-n&&r<=e.bottom+n;return n&&o&&l?a=t:void 0}}),a);if(e){var n={};for(var o in t)t.hasOwnProperty(o)&&(n[o]=t[o]);n.target=n.rootEl=e,n.preventDefault=void 0,n.stopPropagation=void 0,e[H]._onDragOver(n)}}var i,r,a},It=function(t){q&&q.parentNode[H]._isOutsideThisEl(t.target)};function kt(t,e){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=e=n({},e),t[H]=this;var o={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return xt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==kt.supportPointer&&"PointerEvent"in window,emptyInsertThreshold:5};for(var i in W.initializePlugins(this,t,o),o)!(i in e)&&(e[i]=o[i]);for(var r in Ot(e),this)"_"===r.charAt(0)&&"function"==typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!e.forceFallback&&Ct,this.nativeDraggable&&(this.options.touchStartThreshold=1),e.supportPointer?g(t,"pointerdown",this._onTapStart):(g(t,"mousedown",this._onTapStart),g(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(g(t,"dragover",this),g(t,"dragenter",this)),mt.push(this.el),e.store&&e.store.get&&this.sort(e.store.get(this)||[]),n(this,L())}function Pt(t,e,n,o,i,r,a,l){var u,d,h=t[H],f=h.options.onMove;return!window.CustomEvent||s||c?(u=document.createEvent("Event")).initEvent("move",!0,!0):u=new CustomEvent("move",{bubbles:!0,cancelable:!0}),u.to=e,u.from=t,u.dragged=n,u.draggedRect=o,u.related=i||e,u.relatedRect=r||x(e),u.willInsertAfter=l,u.originalEvent=a,t.dispatchEvent(u),f&&(d=f.call(h,u,a)),d}function Rt(t){t.draggable=!1}function Xt(){Et=!1}function Yt(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}function Bt(t){return setTimeout(t,0)}function Ft(t){return clearTimeout(t)}kt.prototype={constructor:kt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(dt=null)},_getDirection:function(t,e){return"function"==typeof this.options.direction?this.options.direction.call(this,t,e,q):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,i=o.preventOnFilter,r=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,l=(a||t).target,s=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,c=o.filter;if(function(t){Dt.length=0;var e=t.getElementsByTagName("input"),n=e.length;for(;n--;){var o=e[n];o.checked&&Dt.push(o)}}(n),!q&&!(/mousedown|pointerdown/.test(r)&&0!==t.button||o.disabled||s.isContentEditable||(l=w(l,o.draggable,n,!1))&&l.animated||J===l)){if(nt=N(l),it=N(l,o.draggable),"function"==typeof c){if(c.call(this,t,l,this))return U({sortable:e,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),G("filter",e,{evt:t}),void(i&&t.cancelable&&t.preventDefault())}else if(c&&(c=c.split(",").some(function(o){if(o=w(s,o.trim(),n,!1))return U({sortable:e,rootEl:o,name:"filter",targetEl:l,fromEl:n,toEl:n}),G("filter",e,{evt:t}),!0})))return void(i&&t.cancelable&&t.preventDefault());o.handle&&!w(s,o.handle,n,!1)||this._prepareDragStart(t,a,l)}}},_prepareDragStart:function(t,e,n){var o,i=this,r=i.el,a=i.options,l=r.ownerDocument;if(n&&!q&&n.parentNode===r)if($=r,V=(q=n).parentNode,Q=q.nextSibling,J=n,at=a.group,kt.dragged=q,st={target:q,clientX:(e||t).clientX,clientY:(e||t).clientY},this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,q.style["will-change"]="all",o=function(){G("delayEnded",i,{evt:t}),kt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!u&&i.nativeDraggable&&(q.draggable=!0),i._triggerDragStart(t,e),U({sortable:i,name:"choose",originalEvent:t}),D(q,a.chosenClass,!0))},a.ignore.split(",").forEach(function(t){C(q,t.trim(),Rt)}),g(l,"dragover",Nt),g(l,"mousemove",Nt),g(l,"touchmove",Nt),g(l,"mouseup",i._onDrop),g(l,"touchend",i._onDrop),g(l,"touchcancel",i._onDrop),u&&this.nativeDraggable&&(this.options.touchStartThreshold=4,q.draggable=!0),G("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(c||s))o();else{if(kt.eventCanceled)return void this._onDrop();g(l,"mouseup",i._disableDelayedDrag),g(l,"touchend",i._disableDelayedDrag),g(l,"touchcancel",i._disableDelayedDrag),g(l,"mousemove",i._delayedDragTouchMoveHandler),g(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&g(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,a.delay)}},_delayedDragTouchMoveHandler:function(t){var e=t.touches?t.touches[0]:t;Math.max(Math.abs(e.clientX-this._lastX),Math.abs(e.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){q&&Rt(q),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._disableDelayedDrag),v(t,"touchend",this._disableDelayedDrag),v(t,"touchcancel",this._disableDelayedDrag),v(t,"mousemove",this._delayedDragTouchMoveHandler),v(t,"touchmove",this._delayedDragTouchMoveHandler),v(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?g(document,"pointermove",this._onTouchMove):g(document,e?"touchmove":"mousemove",this._onTouchMove):(g(q,"dragend",this),g($,"dragstart",this._onDragStart));try{document.selection?Bt(function(){document.selection.empty()}):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,e){if(gt=!1,$&&q){G("dragStarted",this,{evt:e}),this.nativeDraggable&&g(document,"dragover",It);var n=this.options;!t&&D(q,n.dragClass,!1),D(q,n.ghostClass,!0),kt.active=this,t&&this._appendGhost(),U({sortable:this,name:"start",originalEvent:e})}else this._nulling()},_emulateDragOver:function(){if(ct){this._lastX=ct.clientX,this._lastY=ct.clientY,Mt();for(var t=document.elementFromPoint(ct.clientX,ct.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(ct.clientX,ct.clientY))!==e;)e=t;if(q.parentNode[H]._isOutsideThisEl(t),e)do{if(e[H]){if(e[H]._onDragOver({clientX:ct.clientX,clientY:ct.clientY,target:t,rootEl:e})&&!this.options.dragoverBubble)break}t=e}while(e=e.parentNode);At()}},_onTouchMove:function(t){if(st){var e=this.options,n=e.fallbackTolerance,o=e.fallbackOffset,i=t.touches?t.touches[0]:t,r=Z&&S(Z),a=Z&&r&&r.a,l=Z&&r&&r.d,s=_t&&pt&&I(pt),c=(i.clientX-st.clientX+o.x)/(a||1)+(s?s[0]-yt[0]:0)/(a||1),u=(i.clientY-st.clientY+o.y)/(l||1)+(s?s[1]-yt[1]:0)/(l||1),d=t.touches?"translate3d("+c+"px,"+u+"px,0)":"translate("+c+"px,"+u+"px)";if(!kt.active&&!gt){if(n&&Math.max(Math.abs(i.clientX-this._lastX),Math.abs(i.clientY-this._lastY))<n)return;this._onDragStart(t,!0)}ct=i,_(Z,"webkitTransform",d),_(Z,"mozTransform",d),_(Z,"msTransform",d),_(Z,"transform",d),t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!Z){var t=this.options.fallbackOnBody?document.body:$,e=x(q,!0,_t,!0,t),n=this.options;if(_t){for(pt=t;"static"===_(pt,"position")&&"none"===_(pt,"transform")&&pt!==document;)pt=pt.parentNode;pt!==document.body&&pt!==document.documentElement?(pt===document&&(pt=T()),e.top+=pt.scrollTop,e.left+=pt.scrollLeft):pt=T(),yt=I(pt)}D(Z=q.cloneNode(!0),n.ghostClass,!1),D(Z,n.fallbackClass,!0),D(Z,n.dragClass,!0),_(Z,"transition",""),_(Z,"transform",""),_(Z,"box-sizing","border-box"),_(Z,"margin",0),_(Z,"top",e.top),_(Z,"left",e.left),_(Z,"width",e.width),_(Z,"height",e.height),_(Z,"opacity","0.8"),_(Z,"position",_t?"absolute":"fixed"),_(Z,"zIndex","100000"),_(Z,"pointerEvents","none"),kt.ghost=Z,t.appendChild(Z)}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,i=n.options;G("dragStart",this,{evt:t}),kt.eventCanceled?this._onDrop():(G("setupClone",this),kt.eventCanceled||((tt=Y(q)).draggable=!1,tt.style["will-change"]="",this._hideClone(),D(tt,this.options.chosenClass,!1),kt.clone=tt),n.cloneId=Bt(function(){G("clone",n),kt.eventCanceled||(n.options.removeCloneOnHide||$.insertBefore(tt,q),n._hideClone(),U({sortable:n,name:"clone"}))}),!e&&D(q,i.dragClass,!0),e?(vt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(v(document,"mouseup",n._onDrop),v(document,"touchend",n._onDrop),v(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,q)),g(document,"drop",n),_(q,"transform","translateZ(0)")),gt=!0,n._dragStartId=Bt(n._dragStarted.bind(n,e,t)),g(document,"selectstart",n),ut=!0,d&&_(document.body,"user-select","none"))},_onDragOver:function(t){var e,n,i,r,a=this.el,l=t.target,s=this.options,c=s.group,u=kt.active,d=at===c,h=s.sort,f=lt||u,p=this,g=!1;if(!Et){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),l=w(l,s.draggable,a,!0),Y("dragOver"),kt.eventCanceled)return g;if(q.contains(t.target)||l.animated&&l.animatingX&&l.animatingY||p._ignoreWhileAnimating===l)return F(!1);if(vt=!1,u&&!s.disabled&&(d?h||(i=!$.contains(q)):lt===this||(this.lastPutMode=at.checkPull(this,u,q,t))&&c.checkPut(this,u,q,t))){if(r="vertical"===this._getDirection(t,l),e=x(q),Y("dragOverValid"),kt.eventCanceled)return g;if(i)return V=$,B(),this._hideClone(),Y("revert"),kt.eventCanceled||(Q?$.insertBefore(q,Q):$.appendChild(q)),F(!0);var v=A(a,s.draggable);if(!v||function(t,e,n){var o=x(A(n.el,n.options.draggable));return e?t.clientX>o.right+10||t.clientX<=o.right&&t.clientY>o.bottom&&t.clientX>=o.left:t.clientX>o.right&&t.clientY>o.top||t.clientX<=o.right&&t.clientY>o.bottom+10}(t,r,this)&&!v.animated){if(v===q)return F(!1);if(v&&a===t.target&&(l=v),l&&(n=x(l)),!1!==Pt($,a,q,e,l,n,t,!!l))return B(),a.appendChild(q),V=a,L(),F(!0)}else if(l.parentNode===a){n=x(l);var m,b,y,E=q.parentNode!==a,S=!function(t,e,n){var o=n?t.left:t.top,i=n?t.right:t.bottom,r=n?t.width:t.height,a=n?e.left:e.top,l=n?e.right:e.bottom,s=n?e.width:e.height;return o===a||i===l||o+r/2===a+s/2}(q.animated&&q.toRect||e,l.animated&&l.toRect||n,r),C=r?"top":"left",T=O(l,null,"top","top")||O(q,null,"top","top"),M=T?T.scrollTop:void 0;if(dt!==l&&(b=n[C],bt=!1,wt=!S&&s.invertSwap||E),0!==(m=function(t,e,n,o,i,r,a,l){var s=o?t.clientY:t.clientX,c=o?n.height:n.width,u=o?n.top:n.left,d=o?n.bottom:n.right,h=!1;if(!a)if(l&&ft<c*i){if(!bt&&(1===ht?s>u+c*r/2:s<d-c*r/2)&&(bt=!0),bt)h=!0;else if(1===ht?s<u+ft:s>d-ft)return-ht}else if(s>u+c*(1-i)/2&&s<d-c*(1-i)/2)return function(t){return N(q)<N(t)?1:-1}(e);if((h=h||a)&&(s<u+c*r/2||s>d-c*r/2))return s>u+c/2?1:-1;return 0}(t,l,n,r,S?1:s.swapThreshold,null==s.invertedSwapThreshold?s.swapThreshold:s.invertedSwapThreshold,wt,dt===l))){var I=N(q);do{I-=m,y=V.children[I]}while(y&&("none"===_(y,"display")||y===Z))}if(0===m||y===l)return F(!1);dt=l,ht=m;var k=l.nextElementSibling,P=!1,R=Pt($,a,q,e,l,n,t,P=1===m);if(!1!==R)return 1!==R&&-1!==R||(P=1===R),Et=!0,setTimeout(Xt,30),B(),P&&!k?a.appendChild(q):l.parentNode.insertBefore(q,P?k:l),T&&X(T,0,M-T.scrollTop),V=q.parentNode,void 0===b||wt||(ft=Math.abs(b-x(l)[C])),L(),F(!0)}if(a.contains(q))return F(!1)}return!1}function Y(s,c){G(s,p,o({evt:t,isOwner:d,axis:r?"vertical":"horizontal",revert:i,dragRect:e,targetRect:n,canSort:h,fromSortable:f,target:l,completed:F,onMove:function(n,o){return Pt($,a,q,e,n,x(n),t,o)},changed:L},c))}function B(){Y("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function F(e){return Y("dragOverCompleted",{insertion:e}),e&&(d?u._hideClone():u._showClone(p),p!==f&&(D(q,lt?lt.options.ghostClass:u.options.ghostClass,!1),D(q,s.ghostClass,!0)),lt!==p&&p!==kt.active?lt=p:p===kt.active&&lt&&(lt=null),f===p&&(p._ignoreWhileAnimating=l),p.animateAll(function(){Y("dragOverAnimationComplete"),p._ignoreWhileAnimating=null}),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(l===q&&!q.animated||l===a&&!l.animated)&&(dt=null),s.dragoverBubble||t.rootEl||l===document||(q.parentNode[H]._isOutsideThisEl(t.target),!e&&Nt(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),g=!0}function L(){ot=N(q),rt=N(q,s.draggable),U({sortable:p,name:"change",toEl:a,newIndex:ot,newDraggableIndex:rt,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){v(document,"mousemove",this._onTouchMove),v(document,"touchmove",this._onTouchMove),v(document,"pointermove",this._onTouchMove),v(document,"dragover",Nt),v(document,"mousemove",Nt),v(document,"touchmove",Nt)},_offUpEvents:function(){var t=this.el.ownerDocument;v(t,"mouseup",this._onDrop),v(t,"touchend",this._onDrop),v(t,"pointerup",this._onDrop),v(t,"touchcancel",this._onDrop),v(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;ot=N(q),rt=N(q,n.draggable),G("drop",this,{evt:t}),ot=N(q),rt=N(q,n.draggable),kt.eventCanceled?this._nulling():(gt=!1,wt=!1,bt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Ft(this.cloneId),Ft(this._dragStartId),this.nativeDraggable&&(v(document,"drop",this),v(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),d&&_(document.body,"user-select",""),t&&(ut&&(t.cancelable&&t.preventDefault(),!n.dropBubble&&t.stopPropagation()),Z&&Z.parentNode&&Z.parentNode.removeChild(Z),($===V||lt&&"clone"!==lt.lastPutMode)&&tt&&tt.parentNode&&tt.parentNode.removeChild(tt),q&&(this.nativeDraggable&&v(q,"dragend",this),Rt(q),q.style["will-change"]="",ut&&!gt&&D(q,lt?lt.options.ghostClass:this.options.ghostClass,!1),D(q,this.options.chosenClass,!1),U({sortable:this,name:"unchoose",toEl:V,newIndex:null,newDraggableIndex:null,originalEvent:t}),$!==V?(ot>=0&&(U({rootEl:V,name:"add",toEl:V,fromEl:$,originalEvent:t}),U({sortable:this,name:"remove",toEl:V,originalEvent:t}),U({rootEl:V,name:"sort",toEl:V,fromEl:$,originalEvent:t}),U({sortable:this,name:"sort",toEl:V,originalEvent:t})),lt&&lt.save()):ot!==nt&&ot>=0&&(U({sortable:this,name:"update",toEl:V,originalEvent:t}),U({sortable:this,name:"sort",toEl:V,originalEvent:t})),kt.active&&(null!=ot&&-1!==ot||(ot=nt,rt=it),U({sortable:this,name:"end",toEl:V,originalEvent:t}),this.save()))),this._nulling())},_nulling:function(){G("nulling",this),$=q=V=Z=Q=tt=J=et=st=ct=ut=ot=rt=nt=it=dt=ht=lt=at=kt.dragged=kt.ghost=kt.clone=kt.active=null,Dt.forEach(function(t){t.checked=!0}),Dt.length=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":q&&(this._onDragOver(t),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move");t.cancelable&&t.preventDefault()}(t));break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,e=[],n=this.el.children,o=0,i=n.length,r=this.options;o<i;o++)w(t=n[o],r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||Yt(t));return e},sort:function(t){var e={},n=this.el;this.toArray().forEach(function(t,o){var i=n.children[o];w(i,this.options.draggable,n,!1)&&(e[t]=i)},this),t.forEach(function(t){e[t]&&(n.removeChild(e[t]),n.appendChild(e[t]))})},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return w(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(void 0===e)return n[t];var o=W.modifyOption(this,t,e);n[t]=void 0!==o?o:e,"group"===t&&Ot(n)},destroy:function(){G("destroy",this);var t=this.el;t[H]=null,v(t,"mousedown",this._onTapStart),v(t,"touchstart",this._onTapStart),v(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(v(t,"dragover",this),v(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),function(t){t.removeAttribute("draggable")}),this._onDrop(),mt.splice(mt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){if(!et){if(G("hideClone",this),kt.eventCanceled)return;_(tt,"display","none"),this.options.removeCloneOnHide&&tt.parentNode&&tt.parentNode.removeChild(tt),et=!0}},_showClone:function(t){if("clone"===t.lastPutMode){if(et){if(G("showClone",this),kt.eventCanceled)return;$.contains(q)&&!this.options.group.revertClone?$.insertBefore(tt,q):Q?$.insertBefore(tt,Q):$.appendChild(tt),this.options.group.revertClone&&this._animate(q,tt),_(tt,"display",""),et=!1}}else this._hideClone()}},g(document,"touchmove",function(t){(kt.active||gt)&&t.cancelable&&t.preventDefault()}),kt.utils={on:g,off:v,css:_,find:C,is:function(t,e){return!!w(t,e,t,!1)},extend:function(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},throttle:R,closest:w,toggleClass:D,clone:Y,index:N,nextTick:Bt,cancelNextTick:Ft,detectDirection:xt,getChild:M},kt.mount=function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];e[0].constructor===Array&&(e=e[0]),e.forEach(function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ${ {}.toString.call(plugin) }";t.utils&&(kt.utils=o({},kt.utils,t.utils)),W.mount(t)})},kt.create=function(t,e){return new kt(t,e)},kt.version="1.10.0-rc3";var Ht,Lt,jt,Kt,Wt,zt,Gt=[],Ut=!1;function qt(){Gt.forEach(function(t){clearInterval(t.pid)}),Gt=[]}function Vt(){clearInterval(zt)}var Zt,$t=R(function(t,e,n,o){if(e.scroll){var i,r=e.scrollSensitivity,a=e.scrollSpeed,l=T(),s=!1;Lt!==n&&(Lt=n,qt(),Ht=e.scroll,i=e.scrollFn,!0===Ht&&(Ht=k(n,!0)));var c=0,u=Ht;do{var d=u,h=x(d),f=h.top,p=h.bottom,g=h.left,v=h.right,m=h.width,b=h.height,w=void 0,y=void 0,E=d.scrollWidth,D=d.scrollHeight,S=_(d),C=d.scrollLeft,O=d.scrollTop;d===l?(w=m<E&&("auto"===S.overflowX||"scroll"===S.overflowX||"visible"===S.overflowX),y=b<D&&("auto"===S.overflowY||"scroll"===S.overflowY||"visible"===S.overflowY)):(w=m<E&&("auto"===S.overflowX||"scroll"===S.overflowX),y=b<D&&("auto"===S.overflowY||"scroll"===S.overflowY));var M=w&&(Math.abs(v-t.clientX)<=r&&C+m<E)-(Math.abs(g-t.clientX)<=r&&!!C),A=y&&(Math.abs(p-t.clientY)<=r&&O+b<D)-(Math.abs(f-t.clientY)<=r&&!!O);if(!Gt[c])for(var N=0;N<=c;N++)Gt[N]||(Gt[N]={});Gt[c].vx==M&&Gt[c].vy==A&&Gt[c].el===d||(Gt[c].el=d,Gt[c].vx=M,Gt[c].vy=A,clearInterval(Gt[c].pid),0==M&&0==A||(s=!0,Gt[c].pid=setInterval(function(){o&&0===this.layer&&kt.active._onTouchMove(Wt);var e=Gt[this.layer].vy?Gt[this.layer].vy*a:0,n=Gt[this.layer].vx?Gt[this.layer].vx*a:0;"function"==typeof i&&"continue"!==i.call(kt.dragged.parentNode[H],n,e,t,Wt,Gt[this.layer].el)||X(Gt[this.layer].el,n,e)}.bind({layer:c}),24))),c++}while(e.bubbleScroll&&u!==l&&(u=k(u,!1)));Ut=s}},30),Qt=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,i=t.activeSortable,r=t.dispatchSortableEvent,a=t.hideGhostForTarget,l=t.unhideGhostForTarget,s=n||i;a();var c=document.elementFromPoint(e.clientX,e.clientY);l(),s&&!s.el.contains(c)&&(r("spill"),this.onSpill(o))};function Jt(){}function te(){}Jt.prototype={startIndex:null,dragStart:function(t){var e=t.oldDraggableIndex;this.startIndex=e},onSpill:function(t){this.sortable.captureAnimationState();var e=M(this.sortable.el,this.startIndex,this.sortable.options);e?this.sortable.el.insertBefore(t,e):this.sortable.el.appendChild(t),this.sortable.animateAll()},drop:Qt},n(Jt,{pluginName:"revertOnSpill"}),te.prototype={onSpill:function(t){this.sortable.captureAnimationState(),t.parentNode&&t.parentNode.removeChild(t),this.sortable.animateAll()},drop:Qt},n(te,{pluginName:"removeOnSpill"});var ee,ne,oe,ie,re,ae=[],le=[],se=!1,ce=!1,ue=!1;function de(t,e){le.forEach(function(n){var o=e.children[n.sortableIndex+(t?Number(i):0)];o?e.insertBefore(n,o):e.appendChild(n)})}function he(){ae.forEach(function(t){t!==oe&&t.parentNode&&t.parentNode.removeChild(t)})}return kt.mount(new function(){function t(){for(var t in this.options={scroll:!0,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){var e=t.originalEvent;this.sortable.nativeDraggable?g(document,"dragover",this._handleAutoScroll):this.sortable.options.supportPointer?g(document,"pointermove",this._handleFallbackAutoScroll):e.touches?g(document,"touchmove",this._handleFallbackAutoScroll):g(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){var e=t.originalEvent;this.sortable.options.dragOverBubble||e.rootEl||this._handleAutoScroll(e)},drop:function(){this.sortable.nativeDraggable?v(document,"dragover",this._handleAutoScroll):(v(document,"pointermove",this._handleFallbackAutoScroll),v(document,"touchmove",this._handleFallbackAutoScroll),v(document,"mousemove",this._handleFallbackAutoScroll)),Vt(),qt(),clearTimeout(y),y=void 0},nulling:function(){Wt=Lt=Ht=Ut=zt=jt=Kt=null,Gt.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var n=this,o=t.clientX,i=t.clientY,r=document.elementFromPoint(o,i);if(Wt=t,e||c||s||d){$t(t,this.options,r,e);var a=k(r,!0);!Ut||zt&&o===jt&&i===Kt||(zt&&Vt(),zt=setInterval(function(){var r=k(document.elementFromPoint(o,i),!0);r!==a&&(a=r,qt()),$t(t,n.options,r,e)},10),jt=o,Kt=i)}else{if(!this.sortable.options.bubbleScroll||k(r,!0)===T())return void qt();$t(t,this.options,k(r,!1),!1)}}},n(t,{pluginName:"scroll",initializeByDefault:!0})}),kt.mount(te,Jt),kt.mount(new function(){function t(){this.options={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){var e=t.dragEl;Zt=e},dragOverValid:function(t){var e=t.completed,n=t.target,o=t.onMove,i=t.activeSortable,r=t.changed;if(i.options.swap){var a=this.sortable.el,l=this.sortable.options;if(n&&n!==a){var s=Zt;!1!==o(n)?(D(n,l.swapClass,!0),Zt=n):Zt=null,s&&s!==Zt&&D(s,l.swapClass,!1)}return r(),e(!0)}},drop:function(t){var e,n,o,i,r,a,l=t.activeSortable,s=t.putSortable,c=t.dragEl,u=s||this.sortable,d=this.sortable.options;Zt&&D(Zt,d.swapClass,!1),Zt&&(d.swap||s&&s.options.swap)&&c!==Zt&&(u.captureAnimationState(),u!==l&&l.captureAnimationState(),n=Zt,r=(e=c).parentNode,a=n.parentNode,r&&a&&!r.isEqualNode(n)&&!a.isEqualNode(e)&&(o=N(e),i=N(n),r.isEqualNode(a)&&o<i&&i++,r.insertBefore(n,r.children[o]),a.insertBefore(e,a.children[i])),u.animateAll(),u!==l&&l.animateAll())},nulling:function(){Zt=null}},n(t,{pluginName:"swap",eventOptions:function(){return{swapItem:Zt}}})}),kt.mount(new function(){function t(t){for(var e in this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this));t.options.supportPointer?g(document,"pointerup",this._deselectMultiDrag):(g(document,"mouseup",this._deselectMultiDrag),g(document,"touchend",this._deselectMultiDrag)),g(document,"keydown",this._checkKeyDown),g(document,"keyup",this._checkKeyUp),this.options={selectedClass:"sortable-selected",multiDragKey:null,setData:function(e,n){var o="";ae.length&&ne===t?ae.forEach(function(t,e){o+=(e?", ":"")+t.textContent}):o=n.textContent,e.setData("Text",o)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){var e=t.dragEl;oe=e},delayEnded:function(){this.isMultiDrag=~ae.indexOf(oe)},setupClone:function(t){var e=t.sortable;if(this.isMultiDrag){for(var n=0;n<ae.length;n++)le.push(Y(ae[n])),le[n].sortableIndex=ae[n].sortableIndex,le[n].draggable=!1,le[n].style["will-change"]="",D(le[n],e.options.selectedClass,!1),ae[n]===oe&&D(le[n],e.options.chosenClass,!1);return e._hideClone(),!0}},clone:function(t){var e=t.sortable,n=t.rootEl,o=t.dispatchSortableEvent;if(this.isMultiDrag)return!e.options.removeCloneOnHide&&ae.length&&ne===e?(de(!0,n),o("clone"),!0):void 0},showClone:function(t){var e=t.cloneNowShown,n=t.rootEl;if(this.isMultiDrag)return de(!1,n),le.forEach(function(t){_(t,"display","")}),e(),re=!1,!0},hideClone:function(t){var e=t.sortable,n=t.cloneNowHidden;if(this.isMultiDrag)return le.forEach(function(t){_(t,"display","none"),e.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)}),n(),re=!0,!0},dragStartGlobal:function(t){t.sortable,!this.isMultiDrag&&ne&&ne.multiDrag._deselectMultiDrag(),ae.forEach(function(t){t.sortableIndex=N(t)}),ae=ae.sort(function(t,e){return t.sortableIndex-e.sortableIndex}),ue=!0},dragStarted:function(t){var e=t.sortable;if(this.isMultiDrag){if(e.options.sort&&(e.captureAnimationState(),e.options.animation)){ae.forEach(function(t){t!==oe&&_(t,"position","absolute")});var n=x(oe,!1,!0,!0);ae.forEach(function(t){t!==oe&&B(t,n)}),ce=!0,se=!0}e.animateAll(function(){ce=!1,se=!1,e.options.animation&&ae.forEach(function(t){F(t)}),e.options.sort&&he()})}},dragOver:function(t){var e=t.target,n=t.completed;if(ce&&~ae.indexOf(e))return n(!1)},revert:function(t){var e=t.fromSortable,n=t.rootEl,o=t.sortable,r=t.dragRect;ae.length>1&&(ae.forEach(function(t){o.addAnimationState({target:t,rect:ce?x(t):r}),F(t),t.fromRect=r,e.removeAnimationState(t)}),ce=!1,function(t,e){ae.forEach(function(n){var o=e.children[n.sortableIndex+(t?Number(i):0)];o?e.insertBefore(n,o):e.appendChild(n)})}(!o.options.removeCloneOnHide,n))},dragOverCompleted:function(t){var e=t.sortable,n=t.isOwner,o=t.insertion,i=t.activeSortable,r=t.parentEl,a=t.putSortable,l=e.options;if(o){if(n&&i._hideClone(),se=!1,l.animation&&ae.length>1&&(ce||!n&&!i.options.sort&&!a)){var s=x(oe,!1,!0,!0);ae.forEach(function(t){t!==oe&&(B(t,s),r.appendChild(t))}),ce=!0}if(!n)if(ce||he(),ae.length>1){var c=re;i._showClone(e),i.options.animation&&!re&&c&&le.forEach(function(t){i.addAnimationState({target:t,rect:ie}),t.fromRect=ie,t.thisAnimationDuration=null})}else i._showClone(e)}},dragOverAnimationCapture:function(t){var e=t.dragRect,o=t.isOwner,i=t.activeSortable;if(ae.forEach(function(t){t.thisAnimationDuration=null}),i.options.animation&&!o&&i.multiDrag.isMultiDrag){ie=n({},e);var r=S(oe,!0);ie.top-=r.f,ie.left-=r.e}},dragOverAnimationComplete:function(){ce&&(ce=!1,he())},drop:function(t){var e=t.originalEvent,n=t.rootEl,o=t.parentEl,i=t.sortable,r=t.dispatchSortableEvent,a=t.oldIndex,l=t.putSortable,s=l||this.sortable;if(e){var c=i.options,u=o.children;if(!ue)if(c.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),D(oe,c.selectedClass,!~ae.indexOf(oe)),~ae.indexOf(oe))ae.splice(ae.indexOf(oe),1),ee=null,z({sortable:i,rootEl:n,name:"deselect",targetEl:oe,originalEvt:e});else{if(ae.push(oe),z({sortable:i,rootEl:n,name:"select",targetEl:oe,originalEvt:e}),(!c.multiDragKey||this.multiDragKeyDown)&&e.shiftKey&&ee&&i.el.contains(ee)){var d,h,f=N(ee),p=N(oe);if(~f&&~p&&f!==p)for(p>f?(h=f,d=p):(h=p,d=f+1);h<d;h++)~ae.indexOf(u[h])||(D(u[h],c.selectedClass,!0),ae.push(u[h]),z({sortable:i,rootEl:n,name:"select",targetEl:u[h],originalEvt:e}))}else ee=oe;ne=s}if(ue&&this.isMultiDrag){if((o[H].options.sort||o!==n)&&ae.length>1){var g=x(oe),v=N(oe,":not(."+this.options.selectedClass+")");if(!se&&c.animation&&(oe.thisAnimationDuration=null),s.captureAnimationState(),!se&&(c.animation&&(oe.fromRect=g,ae.forEach(function(t){if(t.thisAnimationDuration=null,t!==oe){var e=ce?x(t):g;t.fromRect=e,s.addAnimationState({target:t,rect:e})}})),he(),ae.forEach(function(t){u[v]?o.insertBefore(t,u[v]):o.appendChild(t),v++}),a===N(oe))){var m=!1;ae.forEach(function(t){t.sortableIndex===N(t)||(m=!0)}),m&&r("update")}ae.forEach(function(t){F(t)}),s.animateAll()}ne=s}(n===o||l&&"clone"!==l.lastPutMode)&&le.forEach(function(t){t.parentNode&&t.parentNode.removeChild(t)})}},nullingGlobal:function(){this.isMultiDrag=ue=!1,le.length=0},destroy:function(){this._deselectMultiDrag(),v(document,"pointerup",this._deselectMultiDrag),v(document,"mouseup",this._deselectMultiDrag),v(document,"touchend",this._deselectMultiDrag),v(document,"keydown",this._checkKeyDown),v(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!ue&&ne===this.sortable&&!(t&&w(t.target,this.sortable.options.draggable,this.sortable.el,!1)||t&&0!==t.button))for(;ae.length;){var e=ae[0];D(e,this.sortable.options.selectedClass,!1),ae.shift(),z({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvt:t})}},_checkKeyDown:function(t){t.key===this.sortable.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.sortable.options.multiDragKey&&(this.multiDragKeyDown=!1)}},n(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[H];e&&e.options.multiDrag&&!~ae.indexOf(t)&&(ne&&ne!==e&&(ne.multiDrag._deselectMultiDrag(),ne=e),D(t,e.options.selectedClass,!0),ae.push(t))},deselect:function(t){var e=t.parentNode[H],n=ae.indexOf(t);e&&e.options.multiDrag&&~n&&(D(t,e.options.selectedClass,!1),ae.splice(n,1))}},eventOptions:function(){var t=this,e=[],n=[];return ae.forEach(function(o){var i;e.push({multiDragElement:o,index:o.sortableIndex}),i=ce&&o!==oe?-1:ce?N(o,":not(."+t.options.selectedClass+")"):N(o),n.push({multiDragElement:o,index:i})}),{items:a(ae),clones:[].concat(le),oldIndicies:e,newIndicies:n}},optionListeners:{multiDragKey:function(t){return"ctrl"===(t=t.toLowerCase())?t="Control":t.length>1&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}),kt});