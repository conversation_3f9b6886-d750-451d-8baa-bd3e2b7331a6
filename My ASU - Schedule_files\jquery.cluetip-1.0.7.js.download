(function(f){f.cluetip={version:"1.0.7"};var j,i,h,e,g,b,k,d;f.fn.cluetip=function(m,l){if(typeof m=="object"){l=m;m=null;}if(m=="destroy"){return this.removeData("thisInfo").unbind(".cluetip");}return this.each(function(L){var t=this,w=f(this);var H=f.extend(true,{},f.fn.cluetip.defaults,l||{},f.metadata?w.metadata():f.meta?w.data():{});var p=false;var A=+H.cluezIndex;w.data("thisInfo",{title:t.title,zIndex:A});var W=false,V=0;if(!f("#cluetip").length){f(['<div id="cluetip">','<div id="cluetip-outer">','<h3 id="cluetip-title"></h3>','<div id="cluetip-inner"></div>',"</div>",'<div id="cluetip-extra"></div>','<div id="cluetip-arrows" class="cluetip-arrows"></div>',"</div>"].join(""))[c](a).hide();j=f("#cluetip").css({position:"absolute"});h=f("#cluetip-outer").css({position:"relative",zIndex:A});i=f("#cluetip-inner");e=f("#cluetip-title");g=f("#cluetip-arrows");b=f('<div id="cluetip-waitimage"></div>').css({position:"absolute"}).insertBefore(j).hide();}var K=(H.dropShadow)?+H.dropShadowSteps:0;if(!k){k=f([]);for(var aa=0;aa<K;aa++){k=k.add(f("<div></div>").css({zIndex:A-1,opacity:0.1,top:1+aa,left:1+aa}));}k.css({position:"absolute",backgroundColor:"#000"}).prependTo(j);}var F=w.attr(H.attribute),s=H.cluetipClass;if(!F&&!H.splitTitle&&!m){return true;}if(H.local&&H.localPrefix){F=H.localPrefix+F;}if(H.local&&H.hideLocal){f(F+":first").hide();}var G=parseInt(H.topOffset,10),C=parseInt(H.leftOffset,10);var B,X,y=isNaN(parseInt(H.height,10))?"auto":(/\D/g).test(H.height)?H.height:H.height+"px";var n,u,M,ae,O,Y;var z=parseInt(H.width,10)||275,ab=z+(parseInt(j.css("paddingLeft"),10)||0)+(parseInt(j.css("paddingRight"),10)||0)+K,D=this.offsetWidth,v,N,af,P,o;var U;var I=(H.attribute!="title")?w.attr(H.titleAttribute):"";if(H.splitTitle){if(I==undefined){I="";}U=I.split(H.splitTitle);I=U.shift();}if(H.escapeTitle){I=I.replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;");}var r;function ad(){return false;}var x=function(ag){if(!H.onActivate(w,ag)){return false;}W=true;j.removeClass().css({width:z});if(F==w.attr("href")){w.css("cursor",H.cursor);}if(H.hoverClass){w.addClass(H.hoverClass);}u=M=w.offset().top;v=w.offset().left;P=ag.pageX;O=ag.pageY;if(t.tagName.toLowerCase()!="area"){n=f(document).scrollTop();o=f(window).width();}if(H.positionBy=="fixed"){N=D+v+C;j.css({left:N});}else{N=(D>v&&v>ab)||v+D+ab+C>o?v-ab-C:D+v+C;if(t.tagName.toLowerCase()=="area"||H.positionBy=="mouse"||D+ab>o){if(P+20+ab>o){j.addClass(" cluetip-"+s);N=(P-ab-C)>=0?P-ab-C-parseInt(j.css("marginLeft"),10)+parseInt(i.css("marginRight"),10):P-(ab/2);}else{N=P+C;}}var ah=N<0?ag.pageY+G:ag.pageY;j.css({left:(N>0&&H.positionBy!="bottomTop")?N:(P+(ab/2)>o)?o/2-ab/2:Math.max(P-(ab/2),0),zIndex:w.data("thisInfo").zIndex});g.css({zIndex:w.data("thisInfo").zIndex+1});}X=f(window).height();if(m){if(typeof m=="function"){m=m.call(t);}i.html(m);T(ah);}else{if(U){var aj=U.length;i.html(aj?U[0]:"");if(aj>1){for(var ai=1;ai<aj;ai++){i.append('<div class="split-body">'+U[ai]+"</div>");}}T(ah);}else{if(!H.local&&F.indexOf("#")!==0){if(/\.(jpe?g|tiff?|gif|png)$/i.test(F)){i.html('<img src="'+F+'" alt="'+I+'" />');T(ah);}else{if(p&&H.ajaxCache){i.html(p);T(ah);}else{var an=H.ajaxSettings.beforeSend,ak=H.ajaxSettings.error,al=H.ajaxSettings.success,aq=H.ajaxSettings.complete;var ap={cache:false,url:F,beforeSend:function(ar){if(an){an.call(t,ar,j,i);}h.children().empty();if(H.waitImage){b.css({top:O+20,left:P+20,zIndex:w.data("thisInfo").zIndex-1}).show();}},error:function(ar,at){if(W){if(ak){ak.call(t,ar,at,j,i);}else{i.html("<i>Sorry, the contents could not be loaded. Please try refreshing the page.</i>");}}},success:function(ar,at){p=H.ajaxProcess.call(t,ar);if(W){if(p==""){this.error();}else{if(al){al.call(t,ar,at,j,i);}i.html(p);}}},complete:function(au,aw){if(aq){aq.call(t,au,aw,j,i);}var av=i[0].getElementsByTagName("img");d=av.length;for(var at=0,ar=av.length;at<ar;at++){if(av[at].complete){d--;}}if(d){f(av).bind("load error",function(){d--;if(d==0){b.hide();if(W){T(ah);}}});}else{b.hide();if(W){T(ah);}}}};var am=f.extend(true,{},H.ajaxSettings,ap);f.ajax(am);}}}else{if(H.local){var ao=f(F+(/^#\S+$/.test(F)?"":":eq("+L+")")).clone(true).show();if(ao.css("display")===""){ao.css("display","block");}i.html(ao);T(ah);}}}}};var T=function(ai){j.addClass("cluetip-"+s);if(H.truncate){var aj=i.text().slice(0,H.truncate)+"...";i.html(aj);}function ag(){}I?e.show().html(I):(H.showTitle)?e.show().html("&nbsp;"):e.hide();if(H.sticky){var ah=f('<div id="cluetip-close"><a href="#">'+H.closeText+"</a></div>");(H.closePosition=="bottom")?ah.appendTo(i):(H.closePosition=="title")?ah.prependTo(e):ah.prependTo(i);ah.bind("click.cluetip",function(){E();return false;});if(H.mouseOutClose){j.bind("mouseleave.cluetip",function(){E();});}else{j.unbind("mouseleave.cluetip");}}var ak="";h.css({zIndex:w.data("thisInfo").zIndex,overflow:y=="auto"?"visible":"auto",height:y});B=y=="auto"?Math.max(j.outerHeight(),j.height()):parseInt(y,10);ae=M;Y=n+X;if(H.positionBy=="fixed"){ae=M-H.dropShadowSteps+G;}else{if((N<P&&Math.max(N,0)+ab>P)||H.positionBy=="bottomTop"){if(M+B+G>Y&&O-n>B+G){ae=O-B-G;ak="top";}else{ae=O+G;ak="bottom";}}else{if(M+B+G>Y){ae=(B>=X)?n:Y-B-G;}else{if(w.css("display")=="block"||t.tagName.toLowerCase()=="area"||H.positionBy=="mouse"){ae=ai-G;}else{ae=M-H.dropShadowSteps;}}}}if(ak==""){N<v?ak="left":ak="right";}j.css({top:ae+"px"}).removeClass().addClass("clue-"+ak+"-"+s).addClass(" cluetip-"+s);if(H.arrows){var al=(M-ae-H.dropShadowSteps);g.css({top:(/(left|right)/.test(ak)&&N>=0&&al>0)?al+"px":/(left|right)/.test(ak)?0:""}).show();}else{g.hide();}k.hide();j.hide()[H.fx.open](H.fx.openSpeed||0);if(H.dropShadow){k.css({height:B,width:z,zIndex:w.data("thisInfo").zIndex-1}).show();}if(f.fn.bgiframe){j.bgiframe();}if(H.delayedClose>0){V=setTimeout(E,H.delayedClose);}H.onShow.call(t,j,i);};var ac=function(ag){W=false;b.hide();if(!H.sticky||(/click|toggle/).test(H.activation)){if(H.activation=="hover"){if(H.noDelay==true){f(document).mousemove(J);Z=setInterval(S,0);}else{f(document).mousemove(J);Z=setInterval(S,250);}}else{E();}clearTimeout(V);}if(H.hoverClass){w.removeClass(H.hoverClass);}};var E=function(){h.parent().hide().removeClass();H.onHide.call(t,j,i);w.removeClass("cluetip-clicked");if(I){w.attr(H.titleAttribute,I);}w.css("cursor","");if(H.arrows){g.css({top:""});}};f(document).bind("hideCluetip",function(ag){E();});var R=0,Q=0;var J=function(ag){R=ag.pageX;Q=ag.pageY;};var Z;var S=function(){var ag=h.offset();if(R<ag.left||R>ag.left+h.width()||Q<ag.top||Q>ag.top+h.height()){E();clearTimeout(Z);f(document).unbind("mousemove",J);}};if((/click|toggle/).test(H.activation)){w.bind("click.cluetip",function(ag){if(j.is(":hidden")||!w.is(".cluetip-clicked")){x(ag);f(".cluetip-clicked").removeClass("cluetip-clicked");w.addClass("cluetip-clicked");}else{ac(ag);}this.blur();return false;});}else{if(H.activation=="focus"){w.bind("focus.cluetip",function(ag){x(ag);});w.bind("blur.cluetip",function(ag){ac(ag);});}else{w[H.clickThrough?"unbind":"bind"]("click",ad);var q=function(ag){if(H.tracking==true){var ai=N-ag.pageX;var ah=ae?ae-ag.pageY:M-ag.pageY;w.bind("mousemove.cluetip",function(aj){j.css({left:aj.pageX+ai,top:aj.pageY+ah});});}};if(f.fn.hoverIntent&&H.hoverIntent){w.hoverIntent({sensitivity:H.hoverIntent.sensitivity,interval:H.hoverIntent.interval,over:function(ag){x(ag);q(ag);},timeout:H.hoverIntent.timeout,out:function(ag){ac(ag);w.unbind("mousemove.cluetip");}});}else{w.bind("mouseenter.cluetip",function(ag){x(ag);q(ag);}).bind("mouseleave.cluetip",function(ag){ac(ag);w.unbind("mousemove.cluetip");});}w.bind("mouseover.cluetip",function(ag){w.attr("title","");}).bind("mouseleave.cluetip",function(ag){w.attr("title",w.data("thisInfo").title);});}}});};f.fn.cluetip.defaults={width:275,height:"auto",cluezIndex:510,positionBy:"auto",topOffset:15,leftOffset:15,local:false,localPrefix:null,hideLocal:true,attribute:"rel",titleAttribute:"title",splitTitle:"",escapeTitle:false,showTitle:true,cluetipClass:"default",hoverClass:"",waitImage:true,cursor:"",arrows:false,dropShadow:true,dropShadowSteps:6,sticky:false,mouseOutClose:false,activation:"click",clickThrough:false,tracking:false,delayedClose:0,closePosition:"top",closeText:"Close",truncate:0,fx:{open:"show",openSpeed:""},hoverIntent:{sensitivity:3,interval:50,timeout:0},onActivate:function(l){return true;},onShow:function(m,l){},onHide:function(m,l){},ajaxCache:true,ajaxProcess:function(l){l=l.replace(/<(script|style|title)[^<]+<\/(script|style|title)>/gm,"").replace(/<(link|meta)[^>]+>/g,"");return l;},ajaxSettings:{dataType:"html"},debug:false};var c="appendTo",a="body";f.cluetip.setup=function(l){if(l&&l.insertionType&&(l.insertionType).match(/appendTo|prependTo|insertBefore|insertAfter/)){c=l.insertionType;}if(l&&l.insertionElement){a=l.insertionElement;}};})(jQuery);