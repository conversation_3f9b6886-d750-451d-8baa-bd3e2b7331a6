var tablists={};var tabs={};var tabpanels={};var tabScopes=[];var tabKeys={end:35,home:36,left:37,up:38,right:39,down:40,enter:13,space:32,tab:9};var tabDirection={37:-1,38:-1,39:1,40:1};$(window).keyup(function(h){var g=(h.keyCode?h.keyCode:h.which);if(g==9){var f=document.getElementById("myclasses_dropdown");if(f!=null){if(document.activeElement==f){f.classList.add("dropdown-focused");return;}else{f.classList.remove("dropdown-focused");}}var j=document.getElementById("schedule_dropdown");if(j!=null){if(document.activeElement==j){j.classList.add("dropdown-focused");return;}else{j.classList.remove("dropdown-focused");}}var d=document.getElementById("fayears_dropdown");if(d!=null){if(document.activeElement==d){d.classList.add("dropdown-focused");return;}else{d.classList.remove("dropdown-focused");}}for(var c=0;c<tabScopes.length;c++){var a=tabScopes[c];for(var b=0;b<tabs[a].length;b++){if(document.activeElement==tabs[a][b]){tabs[a][b].classList.add("box-tab-focused");return;}}}}});$(document).mouseup(function(g){var f=document.getElementById("myclasses_dropdown");if(f!=null){f.classList.remove("dropdown-focused");}var h=document.getElementById("schedule_dropdown");if(h!=null){h.classList.remove("dropdown-focused");}var d=document.getElementById("fayears_dropdown");if(d!=null){d.classList.remove("dropdown-focused");}for(var c=0;c<tabScopes.length;c++){var a=tabScopes[c];for(var b=0;b<tabs[a].length;b++){tabs[a][b].classList.remove("box-tab-focused");}}});function initializeTablist(a){tabScopes.push(a);if(tablists[a]){for(var c=0;c<tabs[a].length;c++){tabs[a][c].removeEventListener("click",clickEventListener);tabs[a][c].removeEventListener("keydown",keydownEventListener);tabs[a][c].removeEventListener("keyup",keyupEventListener);}}tablists[a]=document.getElementById(a+"_tablist");tabs[a]=[];var d={};d[a]=tablists[a].querySelectorAll('[role="tab"]');var b=0;for(c=0;c<d[a].length;c++){if(d[a][c].getAttribute("hidden")!==null||d[a][c].style.display=="none"){continue;}tabs[a].push(d[a][c]);tabs[a][b].addEventListener("click",clickEventListener);tabs[a][b].addEventListener("keydown",keydownEventListener);tabs[a][b].addEventListener("keyup",keyupEventListener);tabs[a][b].index=b;b++;}tabpanels[a]=document.getElementById(a).querySelectorAll('[role="tabpanel"]');$("#"+a+"_title_bar").addClass("box-title-has-tabs");}function manualActiveTab(b,a){if(b){activateTab(b,a);}}function clickEventListener(b){var a=b.target;activateTab(a,false);}function keydownEventListener(c){var b=c.keyCode;var a=c.target.getAttribute("data-tab-scope");switch(b){case tabKeys.end:c.preventDefault();focusLastTab(a);break;case tabKeys.home:c.preventDefault();focusFirstTab(a);break;case tabKeys.up:case tabKeys.down:determineOrientation(c);break;case tabKeys.tab:removeFocusClassFromTabs(a);break;}}function keyupEventListener(b){var a=b.keyCode;switch(a){case tabKeys.left:case tabKeys.right:determineOrientation(b);break;case tabKeys.enter:case tabKeys.space:activateTab(b.target);break;}}function determineOrientation(e){var c=e.keyCode;var b=e.target.getAttribute("data-tab-scope");var a=tablists[b].getAttribute("aria-orientation")=="vertical";var d=false;if(a){if(c===tabKeys.up||c===tabKeys.down){e.preventDefault();d=true;}}else{if(c===tabKeys.left||c===tabKeys.right){d=true;}}if(d){switchTabOnArrowPress(e);}}function switchTabOnArrowPress(b){var c=b.keyCode;if(tabDirection[c]){var d=b.target;var a=b.target.getAttribute("data-tab-scope");removeFocusClassFromTabs(a);if(d.index!==undefined){if(tabs[a][d.index+tabDirection[c]]){tabs[a][d.index+tabDirection[c]].focus();tabs[a][d.index+tabDirection[c]].classList.add("box-tab-focused");}else{if(c===tabKeys.left||c===tabKeys.up){focusLastTab(a);}else{if(c===tabKeys.right||c==tabKeys.down){focusFirstTab(a);}}}}}}function activateTab(d,c){var b=d.getAttribute("data-tab-scope");var a=d.getAttribute("aria-controls");if(c==null){c=true;}if(b){deactivateTabs(b,a);}d.removeAttribute("tabindex");d.setAttribute("aria-selected","true");d.classList.remove("box-tab-focused");d.classList.add("box-tab-selected");if(a){$("#"+a).show();}if(c){d.focus();}}function deactivateTabs(b,a){for(var c=0;c<tabs[b].length;c++){tabs[b][c].setAttribute("tabindex","-1");tabs[b][c].setAttribute("aria-selected","false");tabs[b][c].classList.remove("box-tab-selected");}if(a){for(var d=0;d<tabpanels[b].length;d++){$(tabpanels[b][d]).hide();}}}function removeFocusClassFromTabs(a){for(var b=0;b<tabs[a].length;b++){tabs[a][b].classList.remove("box-tab-focused");}}function focusFirstTab(a){tabs[a][0].focus();tabs[a][0].classList.add("box-tab-focused");}function focusLastTab(a){tabs[a][tabs[a].length-1].focus();tabs[a][tabs[a].length-1].classList.add("box-tab-focused");}