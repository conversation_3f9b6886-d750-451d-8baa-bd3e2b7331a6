@echo off
chcp 65001 >nul
title ASU学生信息快速更新

echo.
echo ================================================
echo        ASU学生信息快速更新 - 简化版
echo ================================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查必要文件是否存在
if not exist "My ASU.html" (
    echo 错误: 未找到 "My ASU.html" 文件
    echo 请确保该文件在当前目录中
    pause
    exit /b 1
)

if not exist "My ASU - Schedule.html" (
    echo 错误: 未找到 "My ASU - Schedule.html" 文件
    echo 请确保该文件在当前目录中
    pause
    exit /b 1
)

REM 运行Python脚本
python quick_update.py

echo.
echo 按任意键退出...
pause >nul
