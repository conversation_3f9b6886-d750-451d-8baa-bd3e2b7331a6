(function(global, factory) {
	var args = window[window['NarrowcastingCarouselWidgetObject']].q;
	var carousel_widget_jq;
	let gaLoadAttempts = 0;
	function load() {
		if (typeof carousel_widget_jq === 'undefined' || carousel_widget_jq.fn.jquery !== '3.5.1') {
			global.setTimeout(load, 50);

		} else if (typeof global.Mustache === 'undefined') {
			global.setTimeout(load, 50);

		} else if (typeof global.AdsClient === 'undefined') {
			global.setTimeout(load, 50);

		} else if (typeof global._gat === 'undefined' && gaLoadAttempts < 20) {
			global.setTimeout(load, 50);
			gaLoadAttempts++;

			if(gaLoadAttempts >= 20) {
				console.log('Failed to load Legacy Google Analytics. Load a mock ga-tracker.');
				global._gat = {};
				global._gat._getTracker = function() {
					let mockPageTracker = {};
					mockPageTracker._setDomainName = function(param1) {};
					mockPageTracker._initData =  function() {};
					return mockPageTracker;
				};
			}

		} else {
			if (typeof global.NarrowcastingCarouselWidget === 'undefined') {
				global.NarrowcastingCarouselWidget = {};
				factory(global.NarrowcastingCarouselWidget, carousel_widget_jq, Mustache, AdsClient, _gat);
			}
			global.NarrowcastingCarouselWidget.init(args[0], args[1], args[2], args[3]);
		}
	}

	function resolveJQueryNoConflict() {
		// JQuery loaded, perform jQuery.noConflict to prevent conflicts of multiple JQ instances
		if(global.jQuery.fn.jquery === '3.5.1') {
			carousel_widget_jq =  global.jQuery.noConflict(true);
		}
	}

	function require(src, head, callback) {
		let script = document.createElement('script');
		script.type = 'text/javascript';
		script.src = src;
		script.async = false;
		script.onload = callback;
		head.appendChild(script);
	}

	if (typeof global.NarrowcastingCarouselWidget === 'undefined') {
		let head = document.getElementsByTagName('head')[0] || document.documentElement

		let link = document.createElement('link');
		link.rel = 'stylesheet';
		link.type = 'text/css';
		link.href = args[0] + '/widget/narrowcasting/carousel-widget.css';
		link.media = 'screen';
		head.appendChild(link);

		if (typeof global.jQuery === 'undefined' || global.jQuery.fn.jquery !== '3.5.1') {
			require('https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js', head, resolveJQueryNoConflict);
		}
		if (typeof global.Mustache === 'undefined') {
			require('https://cdnjs.cloudflare.com/ajax/libs/mustache.js/4.0.1/mustache.min.js', head);
		}
		if (typeof global.AdsClient === 'undefined') {
			require(args[0] + '/resources/js/carousel-widget-client.js', head);
		}
		if (typeof global._gat === 'undefined') {
			require('https://ssl.google-analytics.com/ga.js', head);
		}
	}
	load();

})(this, function(widget, carousel_widget_jq, mustache, adsClient, analytics) {

	function isColorHex(hexStr) {
		let colorLength = hexStr.length;
		let hexRegex = /^[0-9a-fA-F]+$/;
		return (hexRegex.test(hexStr));
	}

	function CarouselView() {};

	CarouselView.prototype.init = function() {
		this.node = carousel_widget_jq('#nc_canvas');
		this.btnBar = carousel_widget_jq('#btn_bar');
		this.btnPrevious = carousel_widget_jq('#btn_previous');
		this.btnPlayPause = carousel_widget_jq('#btn_pausePlay');
		this.btnDismiss = carousel_widget_jq('#btn_dismiss');
		this.slidePrevious = carousel_widget_jq('#slide_previous');
		this.slideCurrent = carousel_widget_jq('#slide_current');
		this.slideNext = carousel_widget_jq('#slide_next');
	}

	CarouselView.prototype.ready = function() {
		let self = this;
		if(carouselState.conveyorPaused || !slideIterator.hasNext()) {
			carouselState.conveyorPaused = true;
			clearInterval(carouselState.conveyorInterval);
		} else {
			console.log(carouselState);
			console.log(self);
			console.log(self.onConveyorLoop);
			carouselState.conveyorInterval = setInterval(self.onConveyorLoop, carouselState.conveyorDuration);
		}

		// pause carousel on mouse-over
		this.node.mousemove(function() {
			clearInterval(carouselState.conveyorInterval);
			clearTimeout(carouselState.mouseoverTimeout);
			carouselState.mouseHovering = true;
			carouselState.mouseoverTimeout = setTimeout(function() {
				if(!carouselState.conveyorPaused && carouselState.mouseHovering) {
					carouselState.conveyorInterval = setInterval(self.onConveyorLoop, carouselState.conveyorDuration);
					carouselState.mouseHovering = false;
				}
			}, 2000);
		});

		self.btnPrevious.removeClass('btn_pending');
		self.btnPrevious.click(function() {
			carouselView.previous();
		})
		self.btnDismiss.removeClass('btn_pending');
		self.btnDismiss.click(function() {
			carouselView.dismiss();
		});
		self.btnPlayPause.removeClass('btn_pending');
		self.btnPlayPause.click(function() {
			carouselView.pausePlay();
		});
	}

	CarouselView.prototype.loadPublications = function(zoneId) {
		return new Promise(function(resolve, reject) {
			adsClient.loadAvailablePublicationsForZone(zoneId).then(function(response) {
				response.data.forEach(publication => {
					let bgColor = publication.backgroundColor;
					if(!bgColor) {
						bgColor = '#FFFFFF';
					} else {
						if(isColorHex(bgColor) && !bgColor.startsWith('#')) {
							if(bgColor.length != 3 && bgColor.length != 6 && bgColor.length != 8) {
								publication.backgroundColor = bgColor.substring(0, 3);
							}
							publication.backgroundColor = '#' + publication.backgroundColor;
						}
					}
				});
				carouselState.slides = response.data;
				carouselState.slideCount = response.data.length;
				resolve();
			}).catch(function(err) {
				console.log('Failed to load publications:');
				console.log(err);
				if(err.status == 401) {
					console.log('Unauthorized... requesting public publications instead.');
					adsClient.setJwt(null);
					adsClient.loadAvailablePublicationsForZone(zoneId).then(function(response) {
						carouselState.slides = response.data;
						carouselState.slideCount = response.data.length;
						resolve();
					}).catch(function(err) {
						console.log('Failed to load public publications:');
						console.log(err);
						carouselState.slides = [];
						carouselState.slideCount = 0;
						reject(err);
					});
				} else {
					console.log('Some non-401 error occured... do not show ads.');
					carouselState.slides = [];
					carouselState.slideCount = 0;
					reject(err);
				}
			}); // end -- catch [loadAvailablePublicationsForZone]
		}); // end -- return Promise()
	} // end -- CarouselView.loadPublications

	CarouselView.prototype.render = function() {
		let self = this;

		if(carouselState.slides.length == 0) {
			self.btnBar.hide();
			self.btnPrevious.hide();
			self.btnDismiss.hide();
			self.btnPlayPause.hide();

		} else {
			let defaultView = carousel_widget_jq('#default_view');
			defaultView.fadeOut();

			let slideCurrentView = carousel_widget_jq('#slide_current');
			slideCurrentView.fadeIn();

			if(carouselState.slides.length == 1) {
				let randomFirstSlide = 0;
				carouselState.currentSlideIndex = randomFirstSlide;

				self.setSlideContent(self.slideCurrent, slideIterator.getCurrent());
				self.node.get(0).style.backgroundColor = slideIterator.getCurrent().backgroundColor;

				self.slideCurrent.get(0).style.left = "0px";
				self.slideNext.get(0).style.left = carouselState.slideWidth + "px";
				self.slidePrevious.get(0).style.left = "-" + carouselState.slideWidth + "px";
			} // end if -- slides == 1

			else if(carouselState.slides.length > 1) {
				let randomFirstSlide = 0;
				carouselState.currentSlideIndex = randomFirstSlide;

				self.setSlideContent(self.slideCurrent, slideIterator.getCurrent());
				self.setSlideContent(self.slideNext, slideIterator.getNext());
				self.setSlideContent(self.slidePrevious, slideIterator.getPrevious());
				self.node.get(0).style.backgroundColor = slideIterator.getCurrent().backgroundColor;

				self.slideCurrent.get(0).style.left = "0px";
				self.slideNext.get(0).style.left = carouselState.slideWidth + "px";
				self.slidePrevious.get(0).style.left = "-" + carouselState.slideWidth + "px";

				if(carouselState.conveyorPaused) {
					console.log('showing the ability to play')
					self.btnPlayPause.removeClass('btn_pause')
					self.btnPlayPause.addClass('btn_play')
				} else {
					console.log('showing the ability to pause')
					self.btnPlayPause.removeClass('btn_play')
					self.btnPlayPause.addClass('btn_pause')
				}
			} // end else-if -- slides count > 1
			else {
				self.btnPrevious.hide();
				self.btnDismiss.hide();
				self.btnPlayPause.hide();
			} // end -- slides count undefined
		}
	}

	CarouselView.prototype.setSlideContent = function(element, slide) {
		if(element) {
			let slideTemplate = document.getElementById('nc_carousel_ad_template');
			element.html(Mustache.render(slideTemplate.innerHTML, slide));
		}
	}

	CarouselView.prototype.onConveyorLoop = function() {
		carouselView.next();
	}

	CarouselView.prototype.next = function() {
		let self = this;
		if(slideIterator.hasNext() && !slideIterator.busy) { // don't iterate on single-ad zones
			slideIterator.busy = true;

			//change background
			let nextSlide = slideIterator.getNext();
			self.node.get(0).style.backgroundColor = nextSlide.backgroundColor;

			//animate to next slide
			let moveCurrentToPrevious = self.slideCurrent.animate({left: "-" + carouselState.slideWidth + "px"}, 300).promise();
			let moveNextToCurrent = self.slideNext.animate({left: "0px"}, 300).promise();

			//after completing all animations: iterate state to next slide
			Promise.all([moveCurrentToPrevious, moveNextToCurrent]).then(function() {
				slideIterator.moveNext();

				let soonToBeCurrent = self.slideNext;
				self.slideCurrent.attr('id', "slide_previous");
				self.slidePrevious.attr('id', "slide_next");
				soonToBeCurrent.attr('id', "slide_current");

				self.slidePrevious = carousel_widget_jq('#slide_previous');
				self.slideCurrent = carousel_widget_jq('#slide_current');
				self.slideNext = carousel_widget_jq('#slide_next');

				self.slideCurrent.get(0).style.left = "0px";
				self.slideNext.get(0).style.left = carouselState.slideWidth + "px";
				self.slidePrevious.get(0).style.left = "-" + carouselState.slideWidth + "px";

				self.setSlideContent(self.slideNext, slideIterator.getNext());
				self.setSlideContent(self.slidePrevious, slideIterator.getPrevious());
				slideIterator.busy = false;
			}); // end then -- promiseAll
		} // end if -- not busy and has a next slide
	} // end -- CarouselView.next()

	CarouselView.prototype.previous = function() {
		let self = this;
		if(slideIterator.hasNext() && !slideIterator.busy) { // don't iterate on single-ad zones
			slideIterator.busy = true;

			//change background
			let previousSlide = slideIterator.getPrevious();
			self.node.get(0).style.backgroundColor = previousSlide.backgroundColor;

			//animate to next slide
			let moveCurrentToNext = self.slideCurrent.animate({left: carouselState.slideWidth + "px"}, 300).promise();
			let movePreviousToCurrent = self.slidePrevious.animate({left: "0px"}, 300).promise();

			//after completing all animations: iterate state to next slide
			Promise.all([moveCurrentToNext, movePreviousToCurrent]).then(function() {
				slideIterator.movePrevious();

				let soonToBeCurrent = self.slidePrevious;
				self.slideCurrent.attr('id', "slide_next");
				self.slideNext.attr('id', "slide_previous");
				soonToBeCurrent.attr('id', "slide_current");

				self.slidePrevious = carousel_widget_jq('#slide_previous');
				self.slideCurrent = carousel_widget_jq('#slide_current');
				self.slideNext = carousel_widget_jq('#slide_next');

				self.slideCurrent.get(0).style.left = "0px";
				self.slideNext.get(0).style.left = carouselState.slideWidth + "px";
				self.slidePrevious.get(0).style.left = "-" + carouselState.slideWidth + "px";

				self.setSlideContent(self.slideNext, slideIterator.getNext());
				self.setSlideContent(self.slidePrevious, slideIterator.getPrevious());

				slideIterator.busy = false;
			})
		}
	}

	CarouselView.prototype.dismiss = function() {
		let self = this;
		if(!slideIterator.busy) { // don't iterate on single-ad zones
			slideIterator.busy = true;
			let currentSlide = slideIterator.getCurrent();
			adsClient.dismissPublicationWithId(currentSlide.id).then(function(response) {
				console.log("Dismissed publication:");
				console.log(response);
			}).catch(function(err) {
				console.log('Failed to dismiss publication:');
				console.log(err);
			});

			//change background
			let nextSlide = slideIterator.getNext();
			self.node.get(0).style.backgroundColor = nextSlide.backgroundColor;

			//animate to next slide
			let dismissCurrent = self.slideCurrent.animate({opacity: "0%", paddingTop: "50px"}, 500).promise();

			if(carouselState.slides.length == 1) {
				Promise.all([dismissCurrent]).then(function() {
					self.btnBar.fadeOut();
					self.btnPrevious.fadeOut();
					self.btnPlayPause.fadeOut();
					self.btnDismiss.fadeOut();
					self.node.get(0).style.backgroundColor = '#FFC627';

					let defaultView = carousel_widget_jq('#default_view');
					defaultView.fadeIn();

					let slideCurrentView = carousel_widget_jq('#slide_next');
					slideCurrentView.fadeOut();
				});
			// end if -- only one slide
			} else {
				let moveNextToCurrent = self.slideNext.animate({left: "0px"}, 300).promise()
				//after completing all animations: iterate state to next slide
				Promise.all([dismissCurrent, moveNextToCurrent]).then(function() {
					slideIterator.dismissCurrentMoveNext()

					let soonToBeCurrent = self.slideNext;
					self.slideCurrent.attr('id', "slide_next");
					soonToBeCurrent.attr('id', "slide_current");

					self.slidePrevious = carousel_widget_jq('#slide_previous');
					self.slideCurrent = carousel_widget_jq('#slide_current');
					self.slideNext = carousel_widget_jq('#slide_next');

					self.slideCurrent.get(0).style.left = "0px";
					self.slideNext.get(0).style.left = carouselState.slideWidth + "px";
					self.slideNext.get(0).style.opacity = "100%";
					self.slideNext.get(0).style.paddingTop = "0px";
					self.slidePrevious.get(0).style.left = "-" + carouselState.slideWidth + "px";
				}); // end -- promise.all()

				self.setSlideContent(self.slideNext, slideIterator.getNext());
				self.setSlideContent(self.slidePrevious, slideIterator.getPrevious());
				slideIterator.busy = false;
			} // end if -- more than one slide
		} // end if -- slide not busy
	} // end - Carouse.dismiss()

	CarouselView.prototype.pausePlay = function() {
		console.log('on pause/play');
		let self = this;
		if(carouselState.conveyorPaused) {
			console.log('on play');
			localStorage.removeItem("conveyorPaused");
			this.btnPlayPause.removeClass('btn_play');
			this.btnPlayPause.addClass('btn_pause');
			carouselState.conveyorPaused = false;
			if(!carouselState.mouseHovering) {
				carouselState.conveyorInterval = setInterval(self.onConveyorLoop, carouselState.conveyorDuration);
			} // end if -- mouseHovering
		// end if -- conveyorPaused
		} else {
			console.log('on pause');
			localStorage.setItem("conveyorPaused", "true");
			this.btnPlayPause.removeClass('btn_pause');
			this.btnPlayPause.addClass('btn_play');
			carouselState.conveyorPaused = true;
			clearInterval(carouselState.conveyorInterval);
		} // end else -- conveyor paused
	} // end CaraouselView.pausePlay()

	function SlideIterator() {
		this.busy = false;
	}

	SlideIterator.prototype.getCurrent = function() {
		return carouselState.slides[carouselState.currentSlideIndex];
	}

	SlideIterator.prototype.hasNext = function() {
		if(carouselState.slideCount > 1) {
			return true;
		} else {
			return false;
		}
	}

	SlideIterator.prototype.getNext = function() {
		if(carouselState.currentSlideIndex == carouselState.slideCount - 1) {
			return carouselState.slides[0];
		} else {
			return carouselState.slides[carouselState.currentSlideIndex + 1];
		}
	}

	SlideIterator.prototype.getPrevious = function() {
		if(carouselState.currentSlideIndex == 0){
			return carouselState.slides[carouselState.slideCount - 1];
		} else {
			return carouselState.slides[carouselState.currentSlideIndex - 1];
		}
	}

	SlideIterator.prototype.moveNext = function() {
		if(carouselState.currentSlideIndex == carouselState.slideCount - 1) {
			carouselState.currentSlideIndex = 0;
		} else {
			carouselState.currentSlideIndex = carouselState.currentSlideIndex + 1;
		}
	}

	SlideIterator.prototype.dismissCurrentMoveNext = function() {
		carouselState.slides.splice(carouselState.currentSlideIndex, 1);
		carouselState.slideCount = carouselState.slideCount - 1;
		if(carouselState.currentSlideIndex == carouselState.slideCount) {
			carouselState.currentSlideIndex = 0;
		}
	}

	SlideIterator.prototype.movePrevious = function() {
		if(carouselState.currentSlideIndex == 0){
			carouselState.currentSlideIndex = carouselState.slideCount - 1;
		} else {
			carouselState.currentSlideIndex = carouselState.currentSlideIndex - 1;
		}
	}

	function CarouselState() {};

	CarouselState.prototype.init = function() {
		var self = this;
		self.conveyorPaused = false;
		self.conveyorDuration = 5000;
		self.conveyorInterval = {};
		self.conveyorDirection = 1;
		self.mouseoverTimeout = {};
		self.mouseHovering = false;
		self.slideHeight = 250;
		self.slideWidth = 700;
		self.slideCount = 0;
		self.slides = [];
		self.currentSlideIndex = 0;
		self.currentSlide = {};
		if (localStorage.getItem("conveyorPaused")) {
			console.log('loaded state - conveyor paused');
			self.conveyorPaused = true;
			console.log(self)
		} else {
			console.log('loaded state - conveyor not paused');
			self.conveyorPaused = false;
		}
	}

	CarouselState.prototype.loadState = function() {
		let self = this;
		return new Promise(function(resolve, reject) {
			//load dismissals
			adsClient.loadPublicationDimsissals();
			resolve();
		});
	}

	var carouselView = new CarouselView();
	var carouselState = new CarouselState();
	var slideIterator = new SlideIterator();
	var widgetBaseUrl;
	var pageTracker;

	widget.init = function(baseUrl, element, jwt, zone) {
		widgetBaseUrl = baseUrl;
		carousel_widget_jq.get(widgetBaseUrl + "/widget/narrowcasting/carousel-widget.html?v=2").then(function(template) {
			carousel_widget_jq(element).append(template);
			let defaultView = document.getElementById('default_view');
			let defaultImg = document.createElement('img');
			defaultImg.setAttribute('id', 'pending_view_img');
			defaultImg.setAttribute('src', widgetBaseUrl + '/resources/images/content/default.jpg');
			defaultView.appendChild(defaultImg);
			var checkExist = setInterval(function() {
				console.log('checking if element exists: ' + carouselView.node);
				if (carouselView.node) {
					clearInterval(checkExist);
				} else {
					carouselView.init();
					let promisesToLoadCarouselState = [
						carouselView.loadPublications(zone),
						carouselState.loadState()
					];
					Promise.all(promisesToLoadCarouselState).then(function() {
						carousel_widget_jq.when(carouselView.render()).then(carouselView.ready());
						pageTracker = analytics._getTracker("UA-2392647-1");
						pageTracker._setDomainName(".asu.edu");
						pageTracker._initData();
						window.pageTracker = pageTracker;
					}).catch(function(err) {
						console.log("Encountered error on loading carousel state... render the default view with no publications.");
						console.log(err);
						carousel_widget_jq.when(carouselView.render()).then(carouselView.ready());
					}); // end catch -- promisesToLoadCarouselState
				} // end else -- carouselView.node
			}, 100); // end setInterval -- checkExist
			carouselState.init();
		})// end -- get carousel-widget.html
		if(jwt && jwt != 'null') {
			console.log('JWT provided... initialize as an authorized user.');
			adsClient.init(widgetBaseUrl + '/api/rest', jwt);
		} else {
			console.log('JWT provided... initialize as an anonymous user.');
			adsClient.init(widgetBaseUrl + '/api/rest', null);
		}
		window.adsClient = adsClient;
	}// end -- widget.init()
})// END -- factory
