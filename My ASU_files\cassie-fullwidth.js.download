function _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(r, a) { if (r) { if ("string" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }
function _iterableToArray(r) { if ("undefined" != typeof Symbol && null != r[Symbol.iterator] || null != r["@@iterator"]) return Array.from(r); }
function _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }
function _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }
/* everything above is for es5(pre2015) for maximum compatibility although we should probably update all of this to es6 again now that it is in cloudflare */
function getCookie(name) { /* get specified cookie */
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i];
        while (c.charAt(0) == ' ') c = c.substring(1, c.length);
        if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
    }
    return;
    null;
}
function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    var hostname = window.location.hostname;
    var domainParts = hostname.split('.').slice(-2);
    var baseDomain = domainParts.join('.');
    var domainPart = ";domain=" + (hostname === baseDomain ? "." + baseDomain : baseDomain);
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/" + domainPart;
}
var regionCounter = 0;
function getRegion() { /* get region for grabbing us/non-us banner and for cookies */
    var region = getCookie('geoRegion'); /* assign region variable */
    if(region == null && regionCounter < 10100) { /* loose check, if the region cookie doesn't exist, loop back to the start of the function */
        window.setTimeout(getRegion, 500); /* this checks if the region exists every 100 milliseconds */
        regionCounter += 100; /* counter so that we only check for up to 5 seconds. */
    } else { /* region exists */
        if(regionCounter === 10100 && region == null) { /* we defaulted here to the non us banner. */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            window.dataLayer.push({
                "event": "regionDefaulted",
                "action": "regionDefaulted",
                "type": "event",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "Region was defaulted because either no region exists, or the region check timed out after 5 seconds.",
            });
            setCookie('geoRegion', 'UD', 365);
        }
        if(region.length > 2) { /* check if the region is greater than two characters */
            region = region.substring(0,2);/* trim region to two characters so we're grabbing just the country code. */
        }
        return region === 'US' ? 1 : 2; /* if the region is US, return 1(US profile ID), otherwise return 3(non-us profile ID) */
    }
}
function initializeCookieConsent() { /* initializing the cassie cookie consent js */
    function getRootDomain(url) { /* grab the domain */
        try {
            var domain = new URL(url).hostname; /* get url */
            var elems = domain.split("."); /* split url by . */
            var iMax = elems.length - 1; /* get the length of the array minus 1 for subdomain checking*/
            var isSecondLevel = elems.length >= 3 && (elems[iMax] + elems[iMax - 1]).length <= 5; /* does this have a sub domain?  */
            return elems.splice(isSecondLevel ? -3 : -2).join("."); /* return domain */
        } catch (error) {
            return null;
        }
    }
    function loadScript(src, onSuccess, onError) { /* load scripts into the site */
        var script = document.createElement("script"); /* create script html element */
        script.src = src; /* use src variable to set src for script element */
        script.onload = function () {
            if (onSuccess) onSuccess();
        };
        script.onerror = function () {
            if (onError) onError();
        };
        document.head.appendChild(script); /* append script to head element*/
    }
    function initCassieWidget() {
        var ProfileID = getRegion(); /* get the region to get the profile id for loading the correct banner*/
        if(ProfileID == null) { /* does this id exist? */
            window.setTimeout(initCassieWidget, 100); /* loose check, if the profile id doesn't exist, loop back to the start of the function  */
        } else { /* profile id exists */
            var cassieSettings = {
                widgetProfileId: ProfileID,
                languageCode: "",
                licenseKey: "B37C5C3D-FC0C-43E2-A92F-E4392442F945",
                region: "use",
                environment: "production",
                crossDomainConsent: true
            };
            window.CassieWidgetLoader = new CassieWidgetLoaderModule(cassieSettings); /* init cassie widget with the settings above */
        }
    }
    function loadScriptWithFallback() { /* used in tandem with init cassie widget to get the cassie js from our server */
        var rootDomain = getRootDomain(window.location.href); /* root domain i.e. asu.edu in web.asu.edu */
        var scriptUrl = rootDomain ? "https://cscript-cdn-use." + rootDomain + "/loader.js" : "https://cscript-cdn-use.cassiecloud.com/loader.js"; /* get the script url, check for root domain, if it doesn't exist then use the default cassie server */
        loadScript(scriptUrl, function () { /* load the script using the url above, on success, update the resource root domain and initialize the cassie widget. */
            window.cassieResourceRootDomain = rootDomain;
            initCassieWidget();
        }, function () { /* load the script using the url above, on failure, do not update the resource root domain and use the cassie url to initialize the cassie widget. */
            loadScript("https://cscript-cdn-use.cassiecloud.com/loader.js", function () {
                initCassieWidget();
            });
        });
    }
    loadScriptWithFallback(); /* runs when we run the parent init function */
}
function addCookieConsentAccessibility() {
    var MODAL_SHOW_COOKIE_BUTTON_CLASS = "cassie-expand-cookies--container";
    var CLASSES = {
        NECESSARY_EXPAND_ICON: "cassie-expand-cookies--icon--open",
        EXPANDABLE_DESCRIPTION: "cassie-expandable-description--show",
        COOKIE_GROUP_DESCRIPTION: "cassie-cookie-group--description"
    };
    var toggleExpandedState = function (element) { /* toggle cookie text displays with aria labels */
        var expanded = element.getAttribute("aria-expanded") === "true";
        element.setAttribute("aria-expanded", !expanded);
    };
    var toggleTargetVisibility = function (targetId, target) { /* adding styles/classes to show text when expanded */
        var toggleClass = targetId.includes("necessary") ? CLASSES.NECESSARY_EXPAND_ICON : CLASSES.EXPANDABLE_DESCRIPTION;
        target.classList.toggle(toggleClass);
    };
    var setupAriaAttributes = function (element) {
        var description = element.parentElement.querySelector(".".concat(CLASSES.COOKIE_GROUP_DESCRIPTION));
        var targetId = description.id;
        element.setAttribute("aria-controls", targetId);
        var isVisible = window.getComputedStyle(document.getElementById(targetId)).display !== "none";
        element.setAttribute("aria-expanded", isVisible);
        return targetId;
    };
    var handleInteraction = function (element, targetId, event) { /* handling interactions with cookie descriptions for expansion */
        var target = document.getElementById(targetId);
        toggleExpandedState(element); /* expand */
// Cassie JS already handles the click event
        if (event.type === "click") {
            return;
        }
        toggleTargetVisibility(targetId, target); /* show text */
    };
    var setupEventListeners = function (element, targetId) { /* event listeners for accessibility specifically arrow keys */
        element.addEventListener("keydown", function (event) { /* keydown */
            if (event.key === "Enter" || event.key === " ") { /* space or enter keys */
                event.preventDefault(); /* prevent default interaction */
                handleInteraction(element, targetId, event); /* toggle display */
            }
        });
        element.addEventListener("click", function () { /* click events */
            handleInteraction(element, targetId, event); /* handling without the event itself */
        });
    };

    document.addEventListener('CassieSubmitConsent', function (e) { /* submitting consent button on manage cookies screen*/
        window.dataLayer = window.dataLayer || []; /* init datalayer */
        setTimeout(function () { /* small timeout to make sure we have time to get the cookie, and edit it in the US version of the banner. */
            var ProfileID = getRegion(); /* get the region */
            if(ProfileID === 1) { /* us */
                var cookieValue = getCookie("SyrenisGtmConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924").replace(/[\]}"[{]/g,""); /* get the consent cookie, remove the square brackets. */
                cookieValue = cookieValue.replace(/,/g, "|"); /* replace the commas with pipes */
                setCookie('SyrenisGtmConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924', 'security_storage:granted|personalization_storage:granted|functionality_storage:granted|analytics_storage:granted|'+ cookieValue,365) /* edit cookie to include defaults or anything that is not able to be opted out of, then append the value from opt-ins or opt-outs */
                window.dataLayer.push({ /* push event that submission happened to trigger consent updates in GTM */
                    "event": "cassiePreferencesSubmitted",
                    "action": "click",
                    "name": "onclick",
                    "type": "click",
                    "region": "cookie banner",
                    "section": "consent settings",
                    "text": "submit preferences",
                    "component": "link"
                });
            }
            else { /* not us */
                var cookieValue = getCookie("SyrenisGtmConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924").replace(/[\]}"[{]/g,""); /* get the consent cookie, remove the square brackets. */
                cookieValue = cookieValue.replace(/,/g, "|"); /* replace the commas with pipes */
                setCookie('SyrenisGtmConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924', cookieValue,365) /* edit cookie to include defaults or anything that is not able to be opted out of, then append the value from opt-ins or opt-outs */
                window.dataLayer.push({ /* push event that submission happened to trigger consent updates in GTM */
                    "event": "cassiePreferencesSubmitted",
                    "action": "click",
                    "name": "onclick",
                    "type": "click",
                    "region": "cookie banner",
                    "section": "consent settings",
                    "text": "submit preferences",
                    "component": "link"
                });
            }
        }, 750);
    });
    document.addEventListener("CassieModalVisibility", function (e) { /* is the modal visible */
        if (!e.detail) return;
        var showCookiesButtons = document.getElementsByClassName(MODAL_SHOW_COOKIE_BUTTON_CLASS); /* show cookie buttons */
        _toConsumableArray(showCookiesButtons).forEach(function (element) { /* loop through all buttons */
            var targetId = setupAriaAttributes(element);
            setupEventListeners(element, targetId); /* add event listeners to buttons */
        });
    });
    document.addEventListener("CassieTemplateInitialized", function () { /* is cassie initialized */
        var preBannerDiv = document.querySelector("#cassie_pre_banner_text");
        var gpcBannerDiv = document.querySelector(".cassie-gpc-pre-banner--text-container");
        var CloseX = document.createElement("button"); /* creating close button */
        var XIcon = document.createElement("i");
        var gpcCloseX = document.createElement("button"); /* creating close button */
        var gpcXIcon = document.createElement("i");
        XIcon.classList.add("fas"); /* these are all classes for styling the close button */
        XIcon.classList.add("fa-times");
        CloseX.classList.add("close-button");
        CloseX.setAttribute("aria-label", "Close cookie consent banner"); /* aria labels for accessibility */
        CloseX.appendChild(XIcon); /* adds close icon to close button element */
        preBannerDiv.appendChild(CloseX); /* add close button element to the prebanner  */
        CloseX.addEventListener("click", function () { /* close event listener */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            CassieWidgetLoader.Widget.hideBanner(); /* hide banner */
            window.dataLayer.push({ /* push event on close for tracking purposes */
                "event": "modal",
                "action": "close",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "close cross",
                "component": "icon"
            });
        });
        gpcXIcon.classList.add("fas"); /* these are all classes for styling the close button */
        gpcXIcon.classList.add("fa-times");
        gpcCloseX.classList.add("close-button");
        gpcCloseX.setAttribute("aria-label", "Close cookie consent banner"); /* aria labels for accessibility */
        gpcBannerDiv.appendChild(gpcCloseX);
        gpcCloseX.appendChild(gpcXIcon); /* adds close icon to close button element */
        gpcCloseX.addEventListener("click", function () { /* close event listener */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            CassieWidgetLoader.Widget.hideBanner(); /* hide banner */
            window.dataLayer.push({ /* push event on close for tracking purposes */
                "event": "modal",
                "action": "close",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "close cross",
                "component": "icon"
            });
        });
        /* lots of targeting toggle switches and buttons */
        var manageCookieButton = document.querySelector(".cassie-pre-banner.cassie-bottom");
        var allCookieButton = document.getElementById('cassie_accept_all_pre_banner');
        var necessaryCookieButton = document.getElementById('cassie_reject_all_pre_banner');
        var marketingToggle = document.getElementById('cassie_cookie_group_toggle_switch_14');
        var functionalToggle = document.getElementById('cassie_cookie_group_toggle_switch_12');
        var analyticsToggle = document.getElementById('cassie_cookie_group_toggle_switch_13');
        /* true false toggle checks */
        var marketingBool = false;
        var analyticsBool = false;
        var functionalBool = false;
        /* we're adding all of these on init because otherwise they may not exist */
        manageCookieButton.addEventListener('click', function () { /* clicked manage cookies listener */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            window.dataLayer.push({
                "event": "link",
                "action": "click",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "manage cookies",
                "component": "button"
            });
        });
        allCookieButton.addEventListener('click', function () { /* clicked accept all cookies listener */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            window.dataLayer.push({
                "event": "link",
                "action": "click",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "accept all cookies",
                "component": "button"
            });
        });
        necessaryCookieButton.addEventListener('click', function () { /* clicked necessary cookies listener */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            window.dataLayer.push({
                "event": "link",
                "action": "click",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "accept only required cookies",
                "component": "button"
            });
        });
        marketingToggle.addEventListener('click', function () { /* clicked opt-in opt-out toggle for marketing */
            window.dataLayer = window.dataLayer || []; /* init datalayer */
            if(marketingBool === false) {
                marketingBool = true;
                window.dataLayer.push({
                    "event": "toggle",
                    "action": "on", //Populate as per user action
                    "name": "onclick",
                    "type": "click",
                    "region": "cookie banner",
                    "section": "consent settings",
                    "text": "marketing cookies",
                    "component": "toggle switch"
                });
            }
            else {
                marketingBool = false;
                window.dataLayer.push({
                    "event": "toggle",
                    "action": "off", //Populate as per user action
                    "name": "onclick",
                    "type": "click",
                    "region": "cookie banner",
                    "section": "consent settings",
                    "text": "marketing cookies",
                    "component": "toggle switch"
                });
            }
        });
        if(analyticsToggle !== null && functionalToggle !== null) { /* check if these toggles exist because they only exist in the non-us banner */
            analyticsToggle.addEventListener('click', function () { /* clicked opt-in opt-out toggle for functional */
                window.dataLayer = window.dataLayer || []; /* init datalayer */
                /* add check here for grabbing boolean value if they're revisiting cookie consent after the first time they see the banner */
                if(analyticsBool === false) { /* is it opted out currently? */
                    analyticsBool = true; /* opt in */
                    window.dataLayer.push({ /* push toggle event for on */
                        "event": "toggle",
                        "action": "on", //Populate as per user action
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "analytics cookies",
                        "component": "toggle switch"
                    });
                }
                else {
                    analyticsBool = false; /* it's opt out, if it was opted in previously, we're opting out the variable here */
                    window.dataLayer.push({ /* push toggle event for off */
                        "event": "toggle",
                        "action": "off", //Populate as per user action
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "analytics cookies",
                        "component": "toggle switch"
                    });
                }
            });
            functionalToggle.addEventListener('click', function () { /* clicked opt-in opt-out toggle for functional */
                window.dataLayer = window.dataLayer || []; /* init datalayer */
                /* add check here for grabbing boolean value if they're revisiting cookie consent after the first time they see the banner */
                if(functionalBool === false) { /* is it opted out currently? */
                    functionalBool = true; /* opt in */
                    window.dataLayer.push({ /* push toggle event for on */
                        "event": "toggle",
                        "action": "on", //Populate as per user action
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "functional cookies",
                        "component": "toggle switch"
                    });
                }
                else {
                    functionalBool = false; /* it's opt out, if it was opted in previously, we're opting out the variable here */
                    window.dataLayer.push({ /* push toggle event for off */
                        "event": "toggle",
                        "action": "off", //Populate as per user action
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "functional cookies",
                        "component": "toggle switch"
                    });
                }
            });
        }
    });

}
function initFooterLink() { /* adding the footer link to the end of the footer if the link doesn't exist in the footer or doesn't exist at all. */
    if (document.getElementById("manualConsentoptout") != null){
        var ProfileID = getRegion(); /* get the region to get the profile id for loading the correct banner*/
        if (ProfileID == null) { /* does this id exist? */
            window.setTimeout(initFooterLink, 500); /* loose check, if the profile id doesn't exist, loop back to the start of the function  */
        } else if(ProfileID === 1) { /* is this a US user? */
            var formDefaults = getCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924'); /* assign region variable */
            if (formDefaults == null || formDefaults.includes("[")) { /* loose check, if the form defaults cookie doesn't exist, set it. */
                setCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924', 's27c20-1.s26c19-1.s24c17-1', 365) /* edit cookie to include defaults or anything that is not able to be opted out of, then append the value from opt-ins or opt-outs */
            } else { /* formdefaults cookie exists, do nothing. */

            }
        }
        else {

        }
        /* check against link or button ID as it will exist in UDS and webspark 2, also make sure we update this to the new id below */
        var manualOpt = document.getElementById("manualConsentoptout"); /* select the link we just added */
        manualOpt.addEventListener("click", function() { /* event listener for data layer event and to show the banner */
            CassieWidgetLoader.Widget.showModal(); /* show the banner */
            window.dataLayer = window.dataLayer || []; /* init the data layer */
            window.dataLayer.push({ /* push banner opened event to datalayer */
                "event": "modal",
                "action": "open",
                "name": "onclick",
                "type": "click",
                "region": "cookie banner",
                "section": "consent settings",
                "text": "Manage my privacy settings",
                "component": "button"
            });
        })
    } else {
        var ProfileID = getRegion(); /* get the region to get the profile id for loading the correct banner*/
        if (ProfileID == null) { /* does this id exist? */
            window.setTimeout(initFooterLink, 500); /* loose check, if the profile id doesn't exist, loop back to the start of the function  */
        } else if(ProfileID === 1) { /* is this a US user? */
            var formDefaults = getCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924'); /* assign region variable */
            if(formDefaults == null || formDefaults.includes("[")) { /* loose check, if the form defaults cookie doesn't exist, set it. */
                setCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924', 's27c20-1.s26c19-1.s24c17-1',365) /* edit cookie to include defaults or anything that is not able to be opted out of, then append the value from opt-ins or opt-outs */
            } else { /* formdefaults cookie exists, do nothing. */

            }
            var findFooter = document.querySelectorAll("nav.colophon");
            if(findFooter == null) { /* webspark2 or uds footer does NOT exist. */
                var findFooter = document.querySelectorAll("footer");
                findFooter[findFooter.length - 1].insertAdjacentHTML('afterend', '<div class="wrapper" id="cassieOptOut"><div class="container"><div class="row"><div class="col"><button id="manualConsentoptout">Manage my privacy settings</button></div></div></div></div>'); /* the html we're appending */
                var manualOpt = document.getElementById("manualConsentoptout"); /* select the link we just added */
                manualOpt.addEventListener("click", function() { /* event listener for data layer event and to show the banner */
                    CassieWidgetLoader.Widget.showModal(); /* show the banner */
                    window.dataLayer = window.dataLayer || []; /* init the data layer */
                    window.dataLayer.push({ /* push banner opened event to datalayer */
                        "event": "modal",
                        "action": "open",
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "Manage my privacy settings",
                        "component": "button"
                    });
                })
            }
            else { /* webspark2 or uds footer does exist. */
                findFooter[findFooter.length - 1].insertAdjacentHTML('beforeend', '<button id="manualConsentoptout">Manage my privacy settings</button>'); /* the html we're appending */
                var manualOpt = document.getElementById("manualConsentoptout"); /* select the link we just added */
                manualOpt.addEventListener("click", function() { /* event listener for data layer event and to show the banner */
                    CassieWidgetLoader.Widget.showModal(); /* show the banner */
                    window.dataLayer = window.dataLayer || []; /* init the data layer */
                    window.dataLayer.push({ /* push banner opened event to datalayer */
                        "event": "modal",
                        "action": "open",
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "Manage my privacy settings",
                        "component": "button"
                    });
                })
            }
        } else { /* profile id exists */
            var formDefaults = getCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924'); /* assign region variable */
            if(formDefaults == null) { /* loose check, if the form defaults cookie doesn't exist, set it. */

            } else { /* formdefaults cookie exists, do nothing. */
                if(formDefaults.includes("[")) { /* loose check, if the form defaults cookie doesn't exist, set it. */
                    setCookie('SyrenisCookieFormConsent_434a2412-d1b7-47cd-9d14-9f0dd4d68924', '',365) /* edit cookie to include defaults or anything that is not able to be opted out of, then append the value from opt-ins or opt-outs */
                } else { /* formdefaults cookie exists, do nothing. */

                }
            }
            //add banner with opt out link
            var findFooter = document.querySelectorAll("nav.colophon");
            if(findFooter == null) { /* webspark2 or uds footer does NOT exist. */
                var findFooter = document.querySelectorAll("footer");
                findFooter[findFooter.length - 1].insertAdjacentHTML('afterend', '<div class="wrapper" id="cassieOptOut"><div class="container"><div class="row"><div class="col"><button id="manualConsentoptout">Manage my privacy settings</button></div></div></div></div>'); /* the html we're appending */
                var manualOpt = document.getElementById("manualConsentoptout"); /* select the link we just added */
                manualOpt.addEventListener("click", function() { /* event listener for data layer event and to show the banner */
                    CassieWidgetLoader.Widget.showModal(); /* show the banner */
                    window.dataLayer = window.dataLayer || []; /* init the data layer */
                    window.dataLayer.push({ /* push banner opened event to datalayer */
                        "event": "modal",
                        "action": "open",
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "Manage my privacy settings",
                        "component": "button"
                    });
                })
            }
            else { /* webspark2 or uds footer does exist. */
                findFooter[findFooter.length - 1].insertAdjacentHTML('beforeend', '<button id="manualConsentoptout">Manage my privacy settings</button>'); /* the html we're appending */
                var manualOpt = document.getElementById("manualConsentoptout"); /* select the link we just added */
                manualOpt.addEventListener("click", function() { /* event listener for data layer event and to show the banner */
                    CassieWidgetLoader.Widget.showModal(); /* show the banner */
                    window.dataLayer = window.dataLayer || []; /* init the data layer */
                    window.dataLayer.push({ /* push banner opened event to datalayer */
                        "event": "modal",
                        "action": "open",
                        "name": "onclick",
                        "type": "click",
                        "region": "cookie banner",
                        "section": "consent settings",
                        "text": "Manage my privacy settings",
                        "component": "button"
                    });
                })
            }
        }
    }
}
function allCookieConsentJS() {
    initializeCookieConsent();
    addCookieConsentAccessibility();
}
initFooterLink();
allCookieConsentJS();
