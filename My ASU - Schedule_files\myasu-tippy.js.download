function initMyAsuTippy(c,b){if($(c).is("a")||$(c).is("button")){$(document).on("click",c,function(d){d.stopPropagation();d.preventDefault();});}var a={theme:"myasu",trigger:"click",content:function(d){if(b.ajax==true){return'<div class="myasu-tippy-loading">Loading...</span>';}else{if(b.showTitle==true){const e=d.getAttribute("title");if(e!=null&&e!=undefined&&e!=""){b.title=e;}}return createMyAsuTippyContent(d,b);}},placement:"top",allowHTML:true,interactive:true,maxWidth:"none",onShow:function(d){if(b.ajax==true){if(b.showTitle==true){const f=d.reference.getAttribute("title");if(f!=null&&f!=undefined&&f!=""){b.title=f;}}const e=d.reference.getAttribute("data-template");if(e!=null){$.ajax({url:e,success:function(g){d.setContent(createMyAsuTippyContent(g,b));if(b.onShowCallback!=null){b.onShowCallback(d);}},error:function(){b.error=true;d.setContent(createMyAsuTippyContent("Sorry, there was a loading error. Please try again later.",b));}});}else{b.error=true;d.setContent(createMyAsuTippyContent("Sorry, there was a loading error. Please try again later.",b));}}else{if(b.onShowCallback!=null){b.onShowCallback(d);}}if(b.addLinkTracking==true){addLinkTracking(d.reference);}},onHidden:function(d){if(b.ajax==true){d.setContent('<div class="myasu-tippy-loading">Loading...</span>');}if(b.onHide!=null){b.onHide(d);}},onTrigger:function(d){if(b.onTrigger!=null){b.onTrigger(d);}},onCreate:function(d){document.addEventListener("keydown",function(e){if(e.keyCode===27){d.hide();}});if(b.close==true){d.popper.querySelector(".tippy-close-button").addEventListener("click",function(e){d.hide();});}},popperOptions:{modifiers:[{name:"flip",options:{fallbackPlacements:["bottom","left","top"]}}]}};$.extend(a,b);return tippy(c,a);}function updateMyAsuTippy(b,c){var a=b.reference;b.setContent(createMyAsuTippyContent(a,c));if(c.close==true&&b.popper.querySelector(".tippy-close-button")!=null){b.popper.querySelector(".tippy-close-button").addEventListener("click",function(d){b.hide();});}}function createMyAsuTippyContent(b,e){var d=document.createElement("div");d.className="tippy-container";if(!e.error&&e.disablePadding){d.classList.add("tippy-container-no-padding");}if(e.showTitle==true){const a=document.createElement("div");if(!e.error&&e.disablePadding){a.className="tippy-container-padding-title";}else{a.className="tippy-container-title";}a.innerHTML=e.title;d.appendChild(a);}if(e.close==true){const g=document.createElement("div");g.className="tippy-close-button-container";const c=document.createElement("button");c.className="icon-btn btn-sm space-inset-stretch-sm tippy-close-button";c.innerHTML='<i class="fas fa-times icon-sm" aria-hidden="true" title="Close"></i><span class="sr-only">Close</span>';g.appendChild(c);d.appendChild(g);}var f=document.createElement("div");f.className="tippy-container-content";if(e.ajax==true){f.innerHTML=b;}else{const h=b.getAttribute("data-template");template=document.getElementById(h);if(template!=null){f.innerHTML=template.innerHTML;}else{f.innerHTML="Sorry, there was a problem while loading this content. Please try again later.";}}d.appendChild(f);return d.outerHTML;}