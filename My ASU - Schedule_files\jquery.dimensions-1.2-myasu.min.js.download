(function(b) {
	b.dimensions = {
		version : "@VERSION"
	};
	b
			.each(
					[ "Height", "Width" ],
					function(d, c) {
						b.fn["inner" + c] = function() {
							if (!this[0]) {
								return
							}
							var f = c == "Height" ? "Top" : "Left", e = c == "Height" ? "Bottom"
									: "Right";
							return this.css("display") != "none" ? this[0]["client"
									+ c]
									: a(this, c.toLowerCase())
											+ a(this, "padding" + f)
											+ a(this, "padding" + e)
						};
						b.fn["outer" + c] = function(f) {
							if (!this[0]) {
								return
							}
							var h = c == "Height" ? "Top" : "Left", e = c == "Height" ? "Bottom"
									: "Right";
							f = b.extend({
								margin : false
							}, f || {});
							var g = this.css("display") != "none" ? this[0]["offset"
									+ c]
									: a(this, c.toLowerCase())
											+ a(this, "border" + h + "Width")
											+ a(this, "border" + e + "Width")
											+ a(this, "padding" + h)
											+ a(this, "padding" + e);
							return g
									+ (f.margin ? (a(this, "margin" + h) + a(
											this, "margin" + e)) : 0)
						}
					});
	b
			.each(
					[ "Left", "Top" ],
					function(d, c) {
						b.fn["scroll" + c] = function(e) {
							if (!this[0]) {
								return
							}
							return e != undefined ? this
									.each(function() {
										this == window || this == document ? window
												.scrollTo(
														c == "Left" ? e
																: b(window)["scrollLeft"]
																		(),
														c == "Top" ? e
																: b(window)["scrollTop"]
																		())
												: this["scroll" + c] = e
									})
									: this[0] == window || this[0] == document ? self[(c == "Left" ? "pageXOffset"
											: "pageYOffset")]
											|| document.documentElement["scroll"
													+ c]
											|| document.body["scroll" + c]
											: this[0]["scroll" + c]
						}
					});
	b.fn
			.extend({
				position : function() {
					var h = 0, g = 0, f = this[0], i, c, e, d;
					if (f) {
						e = this.offsetParent();
						i = this.offset();
						c = e.offset();
						i.top -= a(f, "marginTop");
						i.left -= a(f, "marginLeft");
						c.top += a(e, "borderTopWidth");
						c.left += a(e, "borderLeftWidth");
						d = {
							top : i.top - c.top,
							left : i.left - c.left
						}
					}
					return d
				},
				offsetParent : function() {
					var c = this[0].offsetParent;
					while (c
							&& (!/^body|html$/i.test(c.tagName) && b.css(c,
									"position") == "static")) {
						c = c.offsetParent
					}
					return b(c)
				}
			});
	function a(c, d) {
		//return parseInt(b.curCSS(c.jquery ? c[0] : c, d, true)) || 0 // http://blog.jquery.com/2012/08/09/jquery-1-8-released/
		return parseInt(b(c.jquery ? c[0] : c).css(d, true)) || 0
	}
})(jQuery);