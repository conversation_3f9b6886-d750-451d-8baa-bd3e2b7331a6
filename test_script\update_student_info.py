#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ASU学生信息快速更新脚本
用于快速修改My ASU.html和My ASU - Schedule.html中的学生姓名和学号
"""

import re
import os
import sys
from typing import <PERSON><PERSON>

def update_my_asu_html(file_path: str, old_name: str, new_name: str, old_id: str, new_id: str) -> bool:
    """
    更新My ASU.html文件中的学生信息
    
    Args:
        file_path: 文件路径
        old_name: 原姓名 (格式: "First Last")
        new_name: 新姓名 (格式: "First Last")
        old_id: 原学号
        new_id: 新学号
    
    Returns:
        bool: 更新是否成功
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分离姓名
        old_first, old_last = old_name.split(' ', 1)
        new_first, new_last = new_name.split(' ', 1)
        
        # 更新各种格式的姓名
        replacements = [
            # head标签中的data-ssoname
            (f'<head data-ssoname="{old_first}">', f'<head data-ssoname="{new_first}">'),

            # 登录状态中的名字
            (f'<span class="name">{old_first}</span>', f'<span class="name">{new_first}</span>'),

            # 用户信息弹窗中的完整姓名
            (f'>{old_first} {old_last}<', f'>{new_first} {new_last}<'),

            # 用户信息中的Primary Name
            (f'>{old_first}   {old_last}  <', f'>{new_first}   {new_last}  <'),

            # 用户信息弹窗中的用户名显示（单独的div）
            (f'									{old_first} {old_last}', f'									{new_first} {new_last}'),

            # 打印信息中的姓名
            (f'<div class="printonly name">{old_first} {old_last}</div>',
             f'<div class="printonly name">{new_first} {new_last}</div>'),

            # 学号更新
            (f'<div class="user-info-profile-information-value">{old_id}</div>',
             f'<div class="user-info-profile-information-value">{new_id}</div>'),
            (f'<div class="printonly name">ID: {old_id}</div>',
             f'<div class="printonly name">ID: {new_id}</div>'),
        ]
        
        # 执行替换
        for old_text, new_text in replacements:
            content = content.replace(old_text, new_text)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ 成功更新 {file_path}")
        return True
        
    except Exception as e:
        print(f"✗ 更新 {file_path} 失败: {e}")
        return False

def update_my_asu_schedule_html(file_path: str, old_name: str, new_name: str, old_id: str, new_id: str) -> bool:
    """
    更新My ASU - Schedule.html文件中的学生信息
    
    Args:
        file_path: 文件路径
        old_name: 原姓名 (格式: "First Last")
        new_name: 新姓名 (格式: "First Last")
        old_id: 原学号
        new_id: 新学号
    
    Returns:
        bool: 更新是否成功
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分离姓名
        old_first, old_last = old_name.split(' ', 1)
        new_first, new_last = new_name.split(' ', 1)
        
        # 更新各种格式的姓名
        replacements = [
            # head标签中的data-ssoname
            (f'<head data-ssoname="{old_first}">', f'<head data-ssoname="{new_first}">'),

            # 登录状态中的名字
            (f'<span class="name">{old_first}</span>', f'<span class="name">{new_first}</span>'),

            # 用户信息弹窗中的完整姓名
            (f'>{old_first} {old_last}<', f'>{new_first} {new_last}<'),

            # 用户信息中的Primary Name
            (f'>{old_first}   {old_last}  <', f'>{new_first}   {new_last}  <'),

            # 用户信息弹窗中的用户名显示（单独的div）
            (f'									{old_first} {old_last}', f'									{new_first} {new_last}'),

            # 打印信息中的姓名（使用&nbsp;）
            (f'<div class="printname">{old_first}&nbsp;{old_last}</div>',
             f'<div class="printname">{new_first}&nbsp;{new_last}</div>'),

            # 学号更新
            (f'<div class="user-info-profile-information-value">{old_id}</div>',
             f'<div class="user-info-profile-information-value">{new_id}</div>'),
        ]
        
        # 执行替换
        for old_text, new_text in replacements:
            content = content.replace(old_text, new_text)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✓ 成功更新 {file_path}")
        return True
        
    except Exception as e:
        print(f"✗ 更新 {file_path} 失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("ASU学生信息快速更新脚本")
    print("=" * 50)
    
    # 获取用户输入
    print("\n请输入原学生信息:")
    old_name = input("原姓名 (格式: First Last): ").strip()
    old_id = input("原学号: ").strip()
    
    print("\n请输入新学生信息:")
    new_name = input("新姓名 (格式: First Last): ").strip()
    new_id = input("新学号: ").strip()
    
    # 验证输入
    if not all([old_name, old_id, new_name, new_id]):
        print("✗ 错误: 所有字段都必须填写")
        return
    
    if len(old_name.split()) != 2 or len(new_name.split()) != 2:
        print("✗ 错误: 姓名格式必须是 'First Last'")
        return
    
    # 确认更新
    print(f"\n即将进行以下更新:")
    print(f"  姓名: {old_name} → {new_name}")
    print(f"  学号: {old_id} → {new_id}")
    
    confirm = input("\n确认更新? (y/N): ").strip().lower()
    if confirm != 'y':
        print("取消更新")
        return
    
    # 定义文件路径
    files_to_update = [
        ("My ASU.html", update_my_asu_html),
        ("My ASU - Schedule.html", update_my_asu_schedule_html)
    ]
    
    # 更新文件
    print("\n开始更新文件...")
    success_count = 0
    
    for file_name, update_func in files_to_update:
        if os.path.exists(file_name):
            if update_func(file_name, old_name, new_name, old_id, new_id):
                success_count += 1
        else:
            print(f"✗ 文件不存在: {file_name}")
    
    # 总结
    print(f"\n更新完成! 成功更新 {success_count}/{len(files_to_update)} 个文件")
    
    if success_count == len(files_to_update):
        print("✓ 所有文件更新成功!")
    else:
        print("⚠ 部分文件更新失败，请检查错误信息")

if __name__ == "__main__":
    main()
