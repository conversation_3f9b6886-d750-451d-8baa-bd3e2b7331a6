# ASU学生信息快速更新脚本

这个脚本可以快速修改ASU网站HTML文件中的学生姓名和学号信息。

## 功能特点

- 🚀 快速更新学生姓名和学号
- 📁 同时处理多个HTML文件
- ✅ 自动验证输入格式
- 🔒 更新前确认机制
- 📝 详细的操作日志

## 支持的文件

- `My ASU.html` - 学生财务信息页面
- `My ASU - Schedule.html` - 学生课程表页面

## 使用方法

### 方法一：使用批处理文件（推荐）

1. 将需要修改的HTML文件放在脚本同一目录下
2. 双击运行 `run_update.bat`
3. 按提示输入原学生信息和新学生信息
4. 确认更新

### 方法二：直接运行Python脚本

```bash
python update_student_info.py
```

## 输入格式要求

### 姓名格式
- 必须是英文姓名
- 格式：`First Last`（名 姓）
- 示例：`<PERSON>`、`Marca Cortez`

### 学号格式
- 纯数字
- 示例：`1239458054`、`12847593`

## 使用示例

```
请输入原学生信息:
原姓名 (格式: First Last): <PERSON>
原学号: 1239458054

请输入新学生信息:
新姓名 (格式: First Last): Marca Cortez
新学号: 12847593

即将进行以下更新:
  姓名: Amy Rodrigue → Marca Cortez
  学号: 1239458054 → 12847593

确认更新? (y/N): y
```

## 更新内容

脚本会自动更新以下位置的信息：

### My ASU.html
- 页面头部的用户标识
- 登录状态显示的名字
- 用户信息弹窗中的完整姓名
- 用户资料中的主要姓名
- 打印信息中的姓名和学号
- 所有相关的学号显示

### My ASU - Schedule.html
- 页面头部的用户标识
- 登录状态显示的名字
- 用户信息弹窗中的完整姓名
- 用户资料中的主要姓名
- 课程表打印信息中的姓名
- 所有相关的学号显示

## 注意事项

⚠️ **重要提醒**：
- 使用前请备份原始文件
- 确保输入的姓名格式正确
- 脚本会直接修改原文件，无法撤销
- 建议先在测试环境中验证效果

## 系统要求

- Windows 操作系统
- Python 3.6 或更高版本
- UTF-8 编码支持

## 故障排除

### 常见问题

1. **Python未找到**
   - 请安装Python：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"

2. **文件不存在**
   - 确保HTML文件在脚本同一目录下
   - 检查文件名是否完全匹配

3. **编码问题**
   - 确保HTML文件使用UTF-8编码
   - 在记事本中另存为UTF-8格式

4. **权限问题**
   - 以管理员身份运行脚本
   - 确保文件没有被其他程序占用

## 版本历史

- v1.0 - 初始版本，支持基本的姓名和学号更新功能

## 联系方式

如有问题或建议，请联系开发者。
