/**
 * 
 */
$(document).ready(function(){

	// Because the official header forces '/' for the title link's path
	if ($('#asu_hdr_title').length > 0) {
		$("#asu_hdr_title").attr("href", "/myasu");
	}
	
	if ($('#myasu-report-accessibility').length > 0) {
		$('#myasu-report-accessibility').attr('href', 'https://www.asu.edu/accessibility/feedback?a11yref='+encodeURI(location.href));		
	}
	
	$(document).on('mouseup', 'button.jslink', buttonControlFocus);
	$(document).on('mousedown', 'button.jslink', buttonControlFocus);
	// Consider setting all button elements to have type="button", because default of "submit" is not sane
	
	boxDrawerInit();
	
	dataTogglesInit();

	systemMessageInit();
});

function systemMessageInit() {
	$('#system-message-dismiss-icon').click(function() {
		$(this).closest('#emergency_alert').hide();

		var token = $('#global-user-form-token').val();
		if (token !== undefined) {
			var ajaxData = {
				token: token,
				func: "system-message-dismiss",
				hash: $('#system-message-dismiss-version-hash').val(),
			};

			$.ajax({
				cache: false,
				url: "pref",
				type: "POST",
				data: ajaxData,
				error: function() {
					//console.log('Error storing system message dismissal!');
				}
			});
		}
	});	
}

var buttonControlFocus = function () {
	$(this).blur();
	return false;
}

function dataTogglesInit() {
	$(document).on("click", "a[data-toggles]", dataToggle);
	$(document).on("click", "button[data-toggles]", dataToggle);
}

var dataToggle = function () {
	var elem = $(this).attr("data-toggles");
	$(this).find("[class*='fa-chevron-']").toggleClass("fa-chevron-up fa-chevron-down");
	if ($('#'+elem).length > 0) {
		$('#'+elem).slideToggle();
	}
	return false;
}

// for use with box-drawer
function boxDrawerInit() {
	// enable drawer toggles
	$(document).on('click', '.box-drawer-toggle', function(event) {
		event.preventDefault();
		toggleBoxDrawer($(this));
	});
	
	// preload the up caret image
	//$('<img />').attr('src','v4.0/images/arrow-blue-up2.png').appendTo('body').css('display','none');
}

function toggleBoxDrawer(drawerToggle) {
	var drawer = drawerToggle.closest('.box-drawer');

	// close other drawers
//	drawer.siblings().each(function() {
//		$(this).find(".box-drawer-caret").removeClass("selected");
//		$(this).find(".box-drawer-body").slideUp();
//	});
	

	// toggle this drawer
	var drawerBody = drawer.find(".box-drawer-body:first");
	var isOpening = drawerBody.css("display") == 'none';
	drawer.find(".box-drawer-caret-container:first").find(".fa").toggleClass("fa-chevron-up fa-chevron-down");
	drawerBody.slideToggle();

	// and close any child drawers
	if (!isOpening) {
		drawer.find(".box-drawer-caret:not(:first)").find(".fa").removeClass("fa-chevron-up fa-chevron-down").addClass("fa-chevron-down");
		drawer.find(".box-drawer-body:not(:first)").slideUp();
	}

	// google analytics - log drawer open events
	if (isOpening) {
		var dataTracking = drawer.attr('data-tracking');
		if (dataTracking) {
			trackEvent('box-drawer-open', dataTracking);
		}
	}
	return false;
}

/**
 * 
 */
function toggleDetails(detailsTargetObj, detailsGroup) {
	//console.log($(detailsTargetObj));
	if (typeof detailsGroup !== 'undefined') {
		$(detailsGroup).each(function() {
			if ($(this).attr("id") != detailsTargetObj.attr("id")) {
				$(this).slideUp();
			} else {
				$(this).slideToggle();
			}
		});
	} else {
		$(detailsTargetObj).slideToggle();
	}
	return false;
};

function toggleControl(control, toggleGroup) {
	if (typeof toggleGroup !== 'undefined') {
		$(toggleGroup).each(function() {
			if ($(this).attr("id") != control.attr("id")) {
				$(this).removeClass("selected");
				$(this).find("[class*='fa-chevron-']").removeClass("fa-chevron-up").addClass("fa-chevron-down");
			} else {
				$(this).toggleClass("selected");
				$(this).find("[class*='fa-chevron-']").toggleClass("fa-chevron-up fa-chevron-down");
			}
		});
	} else {
		$(this).toggleClass("selected");
		$(control).find("[class*='fa-chevron-']").toggleClass("fa-chevron-up fa-chevron-down");
	}
	return false;
};


function toggleDetailsOld(detailsBlockObj, parentBlockObj) {
	if (typeof parentBlockObj !== 'undefined') {
		$(parentBlockObj).each(function() {
			if ($(this).attr("id") != detailsBlockObj.attr("id")) {
				$(this).slideUp();
			} else {
				$(this).slideToggle();
			}
		});
	} else {
		$(detailsBlockObj).slideToggle();
	}
	return false;
};