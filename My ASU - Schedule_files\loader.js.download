!function(){var e={669:function(e,t,n){e.exports=n(609)},448:function(e,t,n){"use strict";var i=n(867),s=n(26),r=n(372),o=n(327),a=n(97),c=n(109),l=n(985),d=n(61);e.exports=function(e){return new Promise((function(t,n){var S=e.data,E=e.headers;i.isFormData(S)&&delete E["Content-Type"];var u=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",g=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";E.Authorization="Basic "+btoa(h+":"+g)}var _=a(e.baseURL,e.url);if(u.open(e.method.toUpperCase(),o(_,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,u.onreadystatechange=function(){if(u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))){var i="getAllResponseHeaders"in u?c(u.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:i,config:e,request:u};s(t,n,r),u=null}},u.onabort=function(){u&&(n(d("Request aborted",e,"ECONNABORTED",u)),u=null)},u.onerror=function(){n(d("Network Error",e,null,u)),u=null},u.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(d(t,e,"ECONNABORTED",u)),u=null},i.isStandardBrowserEnv()){var T=(e.withCredentials||l(_))&&e.xsrfCookieName?r.read(e.xsrfCookieName):void 0;T&&(E[e.xsrfHeaderName]=T)}if("setRequestHeader"in u&&i.forEach(E,(function(e,t){void 0===S&&"content-type"===t.toLowerCase()?delete E[t]:u.setRequestHeader(t,e)})),i.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),e.responseType)try{u.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){u&&(u.abort(),n(e),u=null)})),S||(S=null),u.send(S)}))}},609:function(e,t,n){"use strict";var i=n(867),s=n(849),r=n(321),o=n(185);function a(e){var t=new r(e),n=s(r.prototype.request,t);return i.extend(n,r.prototype,t),i.extend(n,t),n}var c=a(n(655));c.Axios=r,c.create=function(e){return a(o(c.defaults,e))},c.Cancel=n(263),c.CancelToken=n(972),c.isCancel=n(502),c.all=function(e){return Promise.all(e)},c.spread=n(713),c.isAxiosError=n(268),e.exports=c,e.exports.default=c},263:function(e){"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},972:function(e,t,n){"use strict";var i=n(263);function s(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new i(e),t(n.reason))}))}s.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},s.source=function(){var e;return{token:new s((function(t){e=t})),cancel:e}},e.exports=s},502:function(e){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},321:function(e,t,n){"use strict";var i=n(867),s=n(327),r=n(782),o=n(572),a=n(185);function c(e){this.defaults=e,this.interceptors={request:new r,response:new r}}c.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=a(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[o,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},c.prototype.getUri=function(e){return e=a(this.defaults,e),s(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},i.forEach(["delete","get","head","options"],(function(e){c.prototype[e]=function(t,n){return this.request(a(n||{},{method:e,url:t,data:(n||{}).data}))}})),i.forEach(["post","put","patch"],(function(e){c.prototype[e]=function(t,n,i){return this.request(a(i||{},{method:e,url:t,data:n}))}})),e.exports=c},782:function(e,t,n){"use strict";var i=n(867);function s(){this.handlers=[]}s.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},s.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},s.prototype.forEach=function(e){i.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=s},97:function(e,t,n){"use strict";var i=n(793),s=n(303);e.exports=function(e,t){return e&&!i(t)?s(e,t):t}},61:function(e,t,n){"use strict";var i=n(481);e.exports=function(e,t,n,s,r){var o=new Error(e);return i(o,t,n,s,r)}},572:function(e,t,n){"use strict";var i=n(867),s=n(527),r=n(502),o=n(655);function a(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return a(e),e.headers=e.headers||{},e.data=s(e.data,e.headers,e.transformRequest),e.headers=i.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),i.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||o.adapter)(e).then((function(t){return a(e),t.data=s(t.data,t.headers,e.transformResponse),t}),(function(t){return r(t)||(a(e),t&&t.response&&(t.response.data=s(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:function(e){"use strict";e.exports=function(e,t,n,i,s){return e.config=t,n&&(e.code=n),e.request=i,e.response=s,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},185:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t){t=t||{};var n={},s=["url","method","data"],r=["headers","auth","proxy","params"],o=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],a=["validateStatus"];function c(e,t){return i.isPlainObject(e)&&i.isPlainObject(t)?i.merge(e,t):i.isPlainObject(t)?i.merge({},t):i.isArray(t)?t.slice():t}function l(s){i.isUndefined(t[s])?i.isUndefined(e[s])||(n[s]=c(void 0,e[s])):n[s]=c(e[s],t[s])}i.forEach(s,(function(e){i.isUndefined(t[e])||(n[e]=c(void 0,t[e]))})),i.forEach(r,l),i.forEach(o,(function(s){i.isUndefined(t[s])?i.isUndefined(e[s])||(n[s]=c(void 0,e[s])):n[s]=c(void 0,t[s])})),i.forEach(a,(function(i){i in t?n[i]=c(e[i],t[i]):i in e&&(n[i]=c(void 0,e[i]))}));var d=s.concat(r).concat(o).concat(a),S=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===d.indexOf(e)}));return i.forEach(S,l),n}},26:function(e,t,n){"use strict";var i=n(61);e.exports=function(e,t,n){var s=n.config.validateStatus;n.status&&s&&!s(n.status)?t(i("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},527:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t,n){return i.forEach(n,(function(n){e=n(e,t)})),e}},655:function(e,t,n){"use strict";var i=n(867),s=n(16),r={"Content-Type":"application/x-www-form-urlencoded"};function o(e,t){!i.isUndefined(e)&&i.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,c={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(a=n(448)),a),transformRequest:[function(e,t){return s(t,"Accept"),s(t,"Content-Type"),i.isFormData(e)||i.isArrayBuffer(e)||i.isBuffer(e)||i.isStream(e)||i.isFile(e)||i.isBlob(e)?e:i.isArrayBufferView(e)?e.buffer:i.isURLSearchParams(e)?(o(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):i.isObject(e)?(o(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};i.forEach(["delete","get","head"],(function(e){c.headers[e]={}})),i.forEach(["post","put","patch"],(function(e){c.headers[e]=i.merge(r)})),e.exports=c},849:function(e){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];return e.apply(t,n)}}},327:function(e,t,n){"use strict";var i=n(867);function s(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(i.isURLSearchParams(t))r=t.toString();else{var o=[];i.forEach(t,(function(e,t){null!=e&&(i.isArray(e)?t+="[]":e=[e],i.forEach(e,(function(e){i.isDate(e)?e=e.toISOString():i.isObject(e)&&(e=JSON.stringify(e)),o.push(s(t)+"="+s(e))})))})),r=o.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e}},303:function(e){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},372:function(e,t,n){"use strict";var i=n(867);e.exports=i.isStandardBrowserEnv()?{write:function(e,t,n,s,r,o){var a=[];a.push(e+"="+encodeURIComponent(t)),i.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),i.isString(s)&&a.push("path="+s),i.isString(r)&&a.push("domain="+r),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},793:function(e){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},268:function(e){"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},985:function(e,t,n){"use strict";var i=n(867);e.exports=i.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function s(e){var i=e;return t&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=s(window.location.href),function(t){var n=i.isString(t)?s(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},16:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t){i.forEach(e,(function(n,i){i!==t&&i.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[i])}))}},109:function(e,t,n){"use strict";var i=n(867),s=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,r,o={};return e?(i.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=i.trim(e.substr(0,r)).toLowerCase(),n=i.trim(e.substr(r+1)),t){if(o[t]&&s.indexOf(t)>=0)return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([n]):o[t]?o[t]+", "+n:n}})),o):o}},713:function(e){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},867:function(e,t,n){"use strict";var i=n(849),s=Object.prototype.toString;function r(e){return"[object Array]"===s.call(e)}function o(e){return void 0===e}function a(e){return null!==e&&"object"==typeof e}function c(e){if("[object Object]"!==s.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function l(e){return"[object Function]"===s.call(e)}function d(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),r(e))for(var n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.call(null,e[s],s,e)}e.exports={isArray:r,isArrayBuffer:function(e){return"[object ArrayBuffer]"===s.call(e)},isBuffer:function(e){return null!==e&&!o(e)&&null!==e.constructor&&!o(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isPlainObject:c,isUndefined:o,isDate:function(e){return"[object Date]"===s.call(e)},isFile:function(e){return"[object File]"===s.call(e)},isBlob:function(e){return"[object Blob]"===s.call(e)},isFunction:l,isStream:function(e){return a(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:d,merge:function e(){var t={};function n(n,i){c(t[i])&&c(n)?t[i]=e(t[i],n):c(n)?t[i]=e({},n):r(n)?t[i]=n.slice():t[i]=n}for(var i=0,s=arguments.length;i<s;i++)d(arguments[i],n);return t},extend:function(e,t,n){return d(t,(function(t,s){e[s]=n&&"function"==typeof t?i(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}}},t={};function n(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=n(669),t=n.n(e);t().defaults.withCredentials=!1;var i=function(e){return new Promise(((n,i)=>{t().get(e).then((e=>{n(e.data)})).catch((e=>{i(e)}))}))},s=function(e,n){return new Promise(((i,s)=>{t().get(e,n).then((e=>{i(e.data)})).catch((e=>{s(e)}))}))},r=function(e,n,i){return new Promise(((s,r)=>{try{t().post(e,n,i).then((e=>{s(e.data)})).catch((e=>{r(e)}))}catch(e){r(e)}}))},o=function(e,t,n,i,s,r,o,a,c,l,d){return{licenseKey:e,profileSettings:t,baseUrl:n,languageCode:i,supportedDomains:s,environment:r,region:o,recordUrlDomain:a,identityServiceUrl:c,crossDomainConsent:l,customOptions:d}},a=function(e){function t(e){try{const t=new URL(e).hostname.split("."),n=t.length-1,i=t.length>=3&&(t[n]+t[n-1]).length<=5;return t.splice(i?-3:-2).join(".")}catch(e){return null}}function n(e,t){let n=e||window.location.hostname;const i=t||!1;let s=0;if(n.includes(".co.")||n.includes(".com.")){const e=n.split(".");e[e.length-1].length<=3&&(s=1)}if(n=n.replace(/(https?:\/\/)?(www.)?/i,""),!i){const e=n.split(".");n=e.slice(e.length-(2+s)).join(".")}return-1!==n.indexOf("/")?n.split("/")[0]:n}return e&&(e.environment="production"===e.environment?"":e.environment),{getDomainName:n,getDefaultSubmissionUrl:function(){const t=e.recordUrlDomain;if(t||t?.length>0)return t;const{region:i}=e;let s=e?.environment;return s&&s?.length>0&&(s=`-${s}`),`https://cscript-${i}${s}.${n()}/cookiesapi/submit`},getSubmissionUrl:function(){const t=""===e.environment?"":`-${e.environment}`,n=window?.cassieResourceRootDomain??"cassiecloud.com";return`https://cscript-${e.region}${t}.${n}/cookiesapi/submit`},determineJsonUrl:function(){return`${e.baseUrl}/${e.licenseKey}/V2/${e.profileSettings.WidgetId}_${e.languageCode}.json`},checkDomain:function(e){if("all"===e||"N/A"===e)return!0;const n=e.trim().split(",");for(let e=0;e<n.length;e++){let i=n[e].trim();if("all"===i)return!0;-1!==i.indexOf("*.")&&(i=i.replace("*.",""));const s=document.domain;if(-1!==s.indexOf(i))return!0;const r=t(`https://${i}`);if(r&&-1!==s.indexOf(r))return!0}return!1}}},c=function(e,t){e&&0!==e.length&&function(e,t){const n=t?"head":"body",i=document.createElement("body"),s=["script","link","meta","title","style","base","noscript"];i.innerHTML=e;const{children:r}=i;for(let e=0;e<r.length;e++){const i=r[e],o=document.createElement(i.tagName.toLocaleLowerCase());for(let e=0;e<i.attributes.length;e++){const t=i.attributes[e];o.setAttribute(t.name,t.value)}o.innerHTML=i.innerHTML,s.includes(o.tagName.toLocaleLowerCase())||!t?document.getElementsByTagName(n)[0].appendChild(o):document.getElementsByTagName("body")[0].appendChild(o)}i.remove()}(e,t)},l=async function(e,t,n,i,r,o,a,c,l,d,S,E){const u=`${l}/Home/SaveConsent?accessKey=${t}&domain=${d}&consentedCookieIds=${n}&cookieFormConsent=${i}&runFirstCookieIds=${r}&privacyCookieIds=${o}&custom1stPartyData=${a}&privacyLink=${c}${S?`&userProfile=${S}`:""}&cookieCollectionId=${E}&expiryDays=${e}`,h=await s(u,{withCredentials:!0});return JSON.parse(h.CassieConsent)},d=async function(e,t,n,i,r,o){const a=`${i}/Home/Index?accessKey=${t}&domain=${n}${r?`&userProfile=${r}`:""}&cookieCollectionId=${o}&expiryDays=${e}`;return await s(a,{withCredentials:!0})},S=async function(e,t,n){const i=`${n}/Home/CdcPropagate?accessKey=${e}&domain=${t}`;return await s(i,{withCredentials:!0})},E=function(){return/Trident\/|MSIE/.test(window.navigator.userAgent)};function u(e){return e&&"string"==typeof e&&""!==e.trim()?e.split(".").map((e=>{const t=e.split("-");if(2!==t.length)return null;const n=t[0].match(/s(\d+)c(\d+)/);return n?{FieldID:`s${n[1]}_c${n[2]}`,IsChecked:parseInt(t[1],10)}:null})).filter((e=>null!==e)):[]}function h(e){try{return JSON.parse(e),!0}catch(e){return!1}}var g=class{constructor(e,t=null){this.accessKey=e,this.cookies=this.getAllCookies(),this.defaultCookieExpiry=t??365,this.maxTimeout=2e3}getAllCookies(){for(var e={},t=document.cookie.split(";"),n=1;n<=t.length;n++){var i=t[n-1].split("="),s=i[0],r=i[1];void 0!==s&&s.length>0&&(s=s.trim()),void 0!==r&&r.length>0&&(r=r.trim()),e[s]=r}return e}getCookieValueByName=function(e){const t=`; ${document.cookie}`.split(`; ${e}=`);return 2===t.length?t.pop().split(";").shift().trim():null};sleep(e){return new Promise((t=>setTimeout(t,e)))}getProtectedCookieByName=function(e,t,n=2e3){return new Promise((async i=>{let s=n/100;for(let n=1;n<s;n++){const n=`; ${document.cookie}`.split(`; ${e}=`);if(2===n.length){let s=n.pop().split(";").shift();i({Key:e,Value:s,Expiry:t})}await this.sleep(100)}setTimeout(i(null),n)}))};getFormConsent=function(){let e="SyrenisCookieFormConsent_"+this.accessKey,t=this.getCookieValueByName(e);if(!t)return[];try{return h(t)?JSON.parse(t)??[]:u(t)}catch(e){return[]}};getProtectedCookies=async function(e,t){let n=[],i=[];if(0==e?.length)return i;e.includes(",")?n=e.split(","):n[0]=e.trim();for(let e=0;e<n.length;e++){const r=n[e]?.trim();if(!r)continue;const o=t.find((e=>{let t=e.browserCookieName.trim().toLowerCase(),n=[];return t.includes(",")?n=t.split(",").map((e=>e.trim())):n.push(t),n.includes(r.toLowerCase())}));if(o&&0!=o?.expiry){var s=new Date;s.setDate(s.getDate()+o.expiry),i.push(this.getProtectedCookieByName(r,s.getTime().toString(),this.maxTimeout))}}return i=(await Promise.all(i)).filter((e=>e)),i.filter((e=>e))};recreateProtectedCookies(){try{const e=this.getCookieValueByName("SyrenisCustom_"+this.accessKey);let t=[];t=h(e)?JSON.parse(e):function(e){if(!e||"string"!=typeof e||""===e.trim())return[];let t=null;try{t=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");for(;t.length%4!=0;)t+="=";return decodeURIComponent(Array.from(atob(t)).map((e=>`%${e.charCodeAt(0).toString(16).padStart(2,"0")}`)).join(""))}(e)}catch(e){return console.error("Error decoding base64 string:",e),[]}return t.split("|$|").map((e=>{const t=e.split("|*|");return{Key:t[0],Value:t[1],Expiry:t[2]}}))}(e);for(let e=0;e<t.length;e++){const n=t[e];this.getAllCookies()[n.Key]||this.storeCookieWithTimeExpiry(n.Key,n.Value,null,n.Expiry)}}catch(e){}}storeCookie=function(e,t,n,i){i=i??this.defaultCookieExpiry;let s=new Date;s.setTime(s.getTime()+24*i*60*60*1e3);let r="expires="+s.toUTCString();n=n??window.document.domain;let o=E()?"":";domain="+n;try{t=JSON.parse(t)}catch{}let a=t;var c;Array.isArray(t)&&0===t.length||"string"==typeof t&&"[]"===t.trim()?a="":"object"==typeof t&&function(e){if(!e||"string"!=typeof e)return!1;const t=e.toLowerCase();return t.startsWith("syrenis")||t.startsWith("cassie")}(e)?a=Array.isArray(t)&&t.length>0&&t[0].FieldID?(c=t)&&Array.isArray(c)&&0!==c.length?c.map((e=>`${e.FieldID.replace("_","")}-${e.IsChecked}`)).join("."):"":Array.isArray(t)&&t.length>0&&t[0].Key?function(e){return e&&Array.isArray(e)&&0!==e.length?(t=e.map((e=>`${e.Key}|*|${e.Value}|*|${e.Expiry}`)).join("|$|"),btoa(Array.from((new TextEncoder).encode(t)).map((e=>String.fromCharCode(e))).join("")).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")):"";var t}(t):e.includes("GtmConsent")?function(e){return e&&Array.isArray(e)&&0!==e.length?e.map((e=>Object.entries(e).map((([e,t])=>`${e}:${t}`)).join("|"))).join("|"):""}(t):JSON.stringify(t):"object"==typeof t&&(a=JSON.stringify(t));let l=e+"="+a+";"+r+o+";path=/";document.cookie=l};storeCookieWithTimeExpiry=function(e,t,n,i){let s=new Date;s.setTime(i);let r=e+"="+t+";expires="+s.toUTCString()+";domain="+(n=n??window.document.domain)+";path=/";document.cookie=r};hasConsent=function(){let e=this.getAllCookies(),t=e["SyrenisCookieFormConsent_"+this.accessKey],n=e["SyrenisGuid_"+this.accessKey];return!(!t||!n)};getVisitGuid=function(){return this.getCookieValueByName("SyrenisGuid_"+this.accessKey)};getConsentDate=function(){let e=this.getCookieValueByName("SyrenisCookieConsentDate_"+this.accessKey);return e||(e=new Date(1970,0,1).getTime()),e};getPrivacyPolicyId=function(){let e=this.getCookieValueByName("SyrenisCookiePrivacyLink_"+this.accessKey);return e&&0!=e.length?e:null};getProtectedCookiesContainer=function(){return this.getCookieValueByName("SyrenisCustom_"+this.accessKey)}};let _,T,p;function O(e){const t=e.split(",").map((e=>e.trim().replace("*","").replace("https://","").replace("http://",""))).filter((e=>(e||e.length>0)&&"all"!==e.trim())).map((e=>e.trim().replace("*","")));return 0===t.length&&t.push(`.${document.domain}`),t}var N,I,C,A,P,D,w=function(e,t){function n(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}function i(e){e.sort(((e,t)=>e.executionOrder-t.executionOrder)),e.sort(((e,t)=>t.isRunFirst-e.isRunFirst)),e.forEach((e=>{e.isStrictlyNecessary||(e.isOptIn?(c(e.bodyOptIn,!1),c(e.headOptIn,!0)):(c(e.bodyOptOut,!1),c(e.headOptOut,!0)))}))}function s(e){for(const[t,n]of Object.entries(e))O(T.supportedDomains).forEach((e=>{_.storeCookie(t.replace("Cassie","Syrenis"),n,e)}))}function o(e,t){e.forEach((e=>{const n=t.find((t=>t.channelId==e.channelId));e.isStrictlyNecessary?(e.hasConsent=!0,e.isOptIn=!0):n&&(e.hasConsent=!0,e.isOptIn=n.isOptIn)})),i(e.filter((e=>e.hasConsent)))}return T=e,p=t,_=new g(T.profileSettings.AccessKey,p),{runStrictlyNecessaryScripts:function(e){e.sort(((e,t)=>e.executionOrder-t.executionOrder)),e.sort(((e,t)=>t.isRunFirst-e.isRunFirst)),e.forEach((e=>{e.isStrictlyNecessary&&(c(e.bodyOptIn,!1),c(e.headOptIn,!0))}))},runOptInScripts:i,placeSyrenisCookies:s,processConsent:async function(e,t,i){const r=T.profileSettings.AccessKey,a=e.map((e=>({FieldID:`s${e.statementId}_c${e.channelId}`,IsChecked:e.isOptIn?1:0})));this.runStrictlyNecessaryScripts(t),o(t,e);const c=await _.getProtectedCookies(i.persistCookies,t);T.identityServiceUrl?s(await l(p,r,JSON.stringify([]),JSON.stringify(a),JSON.stringify([]),JSON.stringify([]),JSON.stringify(c),i.privacyId,T.identityServiceUrl,T.supportedDomains,T.crossDomainConsent,T?.profileSettings?.CookieCollectionId??1)):function(e,t){let i=T.profileSettings.AccessKey,s=O(T.supportedDomains);var r=n(),o=(new Date).getTime();s.forEach((n=>{null===_.getCookieValueByName("SyrenisGuid_"+i)&&_.storeCookie("SyrenisGuid_"+i,r,n),_.storeCookie("SyrenisCookieFormConsent_"+i,e,n),_.storeCookie("SyrenisCookiePrivacyLink_"+i,t,n),_.storeCookie("SyrenisCookieConsentDate_"+i,o,n)}))}(a,i.privacyId)},runConsent:async function(e){let t=T?.profileSettings?.CookieCollectionId??1;if(T.identityServiceUrl&&await async function(e){let t=(!_.hasConsent()||T.crossDomainConsent)&&await d(p,T.profileSettings.AccessKey,T.supportedDomains,T.identityServiceUrl,T.crossDomainConsent,e);if(null!==t){const e=JSON.parse(t.CassieConsent),n=t.Restored,i="boolean"!=typeof e&&e;if(i&&s(i),!1===n&&T.crossDomainConsent){const e=await S(T.profileSettings.AccessKey,T.supportedDomains,T.identityServiceUrl);document.getElementsByTagName("body")[0].insertAdjacentHTML("beforeend",e)}}}(t),!_.hasConsent())return;const n=_.getCookieValueByName("SyrenisCookieFormConsent_"+T.profileSettings.AccessKey);let i;try{i=h(n)?JSON.parse(n):u(n)}catch(e){return void console.error("Error parsing cookie consent:",e)}let r=i.map((e=>{let t=e.FieldID.indexOf("c")+1;return{channelId:e.FieldID.substring(t,e.FieldID.length),isOptIn:1==e.IsChecked}}));_.recreateProtectedCookies(),this.runStrictlyNecessaryScripts(e),o(e,r)},getCurrentConsent:function(){let e=_.getFormConsent(),t=[];for(let n=0;n<e.length;n++){const i=e[n],s=i.FieldID.indexOf("_"),r=i.FieldID.substring(1,s),o=i.FieldID.substr(s+2),a=1==i.IsChecked;t.push({statementId:r,channelId:o,isOptIn:a})}return t},postConsentToCassie:async function(e,t,i,s){return new Promise((async(o,a)=>{let c={CookieFormID:T?.profileSettings?.WidgetId??null,LicenseID:T?.licenseKey,DivID:s,Preferences:_.getFormConsent(),appCodeName:navigator.appCodeName,appName:navigator.appName,appVersion:navigator.appVersion,cookieEnabled:navigator.cookieEnabled,geolocation:"",language:navigator.language,platform:navigator.platform,referrer:document.referrer.replace(/&/g,"%26"),submissionSource:t??"undefined_source",visitGUID:_.getVisitGuid()??n(),WebsiteURL:document.URL.replace(/&/g,"%26"),PrivacyPolicyID:i,custom1stPartyData:_.getProtectedCookiesContainer()};try{return o(await r(e,c,{timeout:5e3}))}catch(e){return a(e)}}))},dropStrictlyNecessaryCookies:function(e,t){let i=T.profileSettings.AccessKey,s=O(T.supportedDomains);var r=n(),o=(new Date).getTime();s.forEach((t=>{null===_.getCookieValueByName("SyrenisGuid_"+i)&&_.storeCookie("SyrenisGuid_"+i,r,t),_.storeCookie("SyrenisCookiePrivacyLink_"+i,e,t),_.storeCookie("SyrenisCookieConsentDate_"+i,o,t)})),this.runStrictlyNecessaryScripts(t)},saveGtmConsent:function(e){let t=T.profileSettings.AccessKey,n=O(T.supportedDomains),i=e;n.forEach((e=>{_.storeCookie("SyrenisGtmConsent_"+t,i,e)}))}}},m={emit:function(e,t){!function(e,t){const n=t||{bubbles:!0,cancelable:!1},i=document.createEvent("CustomEvent");i.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),document.dispatchEvent(i)}(e,{detail:t,bubbles:!0,cancelable:!0})}};class f{gppVersion;cmpStatus;cmpDisplayStatus;signalStatus;supportedAPIs;cmpId;sectionList;applicableSections;gppString;parsedSections;constructor(e){this.gppVersion=e.gppVersion,this.cmpStatus=e.cmpStatus,this.cmpDisplayStatus=e.cmpDisplayStatus,this.signalStatus=e.signalStatus,this.supportedAPIs=e.supportedAPIs,this.cmpId=e.cmpId,this.sectionList=e.gppModel.getSectionIds(),this.applicableSections=e.applicableSections,this.gppString=e.gppModel.encode(),this.parsedSections=e.gppModel.toObject()}}class V{callback;parameter;success=!0;cmpApiContext;constructor(e,t,n){this.cmpApiContext=e,Object.assign(this,{callback:t,parameter:n})}execute(){try{return this.respond()}catch(e){return this.invokeCallback(null),null}}invokeCallback(e){const t=null!==e;this.callback&&this.callback(e,t)}}class R extends V{respond(){let e=new f(this.cmpApiContext);this.invokeCallback(e)}}class M extends V{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>.<field> parameter required");let e=this.parameter.split(".");if(2!=e.length)throw new Error("Field name must be in the format <section>.<fieldName>");let t=e[0],n=e[1],i=this.cmpApiContext.gppModel.getFieldValue(t,n);this.invokeCallback(i)}}class U extends V{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section> parameter required");let e=null;this.cmpApiContext.gppModel.hasSection(this.parameter)&&(e=this.cmpApiContext.gppModel.getSection(this.parameter)),this.invokeCallback(e)}}class G extends V{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>[.version] parameter required");let e=this.cmpApiContext.gppModel.hasSection(this.parameter);this.invokeCallback(e)}}!function(e){e.ADD_EVENT_LISTENER="addEventListener",e.GET_FIELD="getField",e.GET_SECTION="getSection",e.HAS_SECTION="hasSection",e.PING="ping",e.REMOVE_EVENT_LISTENER="removeEventListener"}(N||(N={}));class y{eventName;listenerId;data;pingData;constructor(e,t,n,i){this.eventName=e,this.listenerId=t,this.data=n,this.pingData=i}}class b extends V{respond(){let e=this.cmpApiContext.eventQueue.add({callback:this.callback,parameter:this.parameter}),t=new y("listenerRegistered",e,!0,new f(this.cmpApiContext));this.invokeCallback(t)}}class L extends V{respond(){let e=this.parameter,t=this.cmpApiContext.eventQueue.remove(e),n=new y("listenerRemoved",e,t,new f(this.cmpApiContext));this.invokeCallback(n)}}class v{static[N.ADD_EVENT_LISTENER]=b;static[N.GET_FIELD]=M;static[N.GET_SECTION]=U;static[N.HAS_SECTION]=G;static[N.PING]=R;static[N.REMOVE_EVENT_LISTENER]=L}!function(e){e.STUB="stub",e.LOADING="loading",e.LOADED="loaded",e.ERROR="error"}(I||(I={})),function(e){e.VISIBLE="visible",e.HIDDEN="hidden",e.DISABLED="disabled"}(C||(C={})),function(e){e.GPP_LOADED="gpploaded",e.CMP_UI_SHOWN="cmpuishown",e.USER_ACTION_COMPLETE="useractioncomplete"}(A||(A={})),function(e){e.NOT_READY="not ready",e.READY="ready"}(P||(P={}));class F{callQueue;customCommands;cmpApiContext;constructor(e,t){if(this.cmpApiContext=e,t){let e=N.ADD_EVENT_LISTENER;if(t?.[e])throw new Error(`Built-In Custom Commmand for ${e} not allowed`);if(e=N.REMOVE_EVENT_LISTENER,t?.[e])throw new Error(`Built-In Custom Commmand for ${e} not allowed`);this.customCommands=t}try{this.callQueue=window.__gpp()||[]}catch(e){this.callQueue=[]}finally{window.__gpp=this.apiCall.bind(this),this.purgeQueuedCalls()}}apiCall(e,t,n,i){if("string"!=typeof e)t(null,!1);else{if(t&&"function"!=typeof t)throw new Error("invalid callback function");this.isCustomCommand(e)?this.customCommands[e](t,n):this.isBuiltInCommand(e)?new v[e](this.cmpApiContext,t,n).execute():t&&t(null,!1)}}purgeQueuedCalls(){const e=this.callQueue;this.callQueue=[],e.forEach((e=>{window.__gpp(...e)}))}isCustomCommand(e){return this.customCommands&&"function"==typeof this.customCommands[e]}isBuiltInCommand(e){return void 0!==v[e]}}class x{eventQueue=new Map;queueNumber=1e3;cmpApiContext;constructor(e){this.cmpApiContext=e;try{let e=window.__gpp("events")||[];for(var t=0;t<e.length;t++){let n=e[t];this.eventQueue.set(n.id,{callback:n.callback,parameter:n.parameter})}}catch(e){console.log(e)}}add(e){return this.eventQueue.set(this.queueNumber,e),this.queueNumber++}get(e){return this.eventQueue.get(e)}remove(e){return this.eventQueue.delete(e)}exec(e,t){this.eventQueue.forEach(((n,i)=>{let s=new y(e,i,t,new f(this.cmpApiContext));n.callback(s,!0)}))}clear(){this.queueNumber=1e3,this.eventQueue.clear()}get size(){return this.eventQueue.size}}class k extends Error{constructor(e){super(e),this.name="InvalidFieldError"}}class H{segments;encodedString=null;dirty=!1;decoded=!0;constructor(){this.segments=this.initializeSegments()}hasField(e){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let t=0;t<this.segments.length;t++){let n=this.segments[t];if(n.getFieldNames().includes(e))return n.hasField(e)}return!1}getFieldValue(e){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let t=0;t<this.segments.length;t++){let n=this.segments[t];if(n.hasField(e))return n.getFieldValue(e)}throw new k("Invalid field: '"+e+"'")}setFieldValue(e,t){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let n=0;n<this.segments.length;n++){let i=this.segments[n];if(i.hasField(e))return void i.setFieldValue(e,t)}throw new k("Invalid field: '"+e+"'")}toObj(){let e={};for(let t=0;t<this.segments.length;t++){let n=this.segments[t].toObj();for(const[t,i]of Object.entries(n))e[t]=i}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.encodedString=this.encodeSection(this.segments),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!1}setIsDirty(e){this.dirty=e}}class B extends Error{constructor(e){super(e),this.name="DecodingError"}}class K extends Error{constructor(e){super(e),this.name="EncodingError"}}class W{static encode(e,t){let n=[];if(e>=1)for(n.push(1);e>=2*n[0];)n.unshift(2*n[0]);let i="";for(let t=0;t<n.length;t++){let s=n[t];e>=s?(i+="1",e-=s):i+="0"}if(i.length>t)throw new K("Numeric value '"+e+"' is too large for a bit string length of '"+t+"'");for(;i.length<t;)i="0"+i;return i}static decode(e){if(!/^[0-1]*$/.test(e))throw new B("Undecodable FixedInteger '"+e+"'");let t=0,n=[];for(let t=0;t<e.length;t++)n[e.length-(t+1)]=0===t?1:2*n[e.length-t];for(let i=0;i<e.length;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}class j{static DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";static REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]);encode(e){if(!/^[0-1]*$/.test(e))throw new K("Unencodable Base64Url '"+e+"'");e=this.pad(e);let t="",n=0;for(;n<=e.length-6;){let i=e.substring(n,n+6);try{let e=W.decode(i);t+=j.DICT.charAt(e),n+=6}catch(t){throw new K("Unencodable Base64Url '"+e+"'")}}return t}decode(e){if(!/^[A-Za-z0-9\-_]*$/.test(e))throw new B("Undecodable Base64URL string '"+e+"'");let t="";for(let n=0;n<e.length;n++){let i=e.charAt(n),s=j.REVERSE_DICT.get(i);t+=W.encode(s,6)}return t}}class z extends j{static instance=new z;constructor(){super()}static getInstance(){return this.instance}pad(e){for(;e.length%8>0;)e+="0";for(;e.length%6>0;)e+="0";return e}}class ${static instance=new $;constructor(){}static getInstance(){return this.instance}encode(e,t){let n="";for(let i=0;i<t.length;i++){let s=t[i];if(!e.containsKey(s))throw new Error("Field not found: '"+s+"'");n+=e.get(s).encode()}return n}decode(e,t,n){let i=0;for(let s=0;s<t.length;s++){let r=t[s];if(!n.containsKey(r))throw new Error("Field not found: '"+r+"'");{let t=n.get(r);try{let n=t.substring(e,i);t.decode(n),i+=n.length}catch(e){if("SubstringError"!==e.name||t.getHardFailIfMissing())throw new B("Unable to decode field '"+r+"'");return}}}}}class Y{static encode(e){let t=[];if(e>=1&&(t.push(1),e>=2)){t.push(2);let n=2;for(;e>=t[n-1]+t[n-2];)t.push(t[n-1]+t[n-2]),n++}let n="1";for(let i=t.length-1;i>=0;i--){let s=t[i];e>=s?(n="1"+n,e-=s):n="0"+n}return n}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<2||e.indexOf("11")!==e.length-2)throw new B("Undecodable FibonacciInteger '"+e+"'");let t=0,n=[];for(let t=0;t<e.length-1;t++)0===t?n.push(1):1===t?n.push(2):n.push(n[t-1]+n[t-2]);for(let i=0;i<e.length-1;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}class q{static encode(e){if(!0===e)return"1";if(!1===e)return"0";throw new K("Unencodable Boolean '"+e+"'")}static decode(e){if("1"===e)return!0;if("0"===e)return!1;throw new B("Undecodable Boolean '"+e+"'")}}class J{static encode(e){e=e.sort(((e,t)=>e-t));let t=[],n=0,i=0;for(;i<e.length;){let n=i;for(;n<e.length-1&&e[n]+1===e[n+1];)n++;t.push(e.slice(i,n+1)),i=n+1}let s=W.encode(t.length,12);for(let e=0;e<t.length;e++)if(1==t[e].length){let i=t[e][0]-n;n=t[e][0],s+="0"+Y.encode(i)}else{let i=t[e][0]-n;n=t[e][0];let r=t[e][t[e].length-1]-n;n=t[e][t[e].length-1],s+="1"+Y.encode(i)+Y.encode(r)}return s}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new B("Undecodable FibonacciIntegerRange '"+e+"'");let t=[],n=W.decode(e.substring(0,12)),i=0,s=12;for(let r=0;r<n;r++){let n=q.decode(e.substring(s,s+1));if(s++,!0===n){let n=e.indexOf("11",s),r=Y.decode(e.substring(s,n+2))+i;i=r,s=n+2,n=e.indexOf("11",s);let o=Y.decode(e.substring(s,n+2))+i;i=o,s=n+2;for(let e=r;e<=o;e++)t.push(e)}else{let n=e.indexOf("11",s),r=Y.decode(e.substring(s,n+2))+i;i=r,t.push(r),s=n+2}}return t}}class Q extends Error{constructor(e){super(e),this.name="ValidationError"}}class X{hardFailIfMissing;validator;value;constructor(e=!0){this.hardFailIfMissing=e}withValidator(e){return this.validator=e,this}hasValue(){return void 0!==this.value&&null!==this.value}getValue(){return this.value}setValue(e){if(this.validator&&!this.validator.test(e))throw new Q("Invalid value '"+e+"'");this.value=e}getHardFailIfMissing(){return this.hardFailIfMissing}}class Z extends B{constructor(e){super(e),this.name="SubstringError"}}class ee{static substring(e,t,n){if(n>e.length||t<0||t>n)throw new Z("Invalid substring indexes "+t+":"+n+" for string of length "+e.length);return e.substring(t,n)}}class te extends X{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return J.encode(this.value)}catch(e){throw new K(e)}}decode(e){try{this.value=J.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{let n=W.decode(ee.substring(e,t,t+12)),i=t+12;for(let t=0;t<n;t++)i="1"===e.charAt(i)?e.indexOf("11",e.indexOf("11",i+1)+2)+2:e.indexOf("11",i+1)+2;return ee.substring(e,t,i)}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class ne extends X{bitStringLength;constructor(e,t,n=!0){super(n),this.bitStringLength=e,this.setValue(t)}encode(){try{return W.encode(this.value,this.bitStringLength)}catch(e){throw new K(e)}}decode(e){try{this.value=W.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+this.bitStringLength)}catch(e){throw new Z(e)}}}class ie{fields=new Map;containsKey(e){return this.fields.has(e)}put(e,t){this.fields.set(e,t)}get(e){return this.fields.get(e)}getAll(){return new Map(this.fields)}reset(e){this.fields.clear(),e.getAll().forEach(((e,t)=>{this.fields.set(t,e)}))}}!function(e){e.ID="Id",e.VERSION="Version",e.SECTION_IDS="SectionIds"}(D||(D={}));const se=[D.ID,D.VERSION,D.SECTION_IDS];class re{fields;encodedString=null;dirty=!1;decoded=!0;constructor(){this.fields=this.initializeFields()}validate(){}hasField(e){return this.fields.containsKey(e)}getFieldValue(e){if(this.decoded||(this.decodeSegment(this.encodedString,this.fields),this.dirty=!1,this.decoded=!0),this.fields.containsKey(e))return this.fields.get(e).getValue();throw new k("Invalid field: '"+e+"'")}setFieldValue(e,t){if(this.decoded||(this.decodeSegment(this.encodedString,this.fields),this.dirty=!1,this.decoded=!0),!this.fields.containsKey(e))throw new k(e+" not found");this.fields.get(e).setValue(t),this.dirty=!0}toObj(){let e={},t=this.getFieldNames();for(let n=0;n<t.length;n++){let i=t[n],s=this.getFieldValue(i);e[i]=s}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.validate(),this.encodedString=this.encodeSegment(this.fields),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.dirty=!1,this.decoded=!1}}class oe extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return se}initializeFields(){let e=new ie;return e.put(D.ID.toString(),new ne(6,ae.ID)),e.put(D.VERSION.toString(),new ne(6,ae.VERSION)),e.put(D.SECTION_IDS.toString(),new te([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode HeaderV1CoreSegment '"+e+"'")}}}class ae extends H{static ID=3;static VERSION=1;static NAME="header";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return ae.ID}getName(){return ae.NAME}getVersion(){return ae.VERSION}initializeSegments(){let e=[];return e.push(new oe),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var ce;!function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.POLICY_VERSION="PolicyVersion",e.IS_SERVICE_SPECIFIC="IsServiceSpecific",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_OPTINS="SpecialFeatureOptins",e.PURPOSE_CONSENTS="PurposeConsents",e.PURPOSE_LEGITIMATE_INTERESTS="PurposeLegitimateInterests",e.PURPOSE_ONE_TREATMENT="PurposeOneTreatment",e.PUBLISHER_COUNTRY_CODE="PublisherCountryCode",e.VENDOR_CONSENTS="VendorConsents",e.VENDOR_LEGITIMATE_INTERESTS="VendorLegitimateInterests",e.PUBLISHER_RESTRICTIONS="PublisherRestrictions",e.PUBLISHER_PURPOSES_SEGMENT_TYPE="PublisherPurposesSegmentType",e.PUBLISHER_CONSENTS="PublisherConsents",e.PUBLISHER_LEGITIMATE_INTERESTS="PublisherLegitimateInterests",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.PUBLISHER_CUSTOM_CONSENTS="PublisherCustomConsents",e.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS="PublisherCustomLegitimateInterests",e.VENDORS_ALLOWED_SEGMENT_TYPE="VendorsAllowedSegmentType",e.VENDORS_ALLOWED="VendorsAllowed",e.VENDORS_DISCLOSED_SEGMENT_TYPE="VendorsDisclosedSegmentType",e.VENDORS_DISCLOSED="VendorsDisclosed"}(ce||(ce={}));const le=[ce.VERSION,ce.CREATED,ce.LAST_UPDATED,ce.CMP_ID,ce.CMP_VERSION,ce.CONSENT_SCREEN,ce.CONSENT_LANGUAGE,ce.VENDOR_LIST_VERSION,ce.POLICY_VERSION,ce.IS_SERVICE_SPECIFIC,ce.USE_NON_STANDARD_STACKS,ce.SPECIAL_FEATURE_OPTINS,ce.PURPOSE_CONSENTS,ce.PURPOSE_LEGITIMATE_INTERESTS,ce.PURPOSE_ONE_TREATMENT,ce.PUBLISHER_COUNTRY_CODE,ce.VENDOR_CONSENTS,ce.VENDOR_LEGITIMATE_INTERESTS,ce.PUBLISHER_RESTRICTIONS],de=[ce.PUBLISHER_PURPOSES_SEGMENT_TYPE,ce.PUBLISHER_CONSENTS,ce.PUBLISHER_LEGITIMATE_INTERESTS,ce.NUM_CUSTOM_PURPOSES,ce.PUBLISHER_CUSTOM_CONSENTS,ce.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS],Se=[ce.VENDORS_ALLOWED_SEGMENT_TYPE,ce.VENDORS_ALLOWED],Ee=[ce.VENDORS_DISCLOSED_SEGMENT_TYPE,ce.VENDORS_DISCLOSED];class ue extends j{static instance=new ue;constructor(){super()}static getInstance(){return this.instance}pad(e){for(;e.length%24>0;)e+="0";return e}}class he{static encode(e){e.sort(((e,t)=>e-t));let t=[],n=0;for(;n<e.length;){let i=n;for(;i<e.length-1&&e[i]+1===e[i+1];)i++;t.push(e.slice(n,i+1)),n=i+1}let i=W.encode(t.length,12);for(let e=0;e<t.length;e++)1===t[e].length?i+="0"+W.encode(t[e][0],16):i+="1"+W.encode(t[e][0],16)+W.encode(t[e][t[e].length-1],16);return i}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new B("Undecodable FixedIntegerRange '"+e+"'");let t=[],n=W.decode(e.substring(0,12)),i=12;for(let s=0;s<n;s++){let n=q.decode(e.substring(i,i+1));if(i++,!0===n){let n=W.decode(e.substring(i,i+16));i+=16;let s=W.decode(e.substring(i,i+16));i+=16;for(let e=n;e<=s;e++)t.push(e)}else{let n=W.decode(e.substring(i,i+16));t.push(n),i+=16}}return t}}class ge extends X{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return he.encode(this.value)}catch(e){throw new K(e)}}decode(e){try{this.value=he.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{let n=W.decode(ee.substring(e,t,t+12)),i=t+12;for(let t=0;t<n;t++)"1"===e.charAt(i)?i+=33:i+=17;return ee.substring(e,t,i)}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class _e{key;type;ids;constructor(e,t,n){this.key=e,this.type=t,this.ids=n}getKey(){return this.key}setKey(e){this.key=e}getType(){return this.type}setType(e){this.type=e}getIds(){return this.ids}setIds(e){this.ids=e}}class Te extends X{keyBitStringLength;typeBitStringLength;constructor(e,t,n,i=!0){super(i),this.keyBitStringLength=e,this.typeBitStringLength=t,this.setValue(n)}encode(){try{let e=this.value,t="";t+=W.encode(e.length,12);for(let n=0;n<e.length;n++){let i=e[n];t+=W.encode(i.getKey(),this.keyBitStringLength),t+=W.encode(i.getType(),this.typeBitStringLength),t+=he.encode(i.getIds())}return t}catch(e){throw new K(e)}}decode(e){try{let t=[],n=W.decode(ee.substring(e,0,12)),i=12;for(let s=0;s<n;s++){let n=W.decode(ee.substring(e,i,i+this.keyBitStringLength));i+=this.keyBitStringLength;let s=W.decode(ee.substring(e,i,i+this.typeBitStringLength));i+=this.typeBitStringLength;let r=new ge([]).substring(e,i),o=he.decode(r);i+=r.length,t.push(new _e(n,s,o))}this.value=t}catch(e){throw new B(e)}}substring(e,t){try{let n="";n+=ee.substring(e,t,t+12);let i=W.decode(n.toString()),s=t+n.length;for(let t=0;t<i;t++){let t=ee.substring(e,s,s+this.keyBitStringLength);s+=t.length,n+=t;let i=ee.substring(e,s,s+this.typeBitStringLength);s+=i.length,n+=i;let r=new ge([]).substring(e,s);s+=r.length,n+=r}return n}catch(e){throw new Z(e)}}}class pe extends X{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return q.encode(this.value)}catch(e){throw new K(e)}}decode(e){try{this.value=q.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+1)}catch(e){throw new Z(e)}}}class Oe{static encode(e){return e?W.encode(Math.round(e.getTime()/100),36):W.encode(0,36)}static decode(e){if(!/^[0-1]*$/.test(e)||36!==e.length)throw new B("Undecodable Datetime '"+e+"'");return new Date(100*W.decode(e))}}class Ne extends X{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return Oe.encode(this.value)}catch(e){throw new K(e)}}decode(e){try{this.value=Oe.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+36)}catch(e){throw new Z(e)}}}class Ie{static encode(e,t){if(e.length>t)throw new K("Too many values '"+e.length+"'");let n="";for(let t=0;t<e.length;t++)n+=q.encode(e[t]);for(;n.length<t;)n+="0";return n}static decode(e){if(!/^[0-1]*$/.test(e))throw new B("Undecodable FixedBitfield '"+e+"'");let t=[];for(let n=0;n<e.length;n++)t.push(q.decode(e.substring(n,n+1)));return t}}class Ce extends X{numElements;constructor(e,t=!0){super(t),this.numElements=e.length,this.setValue(e)}encode(){try{return Ie.encode(this.value,this.numElements)}catch(e){throw new K(e)}}decode(e){try{this.value=Ie.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+this.numElements)}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=[...e];for(let e=t.length;e<this.numElements;e++)t.push(!1);t.length>this.numElements&&(t=t.slice(0,this.numElements)),super.setValue(t)}}class Ae{static encode(e,t){for(;e.length<t;)e+=" ";let n="";for(let t=0;t<e.length;t++){let i=e.charCodeAt(t);if(32===i)n+=W.encode(63,6);else{if(!(i>=65))throw new K("Unencodable FixedString '"+e+"'");n+=W.encode(e.charCodeAt(t)-65,6)}}return n}static decode(e){if(!/^[0-1]*$/.test(e)||e.length%6!=0)throw new B("Undecodable FixedString '"+e+"'");let t="";for(let n=0;n<e.length;n+=6){let i=W.decode(e.substring(n,n+6));t+=63===i?" ":String.fromCharCode(i+65)}return t.trim()}}class Pe extends X{stringLength;constructor(e,t,n=!0){super(n),this.stringLength=e,this.setValue(t)}encode(){try{return Ae.encode(this.value,this.stringLength)}catch(e){throw new K(e)}}decode(e){try{this.value=Ae.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+6*this.stringLength)}catch(e){throw new Z(e)}}}class De extends X{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{let e=this.value.length>0?this.value[this.value.length-1]:0,t=he.encode(this.value),n=t.length,i=e;if(n<=i)return W.encode(e,16)+"1"+t;{let t=[],n=0;for(let i=0;i<e;i++)i===this.value[n]-1?(t[i]=!0,n++):t[i]=!1;return W.encode(e,16)+"0"+Ie.encode(t,i)}}catch(e){throw new K(e)}}decode(e){try{if("1"===e.charAt(16))this.value=he.decode(e.substring(17));else{let t=[],n=Ie.decode(e.substring(17));for(let e=0;e<n.length;e++)!0===n[e]&&t.push(e+1);this.value=t}}catch(e){throw new B(e)}}substring(e,t){try{let n=W.decode(ee.substring(e,t,t+16));return"1"===e.charAt(t+16)?ee.substring(e,t,t+17)+new ge([]).substring(e,t+17):ee.substring(e,t,t+17+n)}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class we extends re{base64UrlEncoder=ue.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return le}initializeFields(){let e=new Date,t=new ie;return t.put(ce.VERSION.toString(),new ne(6,Me.VERSION)),t.put(ce.CREATED.toString(),new Ne(e)),t.put(ce.LAST_UPDATED.toString(),new Ne(e)),t.put(ce.CMP_ID.toString(),new ne(12,0)),t.put(ce.CMP_VERSION.toString(),new ne(12,0)),t.put(ce.CONSENT_SCREEN.toString(),new ne(6,0)),t.put(ce.CONSENT_LANGUAGE.toString(),new Pe(2,"EN")),t.put(ce.VENDOR_LIST_VERSION.toString(),new ne(12,0)),t.put(ce.POLICY_VERSION.toString(),new ne(6,2)),t.put(ce.IS_SERVICE_SPECIFIC.toString(),new pe(!1)),t.put(ce.USE_NON_STANDARD_STACKS.toString(),new pe(!1)),t.put(ce.SPECIAL_FEATURE_OPTINS.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ce.PURPOSE_CONSENTS.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ce.PURPOSE_LEGITIMATE_INTERESTS.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ce.PURPOSE_ONE_TREATMENT.toString(),new pe(!1)),t.put(ce.PUBLISHER_COUNTRY_CODE.toString(),new Pe(2,"AA")),t.put(ce.VENDOR_CONSENTS.toString(),new De([])),t.put(ce.VENDOR_LEGITIMATE_INTERESTS.toString(),new De([])),t.put(ce.PUBLISHER_RESTRICTIONS.toString(),new Te(6,2,[],!1)),t}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfEuV2CoreSegment '"+e+"'")}}}class me extends X{getLength;constructor(e,t,n=!0){super(n),this.getLength=e,this.setValue(t)}encode(){try{return Ie.encode(this.value,this.getLength())}catch(e){throw new K(e)}}decode(e){try{this.value=Ie.decode(e)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+this.getLength())}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=this.getLength(),n=[...e];for(let e=n.length;e<t;e++)n.push(!1);n.length>t&&(n=n.slice(0,t)),super.setValue([...n])}}class fe extends re{base64UrlEncoder=ue.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return de}initializeFields(){let e=new ie;e.put(ce.PUBLISHER_PURPOSES_SEGMENT_TYPE.toString(),new ne(3,3)),e.put(ce.PUBLISHER_CONSENTS.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),e.put(ce.PUBLISHER_LEGITIMATE_INTERESTS.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));let t=new ne(6,0);return e.put(ce.NUM_CUSTOM_PURPOSES.toString(),t),e.put(ce.PUBLISHER_CUSTOM_CONSENTS.toString(),new me((()=>t.getValue()),[])),e.put(ce.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS.toString(),new me((()=>t.getValue()),[])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfEuV2PublisherPurposesSegment '"+e+"'")}}}class Ve extends re{base64UrlEncoder=ue.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Se}initializeFields(){let e=new ie;return e.put(ce.VENDORS_ALLOWED_SEGMENT_TYPE.toString(),new ne(3,2)),e.put(ce.VENDORS_ALLOWED.toString(),new De([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfEuV2VendorsAllowedSegment '"+e+"'")}}}class Re extends re{base64UrlEncoder=ue.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ee}initializeFields(){let e=new ie;return e.put(ce.VENDORS_DISCLOSED_SEGMENT_TYPE.toString(),new ne(3,1)),e.put(ce.VENDORS_DISCLOSED.toString(),new De([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfEuV2VendorsDisclosedSegment '"+e+"'")}}}class Me extends H{static ID=2;static VERSION=2;static NAME="tcfeuv2";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Me.ID}getName(){return Me.NAME}getVersion(){return Me.VERSION}initializeSegments(){let e=[];return e.push(new we),e.push(new fe),e.push(new Ve),e.push(new Re),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<n.length;e++){let i=n[e];if(0!==i.length){let s=i.charAt(0);if(s>="A"&&s<="H")t[0].decode(n[e]);else if(s>="I"&&s<="P")t[3].decode(n[e]);else if(s>="Q"&&s<="X")t[2].decode(n[e]);else{if(!(s>="Y"&&s<="Z"||s>="a"&&s<="f"))throw new B("Unable to decode TcfEuV2 segment '"+i+"'");t[1].decode(n[e])}}}}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),this.getFieldValue(ce.IS_SERVICE_SPECIFIC)?e.length>=2&&t.push(e[1].encode()):e.length>=2&&(t.push(e[2].encode()),e.length>=3&&t.push(e[3].encode()))),t.join(".")}setFieldValue(e,t){if(super.setFieldValue(e,t),e!==ce.CREATED&&e!==ce.LAST_UPDATED){let e=new Date;super.setFieldValue(ce.CREATED,e),super.setFieldValue(ce.LAST_UPDATED,e)}}}var Ue;!function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.TCF_POLICY_VERSION="TcfPolicyVersion",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_EXPRESS_CONSENT="SpecialFeatureExpressConsent",e.PUB_PURPOSES_SEGMENT_TYPE="PubPurposesSegmentType",e.PURPOSES_EXPRESS_CONSENT="PurposesExpressConsent",e.PURPOSES_IMPLIED_CONSENT="PurposesImpliedConsent",e.VENDOR_EXPRESS_CONSENT="VendorExpressConsent",e.VENDOR_IMPLIED_CONSENT="VendorImpliedConsent",e.PUB_RESTRICTIONS="PubRestrictions",e.PUB_PURPOSES_EXPRESS_CONSENT="PubPurposesExpressConsent",e.PUB_PURPOSES_IMPLIED_CONSENT="PubPurposesImpliedConsent",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.CUSTOM_PURPOSES_EXPRESS_CONSENT="CustomPurposesExpressConsent",e.CUSTOM_PURPOSES_IMPLIED_CONSENT="CustomPurposesImpliedConsent",e.DISCLOSED_VENDORS_SEGMENT_TYPE="DisclosedVendorsSegmentType",e.DISCLOSED_VENDORS="DisclosedVendors"}(Ue||(Ue={}));const Ge=[Ue.VERSION,Ue.CREATED,Ue.LAST_UPDATED,Ue.CMP_ID,Ue.CMP_VERSION,Ue.CONSENT_SCREEN,Ue.CONSENT_LANGUAGE,Ue.VENDOR_LIST_VERSION,Ue.TCF_POLICY_VERSION,Ue.USE_NON_STANDARD_STACKS,Ue.SPECIAL_FEATURE_EXPRESS_CONSENT,Ue.PURPOSES_EXPRESS_CONSENT,Ue.PURPOSES_IMPLIED_CONSENT,Ue.VENDOR_EXPRESS_CONSENT,Ue.VENDOR_IMPLIED_CONSENT,Ue.PUB_RESTRICTIONS],ye=[Ue.PUB_PURPOSES_SEGMENT_TYPE,Ue.PUB_PURPOSES_EXPRESS_CONSENT,Ue.PUB_PURPOSES_IMPLIED_CONSENT,Ue.NUM_CUSTOM_PURPOSES,Ue.CUSTOM_PURPOSES_EXPRESS_CONSENT,Ue.CUSTOM_PURPOSES_IMPLIED_CONSENT],be=[Ue.DISCLOSED_VENDORS_SEGMENT_TYPE,Ue.DISCLOSED_VENDORS];class Le extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ge}initializeFields(){let e=new Date,t=new ie;return t.put(Ue.VERSION.toString(),new ne(6,xe.VERSION)),t.put(Ue.CREATED.toString(),new Ne(e)),t.put(Ue.LAST_UPDATED.toString(),new Ne(e)),t.put(Ue.CMP_ID.toString(),new ne(12,0)),t.put(Ue.CMP_VERSION.toString(),new ne(12,0)),t.put(Ue.CONSENT_SCREEN.toString(),new ne(6,0)),t.put(Ue.CONSENT_LANGUAGE.toString(),new Pe(2,"EN")),t.put(Ue.VENDOR_LIST_VERSION.toString(),new ne(12,0)),t.put(Ue.TCF_POLICY_VERSION.toString(),new ne(6,2)),t.put(Ue.USE_NON_STANDARD_STACKS.toString(),new pe(!1)),t.put(Ue.SPECIAL_FEATURE_EXPRESS_CONSENT.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Ue.PURPOSES_EXPRESS_CONSENT.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Ue.PURPOSES_IMPLIED_CONSENT.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Ue.VENDOR_EXPRESS_CONSENT.toString(),new De([])),t.put(Ue.VENDOR_IMPLIED_CONSENT.toString(),new De([])),t.put(Ue.PUB_RESTRICTIONS.toString(),new Te(6,2,[],!1)),t}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfCaV1CoreSegment '"+e+"'")}}}class ve extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ye}initializeFields(){let e=new ie;e.put(Ue.PUB_PURPOSES_SEGMENT_TYPE.toString(),new ne(3,3)),e.put(Ue.PUB_PURPOSES_EXPRESS_CONSENT.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),e.put(Ue.PUB_PURPOSES_IMPLIED_CONSENT.toString(),new Ce([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));let t=new ne(6,0);return e.put(Ue.NUM_CUSTOM_PURPOSES.toString(),t),e.put(Ue.CUSTOM_PURPOSES_EXPRESS_CONSENT.toString(),new me((()=>t.getValue()),[])),e.put(Ue.CUSTOM_PURPOSES_IMPLIED_CONSENT.toString(),new me((()=>t.getValue()),[])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode TcfCaV1PublisherPurposesSegment '"+e+"'")}}}class Fe extends re{base64UrlEncoder=ue.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return be}initializeFields(){let e=new ie;return e.put(Ue.DISCLOSED_VENDORS_SEGMENT_TYPE.toString(),new ne(3,1)),e.put(Ue.DISCLOSED_VENDORS.toString(),new De([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode HeaderV1CoreSegment '"+e+"'")}}}class xe extends H{static ID=5;static VERSION=1;static NAME="tcfcav1";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return xe.ID}getName(){return xe.NAME}getVersion(){return xe.VERSION}initializeSegments(){let e=[];return e.push(new Le),e.push(new ve),e.push(new Fe),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<n.length;e++){let i=n[e];if(0!==i.length){let s=i.charAt(0);if(s>="A"&&s<="H")t[0].decode(n[e]);else if(s>="I"&&s<="P")t[2].decode(n[e]);else{if(!(s>="Y"&&s<="Z"||s>="a"&&s<="f"))throw new B("Unable to decode TcfCaV1 segment '"+i+"'");t[1].decode(n[e])}}}}return t}encodeSection(e){let t=[];return t.push(e[0].encode()),t.push(e[1].encode()),this.getFieldValue(Ue.DISCLOSED_VENDORS).length>0&&t.push(e[2].encode()),t.join(".")}setFieldValue(e,t){if(super.setFieldValue(e,t),e!==Ue.CREATED&&e!==Ue.LAST_UPDATED){let e=new Date;super.setFieldValue(Ue.CREATED,e),super.setFieldValue(Ue.LAST_UPDATED,e)}}}class ke{validator;value=null;constructor(e,t){this.validator=t||new class{test(e){return!0}},this.setValue(e)}hasValue(){return null!=this.value}getValue(){return this.value}setValue(e){e?this.value=e.charAt(0):e=null}}class He{validator;value=null;constructor(e,t){this.validator=t||new class{test(e){return!0}},this.setValue(e)}hasValue(){return null!=this.value}getValue(){return this.value}setValue(e){this.value=e}}class Be{fields=new Map;containsKey(e){return this.fields.has(e)}put(e,t){this.fields.set(e,t)}get(e){return this.fields.get(e)}getAll(){return new Map(this.fields)}reset(e){this.fields.clear(),e.getAll().forEach(((e,t)=>{this.fields.set(t,e)}))}}var Ke;!function(e){e.VERSION="Version",e.NOTICE="Notice",e.OPT_OUT_SALE="OptOutSale",e.LSPA_COVERED="LspaCovered"}(Ke||(Ke={}));const We=[Ke.VERSION,Ke.NOTICE,Ke.OPT_OUT_SALE,Ke.LSPA_COVERED];class je extends re{constructor(e){super(),e&&this.decode(e)}getFieldNames(){return We}initializeFields(){const e=new class{test(e){return"-"===e||"Y"===e||"N"===e}};let t=new Be;return t.put(Ke.VERSION,new He(ze.VERSION)),t.put(Ke.NOTICE,new ke("-",e)),t.put(Ke.OPT_OUT_SALE,new ke("-",e)),t.put(Ke.LSPA_COVERED,new ke("-",e)),t}encodeSegment(e){let t="";return t+=e.get(Ke.VERSION).getValue(),t+=e.get(Ke.NOTICE).getValue(),t+=e.get(Ke.OPT_OUT_SALE).getValue(),t+=e.get(Ke.LSPA_COVERED).getValue(),t}decodeSegment(e,t){if(null==e||4!=e.length)throw new B("Unable to decode UspV1CoreSegment '"+e+"'");try{t.get(Ke.VERSION).setValue(parseInt(e.substring(0,1))),t.get(Ke.NOTICE).setValue(e.charAt(1)),t.get(Ke.OPT_OUT_SALE).setValue(e.charAt(2)),t.get(Ke.LSPA_COVERED).setValue(e.charAt(3))}catch(t){throw new B("Unable to decode UspV1CoreSegment '"+e+"'")}}}class ze extends H{static ID=6;static VERSION=1;static NAME="uspv1";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return ze.ID}getName(){return ze.NAME}getVersion(){return ze.VERSION}initializeSegments(){let e=[];return e.push(new je),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var $e;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}($e||($e={}));const Ye=[$e.VERSION,$e.SHARING_NOTICE,$e.SALE_OPT_OUT_NOTICE,$e.SHARING_OPT_OUT_NOTICE,$e.TARGETED_ADVERTISING_OPT_OUT_NOTICE,$e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE,$e.SENSITIVE_DATA_LIMIT_USE_NOTICE,$e.SALE_OPT_OUT,$e.SHARING_OPT_OUT,$e.TARGETED_ADVERTISING_OPT_OUT,$e.SENSITIVE_DATA_PROCESSING,$e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,$e.PERSONAL_DATA_CONSENTS,$e.MSPA_COVERED_TRANSACTION,$e.MSPA_OPT_OUT_OPTION_MODE,$e.MSPA_SERVICE_PROVIDER_MODE],qe=[$e.GPC_SEGMENT_TYPE,$e.GPC];class Je{static encode(e,t,n){if(e.length>n)throw new K("Too many values '"+e.length+"'");let i="";for(let n=0;n<e.length;n++)i+=W.encode(e[n],t);for(;i.length<t*n;)i+="0";return i}static decode(e,t,n){if(!/^[0-1]*$/.test(e))throw new B("Undecodable FixedInteger '"+e+"'");if(e.length>t*n)throw new B("Undecodable FixedIntegerList '"+e+"'");if(e.length%t!=0)throw new B("Undecodable FixedIntegerList '"+e+"'");for(;e.length<t*n;)e+="0";e.length>t*n&&(e=e.substring(0,t*n));let i=[];for(let n=0;n<e.length;n+=t)i.push(W.decode(e.substring(n,n+t)));for(;i.length<n;)i.push(0);return i}}class Qe extends X{elementBitStringLength;numElements;constructor(e,t,n=!0){super(n),this.elementBitStringLength=e,this.numElements=t.length,this.setValue(t)}encode(){try{return Je.encode(this.value,this.elementBitStringLength,this.numElements)}catch(e){throw new K(e)}}decode(e){try{this.value=Je.decode(e,this.elementBitStringLength,this.numElements)}catch(e){throw new B(e)}}substring(e,t){try{return ee.substring(e,t,t+this.elementBitStringLength*this.numElements)}catch(e){throw new Z(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=[...e];for(let e=t.length;e<this.numElements;e++)t.push(0);t.length>this.numElements&&(t=t.slice(0,this.numElements)),super.setValue(t)}}class Xe extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ye}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put($e.VERSION.toString(),new ne(6,et.VERSION)),i.put($e.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.SHARING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put($e.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put($e.SHARING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put($e.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put($e.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put($e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put($e.PERSONAL_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put($e.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put($e.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put($e.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);66==n.length&&(n=n.substring(0,48)+"00000000"+n.substring(48,52)+"00"+n.substring(52,62)),this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNatCoreSegment '"+e+"'")}}}class Ze extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return qe}initializeFields(){let e=new ie;return e.put($e.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put($e.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put($e.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNatGpcSegment '"+e+"'")}}}class et extends H{static ID=7;static VERSION=1;static NAME="usnat";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return et.ID}getName(){return et.NAME}getVersion(){return et.VERSION}initializeSegments(){let e=[];return e.push(new Xe),e.push(new Ze),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue($e.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue($e.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue($e.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var tt;!function(e){e.VERSION="Version",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(tt||(tt={}));const nt=[tt.VERSION,tt.SALE_OPT_OUT_NOTICE,tt.SHARING_OPT_OUT_NOTICE,tt.SENSITIVE_DATA_LIMIT_USE_NOTICE,tt.SALE_OPT_OUT,tt.SHARING_OPT_OUT,tt.SENSITIVE_DATA_PROCESSING,tt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,tt.PERSONAL_DATA_CONSENTS,tt.MSPA_COVERED_TRANSACTION,tt.MSPA_OPT_OUT_OPTION_MODE,tt.MSPA_SERVICE_PROVIDER_MODE],it=[tt.GPC_SEGMENT_TYPE,tt.GPC];class st extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return nt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(tt.VERSION.toString(),new ne(6,ot.VERSION)),i.put(tt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tt.SHARING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tt.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(tt.SHARING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(tt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(tt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0]).withValidator(n)),i.put(tt.PERSONAL_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(tt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(tt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(tt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCaCoreSegment '"+e+"'")}}}class rt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return it}initializeFields(){let e=new ie;return e.put(tt.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(tt.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(tt.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCaGpcSegment '"+e+"'")}}}class ot extends H{static ID=8;static VERSION=1;static NAME="usca";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return ot.ID}getName(){return ot.NAME}getVersion(){return ot.VERSION}initializeSegments(){let e=[];return e.push(new st),e.push(new rt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(tt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(tt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(tt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var at;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(at||(at={}));const ct=[at.VERSION,at.SHARING_NOTICE,at.SALE_OPT_OUT_NOTICE,at.TARGETED_ADVERTISING_OPT_OUT_NOTICE,at.SALE_OPT_OUT,at.TARGETED_ADVERTISING_OPT_OUT,at.SENSITIVE_DATA_PROCESSING,at.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,at.MSPA_COVERED_TRANSACTION,at.MSPA_OPT_OUT_OPTION_MODE,at.MSPA_SERVICE_PROVIDER_MODE];class lt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ct}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(at.VERSION.toString(),new ne(6,dt.VERSION)),i.put(at.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(at.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(at.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(at.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(at.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(at.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(at.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(at.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(at.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(at.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsVaCoreSegment '"+e+"'")}}}class dt extends H{static ID=9;static VERSION=1;static NAME="usva";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return dt.ID}getName(){return dt.NAME}getVersion(){return dt.VERSION}initializeSegments(){let e=[];return e.push(new lt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var St;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(St||(St={}));const Et=[St.VERSION,St.SHARING_NOTICE,St.SALE_OPT_OUT_NOTICE,St.TARGETED_ADVERTISING_OPT_OUT_NOTICE,St.SALE_OPT_OUT,St.TARGETED_ADVERTISING_OPT_OUT,St.SENSITIVE_DATA_PROCESSING,St.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,St.MSPA_COVERED_TRANSACTION,St.MSPA_OPT_OUT_OPTION_MODE,St.MSPA_SERVICE_PROVIDER_MODE],ut=[St.GPC_SEGMENT_TYPE,St.GPC];class ht extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Et}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(St.VERSION.toString(),new ne(6,_t.VERSION)),i.put(St.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(St.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(St.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(St.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(St.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(St.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0]).withValidator(n)),i.put(St.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(St.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(St.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(St.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCoCoreSegment '"+e+"'")}}}class gt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ut}initializeFields(){let e=new ie;return e.put(St.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(St.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(St.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCoGpcSegment '"+e+"'")}}}class _t extends H{static ID=10;static VERSION=1;static NAME="usco";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return _t.ID}getName(){return _t.NAME}getVersion(){return _t.VERSION}initializeSegments(){let e=[];return e.push(new ht),e.push(new gt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(St.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(St.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(St.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Tt;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(Tt||(Tt={}));const pt=[Tt.VERSION,Tt.SHARING_NOTICE,Tt.SALE_OPT_OUT_NOTICE,Tt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Tt.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE,Tt.SALE_OPT_OUT,Tt.TARGETED_ADVERTISING_OPT_OUT,Tt.SENSITIVE_DATA_PROCESSING,Tt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Tt.MSPA_COVERED_TRANSACTION,Tt.MSPA_OPT_OUT_OPTION_MODE,Tt.MSPA_SERVICE_PROVIDER_MODE];class Ot extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return pt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(Tt.VERSION.toString(),new ne(6,Nt.VERSION)),i.put(Tt.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Tt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Tt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Tt.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Tt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Tt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Tt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Tt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(Tt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(Tt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(Tt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsUtCoreSegment '"+e+"'")}}}class Nt extends H{static ID=11;static VERSION=1;static NAME="usut";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Nt.ID}getName(){return Nt.NAME}getVersion(){return Nt.VERSION}initializeSegments(){let e=[];return e.push(new Ot),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var It;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(It||(It={}));const Ct=[It.VERSION,It.SHARING_NOTICE,It.SALE_OPT_OUT_NOTICE,It.TARGETED_ADVERTISING_OPT_OUT_NOTICE,It.SALE_OPT_OUT,It.TARGETED_ADVERTISING_OPT_OUT,It.SENSITIVE_DATA_PROCESSING,It.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,It.MSPA_COVERED_TRANSACTION,It.MSPA_OPT_OUT_OPTION_MODE,It.MSPA_SERVICE_PROVIDER_MODE],At=[It.GPC_SEGMENT_TYPE,It.GPC];class Pt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ct}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(It.VERSION.toString(),new ne(6,wt.VERSION)),i.put(It.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(It.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(It.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(It.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(It.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(It.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(It.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put(It.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(It.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(It.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCtCoreSegment '"+e+"'")}}}class Dt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return At}initializeFields(){let e=new ie;return e.put(It.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(It.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(It.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsCtGpcSegment '"+e+"'")}}}class wt extends H{static ID=12;static VERSION=1;static NAME="usct";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return wt.ID}getName(){return wt.NAME}getVersion(){return wt.VERSION}initializeSegments(){let e=[];return e.push(new Pt),e.push(new Dt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(It.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(It.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(It.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var mt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(mt||(mt={}));const ft=[mt.VERSION,mt.PROCESSING_NOTICE,mt.SALE_OPT_OUT_NOTICE,mt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,mt.SALE_OPT_OUT,mt.TARGETED_ADVERTISING_OPT_OUT,mt.SENSITIVE_DATA_PROCESSING,mt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,mt.ADDITIONAL_DATA_PROCESSING_CONSENT,mt.MSPA_COVERED_TRANSACTION,mt.MSPA_OPT_OUT_OPTION_MODE,mt.MSPA_SERVICE_PROVIDER_MODE];class Vt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ft}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(mt.VERSION.toString(),new ne(6,Rt.VERSION)),i.put(mt.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(mt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(mt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(mt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(mt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(mt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(mt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put(mt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(mt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(mt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(mt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsFlCoreSegment '"+e+"'")}}}class Rt extends H{static ID=13;static VERSION=1;static NAME="usfl";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Rt.ID}getName(){return Rt.NAME}getVersion(){return Rt.VERSION}initializeSegments(){let e=[];return e.push(new Vt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var Mt;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Mt||(Mt={}));const Ut=[Mt.VERSION,Mt.SHARING_NOTICE,Mt.SALE_OPT_OUT_NOTICE,Mt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Mt.SALE_OPT_OUT,Mt.TARGETED_ADVERTISING_OPT_OUT,Mt.SENSITIVE_DATA_PROCESSING,Mt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Mt.ADDITIONAL_DATA_PROCESSING_CONSENT,Mt.MSPA_COVERED_TRANSACTION,Mt.MSPA_OPT_OUT_OPTION_MODE,Mt.MSPA_SERVICE_PROVIDER_MODE],Gt=[Mt.GPC_SEGMENT_TYPE,Mt.GPC];class yt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ut}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(Mt.VERSION.toString(),new ne(6,Lt.VERSION)),i.put(Mt.SHARING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Mt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Mt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Mt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Mt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Mt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Mt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put(Mt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(Mt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(Mt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(Mt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsMtCoreSegment '"+e+"'")}}}class bt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Gt}initializeFields(){let e=new ie;return e.put(Mt.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(Mt.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(Mt.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsMtGpcSegment '"+e+"'")}}}class Lt extends H{static ID=14;static VERSION=1;static NAME="usmt";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Lt.ID}getName(){return Lt.NAME}getVersion(){return Lt.VERSION}initializeSegments(){let e=[];return e.push(new yt),e.push(new bt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Mt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Mt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Mt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var vt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(vt||(vt={}));const Ft=[vt.VERSION,vt.PROCESSING_NOTICE,vt.SALE_OPT_OUT_NOTICE,vt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,vt.SALE_OPT_OUT,vt.TARGETED_ADVERTISING_OPT_OUT,vt.SENSITIVE_DATA_PROCESSING,vt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,vt.ADDITIONAL_DATA_PROCESSING_CONSENT,vt.MSPA_COVERED_TRANSACTION,vt.MSPA_OPT_OUT_OPTION_MODE,vt.MSPA_SERVICE_PROVIDER_MODE],xt=[vt.GPC_SEGMENT_TYPE,vt.GPC];class kt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ft}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(vt.VERSION.toString(),new ne(6,Bt.VERSION)),i.put(vt.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(vt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(vt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(vt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(vt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(vt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(vt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put(vt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(vt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(vt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(vt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsOrCoreSegment '"+e+"'")}}}class Ht extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return xt}initializeFields(){let e=new ie;return e.put(vt.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(vt.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(vt.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsOrGpcSegment '"+e+"'")}}}class Bt extends H{static ID=15;static VERSION=1;static NAME="usor";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Bt.ID}getName(){return Bt.NAME}getVersion(){return Bt.VERSION}initializeSegments(){let e=[];return e.push(new kt),e.push(new Ht),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(vt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(vt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(vt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Kt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Kt||(Kt={}));const Wt=[Kt.VERSION,Kt.PROCESSING_NOTICE,Kt.SALE_OPT_OUT_NOTICE,Kt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Kt.SALE_OPT_OUT,Kt.TARGETED_ADVERTISING_OPT_OUT,Kt.SENSITIVE_DATA_PROCESSING,Kt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Kt.ADDITIONAL_DATA_PROCESSING_CONSENT,Kt.MSPA_COVERED_TRANSACTION,Kt.MSPA_OPT_OUT_OPTION_MODE,Kt.MSPA_SERVICE_PROVIDER_MODE],jt=[Kt.GPC_SEGMENT_TYPE,Kt.GPC];class zt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Wt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(Kt.VERSION.toString(),new ne(6,Yt.VERSION)),i.put(Kt.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Kt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Kt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Kt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Kt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Kt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Kt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(Kt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(Kt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(Kt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(Kt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsTxCoreSegment '"+e+"'")}}}class $t extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return jt}initializeFields(){let e=new ie;return e.put(Kt.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(Kt.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(Kt.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsTxGpcSegment '"+e+"'")}}}class Yt extends H{static ID=16;static VERSION=1;static NAME="ustx";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Yt.ID}getName(){return Yt.NAME}getVersion(){return Yt.VERSION}initializeSegments(){let e=[];return e.push(new zt),e.push(new $t),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Kt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Kt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Kt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var qt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(qt||(qt={}));const Jt=[qt.VERSION,qt.PROCESSING_NOTICE,qt.SALE_OPT_OUT_NOTICE,qt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,qt.SALE_OPT_OUT,qt.TARGETED_ADVERTISING_OPT_OUT,qt.SENSITIVE_DATA_PROCESSING,qt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,qt.ADDITIONAL_DATA_PROCESSING_CONSENT,qt.MSPA_COVERED_TRANSACTION,qt.MSPA_OPT_OUT_OPTION_MODE,qt.MSPA_SERVICE_PROVIDER_MODE],Qt=[qt.GPC_SEGMENT_TYPE,qt.GPC];class Xt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Jt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(qt.VERSION.toString(),new ne(6,en.VERSION)),i.put(qt.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(qt.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(qt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(qt.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(qt.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(qt.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(qt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0,0,0]).withValidator(n)),i.put(qt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(qt.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(qt.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(qt.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsDeCoreSegment '"+e+"'")}}}class Zt extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Qt}initializeFields(){let e=new ie;return e.put(qt.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(qt.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(qt.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsDeGpcSegment '"+e+"'")}}}class en extends H{static ID=17;static VERSION=1;static NAME="usde";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return en.ID}getName(){return en.NAME}getVersion(){return en.VERSION}initializeSegments(){let e=[];return e.push(new Xt),e.push(new Zt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(qt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(qt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(qt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var tn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_OPT_OUT_NOTICE="SensitiveDataOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(tn||(tn={}));const nn=[tn.VERSION,tn.PROCESSING_NOTICE,tn.SALE_OPT_OUT_NOTICE,tn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,tn.SENSITIVE_DATA_OPT_OUT_NOTICE,tn.SALE_OPT_OUT,tn.TARGETED_ADVERTISING_OPT_OUT,tn.SENSITIVE_DATA_PROCESSING,tn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,tn.MSPA_COVERED_TRANSACTION,tn.MSPA_OPT_OUT_OPTION_MODE,tn.MSPA_SERVICE_PROVIDER_MODE],sn=[tn.GPC_SEGMENT_TYPE,tn.GPC];class rn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return nn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(tn.VERSION.toString(),new ne(6,an.VERSION)),i.put(tn.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tn.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tn.SENSITIVE_DATA_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(tn.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(tn.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(tn.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(tn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(tn.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(tn.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(tn.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsIaCoreSegment '"+e+"'")}}}class on extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return sn}initializeFields(){let e=new ie;return e.put(tn.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(tn.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(tn.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsIaGpcSegment '"+e+"'")}}}class an extends H{static ID=18;static VERSION=1;static NAME="usia";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return an.ID}getName(){return an.NAME}getVersion(){return an.VERSION}initializeSegments(){let e=[];return e.push(new rn),e.push(new on),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(tn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(tn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(tn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var cn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(cn||(cn={}));const ln=[cn.VERSION,cn.PROCESSING_NOTICE,cn.SALE_OPT_OUT_NOTICE,cn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,cn.SALE_OPT_OUT,cn.TARGETED_ADVERTISING_OPT_OUT,cn.SENSITIVE_DATA_PROCESSING,cn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,cn.ADDITIONAL_DATA_PROCESSING_CONSENT,cn.MSPA_COVERED_TRANSACTION,cn.MSPA_OPT_OUT_OPTION_MODE,cn.MSPA_SERVICE_PROVIDER_MODE],dn=[cn.GPC_SEGMENT_TYPE,cn.GPC];class Sn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ln}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(cn.VERSION.toString(),new ne(6,un.VERSION)),i.put(cn.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(cn.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(cn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(cn.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(cn.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(cn.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(cn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(cn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(cn.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(cn.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(cn.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNeCoreSegment '"+e+"'")}}}class En extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return dn}initializeFields(){let e=new ie;return e.put(cn.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(cn.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(cn.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNeGpcSegment '"+e+"'")}}}class un extends H{static ID=19;static VERSION=1;static NAME="usne";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return un.ID}getName(){return un.NAME}getVersion(){return un.VERSION}initializeSegments(){let e=[];return e.push(new Sn),e.push(new En),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(cn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(cn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(cn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var hn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(hn||(hn={}));const gn=[hn.VERSION,hn.PROCESSING_NOTICE,hn.SALE_OPT_OUT_NOTICE,hn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,hn.SALE_OPT_OUT,hn.TARGETED_ADVERTISING_OPT_OUT,hn.SENSITIVE_DATA_PROCESSING,hn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,hn.ADDITIONAL_DATA_PROCESSING_CONSENT,hn.MSPA_COVERED_TRANSACTION,hn.MSPA_OPT_OUT_OPTION_MODE,hn.MSPA_SERVICE_PROVIDER_MODE],_n=[hn.GPC_SEGMENT_TYPE,hn.GPC];class Tn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return gn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(hn.VERSION.toString(),new ne(6,On.VERSION)),i.put(hn.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(hn.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(hn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(hn.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(hn.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(hn.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(hn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0]).withValidator(n)),i.put(hn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(hn.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(hn.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(hn.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNhCoreSegment '"+e+"'")}}}class pn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return _n}initializeFields(){let e=new ie;return e.put(hn.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(hn.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(hn.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNhGpcSegment '"+e+"'")}}}class On extends H{static ID=20;static VERSION=1;static NAME="usnh";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return On.ID}getName(){return On.NAME}getVersion(){return On.VERSION}initializeSegments(){let e=[];return e.push(new Tn),e.push(new pn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(hn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(hn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(hn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Nn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Nn||(Nn={}));const In=[Nn.VERSION,Nn.PROCESSING_NOTICE,Nn.SALE_OPT_OUT_NOTICE,Nn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Nn.SALE_OPT_OUT,Nn.TARGETED_ADVERTISING_OPT_OUT,Nn.SENSITIVE_DATA_PROCESSING,Nn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Nn.ADDITIONAL_DATA_PROCESSING_CONSENT,Nn.MSPA_COVERED_TRANSACTION,Nn.MSPA_OPT_OUT_OPTION_MODE,Nn.MSPA_SERVICE_PROVIDER_MODE],Cn=[Nn.GPC_SEGMENT_TYPE,Nn.GPC];class An extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return In}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(Nn.VERSION.toString(),new ne(6,Dn.VERSION)),i.put(Nn.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Nn.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Nn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(Nn.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Nn.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(Nn.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Nn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Qe(2,[0,0,0,0,0]).withValidator(n)),i.put(Nn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(Nn.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(Nn.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(Nn.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNjCoreSegment '"+e+"'")}}}class Pn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Cn}initializeFields(){let e=new ie;return e.put(Nn.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(Nn.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(Nn.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsNjGpcSegment '"+e+"'")}}}class Dn extends H{static ID=21;static VERSION=1;static NAME="usnj";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Dn.ID}getName(){return Dn.NAME}getVersion(){return Dn.VERSION}initializeSegments(){let e=[];return e.push(new An),e.push(new Pn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Nn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Nn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Nn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var wn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(wn||(wn={}));const mn=[wn.VERSION,wn.PROCESSING_NOTICE,wn.SALE_OPT_OUT_NOTICE,wn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,wn.SALE_OPT_OUT,wn.TARGETED_ADVERTISING_OPT_OUT,wn.SENSITIVE_DATA_PROCESSING,wn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,wn.ADDITIONAL_DATA_PROCESSING_CONSENT,wn.MSPA_COVERED_TRANSACTION,wn.MSPA_OPT_OUT_OPTION_MODE,wn.MSPA_SERVICE_PROVIDER_MODE],fn=[wn.GPC_SEGMENT_TYPE,wn.GPC];class Vn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return mn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ie;return i.put(wn.VERSION.toString(),new ne(6,Mn.VERSION)),i.put(wn.PROCESSING_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(wn.SALE_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(wn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new ne(2,0).withValidator(e)),i.put(wn.SALE_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(wn.TARGETED_ADVERTISING_OPT_OUT.toString(),new ne(2,0).withValidator(e)),i.put(wn.SENSITIVE_DATA_PROCESSING.toString(),new Qe(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(wn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new ne(2,0).withValidator(e)),i.put(wn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new ne(2,0).withValidator(e)),i.put(wn.MSPA_COVERED_TRANSACTION.toString(),new ne(2,1).withValidator(t)),i.put(wn.MSPA_OPT_OUT_OPTION_MODE.toString(),new ne(2,0).withValidator(e)),i.put(wn.MSPA_SERVICE_PROVIDER_MODE.toString(),new ne(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsTnCoreSegment '"+e+"'")}}}class Rn extends re{base64UrlEncoder=z.getInstance();bitStringEncoder=$.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return fn}initializeFields(){let e=new ie;return e.put(wn.GPC_SEGMENT_TYPE.toString(),new ne(2,1)),e.put(wn.GPC_SEGMENT_INCLUDED.toString(),new pe(!0)),e.put(wn.GPC.toString(),new pe(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new B("Unable to decode UsTnGpcSegment '"+e+"'")}}}class Mn extends H{static ID=22;static VERSION=1;static NAME="ustn";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Mn.ID}getName(){return Mn.NAME}getVersion(){return Mn.VERSION}initializeSegments(){let e=[];return e.push(new Vn),e.push(new Rn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(wn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(wn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(wn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}class Un{static SECTION_ID_NAME_MAP=new Map([[Me.ID,Me.NAME],[xe.ID,xe.NAME],[ze.ID,ze.NAME],[et.ID,et.NAME],[ot.ID,ot.NAME],[dt.ID,dt.NAME],[_t.ID,_t.NAME],[Nt.ID,Nt.NAME],[wt.ID,wt.NAME],[Rt.ID,Rt.NAME],[Lt.ID,Lt.NAME],[Bt.ID,Bt.NAME],[Yt.ID,Yt.NAME],[en.ID,en.NAME],[an.ID,an.NAME],[un.ID,un.NAME],[On.ID,On.NAME],[Dn.ID,Dn.NAME],[Mn.ID,Mn.NAME]]);static SECTION_ORDER=[Me.NAME,xe.NAME,ze.NAME,et.NAME,ot.NAME,dt.NAME,_t.NAME,Nt.NAME,wt.NAME,Rt.NAME,Lt.NAME,Bt.NAME,Yt.NAME,en.NAME,an.NAME,un.NAME,On.NAME,Dn.NAME,Mn.NAME]}class Gn{sections=new Map;encodedString=null;decoded=!0;dirty=!1;constructor(e){e&&this.decode(e)}setFieldValue(e,t,n){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let i=null;if(this.sections.has(e)?i=this.sections.get(e):e===xe.NAME?(i=new xe,this.sections.set(xe.NAME,i)):e===Me.NAME?(i=new Me,this.sections.set(Me.NAME,i)):e===ze.NAME?(i=new ze,this.sections.set(ze.NAME,i)):e===et.NAME?(i=new et,this.sections.set(et.NAME,i)):e===ot.NAME?(i=new ot,this.sections.set(ot.NAME,i)):e===dt.NAME?(i=new dt,this.sections.set(dt.NAME,i)):e===_t.NAME?(i=new _t,this.sections.set(_t.NAME,i)):e===Nt.NAME?(i=new Nt,this.sections.set(Nt.NAME,i)):e===wt.NAME?(i=new wt,this.sections.set(wt.NAME,i)):e===Rt.NAME?(i=new Rt,this.sections.set(Rt.NAME,i)):e===Lt.NAME?(i=new Lt,this.sections.set(Lt.NAME,i)):e===Bt.NAME?(i=new Bt,this.sections.set(Bt.NAME,i)):e===Yt.NAME?(i=new Yt,this.sections.set(Yt.NAME,i)):e===en.NAME?(i=new en,this.sections.set(en.NAME,i)):e===an.NAME?(i=new an,this.sections.set(an.NAME,i)):e===un.NAME?(i=new un,this.sections.set(un.NAME,i)):e===On.NAME?(i=new On,this.sections.set(On.NAME,i)):e===Dn.NAME?(i=new Dn,this.sections.set(Dn.NAME,i)):e===Mn.NAME&&(i=new Mn,this.sections.set(Mn.NAME,i)),!i)throw new k(e+"."+t+" not found");i.setFieldValue(t,n),this.dirty=!0,i.setIsDirty(!0)}setFieldValueBySectionId(e,t,n){this.setFieldValue(Un.SECTION_ID_NAME_MAP.get(e),t,n)}getFieldValue(e,t){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).getFieldValue(t):null}getFieldValueBySectionId(e,t){return this.getFieldValue(Un.SECTION_ID_NAME_MAP.get(e),t)}hasField(e,t){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),!!this.sections.has(e)&&this.sections.get(e).hasField(t)}hasFieldBySectionId(e,t){return this.hasField(Un.SECTION_ID_NAME_MAP.get(e),t)}hasSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)}hasSectionId(e){return this.hasSection(Un.SECTION_ID_NAME_MAP.get(e))}deleteSection(e){!this.decoded&&null!=this.encodedString&&this.encodedString.length>0&&this.decode(this.encodedString),this.sections.delete(e),this.dirty=!0}deleteSectionById(e){this.deleteSection(Un.SECTION_ID_NAME_MAP.get(e))}clear(){this.sections.clear(),this.encodedString="DBAA",this.decoded=!1,this.dirty=!1}getHeader(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e=new ae;return e.setFieldValue("SectionIds",this.getSectionIds()),e.toObj()}getSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).toObj():null}getSectionIds(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e=[];for(let t=0;t<Un.SECTION_ORDER.length;t++){let n=Un.SECTION_ORDER[t];if(this.sections.has(n)){let t=this.sections.get(n);e.push(t.getId())}}return e}encodeModel(e){let t=[],n=[];for(let i=0;i<Un.SECTION_ORDER.length;i++){let s=Un.SECTION_ORDER[i];if(e.has(s)){let i=e.get(s);i.setIsDirty(!0),t.push(i.encode()),n.push(i.getId())}}let i=new ae;return i.setFieldValue("SectionIds",n),t.unshift(i.encode()),t.join("~")}decodeModel(e){if(!e||0==e.length||e.startsWith("DB")){let t=e.split("~"),n=new Map;if(t[0].startsWith("D")){let i=new ae(t[0]).getFieldValue("SectionIds");if(i.length!==t.length-1)throw new B("Unable to decode '"+e+"'. The number of sections does not match the number of sections defined in the header.");for(let s=0;s<i.length;s++){if(""===t[s+1].trim())throw new B("Unable to decode '"+e+"'. Section "+(s+1)+" is blank.");if(i[s]===xe.ID){let e=new xe(t[s+1]);n.set(xe.NAME,e)}else if(i[s]===Me.ID){let e=new Me(t[s+1]);n.set(Me.NAME,e)}else if(i[s]===ze.ID){let e=new ze(t[s+1]);n.set(ze.NAME,e)}else if(i[s]===et.ID){let e=new et(t[s+1]);n.set(et.NAME,e)}else if(i[s]===ot.ID){let e=new ot(t[s+1]);n.set(ot.NAME,e)}else if(i[s]===dt.ID){let e=new dt(t[s+1]);n.set(dt.NAME,e)}else if(i[s]===_t.ID){let e=new _t(t[s+1]);n.set(_t.NAME,e)}else if(i[s]===Nt.ID){let e=new Nt(t[s+1]);n.set(Nt.NAME,e)}else if(i[s]===wt.ID){let e=new wt(t[s+1]);n.set(wt.NAME,e)}else if(i[s]===Rt.ID){let e=new Rt(t[s+1]);n.set(Rt.NAME,e)}else if(i[s]===Lt.ID){let e=new Lt(t[s+1]);n.set(Lt.NAME,e)}else if(i[s]===Bt.ID){let e=new Bt(t[s+1]);n.set(Bt.NAME,e)}else if(i[s]===Yt.ID){let e=new Yt(t[s+1]);n.set(Yt.NAME,e)}else if(i[s]===en.ID){let e=new en(t[s+1]);n.set(en.NAME,e)}else if(i[s]===an.ID){let e=new an(t[s+1]);n.set(an.NAME,e)}else if(i[s]===un.ID){let e=new un(t[s+1]);n.set(un.NAME,e)}else if(i[s]===On.ID){let e=new On(t[s+1]);n.set(On.NAME,e)}else if(i[s]===Dn.ID){let e=new Dn(t[s+1]);n.set(Dn.NAME,e)}else if(i[s]===Mn.ID){let e=new Mn(t[s+1]);n.set(Mn.NAME,e)}}}return n}if(e.startsWith("C")){let t=new Map,n=new Me(e);return t.set(Me.NAME,n),(new ae).setFieldValue(D.SECTION_IDS,[2]),t.set(ae.NAME,n),t}throw new B("Unable to decode '"+e+"'")}encodeSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).encode():null}encodeSectionById(e){return this.encodeSection(Un.SECTION_ID_NAME_MAP.get(e))}decodeSection(e,t){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let n=null;this.sections.has(e)?n=this.sections.get(e):e===xe.NAME?(n=new xe,this.sections.set(xe.NAME,n)):e===Me.NAME?(n=new Me,this.sections.set(Me.NAME,n)):e===ze.NAME?(n=new ze,this.sections.set(ze.NAME,n)):e===et.NAME?(n=new et,this.sections.set(et.NAME,n)):e===ot.NAME?(n=new ot,this.sections.set(ot.NAME,n)):e===dt.NAME?(n=new dt,this.sections.set(dt.NAME,n)):e===_t.NAME?(n=new _t,this.sections.set(_t.NAME,n)):e===Nt.NAME?(n=new Nt,this.sections.set(Nt.NAME,n)):e===wt.NAME?(n=new wt,this.sections.set(wt.NAME,n)):e===Rt.NAME?(n=new Rt,this.sections.set(Rt.NAME,n)):e===Lt.NAME?(n=new Lt,this.sections.set(Lt.NAME,n)):e===Bt.NAME?(n=new Bt,this.sections.set(Bt.NAME,n)):e===Yt.NAME?(n=new Yt,this.sections.set(Yt.NAME,n)):e===en.NAME?(n=new en,this.sections.set(en.NAME,n)):e===an.NAME?(n=new an,this.sections.set(an.NAME,n)):e===un.NAME?(n=new un,this.sections.set(un.NAME,n)):e===On.NAME?(n=new On,this.sections.set(On.NAME,n)):e===Dn.NAME?(n=new Dn,this.sections.set(Dn.NAME,n)):e===Mn.NAME&&(n=new Mn,this.sections.set(Mn.NAME,n)),n&&(n.decode(t),this.dirty=!0)}decodeSectionById(e,t){this.decodeSection(Un.SECTION_ID_NAME_MAP.get(e),t)}toObject(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e={};for(let t=0;t<Un.SECTION_ORDER.length;t++){let n=Un.SECTION_ORDER[t];this.sections.has(n)&&(e[n]=this.sections.get(n).toObj())}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.encodedString=this.encodeModel(this.sections),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.dirty=!1,this.decoded=!1}}class yn{gppVersion="1.1";supportedAPIs=[];eventQueue=new x(this);cmpStatus=I.LOADING;cmpDisplayStatus=C.HIDDEN;signalStatus=P.NOT_READY;applicableSections=[];gppModel=new Gn;cmpId;cmpVersion;eventStatus;reset(){this.eventQueue.clear(),this.cmpStatus=I.LOADING,this.cmpDisplayStatus=C.HIDDEN,this.signalStatus=P.NOT_READY,this.applicableSections=[],this.supportedAPIs=[],this.gppModel=new Gn,delete this.cmpId,delete this.cmpVersion,delete this.eventStatus}}class bn extends Error{constructor(e){super(e),this.name="GVLError"}}class Ln{static langSet=new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH"]);has(e){return Ln.langSet.has(e)}forEach(e){Ln.langSet.forEach(e)}get size(){return Ln.langSet.size}}class vn{static absCall(e,t,n,i){return new Promise(((s,r)=>{const o=new XMLHttpRequest;o.withCredentials=n,o.addEventListener("load",(()=>{if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){let e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}s(e)}else r(new Error(`HTTP Status: ${o.status} response type: ${o.responseType}`))})),o.addEventListener("error",(()=>{r(new Error("error"))})),o.addEventListener("abort",(()=>{r(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=i,o.ontimeout=()=>{r(new Error("Timeout "+i+"ms "+e))},o.send(t)}))}static post(e,t,n=!1,i=0){return this.absCall(e,JSON.stringify(t),n,i)}static fetch(e,t=!1,n=0){return this.absCall(e,null,t,n)}}class Fn{vendors;static DEFAULT_LANGUAGE="EN";consentLanguages=new Ln;gvlSpecificationVersion;vendorListVersion;tcfPolicyVersion;lastUpdated;purposes;specialPurposes;features;specialFeatures;stacks;dataCategories;language=Fn.DEFAULT_LANGUAGE;vendorIds;ready=!1;fullVendorList;byPurposeVendorMap;bySpecialPurposeVendorMap;byFeatureVendorMap;bySpecialFeatureVendorMap;baseUrl;languageFilename="purposes-[LANG].json";static fromVendorList(e){let t=new Fn;return t.populate(e),t}static async fromUrl(e){let t=e.baseUrl;if(!t||0===t.length)throw new bn("Invalid baseUrl: '"+t+"'");if(/^https?:\/\/vendorlist\.consensu\.org\//.test(t))throw new bn("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");t.length>0&&"/"!==t[t.length-1]&&(t+="/");let n=new Fn;if(n.baseUrl=t,e.languageFilename?n.languageFilename=e.languageFilename:n.languageFilename="purposes-[LANG].json",e.version>0){let i=e.versionedFilename;i||(i="archives/vendor-list-v[VERSION].json");let s=t+i.replace("[VERSION]",String(e.version));n.populate(await vn.fetch(s))}else{let i=e.latestFilename;i||(i="vendor-list.json");let s=t+i;n.populate(await vn.fetch(s))}return n}async changeLanguage(e){const t=e.toUpperCase();if(!this.consentLanguages.has(t))throw new bn(`unsupported language ${e}`);if(t!==this.language){this.language=t;const n=this.baseUrl+this.languageFilename.replace("[LANG]",e);try{this.populate(await vn.fetch(n))}catch(e){throw new bn("unable to load language: "+e.message)}}}getJson(){return JSON.parse(JSON.stringify({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories,vendors:this.fullVendorList}))}isVendorList(e){return void 0!==e&&void 0!==e.vendors}populate(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.ready=!0)}mapVendors(e){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((e=>{this.byPurposeVendorMap[e]={legInt:new Set,impCons:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((e=>{this.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((e=>{this.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((e=>{this.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((e=>+e))),this.vendorIds=new Set(e),this.vendors=e.reduce(((e,t)=>{const n=this.vendors[String(t)];return n&&void 0===n.deletedDate&&(n.purposes.forEach((e=>{this.byPurposeVendorMap[String(e)].consent.add(t)})),n.specialPurposes.forEach((e=>{this.bySpecialPurposeVendorMap[String(e)].add(t)})),n.legIntPurposes&&n.legIntPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].legInt.add(t)})),n.impConsPurposes&&n.impConsPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].impCons.add(t)})),n.flexiblePurposes&&n.flexiblePurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].flexible.add(t)})),n.features.forEach((e=>{this.byFeatureVendorMap[String(e)].add(t)})),n.specialFeatures.forEach((e=>{this.bySpecialFeatureVendorMap[String(e)].add(t)})),e[t]=n),e}),{})}getFilteredVendors(e,t,n,i){const s=e.charAt(0).toUpperCase()+e.slice(1);let r;const o={};return r="purpose"===e&&n?this["by"+s+"VendorMap"][String(t)][n]:this["by"+(i?"Special":"")+s+"VendorMap"][String(t)],r.forEach((e=>{o[String(e)]=this.vendors[String(e)]})),o}getVendorsWithConsentPurpose(e){return this.getFilteredVendors("purpose",e,"consent")}getVendorsWithLegIntPurpose(e){return this.getFilteredVendors("purpose",e,"legInt")}getVendorsWithFlexiblePurpose(e){return this.getFilteredVendors("purpose",e,"flexible")}getVendorsWithSpecialPurpose(e){return this.getFilteredVendors("purpose",e,void 0,!0)}getVendorsWithFeature(e){return this.getFilteredVendors("feature",e)}getVendorsWithSpecialFeature(e){return this.getFilteredVendors("feature",e,void 0,!0)}narrowVendorsTo(e){this.mapVendors(e)}get isReady(){return this.ready}static isInstanceOf(e){return"object"==typeof e&&"function"==typeof e.narrowVendorsTo}}class xn{callResponder;cmpApiContext;constructor(e,t,n){this.cmpApiContext=new yn,this.cmpApiContext.cmpId=e,this.cmpApiContext.cmpVersion=t,this.callResponder=new F(this.cmpApiContext,n)}fireEvent(e,t){this.cmpApiContext.eventQueue.exec(e,t)}fireErrorEvent(e){this.cmpApiContext.eventQueue.exec("error",e)}fireSectionChange(e){this.cmpApiContext.eventQueue.exec("sectionChange",e)}getEventStatus(){return this.cmpApiContext.eventStatus}setEventStatus(e){this.cmpApiContext.eventStatus=e}getCmpStatus(){return this.cmpApiContext.cmpStatus}setCmpStatus(e){this.cmpApiContext.cmpStatus=e,this.cmpApiContext.eventQueue.exec("cmpStatus",e)}getCmpDisplayStatus(){return this.cmpApiContext.cmpDisplayStatus}setCmpDisplayStatus(e){this.cmpApiContext.cmpDisplayStatus=e,this.cmpApiContext.eventQueue.exec("cmpDisplayStatus",e)}getSignalStatus(){return this.cmpApiContext.signalStatus}setSignalStatus(e){this.cmpApiContext.signalStatus=e,this.cmpApiContext.eventQueue.exec("signalStatus",e)}getApplicableSections(){return this.cmpApiContext.applicableSections}setApplicableSections(e){this.cmpApiContext.applicableSections=e}getSupportedAPIs(){return this.cmpApiContext.supportedAPIs}setSupportedAPIs(e){this.cmpApiContext.supportedAPIs=e}setGppString(e){this.cmpApiContext.gppModel.decode(e)}getGppString(){return this.cmpApiContext.gppModel.encode()}setSectionString(e,t){this.cmpApiContext.gppModel.decodeSection(e,t)}setSectionStringById(e,t){this.setSectionString(Un.SECTION_ID_NAME_MAP.get(e),t)}getSectionString(e){return this.cmpApiContext.gppModel.encodeSection(e)}getSectionStringById(e){return this.getSectionString(Un.SECTION_ID_NAME_MAP.get(e))}setFieldValue(e,t,n){this.cmpApiContext.gppModel.setFieldValue(e,t,n)}setFieldValueBySectionId(e,t,n){this.setFieldValue(Un.SECTION_ID_NAME_MAP.get(e),t,n)}getFieldValue(e,t){return this.cmpApiContext.gppModel.getFieldValue(e,t)}getFieldValueBySectionId(e,t){return this.getFieldValue(Un.SECTION_ID_NAME_MAP.get(e),t)}getSection(e){return this.cmpApiContext.gppModel.getSection(e)}getSectionById(e){return this.getSection(Un.SECTION_ID_NAME_MAP.get(e))}hasSection(e){return this.cmpApiContext.gppModel.hasSection(e)}hasSectionId(e){return this.hasSection(Un.SECTION_ID_NAME_MAP.get(e))}deleteSection(e){this.cmpApiContext.gppModel.deleteSection(e)}deleteSectionById(e){this.deleteSection(Un.SECTION_ID_NAME_MAP.get(e))}clear(){this.cmpApiContext.gppModel.clear()}getObject(){return this.cmpApiContext.gppModel.toObject()}getGvlFromVendorList(e){return Fn.fromVendorList(e)}async getGvlFromUrl(e){return Fn.fromUrl(e)}}let kn=null,Hn=null,Bn=null;function Kn(e=200,t=1){return kn&&Hn===e&&Bn===t||(kn=new xn(e,t),Hn=e,Bn=t),kn}function Wn(e){const t=Kn();if(t&&e)try{t.setGppString(e);const n=t.getApplicableSections();"function"==typeof t.fireSectionChange&&n.forEach((e=>{try{const n={2:"tcfeuv2",7:"usnat",8:"usca"}[e];n&&t.fireSectionChange(n)}catch(e){console.warn("Could not fire section change event:",e)}}))}catch(t){throw console.error("Failed to update GPP API instance with string:",{gppString:e,error:t.message,stack:t.stack}),new Error(`GPP API update failed: ${t.message}. Unable to set GPP string through official API.`)}}function jn(e){return"number"==typeof e&&Number.isInteger(e)&&e>=0&&e<=2?e:0}function zn(e,t,n){const i=e.ChannelID,s=t.Scripts&&t.Scripts.find((e=>e.ChannelID===i)),r=t.Template&&t.Template.Categories&&t.Template.Categories.find((e=>e.ChannelID===i));let o;if(s&&s.IsStrictlyNecessary)o=e.False;else if(n&&Array.isArray(n)){const t=n.find((e=>String(e.channelId)===String(i)));o=t?t.isOptIn?e.False:e.True:r&&"boolean"==typeof r.DefaultValue?r.DefaultValue?e.False:e.True:0}else o=r&&"boolean"==typeof r.DefaultValue?r.DefaultValue?e.False:e.True:0;return jn(o)}function $n(e){return!(!e?.MSPAConfig&&!e?.TCFconfig)}window.CassieWidgetLoaderModule=class{constructor(e){this.widgetProfileId=e.widgetProfileId??null,this.licenseKey=e.licenseKey??null,this.licenseKey||console.error("licenseKey cannot be null"),this.identityUrl=e.identityUrl??null,this.supportedDomains=e.supportedDomains??null,this.languageCode=e?.languageCode??"en",this.customOptions=e.customOptions??null,this.region=e.region??"use",this.environment=e.environment??"production",this.crossDomainConsent=e.crossDomainConsent??!1,this.widgetProfileUrl=e.widgetProfileUrl??null,this.profileSettings=e.widgetProfileSettings??null,this.widgetLocation=e.widgetLocationUrl??null,this.customServerPath=e.customServerPathUrl??null,this.Widget=null,this.baseUrl=e.baseUrl??this.getBaseUrl(),this.recordUrl=e.recordUrl??null,this.error={isError:!1,errorMessage:""},this.isLoading=!0,this.init()}async init(){if("dev"===this.environment)await this.initWidget();else try{await this.initWidget()}catch(e){this.isLoading=!1,this.error.isError=!0,this.error.errorMessage=e}}async initWidget(){this.profileSettings=this.profileSettings??await i(this.getProfileUrl()),this.widgetLocation=this.widgetLocation??this.getWidgetUrl(this.profileSettings.Version);const[e,t]=await Promise.all([this.getWidget(),this.getProfiler()]);this.loadScriptToDOM(e);const n=o(this.licenseKey,this.profileSettings,this.baseUrl,this.languageCode,this.supportedDomains,this.environment,this.region,this.recordUrl,this.identityUrl,t,this.customOptions);this.Widget=new class{constructor(e){this.eventManager=m,this.datetimestamp=(new Date).getTime(),this.domain=(new a).getDomainName();let t=e.languageCode??"";(e.profileSettings?.SupportedLanguages??[]).includes(t)&&""!==t||(t="Default"),this.cassieSettings=o(e.licenseKey,e.profileSettings,e.baseUrl,t,e.supportedDomains??`*.${this.domain}`,e.environment,e.region,e.recordUrlDomain,e.identityServiceUrl,e.crossDomainConsent,e.customOptions),this.cassieSettings.profileSettings?.TemplateOptions||(this.cassieSettings.profileSettings.TemplateOptions={},this.cassieSettings.profileSettings.TemplateOptions.PreBannerPosition=this.cassieSettings.profileSettings?.TemplateOptions?.PreBannerPosition??1,this.cassieSettings.profileSettings.TemplateOptions.WidgetPosition=this.cassieSettings.profileSettings?.TemplateOptions?.WidgetPosition??2),this.urlHelper=new a(this.cassieSettings),this.cookieWidgetDivId="cassie-widget",this.cassieScripts=[],this.widgetTemplate={},this.cassieWidgetFileSettings=null,this.gpcEnabled=(e.profileSettings.GpcEnabled&&navigator.globalPrivacyControl)??!1,this.gppString=null,this.bannerIsVisible=!1,this.modalIsVisible=!1,this.handlerUrl=this.urlHelper.getSubmissionUrl(),this.forceReconsent=!1,this.extentions=[{event:"Sample",extentionFunction:function(){}}],this.init()}async init(){this.createWidgetElement(),await this.getWidgetJson(),this.initializeGppWithConfig(),this.cassieWidgetFileSettings.identityServiceUrl&&!this.cassieSettings?.customOptions?.isPreview&&(this.cassieSettings.identityServiceUrl=this.cassieWidgetFileSettings.identityServiceUrl),this.cassieSettings?.crossDomainConsent&&this.cassieWidgetFileSettings.identityServiceUrlPrefix&&(this.cassieSettings.identityServiceUrl=`https://${this.cassieWidgetFileSettings.identityServiceUrlPrefix}.${this.domain}`),this.syrenisHelper=new w(this.cassieSettings,this.cassieWidgetFileSettings.CassieCookiesExpiry),this.cookieHelper=new g(this.cassieSettings.profileSettings.AccessKey,this.cassieWidgetFileSettings.CassieCookiesExpiry),this.loadExistingGppString();const e=this.cassieWidgetFileSettings.reconsentDate.toString(),t=e.endsWith("Z")?new Date(e):new Date(`${e}Z`);this.forceReconsent=t.getTime()>this.cookieHelper.getConsentDate()&&this.cookieHelper.hasConsent(),!this.forceReconsent&&await this.syrenisHelper.runConsent(this.cassieScripts),this.widgetTemplate.hasConsent=this.cookieHelper.hasConsent()&&!this.forceReconsent,this.widgetTemplate.useRadioButtons=this.cassieSettings.profileSettings.UseRadioButtons??!1,this.widgetTemplate.hasConsent||this.dropCookiesOnLoad(),await this.getTemplateFiles(),m.emit("CassieTemplateFilesLoaded")}registerExtention(e,t){this.extentions.push({event:e,extentionFunction:t})}runExtentions(e){const t=this.extentions.filter((t=>t.event===e));for(const e of t)e.extentionFunction()}dropCookiesOnLoad(){const e="auto_save",t=this.widgetTemplate.CookieDropBehaviourType??1;if(3===t)this.acceptAll(e);else if(2===t){const e=this.cookieHelper.getPrivacyPolicyId()??this.cassieWidgetFileSettings.privacyId;this.syrenisHelper.dropStrictlyNecessaryCookies(e,this.cassieScripts)}else if(4===t)this.acceptAll({respectGpc:!0,source:e});else if(1===t)return}getCurrentConsent(){return this.syrenisHelper.getCurrentConsent()}getGtmConsent(){const e=this.syrenisHelper.getCurrentConsent(),t=this.cassieScripts.filter((e=>null!==e.gtmConsentType&&""!==e.gtmConsentType)).reduce(((e,t)=>{const n=t.gtmConsentType;return e[n]||(e[n]=[]),e[n].push(t.channelId),e}),{});return Object.keys(t).map((n=>{const i=t[n].every((t=>e.find((e=>e.channelId==t))?.isOptIn))?"granted":"denied",s={};return s[n]=i,s}))}storeGtmConsent(){const e=this.getGtmConsent();e.length>0&&this.syrenisHelper.saveGtmConsent(e)}initializeTemplate(){m.emit("CassieTemplateInitialized")}hideBanner(){this.runExtentions("hideBanner"),this.bannerIsVisible=!1,m.emit("CassieBannerVisibility",!1)}showBanner(){this.runExtentions("showBanner"),this.bannerIsVisible=!0,m.emit("CassieBannerVisibility",!0)}showModal(){this.runExtentions("showModal"),this.modalIsVisible=!0,m.emit("CassieModalVisibility",!0)}hideModal(){this.runExtentions("hideModal"),this.modalIsVisible=!1,m.emit("CassieModalVisibility",!1)}getPreferences(){const e=this.widgetTemplate.Categories,t=[];return e.forEach((e=>{const n=e.Cookies.map((e=>({channelId:e.ChannelID,gpcEnabled:e.GpcEnabled,statementId:e.Statements[0].StatementID,isOptIn:e.DefaultValue})));t.push(...n)})),t}async submitConsent(e,t){if(m.emit("CassieSubmitConsent",e),await this.syrenisHelper.processConsent(e,this.cassieScripts,this.cassieWidgetFileSettings),this.storeGtmConsent(),this.widgetTemplate.hasConsent=!0,m.emit("CassieProcessedConsent",e),this.generateAndStoreGPPString(e),this.handlerUrl)try{const n=this.cookieHelper.getPrivacyPolicyId()??this.cassieWidgetFileSettings.privacyId;this.syrenisHelper.postConsentToCassie(this.handlerUrl,t,n,this.cookieWidgetDivId).then((t=>(m.emit("CassieSubmittedConsent",e),t))).catch((async()=>{const i=await this.syrenisHelper.postConsentToCassie(this.defaultSubmissionUrl,t,n,this.cookieWidgetDivId);return m.emit("CassieSubmittedConsent",e),i}))}catch(e){return console.error(e),e}}addConsentListener(e){document.addEventListener("CassieProcessedConsent",e)}hasConsent(){return this.widgetTemplate.hasConsent=this.cookieHelper.hasConsent(),this.widgetTemplate.hasConsent}getGppString(){return this.gppString}acceptAll(e){if("string"==typeof e||null==e){var t=e;(e={}).source=t,e.respectGpc=!1}let n=this.getPreferences();n.forEach((t=>{t.isOptIn=!0,e.respectGpc&&this.gpcEnabled&&t.gpcEnabled&&(t.isOptIn=!1)})),this.submitConsent(n,e.source)}rejectAll(e){const t=this.getPreferences();t.forEach((e=>e.isOptIn=!1)),this.submitConsent(t,e)}createWidgetElement(){if(!this.cassieSettings.profileSettings.LoadTemplateHtml)return;const e=this.cookieWidgetDivId;if(null===document.getElementById(e)||0===document.getElementById(e).length){const t=document.createElement("div");t.setAttribute("id",e),t.style.display="none",t.classList.add("syrenis-cookie-widget"),document.body.appendChild(t)}}async getWidgetJson(){const e=this.urlHelper.determineJsonUrl(),t=await i(e);this.populateWidget(t),m.emit("CassieWidgetFileLoaded",null)}async getTemplateFiles(){const e=`${this.cassieSettings.baseUrl}/templates/${this.cassieSettings.profileSettings.Template}/`;if(this.cassieSettings.profileSettings.LoadTemplateCss&&this.AddCssToPage(`${e}template.min.css`),this.cassieSettings.profileSettings.LoadTemplateHtml){const t=await i(`${e}index.min.html`);this.loadHtmlToElement(this.cookieWidgetDivId,t)}if(this.cassieSettings.profileSettings.LoadTemplateJs){const t=await i(`${e}template.min.js`);this.loadScriptToDOM(t)}}AddCssToPage(e){const{head:t}=document,n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.insertBefore(n,t.childNodes[0])}loadHtmlToElement(e,t){const n=document.getElementById(e);n.innerHTML=t;const i=document.getElementsByTagName("body")[0];i.insertBefore(n,i.firstChild)}loadScriptToDOM(e){const t=document.createElement("script");t.innerHTML=e,document.body.appendChild(t)}loadExistingGppString(){if((!this.cassieWidgetFileSettings||!this.cassieWidgetFileSettings.widgetResponse||$n(this.cassieWidgetFileSettings.widgetResponse))&&this.cookieHelper){const e=this.cookieHelper.getCookieValueByName("__gpp");e&&(this.gppString=e,function(e,t=200,n=1){const i=Kn(t,n);if(e)try{Wn(e),"function"==typeof i.setCmpStatus&&i.setCmpStatus("loaded"),"function"==typeof i.setSignalStatus&&i.setSignalStatus("ready"),"function"==typeof i.fireEvent&&(i.fireEvent("cmpStatus","loaded"),i.fireEvent("signalStatus","ready"))}catch(e){console.error("Failed to initialize GPP for returning user:",e)}}(e))}}initializeGppWithConfig(){if(this.cassieWidgetFileSettings&&this.cassieWidgetFileSettings.widgetResponse){const{widgetResponse:e}=this.cassieWidgetFileSettings;if(!$n(e))return;!function(e,t=200,n=1){$n(e)&&function(e=200,t=1){const n=Kn(e,t);if("undefined"!=typeof window&&(window.__gpp=function(t,i,s){if(!n)return window.__gpp.queue||(window.__gpp.queue=[]),void window.__gpp.queue.push({command:t,callback:i,parameter:s});try{switch(t){case"addEventListener":"function"==typeof i&&n.addEventListener(s,i);break;case"removeEventListener":"function"==typeof i&&n.removeEventListener(s,i);break;case"hasSection":{const e=n.hasSection(s);return"function"==typeof i&&i(e,!0),e}case"getSection":{const e=n.getSection(s);return"function"==typeof i&&i(e,!0),e}case"getGPPString":{const e=n.getGppString();return"function"==typeof i&&i(e,!0),e}case"getField":{const e=n.getFieldValue(s);return"function"==typeof i&&i(e,!0),e}case"ping":{const t=n.getApplicableSections()||[],s=n.getGppString()||"",r=n.getObject()||{},o={gppVersion:"1.1",cmpStatus:"loaded",cmpDisplayStatus:"hidden",signalStatus:"ready",supportedAPIs:["2:tcfeuv2","7:usnat"],cmpId:e,sectionList:t,applicableSections:t,gppString:s,parsedSections:r};return"function"==typeof i&&i(o,!0),o}default:"function"==typeof i&&i(null,!1)}}catch(e){console.error("GPP API Error:",e),"function"==typeof i&&i(null,!1)}},window.__gpp.queue&&Array.isArray(window.__gpp.queue))){const e=window.__gpp.queue.slice();window.__gpp.queue=[],e.forEach((e=>{window.__gpp(e.command,e.callback,e.parameter)}))}}(t,n)}(e)}}generateAndStoreGPPString(e){if(!this.cassieWidgetFileSettings||!this.cassieWidgetFileSettings.widgetResponse)return void console.error("CassieWidget: widgetResponse not available for GPP string generation.");const{widgetResponse:t}=this.cassieWidgetFileSettings;if($n(t)){e||console.warn("CassieWidget: formConsent not available for GPP string generation.");try{const n=200,i=1;this.gppString=function(e,t,n,i=null){if(!e?.MSPAConfig&&!e?.TCFconfig)return null;const s=Kn(t,n),r=[];if(e.MSPAConfig){const t=e.MSPAConfig,n="usnat";if(t.Core)for(const[r,o]of Object.entries(t.Core))if("SaleOptOut"===r||"SharingOptOut"===r||"TargetedAdvertisingOptOut"===r){const t=o;if("object"==typeof t&&null!==t&&"ChannelID"in t&&"True"in t&&"False"in t){const o=zn(t,e,i);s.setFieldValue(n,r,o)}else{const e=jn(o);s.setFieldValue(n,r,e)}}else s.setFieldValue(n,r,o);if(t.SensitiveDataProcessing&&t.SensitiveDataProcessing.items){const r=new Array(16).fill(0);for(let n=1;n<=16;n++){const s=n.toString();if(void 0!==t.SensitiveDataProcessing.items[s]){const o=t.SensitiveDataProcessing.items[s];if("object"==typeof o&&null!==o&&"ChannelID"in o&&"True"in o&&"False"in o){const t=zn(o,e,i);r[n-1]=t}else r[n-1]=jn(o)}}s.setFieldValue(n,"SensitiveDataProcessing",r)}if(t.KnownChildSensitiveDataConsents&&t.KnownChildSensitiveDataConsents.items){const r=new Array(10).fill(0);for(let n=1;n<=10;n++){const s=n.toString();if(void 0!==t.KnownChildSensitiveDataConsents.items[s]){const o=t.KnownChildSensitiveDataConsents.items[s];if("object"==typeof o&&null!==o&&"ChannelID"in o&&"True"in o&&"False"in o){const t=zn(o,e,i);r[n-1]=t}else r[n-1]=jn(o)}}s.setFieldValue(n,"KnownChildSensitiveDataConsents",r)}if(void 0!==t.PersonalDataConsents){const r=t.PersonalDataConsents;if("object"==typeof r&&null!==r&&"ChannelID"in r&&"True"in r&&"False"in r){const t=zn(r,e,i);s.setFieldValue(n,"PersonalDataConsents",t)}else s.setFieldValue(n,"PersonalDataConsents",jn(r))}void 0!==t.MspaCoveredTransaction&&s.setFieldValue(n,"MspaCoveredTransaction",t.MspaCoveredTransaction),void 0!==t.MspaOptOutOptionMode&&s.setFieldValue(n,"MspaOptOutOptionMode",t.MspaOptOutOptionMode),void 0!==t.MspaServiceProviderMode&&s.setFieldValue(n,"MspaServiceProviderMode",t.MspaServiceProviderMode),r.includes(7)||r.push(7)}if(e.TCFconfig){const t=e.TCFconfig,n="tcfeuv2";for(const[e,i]of Object.entries(t))null!=i&&("Created"===e||"LastUpdated"===e?s.setFieldValue(n,e,new Date(i)):s.setFieldValue(n,e,i));r.includes(2)||r.push(2)}return r.sort(((e,t)=>e-t)),s.setApplicableSections(r),s.getGppString()}(t,n,i,e),this.gppString&&this.cookieHelper?(this.cookieHelper.storeCookie("__gpp",this.gppString,null,null),Wn(this.gppString)):this.gppString||this.cookieHelper&&this.cookieHelper.storeCookie("__gpp","",null,null)}catch(e){console.error("CassieWidget: Error generating or storing GPP string:",e),this.gppString=null}}}populateWidget(e){if(this.cassieWidgetFileSettings=new class{constructor(e){this.persistCookies=e.Custom1stPartyData?.length>0?e.Custom1stPartyData:[],this.privacyId=function(e){if(!e)return 1;const t=e.substring(0,e.length-1).split("|"),n=[];for(let e=0;e<t.length;e++){const i=t[e].split(","),s="1"===i[1],r={PrivacyId:Number(i[0]),Default:s,ValidFrom:i[2]};n.push(r)}n.sort((function(e,t){return Date.parse(t.ValidFrom)-Date.parse(e.ValidFrom)}));const i=n.find((e=>!1===e.Default));return i?i.PrivacyId:1}(e.Template.PrivacyPolicies),this.identityServiceUrl=e.IdentityServiceEndpoint??null,this.reconsentDate=e.ReconsentDate??"1970-01-01T00:00:00.000Z",this.cookieCollectionId=e.CookieCollectionId??1,this.identityServiceUrlPrefix=e.IdentityServiceUrlPrefix??null,this.CassieCookiesExpiry=e.CassieCookiesExpiry??365}}(e),this.cassieWidgetFileSettings.widgetResponse=e,e.Scripts.forEach((e=>{const t=new class{constructor(e){this.cookieId=e.CookieID,this.scriptName=e.CookieName,this.scriptDescription=e.CookieDescription??new String(""),this.isStrictlyNecessary=e.IsStrictlyNecessary??!1,this.isRunFirst=e.IsRunFirst??!1,this.executionOrder=e.ExecutionOrder??100,this.bodyOptIn=e.CookieBodyDivID,this.bodyOptOut=e.CookieBodyDivIDOptOut,this.headOptIn=e.CookieScript,this.headOptOut=e.CookieScriptOptOut,this.cookieUrl=e.CookieURL,this.channelId=e.ChannelID,this.expiry=e.Expiry,this.browserCookieName=e.BrowserCookieName,this.gtmConsentType=e.GtmConsentType}CreateScript(e){this.cookieId=e.cookieId,this.scriptName=e.scriptName,this.scriptDescription=e.scriptDescription,this.isStrictlyNecessary=e.isStrictlyNecessary,this.isRunFirst=e.isRunFirst,this.executionOrder=e.executionOrder,this.bodyOptIn=e.bodyOptIn,this.bodyOptOut=e.bodyOptOut,this.headOptIn=e.headOptIn,this.headOptOut=e.headOptOut,this.cookieUrl=e.cookieUrl,this.channelId=e.channelID,this.gtmConsentType=e.gtmConsentType}}(e);this.cassieScripts.push(t)})),!(e.AccessKeys?.split(",").map((e=>e.toLowerCase().trim()))??[]).includes(this.cassieSettings.profileSettings.AccessKey.toLowerCase().trim())&&"n/a"!==this.cassieSettings.profileSettings.AccessKey&&!this.cassieSettings?.customOptions?.isPreview)throw new Error(`Access Key: ${this.cassieSettings.profileSettings.AccessKey}\n        can't access widget with id: ${this.cassieSettings.widgetId}`);const t=e.SupportedDomains;if(t&&!this.urlHelper.checkDomain(t)&&!this.cassieSettings?.customOptions?.isPreview)throw new Error(`Not supported domain: ${this.domain} the supported domains are ${t}.\n        Please add this domain to your Cassie system`);this.cassieSettings.supportedDomains=t,this.widgetTemplate=e.Template,this.widgetTemplate.strictlyNecessary=e.Scripts.filter((e=>e.IsStrictlyNecessary)).map((e=>({cookieName:e.CookieName,cookieDescription:e.CookieDescription}))),this.widgetTemplate.Services=e.Scripts}}(n),this.isLoading=!1}getProfiler(){return this.crossDomainConsent?new Promise((e=>{try{e(this.crossDomainConsent?"true":null)}catch(t){this.error.isError=!0,this.error.errorMessage=t,e(!1)}})):Promise.resolve(null)}getWidget(){return new Promise((async(e,t)=>{try{e(await i(this.widgetLocation))}catch(e){t(e)}}))}getWidgetUrl(e){return`${this.baseUrl}/versions/${e??"4"}/widget.js`}getBaseUrl(){if(this.customServerPath)return this.customServerPath;const e="production"===this.environment?"":`-${this.environment}`,t=window?.cassieResourceRootDomain??"cassiecloud.com";return`https://cscript-cdn-${this.region}${e}.${t}`}getProfileUrl(){return this.widgetProfileUrl?this.widgetProfileUrl:`${this.getBaseUrl()}/${this.licenseKey}/widgetProfile/widgetProfile_${this.widgetProfileId}.json`}loadScriptToDOM(e){const t=document.createElement("script");t.innerHTML=e,document.body.appendChild(t)}loadExternalScriptToDOM(e){const t=document.createElement("script");t.src=e,document.body.appendChild(t)}compactFormatKeyValueToJson(e){if(!e||"string"!=typeof e||""===e.trim())return[];let t=[],n=e.split("|"),i={};return n.forEach((e=>{let[n,s]=e.split(":");n&&void 0!==s?i[n]=s:(t.push(i),i={})})),Object.keys(i).length>0&&t.push(i),t}getSyrenisGtmConsent(){const e=this.profileSettings.AccessKey;let t="";const n=document.cookie.split(";");for(let i=0;i<n.length;i++){const s=n[i].trim(),r=`SyrenisGtmConsent_${e}=`;if(0===s.indexOf(r)){t=s.substring(r.length);break}}if(""!==t)try{return JSON.parse(t)}catch(e){return compactFormatKeyValueToJson(t)}return null}}}()}();