function initMenus(b,a){if(typeof a=="undefined"||a==null){a=document;}$("li.treenode",a).children("a.title").click(function(){$this=$(this);$this.toggleClass("treeopen");$this.siblings("div.desc").slideToggle(150);return false;});if(b){b();}return false;}function initTippyMenus(b,a){if(typeof a=="undefined"||a==null){a=document;}$(a).off().on("click","a.myasu-tippy-option",function(c){c.preventDefault();$this=$(this);$this.siblings("div.desc").slideToggle(150);$this.find(".myasu-tippy-option-expand-up").toggle();$this.find(".myasu-tippy-option-expand-down").toggle();});$(a).off().on("click","button.myasu-tippy-option",function(c){$this=$(this);$this.siblings("div.desc").slideToggle(150);$this.find(".myasu-tippy-option-expand-up").toggle();$this.find(".myasu-tippy-option-expand-down").toggle();});addLinkTracking($("a.myasu-tippy-option",a));addLinkTracking($("button.myasu-tippy-option",a));if(b){b();}return false;}