#quicklinks-page {
	max-width: 40em;
}

#quicklinks-page h2 {
	font-weight: bold;
	padding-bottom: 0.25em;
	border-bottom: 1px solid gray;
}

.quicklinks-list {
	text-align: center;
}

.quicklinks-list .quicklink-item {
	margin: 1em;
}

.quicklinks-table {
	font-size: 1.167em;
	border: 1px solid #aaa;
	min-width: 100%;
}

.quicklinks-table .table-row:nth-child(even) {
	background-color: #efefef;
}

.quicklinks-table .table-row {
	/* padding: 0.5em; */
}

.quicklinks-table .table-cell {
	padding: 0.6em;
}

.quicklinks-table .table-header-group .table-cell {
	background-color: #ddd;
	color: black;
	border-bottom: 1px solid #aaa;
}

.quicklinks-table .table-cell a {
	color: #333;
	border-bottom: 1px dotted #333;
}

.quicklinks-table .table-cell a:hover {
	color: #8C1D40;
	border-bottom: 1px solid #8C1D40;
}

.quicklinks-table .table-cell a .quicklink-anchor-img {
	vertical-align: middle;
	margin-right: 4px;
}

.quicklinks-table .quicklinks-entry-sortable-chosen {
	background-color: #8C1D40 !important;
}

.quicklinks-table .quicklinks-entry-sortable-chosen a, .quicklinks-table .quicklinks-entry-sortable-chosen span {
	color: #f2f2f2 !important;
}

.handle {
	cursor: grab;
	border: 0;
    background-color: transparent;
    padding: 0px;
}

.handle:active {
	cursor: grabbing;
}

#my_links_add {
	font-size: 1.167em;
	margin: 1em 0;
}

.add_bookmark_modal
, .edit_bookmark_modal {
	position: fixed;
	width: 300px;
	left: 50%;
	top: 50%;
	background-color: white;
	padding: 16px;
	transform: translate(-50%, -50%);
	border-radius: 5px;
}

.add_bookmark_modal .modal_title
, .edit_bookmark_modal .modal_title {
	font-size: 1.333em;
	color: #555555;
}

/* .edit_bookmark_modal {
	position: absolute;
	left: 30%;
	width: 300px;
	background-color: white;
	padding: 12px;	
} */

.bookmark-form-controls {
	display: flex;
	justify-content: space-between;
}

.bookmark-form-error {
	margin-top: 0.2em;
	margin-bottom: 0.2em;
	color: red;
}

#edit-remove-bookmark {
	background-color: #dbdbdb;
    color: #2a2a2a;
}