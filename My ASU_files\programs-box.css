/* START New 2021 Programs Box */

.programs-notification {
	font-size: 0.85em;
	background-color: #8c1d40;
	color: white;
	display: inline-flex;
	align-content: center;
    justify-content: center;
	padding: 2px;
	margin-left: 0.5em;
	width: 1.5em;
	height: 1.5em;
	text-align: center;
	font-weight: normal;
	border-radius: 50%;
}

.programs-task {
	margin-bottom: 0.5em;
}

.programs-task > .task-title {
	font-weight: bold;
	padding-right: 0.5em;
}

.programs-task.alert > .task-title {
	color: #FF7F32;
}

.programs-task.alert > .task-title:before {
	color: #FF7F32;
	content: "\26A0";
	padding-right: 0.5em;
}

.graduation-tab .box-section-2020-need-bottom:last-of-type {
	border-bottom: 1px solid #d0d0d0;
}

#candidacy-advance-congrats-container {
	background-color: var(--myasu-maroon);
	color: var(--myasu-white);
	margin-left: -16px;
    width: calc(100% + 32px);
    padding: 16px;
    display: flex;
    align-items: center;
    position: relative;
}

.candidacy-advance-congrats-container-dismissed {
	background-color: var(--myasu-gray-10) !important;
	color: var(--myasu-gray-80) !important;
    padding: 0px 16px !important;
}

#candidacy-advance-congrats-container #candidacy-advance-congrats-icon-container {
	filter: brightness(0) invert(1);
    margin-right: 16px;
    width: 40px;
    height: 40px;
    padding: 0px;
    background: transparent;
    border: 0;
}

#candidacy-advance-congrats-container #candidacy-advance-congrats-icon-container #candidacy-advance-congrats-icon-party-horn {
	width: 40px;
	height: 40px;
}

#candidacy-advance-congrats-container #candidacy-advance-congrats-text-container #candidacy-advance-congrats-text {
	font-size: var(--myasu-font-lg);
	font-weight: var(--myasu-font-bold);
	margin-bottom: 8px;
}

#candidacy-advance-congrats-container.candidacy-advance-congrats-container-dismissed #candidacy-advance-congrats-text-container #candidacy-advance-congrats-text {
	font-size: var(--myasu-font-md);
}

#candidacy-advance-congrats-container #candidacy-advance-congrats-text-container a {
	color: var(--myasu-white);
	font-weight: var(--myasu-font-bold);
	text-decoration: underline;
	border: 0px;
}

#candidacy-advance-congrats-container.candidacy-advance-congrats-container-dismissed #candidacy-advance-congrats-text-container a {
	color: var(--myasu-maroon);
}

#candidacy-advance-congrats-container #candidacy-advance-congrats-dismiss {
	position: absolute;
    right: 0px;
    top: 0px;
    cursor: pointer;
    font-size: var(--myasu-font-sm);
}

.milestone-congrats-container {
	background-color: var(--myasu-maroon);
	color: var(--myasu-white);
	margin-left: -16px;
    width: calc(100% + 32px);
    padding: 16px;
    display: flex;
    align-items: center;
    position: relative;
}

.milestone-congrats-container-dismissed {
	background-color: var(--myasu-gray-10) !important;
	color: var(--myasu-gray-80) !important;
    padding: 0px 16px !important;
}

.milestone-congrats-container .milestone-congrats-icon-container {
	filter: brightness(0) invert(1);
    margin-right: 16px;
    width: 30px;
    height: 30px;
    padding: 0px;
    background: transparent;
    border: 0;
}

.milestone-congrats-container .milestone-congrats-icon-container .milestone-congrats-icon-party-horn {
	width: 30px;
	height: 30px;
}

.milestone-congrats-container .milestone-congrats-text-container .milestone-congrats-text {
	font-size: var(--myasu-font-md);
	font-weight: var(--myasu-font-bold);
}

.milestone-congrats-container .milestone-congrats-text-container a {
	color: var(--myasu-white);
	font-weight: var(--myasu-font-bold);
	text-decoration: underline;
	border: 0px;
}

.milestone-congrats-container.milestone-congrats-container-dismissed .milestone-congrats-text-container a {
	color: var(--myasu-maroon);
}

.milestone-congrats-container .milestone-congrats-dismiss {
	position: absolute;
    right: 0px;
    cursor: pointer;
    font-size: var(--myasu-font-sm);
}

/* END New 2021 Programs Box */


/* 
Below this comment is old 2016 programs box update stuff.  
It should be considered for deletion and not used in new code. 
If any of it is still relevant, move it above this comment.
Ideally, the programs box would use common styling and this file wouldn't exist.
*/


.programs-tab .box-padding-horiz {
	padding-left: 16px;
	padding-right: 16px;
}
.programs-tab.box-padding-vert {
	padding-top: 16px;
	padding-bottom: 0;
}

.no-programs {
	display: flex;
	padding: 16px;
	align-items: center;
}

.no-programs #guest-no-degree-programs {
	font-style: italic;
}

.no-programs .no-programs-image {
	width: 100px;
	margin-right: 16px;
}

.no-programs #find-a-program-btn {
	margin-top: 16px;
}

#my-programs-box #find_programs_tab a {
	color: var(--myasu-maroon);
	text-decoration: underline;	
	border: 0px;
}

#my-programs-box #find_programs_tab .program-search {
	display: inline-block;
    margin: 0px;
    padding: 10px 8px;
    width: calc(100% - 96px);
    max-width: 360px;
    height: 35px;
    border: 1px solid #cccccc;
    margin-right: 12px;
}

#my-programs-box #find_programs_tab .find-programs-search-explore {
	margin-top: 16px;
}

#my-programs-box #find_programs_tab .find-programs-search-explore a {
	font-weight: var(--myasu-font-bold);
}

#my-programs-box #find_programs_tab .find-program-search {
	background-color: var(--myasu-gray-95);	
}

#my-programs-box #find_programs_tab .find-program-search:hover {
	background-color: var(--myasu-gray-80);	
}

.program-summary {
	display: table-cell;
	vertical-align: top;
}
.program-title {
	margin-bottom: 3px;
}
.program-name {
	/* color: #007BB6; */
	/* font-size: 1.1em; */ 
	/* line-height: 1.4; */
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	margin-bottom: 8px;
}

.program-name a {
	border-bottom: none;
}

a.action-blue {
	color: #007BB6 ;
	text-decoration: none ;
	border-bottom: none ;
}

a:hover.action-blue {
	color: #007BB6 ;
	text-decoration: underline ;
	border-bottom: none ;
}

a.arrowed, a.arrowed:hover {
	background-image: url(/myasu/v4.0/images/arrow-blue-down2.png);
	background-repeat: no-repeat;
	padding-right: 12px;
	width: 9px;
	height: 9px;
	background-position: right center;
}

a.arrowed.selected, a.arrowed.selected:hover {
	background-image: url(/myasu/v4.0/images/arrow-blue-up2.png);
	background-repeat: no-repeat;
	padding-right: 12px;
	width: 9px;
	height: 9px;
	background-position: right center;
}

.program-detail-links a:not(.just-a-link) {
	border-bottom: initial;
	text-decoration: none;
}

.program-detail-links a:hover {
	border-bottom: none;
	text-decoration: underline;
}

.program-detail-links a.arrowed, .program-detail-links a.arrowed:hover {
	background-image: url(/myasu/v4.0/images/arrow-gray-down-sml.png);
	background-repeat: no-repeat;
	padding-right: 12px;
	width: 9px;
	height: 9px;
	background-position: right center;
}

.program-detail-links a.arrowed.selected, .program-detail-links a.arrowed.selected:hover {
	background-image: url(/myasu/v4.0/images/arrow-gray-up-sml.png);
	background-repeat: no-repeat;
	padding-right: 12px;
	width: 9px;
	height: 9px;
	background-position: right center;
}


.program-icon-holder {
	display: table-cell;
	vertical-align: top;
}

.program-icon {
	display: inline-block;
	margin-right: 8px;
	border: 1px solid #007BB6;
	padding: 0;
	color: #007BB6;
	min-width: 34px;
}

.program-icon.image-holder {
	height: 30px;
	position: relative;
}

.program-icon img {
	position: absolute;
	top: 0;
	bottom: 0;
	left: 0;
	right: 0;
	margin: auto;
}

.program-icon.completed {
	border-color: #088A08;
}


.program-icon.honors {
	background-color: #007BB6;
}

.program-icon > .degree-type {
	min-height: 1em;
	font-weight: bold;
	font-size: 0.917em;
	padding: 2px;
	background-color: #007BB6;
	color: white;
	text-align: center;
}

.program-icon > .plan-type {
	min-height: 1em;
	font-weight: bold;
	font-size: 0.917em;
	padding: 2px;
	background-color: white;
	color: #A0A0A0;
	text-align: center;
}

/* Remove this when 3190 is closed */
.program-track-status, .est-grad-term {
	font-weight: bold;
	font-size: 0.8em;
	background-color: #BDBDBD;
	color: black;
	border-radius: 0.25rem;
	padding: .25rem .75rem;
	margin-left: 0.5rem;
}

.program-track-status a, .est-grad-term a, .program-track-status a:hover, .est-grad-term a:hover {
	color: white ; /* remove importants once the overall a tags issue is fixed in myasucss */
	border: 0  ;
}

.confer-date {
	white-space: nowrap;
}

.program-basics {
	/* margin-bottom: 5px; */
	/* display: table; */
}

.program-details {
	display: none;
	/* margin-top: 10px; */ /* disable for 2021 update */
	background-color: #f5f5f5;
	/* padding: 5px 5px 5px 15px; */ /* disable for 2021 update */
}

.program-details .table-cell {
	padding: 2px 0;
}

.program-details .table-row .table-cell:first-of-type {
	width: 1%;
	padding-right: 1.5em;
}

/* .program-details .table-row {
	padding: 2px 0;
} */

.program-detail-links span {
	margin-right: 10px;
}

.grad-apply-link.btn-maroon {
	font-weight: normal;
	border: 0 ;
}

.program-detail-links .alert {
	font-size: 0.833em;
	line-height: 1;
	background-color: #FF7F32;
	color: white;
	position: relative;
	padding: 0px 4px;
	margin-left: 8px;
	margin-right: 0;
	border-radius: 2px;
}
.program-detail-links .alert:before {
	content: "";
	position: absolute;
	right: 100%;
	top: 3px;
	width: 0;
	height: 0;
	border-top: 3px solid transparent;
	border-right: 4px solid #FF7F32;
	border-bottom: 3px solid transparent;	
}

.link-descr {
	/* padding-bottom: 8px; */ /* Disabled for 2021 update */
}

.link-descr a {
	/* color: #8C1D40 ; */
	/* font-size: 1.1em; */
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	/* border-bottom: none; */
}

/* .link-descr a:hover {
	color: #D23153;
	border-bottom: 1px solid #D23153;
} */

.program-details .task {
	margin-bottom: 0.5em;
	/* margin-left: 1em; */
	/* line-height: 1.1; */
}

.program-details .task > .task-title {
	font-weight: bold;
	padding-right: 0.5em;
}

.program-details .task.alert > .task-title {
	color: #FF7F32;
}

.program-details .task.alert > .task-title:before {
	color: #FF7F32;
	content: "\26A0";
	padding-right: 0.5em;
}

.program-details .task > .task-details {
	/* color: var(--myasu-gray-95); */ /* leave as default #333? check contrast*/
}

.program-details .contact-info span {
	padding-right: 1em;
}

ul.barrett-status {
	padding-left: 0;
	margin-left: 16px;
}

/** Legacy stuff; Goal: get rid of use of ID for styling **/

/* For My Programs box */
.my-programs-tabs h4 {
    border-bottom: 0;
    margin: 0;
}
.my-programs-tabs label {
    font-weight: bold;
}
.my-programs-tabs select {
    font-size:1.0em;
}
.my-programs-tabs input[type=image] {
    margin-top:-1px;
}
#ungrad-links {
    margin: 0 8px;
    padding: 4px;
    background-color: var(--myasu-gray-05);
}
#arrowDiv-ungrad {
    padding-bottom: 5px;
}
#arrowDiv-ungrad form {
    margin:0;
    padding:5px 0;
}
#arrowDiv-certs form {
    margin:0;
    padding:5px 0;
}
#arrowDiv-grad form {
    margin:0;
    padding:5px 0;
}
#graduation-tab-text {
    padding: 2px 10px 0px 20px;
}
#gradimage {
    border: 1px solid gray;
}
.gray-letter {
    color: #666;
}
#programs h4 {
    padding: 2px 0;
}
.progress-report-descr {
    padding:0 0 8px 0;
    color:#333;
    font-size:1em;
}
.progress-report-descr a {
    font-size: 1em;
}
#my-programs-box h4 {
  font-weight: bold; font-size: 1em; color: #444; /* 14px */
  padding: 2px 0;
}
.no-holds-todos {
    padding: 1px 0 7px 0px;
}
.grad-apply {
    padding: 0;
}
.grad-link-footer {
    padding: 10px 0 0 0;
}
.grad-apply-link {
    font-weight: bold; 
}
#defense-committee-members{
	padding: 4px 0 10px 9px;	
}
#defensetab div.description {
    margin: 5px 0;
}
#defensetab ul {
    list-style-type: disc;
    margin: 0 0 0 20px;
}
#defensetab strong {
    color:#333;
}
.programtoggle {
    padding:5px 6px 1px 0;
    float:right;
}

/* End legacy stuff */

/* Transfer Pathway */
.tracker-div {
	margin-top: 5px;
}

.tracker-label {
	font-weight: bold;
}

.detail-met {
	color: green;
}
.pathway-not-met {
	color: red;
}
.detail-not-met {
	color: orange;
}
.not-met-detail {
	color: grey;
}
.detail-button {
	color: #8C1D40;
	cursor: pointer;
}
.hide-pathway-incomplete-details {
	display: none;
}
.pathway-details {
	display: inline-block;
	vertical-align: top;
}
.pathway-incomplete-details {
	display: none;
}

#completed-degrees-list .completed-degree {
	padding-bottom: 8px;
	/* font-size: 1.1em; */
	/* line-height: 1.4; */
	
}

#completed-degrees-list .separator {
	padding-left: 3px;
	padding-right: 3px;
	/* color: grey; */ /* Don't override for 2021 updates hover state */
}

.graduation-tab .graduation-deadlines-box {
	margin-top: 10px;
	margin-bottom: 10px;
	margin-left: 16px;
	margin-right: 16px;
	border: 1px solid #ddd;
	background-color: #f5f5f5;
	border-radius: 4px;
	padding: 8px;
}

.graduation-tab .graduation-deadlines-header {
	/* font-size: 1.1em; */
	font-weight: bold;
	color: #555;
	padding-top: 2px;
	padding-bottom: 10px;
		
}

.graduation-tab .graduation-deadlines-box .table {
	border-collapse: collapse;
}

.graduation-tab .graduation-deadlines-box .table-headers {
	color: #aaa;
}

.graduation-tab .graduation-deadlines-box .table-headers .table-cell {
	vertical-align: bottom;
	padding-bottom: 2px;
}

.graduation-tab .graduation-deadlines-box .table-data-row {
	font-weight: bold;
	color: #555;
	border-bottom: 1px solid #ddd;
}

.graduation-tab .graduation-deadlines-box .table-data-row:last-child {
	border-bottom: none;
}

.graduation-tab .graduation-deadlines-box .table-data-row .table-cell {
	padding-top: 4px;
	padding-bottom: 4px;
}

.graduation-tab .btn-maroon {
	border-radius: 2px;
	padding-top: 7px;
	padding-bottom: 7px;
	padding-left: 10px;
	padding-right: 10px;
	margin-right: 7px;
	margin-top: 5px;
	margin-bottom: 5px;

}

.graduation-tab .more-info-link {
	font-size: .9em;
	margin-left: 6px;
}

.graduation-tab .more-info-link:hover {
	color: #D23153;
}

.graduation-tab .apply-button {
	margin-top: 10px;
	margin-bottom: 10px;
}

.graduation-tab .rsvp-supplemental-info {
	color: var(--myasu-gray-70);
	margin-top: 4px;
}

.graduation-tab .rsvp-buttons-container {
	display: flex;
}

.graduation-tab .rsvp-button-container {
	margin: 16px;
	text-align: center;
}

.graduation-tab .rsvp-link-container {
	margin: 24px 0px 16px 0px;
}

.graduation-tab .applied-status-element {
	margin-bottom: 10px;
}

.graduation-tab .plan-title {
	/* font-size: 1.1em; */
	font-weight: bold;	
}
.graduation-tab .program-details .table-cell {
	padding: 2px 0;
}

.graduation-tab .program-details .table-row .table-cell:first-of-type {
	white-space: nowrap;
}

.graduation-tab .program-details .table-row .table-cell:last-of-type > div:not(:last-child) {
	margin-bottom: 4px;
}

.graduation-tab .cursive-font {
	font-family: cursive;
	font-size: 1.5em;
}

.graduation-tab .app-status-text-APPLIED {
	font-weight: bold;
}

.graduation-tab .app-status-text-DENIED {
	font-weight: bold;
}

.graduation-tab .app-status-text-WITHDRAWN {
	font-weight: bold;
}

.graduation-tab .app-status-text-AWARDED {
	font-weight: bold;
}

.changing-major .btn-maroon {
	margin-top: 5px;
	margin-bottom: 10px;
}
.alumnus-block {
	padding-top: 16px;
	border-top: 1px solid #e8e8e8;
}

.alumni-resources-link-container {
	/* font-size: 1.1em; */
	font-weight: var(--myasu-font-bold);
	padding-bottom: 16px;
}
.alumni-resources-container .box-section {
	min-height: initial;
	border-bottom: none;
	border-top: 1px solid #e8e8e8;
}
.alumni-resources-container .box-section-title {
	margin: 0;
	padding-top: 16px;
	padding-bottom: 16px;
}
.alumni-resources-container .box-section-title .icon {
	margin-right: 8px;
	color: #8C1D40;
}

/* Degree Information dropdown */
.degree-information-container .degree-information-details {
	display: flex;
	margin-bottom: 12px;
}

.degree-information-container .degree-information-details:last-child {
	margin-bottom: 0px;
}

.degree-information-container .degree-information-details .degree-information-details-label {
	margin-right: 6px;
	font-weight: var(--myasu-font-bold);
}

.degree-information-container .degree-information-details .degree-information-details-admission-value {
	display: flex;
}

.degree-information-container .degree-information-details .degree-information-details-admission-value .degree-information-details-admission-request-change {
	margin-left: 12px;
}

.degree-information-container .degree-information-details a {
	text-decoration: underline;
	border: 0;
}