/*!
* qTip2 - Pretty powerful tooltips
* http://craigsworks.com/projects/qtip2/
*
* Version: nightly
* Copyright 2009-2010 <PERSON> - http://craigsworks.com
*
* Dual licensed under MIT or GPLv2 licenses
*   http://en.wikipedia.org/wiki/MIT_License
*   http://en.wikipedia.org/wiki/GNU_General_Public_License
*
* Date: Sun Jul 15 16:44:57.********** 2012
*/
(function(a){if(typeof define==="function"&&define.amd){define(["jquery"],a);}else{if(jQuery&&!jQuery.fn.qtip){a(jQuery);}}}(function(y){var s=true,O=false,u=null,m,b="x",a="y",d="width",x="height",B="top",p="left",w="bottom",P="right",v="center",l="flip",I="flipinvert",D="shift",K,t,E,g={},F="ui-tooltip",z="ui-widget",A="ui-state-disabled",f="div.qtip."+F,H=F+"-default",N=F+"-focus",k=F+"-hover",j=F+"-fluid",C="-31000px",o="_replacedByqTip",i="oldtitle",M;function h(){h.history=h.history||[];h.history.push(arguments);if("object"===typeof console){var S=console[console.warn?"warn":"log"],R=Array.prototype.slice.call(arguments),Q;if(typeof arguments[0]==="string"){R[0]="qTip2: "+R[0];}Q=S.apply?S.apply(console,R):S(R);}}function r(R){var Q;if(!R||"object"!==typeof R){return O;}if(R.metadata===u||"object"!==typeof R.metadata){R.metadata={type:R.metadata};}if("content" in R){if(R.content===u||"object"!==typeof R.content||R.content.jquery){R.content={text:R.content};}Q=R.content.text||O;if(!y.isFunction(Q)&&((!Q&&!Q.attr)||Q.length<1||("object"===typeof Q&&!Q.jquery))){R.content.text=O;}if("title" in R.content){if(R.content.title===u||"object"!==typeof R.content.title){R.content.title={text:R.content.title};}Q=R.content.title.text||O;if(!y.isFunction(Q)&&((!Q&&!Q.attr)||Q.length<1||("object"===typeof Q&&!Q.jquery))){R.content.title.text=O;}}}if("position" in R){if(R.position===u||"object"!==typeof R.position){R.position={my:R.position,at:R.position};}}if("show" in R){if(R.show===u||"object"!==typeof R.show){if(R.show.jquery){R.show={target:R.show};}else{R.show={event:R.show};}}}if("hide" in R){if(R.hide===u||"object"!==typeof R.hide){if(R.hide.jquery){R.hide={target:R.hide};}else{R.hide={event:R.hide};}}}if("style" in R){if(R.style===u||"object"!==typeof R.style){R.style={classes:R.style};}}y.each(t,function(){if(this.sanitize){this.sanitize(R);}});return R;}function c(am,S,ag,ah){var af=this,ab=document.body,Y=F+"-"+ag,T=0,al=0,U=y(),ac=".qtip-"+ag,ad,ae;af.id=ag;af.rendered=O;af.destroyed=O;af.elements=ad={target:am};af.timers={img:{}};af.options=S;af.checks={};af.plugins={};af.cache=ae={event:{},target:y(),disabled:O,attr:ah,onTarget:O,lastClass:""};function V(ap){var an=0,ar,ao=S,aq=ap.split(".");while(ao=ao[aq[an++]]){if(an<aq.length){ar=ao;}}return[ar||S,aq.pop()];}function ak(){var an=S.style.widget;U.toggleClass(z,an).toggleClass(H,S.style.def&&!an);ad.content.toggleClass(z+"-content",an);if(ad.titlebar){ad.titlebar.toggleClass(z+"-header",an);}if(ad.button){ad.button.toggleClass(F+"-icon",!an);}}function Q(an){if(ad.title){ad.titlebar.remove();ad.titlebar=ad.title=ad.button=u;if(an!==O){af.reposition();}}}function ai(){var ao=S.content.title.button,an=typeof ao==="string",ap=an?ao:"Close tooltip";if(ad.button){ad.button.remove();}if(ao.jquery){ad.button=ao;}else{ad.button=y("<a />",{"class":"ui-state-default ui-tooltip-close "+(S.style.widget?"":F+"-icon"),title:ap,"aria-label":ap}).prepend(y("<span />",{"class":"ui-icon ui-icon-close",html:"&times;"}));}ad.button.appendTo(ad.titlebar).attr("role","button").click(function(aq){if(!U.hasClass(A)){af.hide(aq);}return O;});af.redraw();}function X(){var an=Y+"-title";if(ad.titlebar){Q();}ad.titlebar=y("<div />",{"class":F+"-titlebar "+(S.style.widget?"ui-widget-header":"")}).append(ad.title=y("<div />",{id:an,"class":F+"-title","aria-atomic":s})).insertBefore(ad.content).delegate(".ui-tooltip-close","mousedown keydown mouseup keyup mouseout",function(ao){y(this).toggleClass("ui-state-active ui-state-focus",ao.type.substr(-4)==="down");}).delegate(".ui-tooltip-close","mouseover mouseout",function(ao){y(this).toggleClass("ui-state-hover",ao.type==="mouseover");});if(S.content.title.button){ai();}else{if(af.rendered){af.redraw();}}}function aa(an){var ao=ad.button,ap=ad.title;if(!af.rendered){return O;}if(!an){ao.remove();}else{if(!ap){X();}ai();}}function aj(ap,an){var ao=ad.title;if(!af.rendered||!ap){return O;}if(y.isFunction(ap)){ap=ap.call(am,ae.event,af);}if(ap===O||(!ap&&ap!=="")){return Q(O);}else{if(ap.jquery&&ap.length>0){ao.empty().append(ap.css({display:"block"}));}else{ao.html(ap);}}af.redraw();if(an!==O&&af.rendered&&U[0].offsetWidth>0){af.reposition(ae.event);}}function Z(ap,an){var ao=ad.content;if(!af.rendered||!ap){return O;}if(y.isFunction(ap)){ap=ap.call(am,ae.event,af)||"";}if(ap.jquery&&ap.length>0){ao.empty().append(ap.css({display:"block"}));}else{ao.html(ap);}function aq(at){var ar,au={};function av(aw){if(aw){delete au[aw.src];clearTimeout(af.timers.img[aw.src]);y(aw).unbind(ac);}if(y.isEmptyObject(au)){af.redraw();if(an!==O){af.reposition(ae.event);}at();}}if((ar=ao.find("img[src]:not([height]):not([width])")).length===0){return av();}ar.each(function(ax,az){if(au[az.src]!==m){return;}var ay=0,aw=3;(function aA(){if(az.height||az.width||(ay>aw)){return av(az);}ay+=1;af.timers.img[az.src]=setTimeout(aA,700);}());y(az).bind("error"+ac+" load"+ac,function(){av(this);});au[az.src]=az;});}if(af.rendered<0){U.queue("fx",aq);}else{al=0;aq(y.noop);}return af;}function R(){var aq=S.position,ao={show:S.show.target,hide:S.hide.target,viewport:y(aq.viewport),document:y(document),body:y(document.body),window:y(window)},ap={show:y.trim(""+S.show.event).split(" "),hide:y.trim(""+S.hide.event).split(" ")},an=false;function at(aw){if(U.hasClass(A)){return O;}clearTimeout(af.timers.show);clearTimeout(af.timers.hide);var ax=function(){af.toggle(s,aw);};if(S.show.delay>0){af.timers.show=setTimeout(ax,S.show.delay);}else{ax();}}function ar(az){if(U.hasClass(A)||T||al){return O;}var ax=y(az.relatedTarget||az.target),aw=ax.closest(f)[0]===U[0],ay=ax[0]===ao.show[0];clearTimeout(af.timers.show);clearTimeout(af.timers.hide);if((aq.target==="mouse"&&aw)||(S.hide.fixed&&((/mouse(out|leave|move)/).test(az.type)&&(aw||ay)))){try{az.preventDefault();az.stopImmediatePropagation();}catch(aA){}return;}if(S.hide.delay>0){af.timers.hide=setTimeout(function(){af.hide(az);},S.hide.delay);}else{af.hide(az);}}function au(aw){if(U.hasClass(A)){return O;}clearTimeout(af.timers.inactive);af.timers.inactive=setTimeout(function(){af.hide(aw);},S.hide.inactive);}function av(aw){if(af.rendered&&U[0].offsetWidth>0){af.reposition(aw);}}U.bind("mouseenter"+ac+" mouseleave"+ac,function(aw){var ax=aw.type==="mouseenter";if(ax){af.focus(aw);}U.toggleClass(k,ax);});if(/mouse(out|leave)/i.test(S.hide.event)){if(S.hide.leave==="window"){ao.window.bind("mouseleave"+ac+" blur"+ac,function(aw){if(!/select|option/.test(aw.target.nodeName)&&!aw.relatedTarget){af.hide(aw);}});}}if(S.hide.fixed){ao.hide=ao.hide.add(U);U.bind("mouseover"+ac,function(){if(!U.hasClass(A)){clearTimeout(af.timers.hide);}});}else{if(/mouse(over|enter)/i.test(S.show.event)){ao.hide.bind("mouseleave"+ac,function(aw){clearTimeout(af.timers.show);});}}if((""+S.hide.event).indexOf("unfocus")>-1){aq.container.closest("html").bind("mousedown"+ac,function(az){var ay=y(az.target),ax=af.rendered&&!U.hasClass(A)&&U[0].offsetWidth>0,aw=ay.parents(f).filter(U[0]).length>0;if(ay[0]!==am[0]&&ay[0]!==U[0]&&!aw&&!am.has(ay[0]).length&&!ay.attr("disabled")){af.hide(az);}});}if("number"===typeof S.hide.inactive){ao.show.bind("qtip-"+ag+"-inactive",au);y.each(K.inactiveEvents,function(aw,ax){ao.hide.add(ad.tooltip).bind(ax+ac+"-inactive",au);});}y.each(ap.hide,function(ax,ay){var aw=y.inArray(ay,ap.show),az=y(ao.hide);if((aw>-1&&az.add(ao.show).length===az.length)||ay==="unfocus"){ao.show.bind(ay+ac,function(aA){if(U[0].offsetWidth>0){ar(aA);}else{at(aA);}});delete ap.show[aw];}else{ao.hide.bind(ay+ac,ar);}});y.each(ap.show,function(aw,ax){ao.show.bind(ax+ac,at);});if("number"===typeof S.hide.distance){ao.show.add(U).bind("mousemove"+ac,function(az){var ay=ae.origin||{},ax=S.hide.distance,aw=Math.abs;if(aw(az.pageX-ay.pageX)>=ax||aw(az.pageY-ay.pageY)>=ax){af.hide(az);}});}if(aq.target==="mouse"){ao.show.bind("mousemove"+ac,function(aw){E={pageX:aw.pageX,pageY:aw.pageY,type:"mousemove"};});if(aq.adjust.mouse){if(S.hide.event){U.bind("mouseleave"+ac,function(aw){if((aw.relatedTarget||aw.target)!==ao.show[0]){af.hide(aw);}});ad.target.bind("mouseenter"+ac+" mouseleave"+ac,function(aw){ae.onTarget=aw.type==="mouseenter";});}ao.document.bind("mousemove"+ac,function(aw){if(af.rendered&&ae.onTarget&&!U.hasClass(A)&&U[0].offsetWidth>0){af.reposition(aw||E);}});}}if(aq.adjust.resize||ao.viewport.length){(y.event.special.resize?ao.viewport:ao.window).bind("resize"+ac,av);}if(ao.viewport.length||(an&&U.css("position")==="fixed")){ao.viewport.bind("scroll"+ac,av);}}function W(){var an=[S.show.target[0],S.hide.target[0],af.rendered&&ad.tooltip[0],S.position.container[0],S.position.viewport[0],window,document];if(af.rendered){y([]).pushStack(y.grep(an,function(ao){return typeof ao==="object";})).unbind(ac);}else{S.show.target.unbind(ac+"-create");}}af.checks.builtin={"^id$":function(ap,aq,an){var ar=an===s?K.nextid:an,ao=F+"-"+ar;if(ar!==O&&ar.length>0&&!y("#"+ao).length){U[0].id=ao;ad.content[0].id=ao+"-content";ad.title[0].id=ao+"-title";}},"^content.text$":function(ao,ap,an){Z(an);},"^content.title.text$":function(ao,ap,an){if(!an){return Q();}if(!ad.title&&an){X();}aj(an);},"^content.title.button$":function(ao,ap,an){aa(an);},"^position.(my|at)$":function(ao,ap,an){if("string"===typeof an){ao[ap]=new t.Corner(an);}},"^position.container$":function(ao,ap,an){if(af.rendered){U.appendTo(an);}},"^show.ready$":function(){if(!af.rendered){af.render(1);}else{af.toggle(s);}},"^style.classes$":function(ao,ap,an){U.attr("class",F+" qtip ui-helper-reset "+an);},"^style.widget|content.title":ak,"^events.(render|show|move|hide|focus|blur)$":function(ao,ap,an){U[(y.isFunction(an)?"":"un")+"bind"]("tooltip"+ap,an);},"^(show|hide|position).(event|target|fixed|inactive|leave|distance|viewport|adjust)":function(){var an=S.position;U.attr("tracking",an.target==="mouse"&&an.adjust.mouse);W();R();}};y.extend(af,{render:function(an){if(af.rendered){return af;}var aq=S.content.text,ap=S.content.title.text,ao=S.position,ar=y.Event("tooltiprender");y.attr(am[0],"aria-describedby",Y);U=ad.tooltip=y("<div/>",{id:Y,"class":F+" qtip ui-helper-reset "+H+" "+S.style.classes+" "+F+"-pos-"+S.position.my.abbrev(),width:S.style.width||"",height:S.style.height||"",tracking:ao.target==="mouse"&&ao.adjust.mouse,role:"alert","aria-live":"polite","aria-atomic":O,"aria-describedby":Y+"-content","aria-hidden":s}).toggleClass(A,ae.disabled).data("qtip",af).appendTo(S.position.container).append(ad.content=y("<div />",{"class":F+"-content",id:Y+"-content","aria-atomic":s}));af.rendered=-1;al=1;T=1;if(ap){X();if(!y.isFunction(ap)){aj(ap,O);}}if(!y.isFunction(aq)){Z(aq,O);}af.rendered=s;ak();y.each(S.events,function(at,au){if(y.isFunction(au)){U.bind(at==="toggle"?"tooltipshow tooltiphide":"tooltip"+at,au);}});y.each(t,function(){if(this.initialize==="render"){this(af);}});R();U.queue("fx",function(at){ar.originalEvent=ae.event;U.trigger(ar,[af]);al=0;T=0;af.redraw();if(S.show.ready||an){af.toggle(s,ae.event,O);}at();});return af;},get:function(ao){var an,ap;switch(ao.toLowerCase()){case"dimensions":an={height:U.outerHeight(),width:U.outerWidth()};break;case"offset":an=t.offset(U,S.position.container);break;default:ap=V(ao.toLowerCase());an=ap[0][ap[1]];an=an.precedance?an.string():an;break;}return an;},set:function(aq,ar){var ap=/^position\.(my|at|adjust|target|container)|style|content|show\.ready/i,at=/^content\.(title|attr)|style/i,au=O,aw=O,ao=af.checks,an;function av(aA,ay){var az,aB,ax;for(az in ao){for(aB in ao[az]){if(ax=(new RegExp(aB,"i")).exec(aA)){ay.push(ax);ao[az][aB].apply(af,ay);}}}}if("string"===typeof aq){an=aq;aq={};aq[an]=ar;}else{aq=y.extend(s,{},aq);}y.each(aq,function(ay,az){var aA=V(ay.toLowerCase()),ax;ax=aA[0][aA[1]];aA[0][aA[1]]="object"===typeof az&&az.nodeType?y(az):az;aq[ay]=[aA[0],aA[1],az,ax];au=ap.test(ay)||au;aw=at.test(ay)||aw;});r(S);T=al=1;y.each(aq,av);T=al=0;if(af.rendered&&U[0].offsetWidth>0){if(au){af.reposition(S.position.target==="mouse"?u:ae.event);}if(aw){af.redraw();}}return af;},toggle:function(ap,aq){if(!af.rendered){return ap?af.render(1):af;}var ay=ap?"show":"hide",an=S[ay],at=S[!ap?"show":"hide"],aA=S.position,aw=S.content,au=U[0].offsetWidth>0,ar=ap||an.target.length===1,av=!aq||an.target.length<2||ae.target[0]===aq.target,ax,az;if((typeof ap).search("boolean|number")){ap=!au;}if(!U.is(":animated")&&au===ap&&av){return af;}if(aq){if((/over|enter/).test(aq.type)&&(/out|leave/).test(ae.event.type)&&S.show.target.add(aq.target).length===S.show.target.length&&U.has(aq.relatedTarget).length){return af;}ae.event=y.extend({},aq);}az=y.Event("tooltip"+ay);az.originalEvent=aq?ae.event:u;U.trigger(az,[af,90]);if(az.isDefaultPrevented()){return af;}y.attr(U[0],"aria-hidden",!!!ap);if(ap){ae.origin=y.extend({},E);af.focus(aq);if(y.isFunction(aw.text)){Z(aw.text,O);}if(y.isFunction(aw.title.text)){aj(aw.title.text,O);}if(!M&&aA.target==="mouse"&&aA.adjust.mouse){y(document).bind("mousemove.qtip",function(aB){E={pageX:aB.pageX,pageY:aB.pageY,type:"mousemove"};});M=s;}af.reposition(aq,arguments[2]);if((az.solo=!!an.solo)){y(f,an.solo).not(U).qtip("hide",az);}}else{clearTimeout(af.timers.show);delete ae.origin;if(M&&!y(f+'[tracking="true"]:visible',an.solo).not(U).length){y(document).unbind("mousemove.qtip");M=O;}af.blur(aq);}function ao(){if(ap){U.css("overflow","");if("string"===typeof an.autofocus){y(an.autofocus,U).focus();}an.target.trigger("qtip-"+ag+"-inactive");}else{U.css({display:"",visibility:"",opacity:"",left:"",top:""});}az=y.Event("tooltip"+(ap?"visible":"hidden"));az.originalEvent=aq?ae.event:u;U.trigger(az,[af]);}if(an.effect===O||ar===O){U[ay]();ao.call(U);}else{if(y.isFunction(an.effect)){U.stop(1,1);an.effect.call(U,af);U.queue("fx",function(aB){ao();aB();});}else{U.fadeTo(90,ap?1:0,ao);}}if(ap){an.target.trigger("qtip-"+ag+"-inactive");}return af;},show:function(an){return af.toggle(s,an);},hide:function(an){return af.toggle(O,an);},focus:function(ar){if(!af.rendered){return af;}var at=y(f),ap=parseInt(U[0].style.zIndex,10),ao=K.zindex+at.length,aq=y.extend({},ar),an,au;if(!U.hasClass(N)){au=y.Event("tooltipfocus");au.originalEvent=aq;U.trigger(au,[af,ao]);if(!au.isDefaultPrevented()){if(ap!==ao){at.each(function(){if(this.style.zIndex>ap){this.style.zIndex=this.style.zIndex-1;}});at.filter("."+N).qtip("blur",aq);}U.addClass(N)[0].style.zIndex=ao;}}return af;},blur:function(ao){var an=y.extend({},ao),ap;U.removeClass(N);ap=y.Event("tooltipblur");ap.originalEvent=an;U.trigger(ap,[af]);return af;},reposition:function(aE,aB){if(!af.rendered||T){return af;}T=1;var aI=S.position.target,aH=S.position,az=aH.my,aA=aH.at,aC=aH.adjust,ao=aC.method.split(" "),aF=U.outerWidth(),aD=U.outerHeight(),aw=0,ax=0,au=y.Event("tooltipmove"),aq=U.css("position")==="fixed",aG=aH.viewport,aJ={left:0,top:0},ay=aH.container,an=U[0].offsetWidth>0,ap,av,ar;if(y.isArray(aI)&&aI.length===2){aA={x:p,y:B};aJ={left:aI[0],top:aI[1]};}else{if(aI==="mouse"&&((aE&&aE.pageX)||ae.event.pageX)){aA={x:p,y:B};aE=(aE&&(aE.type==="resize"||aE.type==="scroll")?ae.event:aE&&aE.pageX&&aE.type==="mousemove"?aE:E&&E.pageX&&(aC.mouse||!aE||!aE.pageX)?{pageX:E.pageX,pageY:E.pageY}:!aC.mouse&&ae.origin&&ae.origin.pageX&&S.show.distance?ae.origin:aE)||aE||ae.event||E||{};aJ={top:aE.pageY,left:aE.pageX};}else{if(aI==="event"&&aE&&aE.target&&aE.type!=="scroll"&&aE.type!=="resize"){ae.target=y(aE.target);}else{if(aI!=="event"){ae.target=y(aI.jquery?aI:ad.target);}}aI=ae.target;aI=y(aI).eq(0);if(aI.length===0){return af;}else{if(aI[0]===document||aI[0]===window){aw=t.iOS?window.innerWidth:aI.width();ax=t.iOS?window.innerHeight:aI.height();if(aI[0]===window){aJ={top:(aG||aI).scrollTop(),left:(aG||aI).scrollLeft()};}}else{if(t.imagemap&&aI.is("area")){ap=t.imagemap(af,aI,aA,t.viewport?ao:O);}else{if(t.svg&&typeof aI[0].xmlbase==="string"){ap=t.svg(af,aI,aA,t.viewport?ao:O);}else{aw=aI.outerWidth();ax=aI.outerHeight();aJ=t.offset(aI,ay);}}}}if(ap){aw=ap.width;ax=ap.height;av=ap.offset;aJ=ap.position;}if((t.iOS>3.1&&t.iOS<4.1)||(t.iOS>=4.3&&t.iOS<4.33)||(!t.iOS&&aq)){ar=y(window);aJ.left-=ar.scrollLeft();aJ.top-=ar.scrollTop();}aJ.left+=aA.x===P?aw:aA.x===v?aw/2:0;aJ.top+=aA.y===w?ax:aA.y===v?ax/2:0;}}aJ.left+=aC.x+(az.x===P?-aF:az.x===v?-aF/2:0);aJ.top+=aC.y+(az.y===w?-aD:az.y===v?-aD/2:0);if(t.viewport){aJ.adjusted=t.viewport(af,aJ,aH,aw,ax,aF,aD);if(av&&aJ.adjusted.left){aJ.left+=av.left;}if(av&&aJ.adjusted.top){aJ.top+=av.top;}}else{aJ.adjusted={left:0,top:0};}au.originalEvent=y.extend({},aE);U.trigger(au,[af,aJ,aG.elem||aG]);if(au.isDefaultPrevented()){return af;}delete aJ.adjusted;if(aB===O||!an||isNaN(aJ.left)||isNaN(aJ.top)||aI==="mouse"||!y.isFunction(aH.effect)){U.css(aJ);}else{if(y.isFunction(aH.effect)){aH.effect.call(U,af,y.extend({},aJ));U.queue(function(at){y(this).css({opacity:"",height:""});at();});}}T=0;return af;},redraw:function(){if(af.rendered<1||al){return af;}var ao=S.position.container,aq,ar,an,ap;al=1;if(S.style.height){U.css(x,S.style.height);}if(S.style.width){U.css(d,S.style.width);}else{U.css(d,"").addClass(j);ar=U.width()+1;an=U.css("max-width")||"";ap=U.css("min-width")||"";aq=(an+ap).indexOf("%")>-1?ao.width()/100:0;an=((an.indexOf("%")>-1?aq:1)*parseInt(an,10))||ar;ap=((ap.indexOf("%")>-1?aq:1)*parseInt(ap,10))||0;ar=an+ap?Math.min(Math.max(ar,ap),an):ar;U.css(d,Math.round(ar)).removeClass(j);}al=0;return af;},disable:function(an){if("boolean"!==typeof an){an=!(U.hasClass(A)||ae.disabled);}if(af.rendered){U.toggleClass(A,an);y.attr(U[0],"aria-disabled",an);}else{ae.disabled=!!an;}return af;},enable:function(){return af.disable(O);},destroy:function(){var an=am[0],ao=y.attr(an,i),ap=am.data("qtip");af.destroyed=s;if(af.rendered){U.stop(1,0).remove();y.each(af.plugins,function(){if(this.destroy){this.destroy();}});}clearTimeout(af.timers.show);clearTimeout(af.timers.hide);W();if(!ap||af===ap){y.removeData(an,"qtip");if(S.suppress&&ao){y.attr(an,"title",ao);am.removeAttr(i);}am.removeAttr("aria-describedby");}am.unbind(".qtip-"+ag);delete g[af.id];return am;}});}function L(R,Q){var U,ad,Y,S,ab,T=y(this),V=y(document.body),aa=this===document?V:T,Z=(T.metadata)?T.metadata(Q.metadata):u,ac=Q.metadata.type==="html5"&&Z?Z[Q.metadata.name]:u,W=T.data(Q.metadata.name||"qtipopts");try{W=typeof W==="string"?(new Function("return "+W))():W;}catch(X){h("Unable to parse HTML5 attribute data: "+W);}S=y.extend(s,{},K.defaults,Q,typeof W==="object"?r(W):u,r(ac||Z));ad=S.position;S.id=R;if("boolean"===typeof S.content.text){Y=T.attr(S.content.attr);if(S.content.attr!==O&&Y){S.content.text=Y;}else{h("Unable to locate content for tooltip! Aborting render of tooltip on element: ",T);return O;}}if(!ad.container.length){ad.container=V;}if(ad.target===O){ad.target=aa;}if(S.show.target===O){S.show.target=aa;}if(S.show.solo===s){S.show.solo=ad.container.closest("body");}if(S.hide.target===O){S.hide.target=aa;}if(S.position.viewport===s){S.position.viewport=ad.container;}ad.container=ad.container.eq(0);ad.at=new t.Corner(ad.at);ad.my=new t.Corner(ad.my);if(y.data(this,"qtip")){if(S.overwrite){T.qtip("destroy");}else{if(S.overwrite===O){return O;}}}if(S.suppress&&(ab=y.attr(this,"title"))){y(this).removeAttr("title").attr(i,ab).attr("title","");}U=new c(T,S,R,!!Y);y.data(this,"qtip",U);T.bind("remove.qtip-"+R+" removeqtip.qtip-"+R,function(){U.destroy();});return U;}K=y.fn.qtip=function(R,V,W){var X=(""+R).toLowerCase(),U=u,Q=y.makeArray(arguments).slice(1),T=Q[Q.length-1],S=this[0]?y.data(this[0],"qtip"):u;if((!arguments.length&&S)||X==="api"){return S;}else{if("string"===typeof R){this.each(function(){var Y=y.data(this,"qtip");if(!Y){return s;}if(T&&T.timeStamp){Y.cache.event=T;}if((X==="option"||X==="options")&&V){if(y.isPlainObject(V)||W!==m){Y.set(V,W);}else{U=Y.get(V);return O;}}else{if(Y[X]){Y[X].apply(Y[X],Q);}}});return U!==u?U:this;}else{if("object"===typeof R||!arguments.length){S=r(y.extend(s,{},R));return K.bind.call(this,S,T);}}}};K.bind=function(R,Q){return this.each(function(V){var T,S,U,X,W,Z;Z=y.isArray(R.id)?R.id[V]:R.id;Z=!Z||Z===O||Z.length<1||g[Z]?K.nextid++:(g[Z]=Z);X=".qtip-"+Z+"-create";W=L.call(this,Z,R);if(W===O){return s;}T=W.options;y.each(t,function(){if(this.initialize==="initialize"){this(W);}});S={show:T.show.target,hide:T.hide.target};U={show:y.trim(""+T.show.event).replace(/ /g,X+" ")+X,hide:y.trim(""+T.hide.event).replace(/ /g,X+" ")+X};if(/mouse(over|enter)/i.test(U.show)&&!/mouse(out|leave)/i.test(U.hide)){U.hide+=" mouseleave"+X;}S.show.bind("mousemove"+X,function(aa){E={pageX:aa.pageX,pageY:aa.pageY,type:"mousemove"};W.cache.onTarget=s;});function Y(ab){function aa(){W.render(typeof ab==="object"||T.show.ready);S.show.add(S.hide).unbind(X);}if(W.cache.disabled){return O;}W.cache.event=y.extend({},ab);W.cache.target=ab?y(ab.target):[m];if(T.show.delay>0){clearTimeout(W.timers.show);W.timers.show=setTimeout(aa,T.show.delay);if(U.show!==U.hide){S.hide.bind(U.hide,function(){clearTimeout(W.timers.show);});}}else{aa();}}S.show.bind(U.show,Y);if(T.show.ready||T.prerender){Y(Q);}});};t=K.plugins={Corner:function(Q){Q=(""+Q).replace(/([A-Z])/," $1").replace(/middle/gi,v).toLowerCase();this.x=(Q.match(/left|right/i)||Q.match(/center/)||["inherit"])[0].toLowerCase();this.y=(Q.match(/top|bottom|center/i)||["inherit"])[0].toLowerCase();var R=Q.charAt(0);this.precedance=(R==="t"||R==="b"?a:b);this.string=function(){return this.precedance===a?this.y+this.x:this.x+this.y;};this.abbrev=function(){var S=this.x.substr(0,1),T=this.y.substr(0,1);return S===T?S:this.precedance===a?T+S:S+T;};this.invertx=function(S){this.x=this.x===p?P:this.x===P?p:S||this.x;};this.inverty=function(S){this.y=this.y===B?w:this.y===w?B:S||this.y;};this.clone=function(){return{x:this.x,y:this.y,precedance:this.precedance,string:this.string,abbrev:this.abbrev,clone:this.clone,invertx:this.invertx,inverty:this.inverty};};},offset:function(T,Q){var W=T.offset(),V=T.closest("body")[0],Y=Q,R,S,U;function X(aa,Z){W.left+=Z*aa.scrollLeft();W.top+=Z*aa.scrollTop();}if(Y){do{if(Y.css("position")!=="static"){S=Y.position();W.left-=S.left+(parseInt(Y.css("borderLeftWidth"),10)||0)+(parseInt(Y.css("marginLeft"),10)||0);W.top-=S.top+(parseInt(Y.css("borderTopWidth"),10)||0)+(parseInt(Y.css("marginTop"),10)||0);if(!R&&(U=Y.css("overflow"))!=="hidden"&&U!=="visible"){R=Y;}}}while((Y=y(Y[0].offsetParent)).length);if(R&&R[0]!==V){X(R,1);}}return W;},iOS:parseFloat((""+(/CPU.*OS ([0-9_]{1,5})|(CPU like).*AppleWebKit.*Mobile/i.exec(navigator.userAgent)||[0,""])[1]).replace("undefined","3_2").replace("_",".").replace("_",""))||O,fn:{attr:function(Q,U){if(this.length){var R=this[0],T="title",S=y.data(R,"qtip");if(Q===T&&S&&"object"===typeof S&&S.options.suppress){if(arguments.length<2){return y.attr(R,i);}else{if(S&&S.options.content.attr===T&&S.cache.attr){S.set("content.text",U);}return this.attr(i,U);}}}return y.fn["attr"+o].apply(this,arguments);},clone:function(R){var T=y([]),S="title",Q=y.fn["clone"+o].apply(this,arguments);if(!R){Q.filter("["+i+"]").attr("title",function(){return y.attr(this,i);}).removeAttr(i);}return Q;}}};y.each(t.fn,function(R,S){if(!S||y.fn[R+o]){return s;}var Q=y.fn[R+o]=y.fn[R];y.fn[R]=function(){return S.apply(this,arguments)||Q.apply(this,arguments);};});if(!y.ui){y["cleanData"+o]=y.cleanData;y.cleanData=function(Q){for(var R=0,S;(S=Q[R])!==m;R++){try{y(S).triggerHandler("removeqtip");}catch(T){}}y["cleanData"+o](Q);};}K.version="nightly";K.nextid=0;K.inactiveEvents="click dblclick mousedown mouseup mousemove mouseleave mouseenter".split(" ");K.zindex=15000;K.defaults={prerender:O,id:O,overwrite:s,suppress:s,content:{text:s,attr:"title",title:{text:O,button:O}},position:{my:"top left",at:"bottom right",target:O,container:O,viewport:O,adjust:{x:0,y:0,mouse:s,resize:s,method:"flip flip"},effect:function(R,S,Q){y(this).animate(S,{duration:200,queue:O});}},show:{target:O,event:"mouseenter",effect:s,delay:90,solo:O,ready:O,autofocus:O},hide:{target:O,event:"mouseleave",effect:s,delay:0,fixed:O,inactive:O,leave:"window",distance:O},style:{classes:"",widget:O,width:O,height:O,def:s},events:{render:u,move:u,show:u,hide:u,toggle:u,visible:u,hidden:u,focus:u,blur:u}};function q(U){var Y=this,Z=U.elements.tooltip,Q=U.options.content.ajax,S=K.defaults.content.ajax,R=".qtip-ajax",V=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,T=s,W=O,X;U.checks.ajax={"^content.ajax":function(ac,ab,aa){if(ab==="ajax"){Q=aa;}if(ab==="once"){Y.init();}else{if(Q&&Q.url){Y.load();}else{Z.unbind(R);}}}};y.extend(Y,{init:function(){if(Q&&Q.url){Z.unbind(R)[Q.once?"one":"bind"]("tooltipshow"+R,Y.load);}return Y;},load:function(ab){if(W){W=O;return;}var ae=Q.url.indexOf(" "),ac=Q.url,ad,ai=!Q.loading&&T;if(ai){try{ab.preventDefault();}catch(ag){}}else{if(ab&&ab.isDefaultPrevented()){return Y;}}if(X&&X.abort){X.abort();}if(ae>-1){ad=ac.substr(ae);ac=ac.substr(0,ae);}function aa(){var aj;if(U.destroyed){return;}T=O;if(ai){W=s;U.show(ab.originalEvent);}if((aj=S.complete||Q.complete)&&y.isFunction(aj)){aj.apply(Q.context||U,arguments);}}function ah(al,aj,ak){var am;if(U.destroyed){return;}if(ad){al=y("<div/>").append(al.replace(V,"")).find(ad);}if((am=S.success||Q.success)&&y.isFunction(am)){am.call(Q.context||U,al,aj,ak);}else{U.set("content.text",al);}}function af(al,aj,ak){if(U.destroyed||al.status===0){return;}U.set("content.text",aj+": "+ak);}X=y.ajax(y.extend({error:S.error||af,context:U},Q,{url:ac,success:ah,complete:aa}));},destroy:function(){if(X&&X.abort){X.abort();}U.destroyed=s;}});Y.init();}t.ajax=function(R){var Q=R.plugins.ajax;return"object"===typeof Q?Q:(R.plugins.ajax=new q(R));};t.ajax.initialize="render";t.ajax.sanitize=function(Q){var S=Q.content,R;if(S&&"ajax" in S){R=S.ajax;if(typeof R!=="object"){R=Q.content.ajax={url:R};}if("boolean"!==typeof R.once&&R.once){R.once=!!R.once;}}};y.extend(s,K.defaults,{content:{ajax:{loading:s,once:s}}});t.viewport=function(af,an,al,U,V,aj,ai){var am=al.target,T=af.elements.tooltip,ad=al.my,ag=al.at,ah=al.adjust,Q=ah.method.split(" "),aa=Q[0],Y=Q[1]||Q[0],ak=al.viewport,ab=al.container,ae=af.cache,ac=af.plugins.tip,S={left:0,top:0},R,X,W;if(!ak.jquery||am[0]===window||am[0]===document.body||ah.method==="none"){return S;}R=T.css("position")==="fixed";ak={elem:ak,height:ak[(ak[0]===window?"h":"outerH")+"eight"](),width:ak[(ak[0]===window?"w":"outerW")+"idth"](),scrollleft:R?0:ak.scrollLeft(),scrolltop:R?0:ak.scrollTop(),offset:ak.offset()||{left:0,top:0}};ab={elem:ab,scrollLeft:ab.scrollLeft(),scrollTop:ab.scrollTop(),offset:ab.offset()||{left:0,top:0}};function Z(ap,ao,au,aG,ay,aw,aF,aI,aA){var av=an[ay],aB=ad[ap],aH=ag[ap],aJ=au===D,aD=-ab.offset[ay]+ak.offset[ay]+ak["scroll"+ay],ax=aB===ay?aA:aB===aw?-aA:-aA/2,aC=aH===ay?aI:aH===aw?-aI:-aI/2,aq=ac&&ac.size?ac.size[aF]||0:0,aE=ac&&ac.corner&&ac.corner.precedance===ap&&!aJ?aq:0,at=aD-av+aE,ar=av+aA-ak[aF]-aD+aE,az=ax-(ad.precedance===ap||aB===ad[ao]?aC:0)-(aH===v?aI/2:0);if(aJ){aE=ac&&ac.corner&&ac.corner.precedance===ao?aq:0;az=(aB===ay?1:-1)*ax-aE;an[ay]+=at>0?at:ar>0?-ar:0;an[ay]=Math.max(-ab.offset[ay]+ak.offset[ay]+(aE&&ac.corner[ap]===v?ac.offset:0),av-az,Math.min(Math.max(-ab.offset[ay]+ak.offset[ay]+ak[aF],av+az),an[ay]));}else{aG*=(au===I?2:0);if(at>0&&(aB!==ay||ar>0)){an[ay]-=az+aG;X["invert"+ap](ay);}else{if(ar>0&&(aB!==aw||at>0)){an[ay]-=(aB===v?-az:az)+aG;X["invert"+ap](aw);}}if(an[ay]<aD&&-an[ay]>ar){an[ay]=av;X=m;}}return an[ay]-av;}if(aa!=="shift"||Y!=="shift"){X=ad.clone();}S={left:aa!=="none"?Z(b,a,aa,ah.x,p,P,d,U,aj):0,top:Y!=="none"?Z(a,b,Y,ah.y,B,w,x,V,ai):0};if(X&&ae.lastClass!==(W=F+"-pos-"+X.abbrev())){T.removeClass(af.cache.lastClass).addClass((af.cache.lastClass=W));}return S;};function e(W){var ad=this,af=W.options.show.modal,Q=W.elements,ae=Q.tooltip,S="#qtip-overlay",R=".qtipmodal",T=R+W.id,X="is-modal-qtip",V=y(document.body),ac=t.modal.focusable.join(","),Z={},U;W.checks.modal={"^show.modal.(on|blur)$":function(){ad.init();Q.overlay.toggle(ae.is(":visible"));},"^content.text$":ab};function ab(){Z=y(ac,ae).not("[disabled]").map(function(){return typeof this.focus==="function"?this:null;});}function Y(ag){if(Z.length<1&&ag.length){ag.not("body").blur();}else{Z.first().focus();}}function aa(ah){var ai=y(ah.target),ag=ai.closest(".qtip"),aj;aj=ag.length<1?O:(parseInt(ag[0].style.zIndex,10)>parseInt(ae[0].style.zIndex,10));if(!aj&&(y(ah.target).closest(f)[0]!==ae[0])){Y(ai);}}y.extend(ad,{init:function(){if(!af.on){return ad;}U=ad.create();ae.attr(X,s).css("z-index",t.modal.zindex+y(f+"["+X+"]").length).unbind(R).unbind(T).bind("tooltipshow"+R+" tooltiphide"+R,function(ai,ah,ak){var ag=ai.originalEvent;if(ai.target===ae[0]){if(ag&&ai.type==="tooltiphide"&&/mouse(leave|enter)/.test(ag.type)&&y(ag.relatedTarget).closest(U[0]).length){try{ai.preventDefault();}catch(aj){}}else{if(!ag||(ag&&!ag.solo)){ad[ai.type.replace("tooltip","")](ai,ak);}}}}).bind("tooltipfocus"+R,function(ai){if(ai.isDefaultPrevented()||ai.target!==ae[0]){return;}var aj=y(f).filter("["+X+"]"),ah=t.modal.zindex+aj.length,ag=parseInt(ae[0].style.zIndex,10);U[0].style.zIndex=ah-2;aj.each(function(){if(this.style.zIndex>ag){this.style.zIndex-=1;}});aj.end().filter("."+N).qtip("blur",ai.originalEvent);ae.addClass(N)[0].style.zIndex=ah;try{ai.preventDefault();}catch(ak){}}).bind("tooltiphide"+R,function(ag){if(ag.target===ae[0]){y("["+X+"]").filter(":visible").not(ae).last().qtip("focus",ag);}});if(af.escape){y(document).unbind(T).bind("keydown"+T,function(ag){if(ag.keyCode===27&&ae.hasClass(N)){W.hide(ag);}});}if(af.blur){Q.overlay.unbind(T).bind("click"+T,function(ag){if(ae.hasClass(N)){W.hide(ag);}});}ab();return ad;},create:function(){var ah=y(S);if(ah.length){return(Q.overlay=ah.insertAfter(y(f).last()));}U=Q.overlay=y("<div />",{id:S.substr(1),html:"<div></div>",mousedown:function(){return O;}}).hide().insertAfter(y(f).last());function ag(){U.css({height:y(window).height(),width:y(window).width()});}y(window).unbind(R).bind("resize"+R,ag);ag();return U;},toggle:function(ak,al,am){if(ak&&ak.isDefaultPrevented()){return ad;}var aj=af.effect,ai=al?"show":"hide",an=U.is(":visible"),ah=y("["+X+"]").filter(":visible").not(ae),ag;if(!U){U=ad.create();}if((U.is(":animated")&&an===al)||(!al&&ah.length)){return ad;}if(al){U.css({left:0,top:0});U.toggleClass("blurs",af.blur);if(af.stealfocus!==O){V.bind("focusin"+T,aa);Y(y("body *"));}}else{V.unbind("focusin"+T);}U.stop(s,O);if(y.isFunction(aj)){aj.call(U,al);}else{if(aj===O){U[ai]();}else{U.fadeTo(parseInt(am,10)||90,al?1:0,function(){if(!al){y(this).hide();}});}}if(!al){U.queue(function(ao){U.css({left:"",top:""});ao();});}return ad;},show:function(ag,ah){return ad.toggle(ag,s,ah);},hide:function(ag,ah){return ad.toggle(ag,O,ah);},destroy:function(){var ag=U;if(ag){ag=y("["+X+"]").not(ae).length<1;if(ag){Q.overlay.remove();y(document).unbind(R);}else{Q.overlay.unbind(R+W.id);}V.undelegate("*","focusin"+T);}return ae.removeAttr(X).unbind(R);}});ad.init();}t.modal=function(R){var Q=R.plugins.modal;return"object"===typeof Q?Q:(R.plugins.modal=new e(R));};t.modal.initialize="render";t.modal.sanitize=function(Q){if(Q.show){if(typeof Q.show.modal!=="object"){Q.show.modal={on:!!Q.show.modal};}else{if(typeof Q.show.modal.on==="undefined"){Q.show.modal.on=s;}}}};t.modal.zindex=K.zindex+1000;t.modal.focusable=["a[href]","area[href]","input","select","textarea","button","iframe","object","embed","[tabindex]","[contenteditable]"];y.extend(s,K.defaults,{show:{modal:{on:O,effect:s,blur:s,stealfocus:s,escape:s}}});t.imagemap=function(Z,S,ad,W){if(!S.jquery){S=y(S);}var R=(Z.cache.areas={}),ab=(S[0].shape||S.attr("shape")).toLowerCase(),aa=S[0].coords||S.attr("coords"),V=aa.split(","),ac=[],U=y('img[usemap="#'+S.parent("map").attr("name")+'"]'),af=U.offset(),ae={width:0,height:0,position:{top:1**********,right:0,bottom:0,left:1**********}},X=0,Y=0,Q;function T(ap,an,ao){var ak=0,am=1,al=1,aj=0,ah=0,ai=ap.width,ag=ap.height;while(ai>0&&ag>0&&am>0&&al>0){ai=Math.floor(ai/2);ag=Math.floor(ag/2);if(ao.x===p){am=ai;}else{if(ao.x===P){am=ap.width-ai;}else{am+=Math.floor(ai/2);}}if(ao.y===B){al=ag;}else{if(ao.y===w){al=ap.height-ag;}else{al+=Math.floor(ag/2);}}ak=an.length;while(ak--){if(an.length<2){break;}aj=an[ak][0]-ap.position.left;ah=an[ak][1]-ap.position.top;if((ao.x===p&&aj>=am)||(ao.x===P&&aj<=am)||(ao.x===v&&(aj<am||aj>(ap.width-am)))||(ao.y===B&&ah>=al)||(ao.y===w&&ah<=al)||(ao.y===v&&(ah<al||ah>(ap.height-al)))){an.splice(ak,1);}}}return{left:an[0][0],top:an[0][1]};}af.left+=Math.ceil((U.outerWidth()-U.width())/2);af.top+=Math.ceil((U.outerHeight()-U.height())/2);if(ab==="poly"){X=V.length;while(X--){Y=[parseInt(V[--X],10),parseInt(V[X+1],10)];if(Y[0]>ae.position.right){ae.position.right=Y[0];}if(Y[0]<ae.position.left){ae.position.left=Y[0];}if(Y[1]>ae.position.bottom){ae.position.bottom=Y[1];}if(Y[1]<ae.position.top){ae.position.top=Y[1];}ac.push(Y);}}else{X=-1;while(X++<V.length){ac.push(parseInt(V[X],10));}}switch(ab){case"rect":ae={width:Math.abs(ac[2]-ac[0]),height:Math.abs(ac[3]-ac[1]),position:{left:Math.min(ac[0],ac[2]),top:Math.min(ac[1],ac[3])}};break;case"circle":ae={width:ac[2]+2,height:ac[2]+2,position:{left:ac[0],top:ac[1]}};break;case"poly":ae.width=Math.abs(ae.position.right-ae.position.left);ae.height=Math.abs(ae.position.bottom-ae.position.top);if(ad.abbrev()==="c"){ae.position={left:ae.position.left+(ae.width/2),top:ae.position.top+(ae.height/2)};}else{if(!R[ad+aa]){ae.position=T(ae,ac.slice(),ad);if(W&&(W[0]==="flip"||W[1]==="flip")){ae.offset=T(ae,ac.slice(),{x:ad.x===p?P:ad.x===P?p:v,y:ad.y===B?w:ad.y===w?B:v});ae.offset.left-=ae.position.left;ae.offset.top-=ae.position.top;}R[ad+aa]=ae;}ae=R[ad+aa];}ae.width=ae.height=0;break;}ae.position.left+=af.left;ae.position.top+=af.top;return ae;};function n(V,T,R){var S=Math.ceil(T/2),U=Math.ceil(R/2),Q={bottomright:[[0,0],[T,R],[T,0]],bottomleft:[[0,0],[T,0],[0,R]],topright:[[0,R],[T,0],[T,R]],topleft:[[0,0],[0,R],[T,R]],topcenter:[[0,R],[S,0],[T,R]],bottomcenter:[[0,0],[T,0],[S,R]],rightcenter:[[0,0],[T,U],[0,R]],leftcenter:[[T,0],[T,R],[0,U]]};Q.lefttop=Q.bottomright;Q.righttop=Q.bottomleft;Q.leftbottom=Q.topright;Q.rightbottom=Q.topleft;return Q[V.string()];}function J(aa,S){var Z=this,W=aa.options.style.tip,ag=aa.elements,T=ag.tooltip,X={top:0,left:0},Y={width:W.width,height:W.height},ae={},ac=W.border||0,V=".qtip-tip",ab=!!(y("<canvas />")[0]||{}).getContext;Z.corner=u;Z.mimic=u;Z.border=ac;Z.offset=W.offset;Z.size=Y;aa.checks.tip={"^position.my|style.tip.(corner|mimic|border)$":function(){if(!Z.init()){Z.destroy();}aa.reposition();},"^style.tip.(height|width)$":function(){Y={width:W.width,height:W.height};Z.create();Z.update();aa.reposition();},"^content.title.text|style.(classes|widget)$":function(){if(ag.tip&&ag.tip.length){Z.update();}}};function U(){Y.width=W.height;Y.height=W.width;}function Q(){Y.width=W.width;Y.height=W.height;}function ad(aj,ap,at,aq){if(!ag.tip){return;}var av=Z.corner.clone(),au=at.adjusted,ai=aa.options.position.adjust.method.split(" "),ak=ai[0],am=ai[1]||ai[0],al={left:O,top:O,x:0,y:0},an,ao={},ar;if(Z.corner.fixed!==s){if(ak===D&&av.precedance===b&&au.left&&av.y!==v){av.precedance=av.precedance===b?a:b;}else{if(ak!==D&&au.left){av.x=av.x===v?(au.left>0?p:P):(av.x===p?P:p);}}if(am===D&&av.precedance===a&&au.top&&av.x!==v){av.precedance=av.precedance===a?b:a;}else{if(am!==D&&au.top){av.y=av.y===v?(au.top>0?B:w):(av.y===B?w:B);}}if(av.string()!==X.corner.string()&&(X.top!==au.top||X.left!==au.left)){Z.update(av,O);}}an=Z.position(av,au);an[av.x]+=ah(av,av.x,s);an[av.y]+=ah(av,av.y,s);if(an.right!==m){an.left=-an.right;}if(an.bottom!==m){an.top=-an.bottom;}an.user=Math.max(0,W.offset);if(al.left=(ak===D&&!!au.left)){if(av.x===v){ao["margin-left"]=al.x=an["margin-left"]-au.left;}else{ar=an.right!==m?[au.left,-an.left]:[-au.left,an.left];if((al.x=Math.max(ar[0],ar[1]))>ar[0]){at.left-=au.left;al.left=O;}ao[an.right!==m?P:p]=al.x;}}if(al.top=(am===D&&!!au.top)){if(av.y===v){ao["margin-top"]=al.y=an["margin-top"]-au.top;}else{ar=an.bottom!==m?[au.top,-an.top]:[-au.top,an.top];if((al.y=Math.max(ar[0],ar[1]))>ar[0]){at.top-=au.top;al.top=O;}ao[an.bottom!==m?w:B]=al.y;}}ag.tip.css(ao).toggle(!((al.x&&al.y)||(av.x===v&&al.y)||(av.y===v&&al.x)));at.left-=an.left.charAt?an.user:ak!==D||al.top||!al.left&&!al.top?an.left:0;at.top-=an.top.charAt?an.user:am!==D||al.left||!al.left&&!al.top?an.top:0;X.left=au.left;X.top=au.top;X.corner=av.clone();}function ah(an,al,aj){al=!al?an[an.precedance]:al;var ao=T.hasClass(j),ai=ag.titlebar&&an.y===B,am=ai?ag.titlebar:ag.tooltip,ak="border-"+al+"-width",ap;T.addClass(j);ap=parseInt(am.css(ak),10);ap=(aj?ap||parseInt(T.css(ak),10):ap)||0;T.toggleClass(j,ao);return ap;}function R(am){var ai=ag.titlebar&&am.y===B,al=ai?ag.titlebar:ag.content,ak=am.y+"-"+am.x,aj="border-"+ak+"-radius";return parseInt(al.css(aj),10)||parseInt(T.css(aj),10)||0;}function af(ar){var ap=ar.precedance===a,aj=Y[ap?d:x],at=Y[ap?x:d],ao=ar.string().indexOf(v)>-1,ai=aj*(ao?0.5:1),al=Math.pow,au=Math.round,aq,an,av,ak=Math.sqrt(al(ai,2)+al(at,2)),am=[(ac/ai)*ak,(ac/at)*ak];am[2]=Math.sqrt(al(am[0],2)-al(ac,2));am[3]=Math.sqrt(al(am[1],2)-al(ac,2));aq=ak+am[2]+am[3]+(ao?0:am[0]);an=aq/ak;av=[au(an*at),au(an*aj)];return{height:av[ap?0:1],width:av[ap?1:0]};}y.extend(Z,{init:function(){var ai=Z.detectCorner()&&(ab);if(ai){Z.create();Z.update();T.unbind(V).bind("tooltipmove"+V,ad);}return ai;},detectCorner:function(){var ak=W.corner,aj=aa.options.position,ai=aj.at,al=aj.my.string?aj.my.string():aj.my;if(ak===O||(al===O&&ai===O)){return O;}else{if(ak===s){Z.corner=new t.Corner(al);}else{if(!ak.string){Z.corner=new t.Corner(ak);Z.corner.fixed=s;}}}X.corner=new t.Corner(Z.corner.string());return Z.corner.string()!=="centercenter";},detectColours:function(av){var an,aw,am,at=ag.tip.css("cssText",""),au=av||Z.corner,aj=au[au.precedance],ai="border-"+aj+"-color",ao="border"+aj.charAt(0)+aj.substr(1)+"Color",ap=/rgba?\(0, 0, 0(, 0)?\)|transparent|#123456/i,ar="background-color",ax="transparent",al=" !important",aq=ag.titlebar&&(au.y===B||(au.y===v&&at.position().top+(Y.height/2)+W.offset<ag.titlebar.outerHeight(1))),ak=aq?ag.titlebar:ag.tooltip;T.addClass(j);ae.fill=aw=at.css(ar);ae.border=am=at[0].style[ao]||at.css(ai)||T.css(ai);if(!aw||ap.test(aw)){ae.fill=ak.css(ar)||ax;if(ap.test(ae.fill)){ae.fill=T.css(ar)||aw;}}if(!am||ap.test(am)||am===y(document.body).css("color")){ae.border=ak.css(ai)||ax;if(ap.test(ae.border)||ae.border===ak.css("color")){ae.border=T.css(ai)||T.css(ao)||am;}}y("*",at).add(at).css("cssText",ar+":"+ax+al+";border:0"+al+";");T.removeClass(j);},create:function(){var ak=Y.width,aj=Y.height,ai;if(ag.tip){ag.tip.remove();}ag.tip=y("<div />",{"class":"ui-tooltip-tip"}).css({width:ak,height:aj}).prependTo(T);if(ab){y("<canvas />").appendTo(ag.tip)[0].getContext("2d").save();}else{ai='<vml:shape coordorigin="0,0" style="display:inline-block; position:absolute; behavior:url(#default#VML);"></vml:shape>';ag.tip.html(ai+ai);y("*",ag.tip).bind("click mousedown",function(al){al.stopPropagation();});}},update:function(ar,an){var aq=ag.tip,ax=aq.children(),ak=Y.width,at=Y.height,al="px solid ",aw="px dashed transparent",av=W.mimic,au=Math.round,ai,aj,ap,am,ao;if(!ar){ar=X.corner||Z.corner;}if(av===O){av=ar;}else{av=new t.Corner(av);av.precedance=ar.precedance;if(av.x==="inherit"){av.x=ar.x;}else{if(av.y==="inherit"){av.y=ar.y;}else{if(av.x===av.y){av[ar.precedance]=ar[ar.precedance];}}}}ai=av.precedance;if(ar.precedance===b){U();}else{Q();}ag.tip.css({width:(ak=Y.width),height:(at=Y.height)});Z.detectColours(ar);if(ae.border!=="transparent"){ac=ah(ar,u,s);if(W.border===0&&ac>0){ae.fill=ae.border;}Z.border=ac=W.border!==s?W.border:ac;}else{Z.border=ac=0;}ap=n(av,ak,at);Z.size=ao=af(ar);aq.css(ao);if(ar.precedance===a){am=[au(av.x===p?ac:av.x===P?ao.width-ak-ac:(ao.width-ak)/2),au(av.y===B?ao.height-at:0)];}else{am=[au(av.x===p?ao.width-ak:0),au(av.y===B?ac:av.y===w?ao.height-at-ac:(ao.height-at)/2)];}if(ab){ax.attr(ao);aj=ax[0].getContext("2d");aj.restore();aj.save();aj.clearRect(0,0,3000,3000);aj.fillStyle=ae.fill;aj.strokeStyle=ae.border;aj.lineWidth=ac*2;aj.lineJoin="miter";aj.miterLimit=100;aj.translate(am[0],am[1]);aj.beginPath();aj.moveTo(ap[0][0],ap[0][1]);aj.lineTo(ap[1][0],ap[1][1]);aj.lineTo(ap[2][0],ap[2][1]);aj.closePath();if(ac){if(T.css("background-clip")==="border-box"){aj.strokeStyle=ae.fill;aj.stroke();}aj.strokeStyle=ae.border;aj.stroke();}aj.fill();}else{ap="m"+ap[0][0]+","+ap[0][1]+" l"+ap[1][0]+","+ap[1][1]+" "+ap[2][0]+","+ap[2][1]+" xe";am[2]=ac&&/^(r|b)/i.test(ar.string())?1:0;ax.css({antialias:""+(av.string().indexOf(v)>-1),left:am[0]-(am[2]*Number(ai===b)),top:am[1]-(am[2]*Number(ai===a)),width:ak+ac,height:at+ac}).each(function(ay){var az=y(this);az[az.prop?"prop":"attr"]({coordsize:(ak+ac)+" "+(at+ac),path:ap,fillcolor:ae.fill,filled:!!ay,stroked:!ay}).css({display:ac||ay?"block":"none"});if(!ay&&az.html()===""){az.html('<vml:stroke weight="'+(ac*2)+'px" color="'+ae.border+'" miterlimit="1000" joinstyle="miter"  style="behavior:url(#default#VML); display:inline-block;" />');}});}if(an!==O){Z.position(ar);}},position:function(an){var ao=ag.tip,aj={},ai=Math.max(0,W.offset),ak,am,al;if(W.corner===O||!ao){return O;}an=an||Z.corner;ak=an.precedance;am=af(an);al=[an.x,an.y];if(ak===b){al.reverse();}y.each(al,function(at,ar){var ap,aq;if(ar===v){ap=ak===a?p:B;aj[ap]="50%";aj["margin-"+ap]=-Math.round(am[ak===a?d:x]/2)+ai;}else{ap=ah(an,ar);aq=R(an);aj[ar]=at?0:(ai+(aq>ap?aq:-ap));}});aj[an[ak]]-=am[ak===b?d:x];ao.css({top:"",bottom:"",left:"",right:"",margin:""}).css(aj);return aj;},destroy:function(){if(ag.tip){ag.tip.remove();}ag.tip=false;T.unbind(V);}});Z.init();}t.tip=function(R){var Q=R.plugins.tip;return"object"===typeof Q?Q:(R.plugins.tip=new J(R));};t.tip.initialize="render";t.tip.sanitize=function(Q){var R=Q.style,S;if(R&&"tip" in R){S=Q.style.tip;if(typeof S!=="object"){Q.style.tip={corner:S};}if(!(/string|boolean/i).test(typeof S.corner)){S.corner=s;}if(typeof S.width!=="number"){delete S.width;}if(typeof S.height!=="number"){delete S.height;}if(typeof S.border!=="number"&&S.border!==s){delete S.border;}if(typeof S.offset!=="number"){delete S.offset;}}};y.extend(s,K.defaults,{style:{tip:{corner:s,mimic:O,width:6,height:6,border:s,offset:0}}});t.svg=function(W,V,aa,T){var Z=y(document),S=V[0],ab={width:0,height:0,position:{top:1**********,left:1**********}},U,Q,X,Y,R;while(!S.getBBox){S=S.parentNode;}if(S.getBBox&&S.parentNode){U=S.getBBox();Q=S.getScreenCTM();X=S.farthestViewportElement||S;if(!X.createSVGPoint){return ab;}Y=X.createSVGPoint();Y.x=U.x;Y.y=U.y;R=Y.matrixTransform(Q);ab.position.left=R.x;ab.position.top=R.y;Y.x+=U.width;Y.y+=U.height;R=Y.matrixTransform(Q);ab.width=R.x-ab.position.left;ab.height=R.y-ab.position.top;ab.position.left+=Z.scrollLeft();ab.position.top+=Z.scrollTop();}return ab;};function G(T){var R=this,Q=T.elements,U=Q.tooltip,S=".bgiframe-"+T.id;y.extend(R,{init:function(){Q.bgiframe=y('<iframe class="ui-tooltip-bgiframe" frameborder="0" tabindex="-1" src="javascript:\'\';"  style="display:block; position:absolute; z-index:-1; filter:alpha(opacity=0); -ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";"></iframe>');Q.bgiframe.appendTo(U);U.bind("tooltipmove"+S,R.adjust);},adjust:function(){var X=T.get("dimensions"),W=T.plugins.tip,Y=Q.tip,V,Z;Z=parseInt(U.css("border-left-width"),10)||0;Z={left:-Z,top:-Z};if(W&&Y){V=(W.corner.precedance==="x")?["width","left"]:["height","top"];Z[V[1]]-=Y[V[0]]();}Q.bgiframe.css(Z).css(X);},destroy:function(){Q.bgiframe.remove();U.unbind(S);}});R.init();}t.bgiframe=function(R){var Q=R.plugins.bgiframe;if(y("select, object").length<1){return O;}return"object"===typeof Q?Q:(R.plugins.bgiframe=new G(R));};t.bgiframe.initialize="render";}));