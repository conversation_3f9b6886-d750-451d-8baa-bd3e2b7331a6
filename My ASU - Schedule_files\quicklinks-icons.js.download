var quicklinksAnchorsFaviconOverrides={"email.asu.edu/":"https://www.google.com/a/asu.edu/images/favicon.ico","drive.google.com/a/asu.edu/":"https://ssl.gstatic.com/docs/doclist/images/infinite_arrow_favicon_5.ico","lib.asu.edu/":"https://lib.asu.edu/favicon.ico","www.asu.edu/myapps/":"https://myapps.asu.edu/sites/default/files/favicon_0.ico","calendar.google.com/a/asu.edu":"https://calendar.google.com/googlecalendar/images/favicon_v2014_"+(new Date().getDate())+".ico","orgsync.com/sso_redirect/arizona-state-university":"https://orgsync.com/favicon.ico","office365.asu.edu/exchange":"https://outlook.office365.com/owa/favicon.ico","analytics.asu.edu/":"https://analytics.asu.edu/themes/analytics/favicon.ico","webapp4-dev.asu.edu/uachieve/audit/create.html":"https://webapp4.asu.edu/uachieve/images/favicon.ico","webapp4-qa.asu.edu/uachieve/audit/create.html":"https://webapp4.asu.edu/uachieve/images/favicon.ico","webapp4.asu.edu/uachieve/audit/create.html":"https://webapp4.asu.edu/uachieve/images/favicon.ico","office365.asu.edu/onedrive":"https://arizonastateu-my.sharepoint.com/_layouts/15/images/odbfavicon.ico","www.asu.edu/dropbox":"https://cfl.dropboxstatic.com/static/images/favicon-vflUeLeeY.ico","www.asu.edu/myfiles/":"https://www.asu.edu/asuthemes/2.0/images/favicon.ico","sites.asu.edu":"https://www.google.com/images/icons/product/sites-16.ico","asu.zoom.us/":"https://d24cgw3uvb9a9h.cloudfront.net/zoom.ico"};function populateQuicklinksFavicons(a){$(a).find("a[href^='http']").each(function(){if(this.getAttribute("data-id")==="healthcheck"){return;}var d=new Image();var c=this;var b=this.hostname;var f=b+this.pathname;var e="https://"+b+"/favicon.ico";if(quicklinksAnchorsFaviconOverrides[f]!=null){e=quicklinksAnchorsFaviconOverrides[f];}d.onload=function(){setFavicon(c,e);};d.onerror=function(){var g=new Image();e="https://www.google.com/s2/favicons?domain="+b;g.onload=function(){setFavicon(c,e);};g.src=e;};d.src=e;});}function depopulateQuicklinksFavicons(a){$(a).find("a[href^='http']").each(function(){if($("#show-bookmark-icons-value").val()=="false"){$(this).find("img").remove();}});}function setFavicon(b,d){if($("#show-bookmark-icons-value").val()=="true"){$(b).find(".quicklink-anchor-img").remove();var a=document.createElement("img");a.src=d;a.width="16";a.height="16";a.className="quicklink-anchor-img";$(b).prepend(a);if(resizeQuicklinkData!=null&&resizeQuicklinkData.quicklinksVisibleList!=null&&resizeQuicklinkData.quicklinksVisibleList!=undefined){for(var c=0;c<resizeQuicklinkData.quicklinksVisibleList.length;c++){if($(b).attr("href")==resizeQuicklinkData.quicklinksVisibleList[c].hrefUrl){resizeQuicklinkData.quicklinksVisibleList[c].imageSrc=d;break;}}}}}