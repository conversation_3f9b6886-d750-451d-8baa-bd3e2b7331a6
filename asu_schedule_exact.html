<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My ASU - Schedule</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        /* 第一步：顶部时间和标题栏 */
        .header {
            background-color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }
        
        .date-time {
            font-size: 14px;
            color: #666;
        }
        
        .my-asu-schedule {
            font-size: 14px;
            font-weight: bold;
            color: #333;
        }

        /* 第二步：logo区域 */
        .logo-section {
            background-color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #ddd;
        }

        .my-asu-nav {
            color: #333;
            font-size: 14px;
        }

        .hamburger {
            margin-left: auto;
            font-size: 24px;
            color: #333;
        }

        /* 第三步：内容区域 */
        .content {
            background-color: white;
            margin: 20px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .customize-section {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ddd;
        }

        .customize-text {
            font-size: 14px;
            font-weight: bold;
        }

        /* 第四步：学生姓名 */
        .student-name {
            text-align: right;
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        /* 第五步：课程表标题 */
        .schedule-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
        }

        /* 第六步：表格样式 */
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .schedule-table th {
            background-color: #f8f8f8;
            padding: 8px;
            text-align: left;
            border: 1px solid #000;
            font-weight: bold;
            font-size: 13px;
        }

        .schedule-table td {
            padding: 8px;
            border: 1px solid #000;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <!-- 第一步：顶部时间和标题栏 -->
    <div class="header">
        <div class="date-time">7/20/25, 3:27 PM</div>
        <div class="my-asu-schedule">My ASU - Schedule</div>
    </div>

    <!-- 第二步：logo区域（保留空间，不显示logo） -->
    <div class="logo-section">
        <div class="my-asu-nav">My ASU</div>
        <div class="hamburger">☰</div>
    </div>

    <!-- 第三步：内容区域 -->
    <div class="content">
        <!-- Customize部分（不显示铅笔图标） -->
        <div class="customize-section">
            <span class="customize-text">Customize</span>
        </div>

        <!-- 第四步：学生姓名 -->
        <div class="student-name">Joe Farley</div>

        <!-- 第五步：课程表标题 -->
        <div class="schedule-title">My Schedule for Fall 2025</div>

        <!-- 第六步：表格结构 -->
        <table class="schedule-table">
            <thead>
                <tr>
                    <th>Class #</th>
                    <th>Course</th>
                    <th>Title</th>
                    <th>Units</th>
                    <th>Instructor(s)</th>
                    <th>Days</th>
                    <th>Times</th>
                    <th>Date(s)</th>
                    <th>Location</th>
                </tr>
            </thead>
            <tbody>
                <!-- 第七步：精确按照图片的课程数据 -->
                <tr>
                    <td>74588</td>
                    <td>ENG 101</td>
                    <td>First-Year Composition</td>
                    <td>3.0</td>
                    <td>Anderson</td>
                    <td>MW</td>
                    <td>9:00 AM - 10:15 AM</td>
                    <td>08/19/25 - 12/5/25</td>
                    <td>LL 101</td>
                </tr>
                <tr>
                    <td>75234</td>
                    <td>MAT 142</td>
                    <td>College Mathematics</td>
                    <td>3.0</td>
                    <td>Mitchell</td>
                    <td>TTh</td>
                    <td>11:00 AM - 12:15 PM</td>
                    <td>08/19/25 - 12/5/25</td>
                    <td>PSA 101</td>
                </tr>
                <tr>
                    <td>76891</td>
                    <td>CHM 113</td>
                    <td>General Chemistry I</td>
                    <td>4.0</td>
                    <td>Parker</td>
                    <td>MWF</td>
                    <td>1:30 PM - 2:45 PM</td>
                    <td>08/19/25 - 12/5/25</td>
                    <td>PS A102</td>
                </tr>
                <tr>
                    <td>77456</td>
                    <td>PSY 101</td>
                    <td>Introduction to Psychology</td>
                    <td>3.0</td>
                    <td>Bennett</td>
                    <td>TTh</td>
                    <td>2:00 PM - 3:15 PM</td>
                    <td>08/19/25 - 12/5/25</td>
                    <td>PSA 113</td>
                </tr>
                <tr>
                    <td>78123</td>
                    <td>ASU 101</td>
                    <td>The ASU Experience</td>
                    <td>1.0</td>
                    <td>Foster</td>
                    <td>F</td>
                    <td>10:00 AM - 10:50 AM</td>
                    <td>08/19/25 - 12/5/25</td>
                    <td>MU 145</td>
                </tr>
            </tbody>
        </table>
    </div>
</body>
</html>
