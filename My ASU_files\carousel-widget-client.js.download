(function(global, factory) {
	var carousel_widget_jq;
	function loadClient() {
		if (typeof carousel_widget_jq === 'undefined' || carousel_widget_jq.fn.jquery !== '3.5.1') {
			global.setTimeout(loadClient, 50);
		} else {
			if (typeof global.AdsClient === 'undefined') {
				global.AdsClient = {};
				console.log(global.AdsClient);
				factory(global.AdsClient, carousel_widget_jq);
			}
		}
	}

	function resolveJQueryNoConflict() {
		// JQuery loaded, perform jQuery.noConflict to prevent conflicts of multiple JQ instances
		if(global.jQuery.fn.jquery === '3.5.1') {
			carousel_widget_jq =  global.jQuery.noConflict(true);
		}
	}

	if (typeof global.AdsClient === 'undefined') {
		let head = document.getElementsByTagName('head')[0] || document.documentElement;
		if (typeof global.jQuery === 'undefined' || global.jQuery.fn.jquery !== '3.5.1') {
			require('https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js', head, resolveJQueryNoConflict);
		}
	}

	function require(src, head, callback) {
		let script = document.createElement('script');
		script.type = 'text/javascript';
		script.src = src;
		script.onload = callback;
		head.appendChild(script);
	}

	loadClient();

})(this, function(adsClient, carousel_client_jq) {
	var client;

	adsClient.init = function(restApiBasePath, jwt) {
		client = new Client(restApiBasePath, jwt);
	}

	adsClient.setJwt = function(jwt) {
		client.jwt = jwt;
	}

	adsClient.loadAvailablePublicationsForZone = function(zoneId) {
		if(client.jwt) {
			return client.makeRequest('GET', '/content?zone=' + zoneId);
		} else {
			return client.makeRequest('GET', '/content/public?zone=' + zoneId);
		}
	}

	adsClient.dismissPublicationWithId = function(publicationId) {
		data = {
			publicationId: publicationId
		};
		if(client.jwt) {
			return client.makeRequest('POST', '/content/dismissal', data);
		} else {
			return new Promise(function(resolve, reject){});
		}
	}

		adsClient.clickedPublicationWithId = function(publicationId) {
		data = {
			publicationId: publicationId
		};
		if(client.jwt) {
			return client.makeRequest('POST', '/content/click', data);
		} else {
			return new Promise(function(resolve, reject){});
		}
	}

	adsClient.loadPublicationDimsissals = function() {
		console.log('get dismissals');
//		return client.makeRequest('GET', '/dismissals');
	}

	function Client(restApiBasePath, jwt) {
		this.jwt = jwt;
		this.path = restApiBasePath;
	}

	Client.prototype.makeRequest = function(method, path, data) {
		let self = this;
		return new Promise(function(resolve, reject) {
			carousel_client_jq.ajax({
				url: self.path + path,
				type: method,
				data: data,
				beforeSend: function (xhr) {
					if(self.jwt) {
						xhr.setRequestHeader('Authorization', 'Bearer ' + self.jwt);
					}
				}
			}).done(function(data, status, xhr) {
				resolve({data, status, xhr});
			}).fail(function(xhr, status) {
				reject(xhr, status);
			}); // end fail
		}); // end new promise
	} // end -- Client.makeRequest()
}); // end -- global function
