(function(g){g.popout={version:"1.0"};var m,h,k,j,i,b,e,l,d,n,f;g.fn.popout=function(p,o){if(typeof p=="object"){o=p;p=null;}if(p=="destroy"){return this.removeData("thisInfo").unbind(".popout");}return this.each(function(O){var x=this,B=g(this);var M=g.extend(true,{},g.fn.popout.defaults,o||{},g.metadata?B.metadata():g.meta?B.data():{});var A=false;var q=+M.popzIndex;B.data("thisInfo",{title:x.title,zIndex:q});var aa=false,Z=0;if(!g("#popout").length){g(['<div id="popout">','<div id="popout-outer">','<h3 id="popout-title"></h3>','<div id="popout-inner"></div>',"</div>",'<div id="popout-extra"></div>','<div id="popout-arrows" class="popout-arrows"></div>',"</div>"].join(""))[c](a).hide();h=g("#popout").css({position:"absolute",zIndex:q-1});j=g("#popout-outer").css({position:"relative",zIndex:q});k=g("#popout-inner");i=g("#popout-title");b=g("#popout-arrows").css({zIndex:q-1});e=g('<div id="popout-waitimage"></div>').css({position:"absolute"}).insertBefore(h).hide();}var C=(M.popoutDropShadow)?+M.popoutDropShadowSteps:0;if(!l){l=g([]);for(var ad=0;ad<C;ad++){l=l.add(g('<div class="modal"></div>').css({zIndex:q-6,opacity:0.65,top:0,left:0}));}l.css({position:"absolute",backgroundColor:"#000"}).insertBefore(h);}var K=B.attr(M.attribute),w=M.popoutClass;if(!K&&!M.splitTitle&&!p){return true;}if(M.local&&M.localPrefix){K=M.localPrefix+K;}if(M.local&&M.hideLocal){g(K+":first").hide();}var L=parseInt(M.topOffset,10),I=parseInt(M.leftOffset,10);var G,ab,E=isNaN(parseInt(M.height,10))?"auto":(/\D/g).test(M.height)?M.height:M.height+"px";var s,y,Q,ah,S,ac;var F=parseInt(M.width,10)||275,ae=F+(parseInt(h.css("paddingLeft"),10)||0)+(parseInt(h.css("paddingRight"),10)||0),J=this.offsetWidth,z,R,ai,T,t;var Y;var N=(M.attribute!="title")?B.attr(M.titleAttribute):"";if(M.splitTitle){if(N==undefined){N="";}Y=N.split(M.splitTitle);N=Y.shift();}if(M.escapeTitle){N=N.replace(/&/g,"&amp;").replace(/>/g,"&gt;").replace(/</g,"&lt;");}var v;function ag(){return false;}var D=function(aj){if(!M.onActivate(B,aj)){return false;}aa=true;h.removeClass().css({width:F});if(K==B.attr("href")){B.css("cursor",M.cursor);}if(M.hoverClass){B.addClass(M.hoverClass);}y=Q=B.offset().top;z=B.offset().left;T=aj.pageX;S=aj.pageY;if(x.tagName.toLowerCase()!="area"){s=g(document).scrollTop();t=g(window).width();}if(M.positionBy=="fixed"){R=J+z+I;h.css({left:R});}else{R=(J>z&&z>ae)||z+J+ae+I>t?z-ae-I:J+z+I;if(x.tagName.toLowerCase()=="area"||M.positionBy=="mouse"||J+ae>t){if(T+20+ae>t){h.addClass(" popout-"+w);R=(T-ae-I)>=0?T-ae-I-parseInt(h.css("marginLeft"),10)+parseInt(k.css("marginRight"),10):T-(ae/2);}else{R=T+I;}}var ak=R<0?aj.pageY+L:aj.pageY;h.css({left:(R>0&&M.positionBy!="bottomTop")?R:(T+(ae/2)>t)?t/2-ae/2:Math.max(T-(ae/2),0),zIndex:B.data("thisInfo").zIndex});b.css({zIndex:B.data("thisInfo").zIndex+1});}ab=g(window).height();if(p){if(typeof p=="function"){p=p.call(x);}k.html(p);W(ak);}else{if(Y){var am=Y.length;k.html(am?Y[0]:"");if(am>1){for(var al=1;al<am;al++){k.append('<div class="split-body">'+Y[al]+"</div>");}}W(ak);}else{if(!M.local&&K.indexOf("#")!==0){if(/\.(jpe?g|tiff?|gif|png)$/i.test(K)){k.html('<img src="'+K+'" alt="'+N+'" />');W(ak);}else{if(A&&M.ajaxCache){k.html(A);W(ak);}else{var aq=M.ajaxSettings.beforeSend,an=M.ajaxSettings.error,ao=M.ajaxSettings.success,au=M.ajaxSettings.complete;var at={cache:false,url:K,beforeSend:function(av){if(aq){aq.call(x,av,h,k);}j.children().empty();if(M.waitImage){e.css({top:Q+20,left:R-40,zIndex:B.data("thisInfo").zIndex-1}).show();}},error:function(av,aw){if(aa){if(an){an.call(x,av,aw,h,k);}else{k.html("<i>sorry, the contents could not be loaded</i>");}}},success:function(av,aw){A=M.ajaxProcess.call(x,av);if(aa){if(ao){ao.call(x,av,aw,h,k);}if(A.length==0){k.html("<p><i>Sorry, the contents could not be loaded. Please try refreshing the page.</i></p>");}else{k.html(A);}}},complete:function(av,aw){e.hide();if(aa){W(ak);}}};var ap=g.extend(true,{},M.ajaxSettings,at);g.ajax(ap);}}}else{if(M.local){var ar=g(K+(/#\S+$/.test(K)?"":":eq("+O+")")).clone(true).show();k.html(ar);W(ak);}}}}};var W=function(al){h.addClass("popout-"+w);if(M.truncate){var am=k.text().slice(0,M.truncate)+"...";k.html(am);}function aj(){}N?i.show().html(N):(M.showTitle)?i.show().html("&nbsp;"):i.hide();if(M.sticky){var ak=g('<div id="popout-close"><a href="#">'+M.closeText+"</a></div>");(M.closePosition=="bottom")?ak.appendTo(k):(M.closePosition=="title")?ak.prependTo(i):ak.prependTo(k);ak.bind("click.popout",function(){r();return false;});if(M.mouseOutClose){h.bind("mouseleave.popout",function(){r();});}else{h.unbind("mouseleave.popout");}}var an="";j.css({zIndex:B.data("thisInfo").zIndex,overflow:E=="auto"?"visible":"auto",height:E});G=E=="auto"?Math.max(h.outerHeight(),h.height()):parseInt(E,10);ah=Q;ac=s+ab;if(M.positionBy=="fixed"){ah="125";}else{if((R<T&&Math.max(R,0)+ae>T)||M.positionBy=="bottomTop"){if(Q+G+L>ac&&S-s>G+L){ah=S-G-L;an="top";}else{ah=S+L;an="bottom";}}else{if(Q+G+L>ac){ah=(G>=ab)?s:ac-G-L;}else{if(B.css("display")=="block"||x.tagName.toLowerCase()=="area"||M.positionBy=="mouse"){ah=al-L;}else{ah=Q-M.popoutDropShadowSteps;}}}}if(an==""){R<z?an="left":an="right";}h.css({top:ah+"px"}).removeClass().addClass("pop-"+an+"-"+w).addClass(" popout-"+w);if(M.arrows){var ao=(Q-ah-M.popoutDropShadowSteps);b.css({top:(/(left|right)/.test(an)&&R>=0&&ao>0)?ao+"px":/(left|right)/.test(an)?0:""}).show();}else{b.hide();}h.hide()[M.fx.open](M.fx.openSpeed||0);if(M.popoutDropShadow){l.css({height:"100%",width:"100%",top:"0",left:"0",position:"fixed",zIndex:q-3}).show();}if(g.fn.bgiframe){h.bgiframe();}if(M.delayedClose>0){Z=setTimeout(r,M.delayedClose);}M.onShow.call(x,h,k);};var af=function(aj){aa=false;e.hide();if(!M.sticky||(/click|toggle/).test(M.activation)){if(M.activation=="hover"){g(document).mousemove(H);U=setInterval(P,250);}else{r();}clearTimeout(Z);}if(M.hoverClass){B.removeClass(M.hoverClass);}};var r=function(){var aj=".modal";j.parent().hide().removeClass();g(aj).fadeOut();M.onHide.call(x,h,k,m);B.removeClass("popout-clicked");if(N){B.attr(M.titleAttribute,N);}B.css("cursor","");if(M.arrows){b.css({top:""});}g(document).trigger("hideCluetip");};g(document).bind("hidepopout",function(aj){r();});var X=0,V=0;var H=function(aj){X=aj.pageX;V=aj.pageY;};var U;var P=function(){var aj=j.offset();if(X<aj.left||X>aj.left+j.width()||V<aj.top||V>aj.top+j.height()){r();clearTimeout(U);g(document).unbind("mousemove",H);}};if((/click|toggle/).test(M.activation)){B.bind("click.popout",function(aj){if(h.is(":hidden")||!B.is(".popout-clicked")){D(aj);g(".popout-clicked").removeClass("popout-clicked");B.addClass("popout-clicked");}else{af(aj);}this.blur();return false;});}else{if(M.activation=="focus"){B.bind("focus.popout",function(aj){D(aj);});B.bind("blur.popout",function(aj){af(aj);});}else{B[M.clickThrough?"unbind":"bind"]("click",ag);var u=function(aj){if(M.tracking==true){var al=R-aj.pageX;var ak=ah?ah-aj.pageY:Q-aj.pageY;B.bind("mousemove.popout",function(am){h.css({left:am.pageX+al,top:am.pageY+ak});});}};if(g.fn.hoverIntent&&M.hoverIntent){B.hoverIntent({sensitivity:M.hoverIntent.sensitivity,interval:M.hoverIntent.interval,over:function(aj){D(aj);u(aj);},timeout:M.hoverIntent.timeout,out:function(aj){af(aj);B.unbind("mousemove.popout");}});}else{B.bind("mouseenter.popout",function(aj){D(aj);u(aj);}).bind("mouseleave.popout",function(aj){af(aj);B.unbind("mousemove.popout");});}B.bind("mouseover.popout",function(aj){B.attr("title","");}).bind("mouseleave.popout",function(aj){B.attr("title",B.data("thisInfo").title);});}}});};g.fn.popout.defaults={width:275,height:"auto",popzIndex:101,positionBy:"fixed",topOffset:-300,leftOffset:40,local:false,localPrefix:null,hideLocal:true,attribute:"rel",titleAttribute:"title",splitTitle:"",escapeTitle:false,showTitle:true,popoutClass:"default",hoverClass:"",waitImage:true,cursor:"",arrows:false,popoutDropShadow:true,popoutDropShadowSteps:1,sticky:false,mouseOutClose:false,activation:"hover",clickThrough:false,tracking:false,delayedClose:0,closePosition:"top",closeText:"Close",truncate:0,fx:{open:"show",openSpeed:""},hoverIntent:{sensitivity:3,interval:50,timeout:0},onActivate:function(o){return true;},onShow:function(p,o){},onHide:function(p,o){},ajaxCache:true,ajaxProcess:function(o){o=o.replace(/<(script|style|title)[^<]+<\/(script|style|title)>/gm,"").replace(/<(link|meta)[^>]+>/g,"");return o;},ajaxSettings:{dataType:"html"},debug:false};var c="appendTo",a="body";g.popout.setup=function(o){if(o&&o.insertionType&&(o.insertionType).match(/appendTo|prependTo|insertBefore|insertAfter/)){c=o.insertionType;}if(o&&o.insertionElement){a=o.insertionElement;}};})(jQuery);