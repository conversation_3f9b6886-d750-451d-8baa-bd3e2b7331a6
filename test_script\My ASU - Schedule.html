<!DOCTYPE html>
<!-- saved from url=(0056)https://webapp4.asu.edu/myasu/student/schedule?term=2257 -->
<html lang="en" style="--prebanner-background-color: #FFFFFF; --prebanner-secondary-color: #4f4f50; --prebanner-text-color: #000000; --prebanner-accept-all-button-color: #0679ab; --prebanner-accept-all-button-text-color: #000000; --prebanner-reject-all-button-color: #0679ab; --prebanner-reject-all-button-text-color: #000000; --prebanner-customise-all-button-color: #0679ab; --prebanner-customise-all-button-text-color: #000000; --prebanner-border-color: #4f4f50; --accept-all-button-background-color\): #0679ab; --accept-all-button-text-color\): #000000; --accept-all-button-border-color\): #0679ab; --reject-all-button-background-color\): #0679ab; --reject-all-button-border-color\): #0679ab; --view-all-button-background-color\): #0679ab; --view-all-button-text-color\): #000000; --view-all-button-border-color\): #0679ab; --view-all-button-icon-color\): #000000; --banner-background-color: #FFFFFF; --banner-secondary-color: #FFFFFF; --banner-text-color: #191919; --banner-submit-preferences-button-color: #191919; --banner-submit-preferences-button-text-color: #000000; --banner-slider-background-off: #000000; --banner-slider-background-on: #78BE20; --banner-slider-toggle: #FFFFFF; --scrollbar-background-color: #FFFFFF; --scrollbar-thumb-color: #191919; --scrollbar-thumb-hover-color: #FFFFFF; --cookie-modal-background-color: #FFFFFF; --cookie-modal-text-color: #191919; --close-modal-button-color: #FFFFFF; --header-content-top-border-color: #FFFFFF; --main-content-border-colour: #FFFFFF; --tab-button-background-color: #FFFFFF; --tab-button-text-color: #191919; --tab-button-border-color: #FFFFFF; --active-button-border-color: #FFFFFF; --cookie-group-bottom-border-colour: #FFFFFF; --expand-cookies-icon-color: #FFFFFF; --cookie-children-container-background-color: #191919; --cookie-children-container-text-color: #FFFFFF; --slider-background-colour-unchecked: #000000; --toggle-background-color: #FFFFFF; --slider-background-colour-checked: #78BE20; --manage-cookies-button-background-color: #FFFFFF; --manage-cookies-button-text-color: #191919; ----manage-cookies-button-border-color: #FFFFFF;"><head data-ssoname="Marca"><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><link type="text/css" rel="stylesheet" href="./My ASU - Schedule_files/template.min.css">

	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta name="theme-color" content="#8C1D40">

	
		<noscript>
			<meta http-equiv="refresh" content="0;URL=https://webapp4.asu.edu/myasu/nojs" />
		</noscript>
	

	<title>My ASU - Schedule</title>

	<!--<base href="https://webapp4.asu.edu/myasu/">--><base href=".">

	

	
	

	<style type="text/css">svg:not(:root).svg-inline--fa {
  overflow: visible;
}

.svg-inline--fa {
  display: inline-block;
  font-size: inherit;
  height: 1em;
  overflow: visible;
  vertical-align: -0.125em;
}
.svg-inline--fa.fa-lg {
  vertical-align: -0.225em;
}
.svg-inline--fa.fa-w-1 {
  width: 0.0625em;
}
.svg-inline--fa.fa-w-2 {
  width: 0.125em;
}
.svg-inline--fa.fa-w-3 {
  width: 0.1875em;
}
.svg-inline--fa.fa-w-4 {
  width: 0.25em;
}
.svg-inline--fa.fa-w-5 {
  width: 0.3125em;
}
.svg-inline--fa.fa-w-6 {
  width: 0.375em;
}
.svg-inline--fa.fa-w-7 {
  width: 0.4375em;
}
.svg-inline--fa.fa-w-8 {
  width: 0.5em;
}
.svg-inline--fa.fa-w-9 {
  width: 0.5625em;
}
.svg-inline--fa.fa-w-10 {
  width: 0.625em;
}
.svg-inline--fa.fa-w-11 {
  width: 0.6875em;
}
.svg-inline--fa.fa-w-12 {
  width: 0.75em;
}
.svg-inline--fa.fa-w-13 {
  width: 0.8125em;
}
.svg-inline--fa.fa-w-14 {
  width: 0.875em;
}
.svg-inline--fa.fa-w-15 {
  width: 0.9375em;
}
.svg-inline--fa.fa-w-16 {
  width: 1em;
}
.svg-inline--fa.fa-w-17 {
  width: 1.0625em;
}
.svg-inline--fa.fa-w-18 {
  width: 1.125em;
}
.svg-inline--fa.fa-w-19 {
  width: 1.1875em;
}
.svg-inline--fa.fa-w-20 {
  width: 1.25em;
}
.svg-inline--fa.fa-pull-left {
  margin-right: 0.3em;
  width: auto;
}
.svg-inline--fa.fa-pull-right {
  margin-left: 0.3em;
  width: auto;
}
.svg-inline--fa.fa-border {
  height: 1.5em;
}
.svg-inline--fa.fa-li {
  width: 2em;
}
.svg-inline--fa.fa-fw {
  width: 1.25em;
}

.fa-layers svg.svg-inline--fa {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.fa-layers {
  display: inline-block;
  height: 1em;
  position: relative;
  text-align: center;
  vertical-align: -0.125em;
  width: 1em;
}
.fa-layers svg.svg-inline--fa {
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-counter, .fa-layers-text {
  display: inline-block;
  position: absolute;
  text-align: center;
}

.fa-layers-text {
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  -webkit-transform-origin: center center;
          transform-origin: center center;
}

.fa-layers-counter {
  background-color: #ff253a;
  border-radius: 1em;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  color: #fff;
  height: 1.5em;
  line-height: 1;
  max-width: 5em;
  min-width: 1.5em;
  overflow: hidden;
  padding: 0.25em;
  right: 0;
  text-overflow: ellipsis;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-bottom-right {
  bottom: 0;
  right: 0;
  top: auto;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: bottom right;
          transform-origin: bottom right;
}

.fa-layers-bottom-left {
  bottom: 0;
  left: 0;
  right: auto;
  top: auto;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: bottom left;
          transform-origin: bottom left;
}

.fa-layers-top-right {
  right: 0;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top right;
          transform-origin: top right;
}

.fa-layers-top-left {
  left: 0;
  right: auto;
  top: 0;
  -webkit-transform: scale(0.25);
          transform: scale(0.25);
  -webkit-transform-origin: top left;
          transform-origin: top left;
}

.fa-lg {
  font-size: 1.3333333333em;
  line-height: 0.75em;
  vertical-align: -0.0667em;
}

.fa-xs {
  font-size: 0.75em;
}

.fa-sm {
  font-size: 0.875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}
.fa-ul > li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

.fa-border {
  border: solid 0.08em #eee;
  border-radius: 0.1em;
  padding: 0.2em 0.25em 0.15em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: 0.3em;
}
.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: 0.3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}
.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1);
}

.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical,
:root .fa-flip-both {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  display: inline-block;
  height: 2em;
  position: relative;
  width: 2.5em;
}

.fa-stack-1x,
.fa-stack-2x {
  bottom: 0;
  left: 0;
  margin: auto;
  position: absolute;
  right: 0;
  top: 0;
}

.svg-inline--fa.fa-stack-1x {
  height: 1em;
  width: 1.25em;
}
.svg-inline--fa.fa-stack-2x {
  height: 2em;
  width: 2.5em;
}

.fa-inverse {
  color: #fff;
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

.svg-inline--fa .fa-primary {
  fill: var(--fa-primary-color, currentColor);
  opacity: 1;
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa .fa-secondary {
  fill: var(--fa-secondary-color, currentColor);
  opacity: 0.4;
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-primary {
  opacity: 0.4;
  opacity: var(--fa-secondary-opacity, 0.4);
}

.svg-inline--fa.fa-swap-opacity .fa-secondary {
  opacity: 1;
  opacity: var(--fa-primary-opacity, 1);
}

.svg-inline--fa mask .fa-primary,
.svg-inline--fa mask .fa-secondary {
  fill: black;
}

.fad.fa-inverse {
  color: #fff;
}</style><style data-tippy-stylesheet="">.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}</style><link rel="stylesheet" href="./My ASU - Schedule_files/normalize.css" type="text/css">

	
	
		<script type="text/javascript" id="www-widgetapi-script" src="./My ASU - Schedule_files/www-widgetapi.js.download" async=""></script><script src="./My ASU - Schedule_files/iframe_api"></script><script type="text/javascript" async="" src="./My ASU - Schedule_files/analytics.js.download"></script><script type="text/javascript" async="" src="./My ASU - Schedule_files/gtm.js.download"></script><script type="text/javascript" async="" src="./My ASU - Schedule_files/js"></script><script type="text/javascript" async="" src="./My ASU - Schedule_files/js(1)"></script><script async="" src="./My ASU - Schedule_files/gtm(1).js.download"></script><script src="./My ASU - Schedule_files/jquery-2.2.4.min.js.download" type="text/javascript"></script>
		
			<script src="./My ASU - Schedule_files/jquery-migrate-1.4.1.min.js.download"></script>
		
	

	<script src="./My ASU - Schedule_files/jquery-ui-1.12.1.custom.min.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/myasu.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/listmenu.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/jquery.qtip.js.download" type="text/javascript"></script>
	<link rel="stylesheet" href="./My ASU - Schedule_files/jquery.qtip.css" type="text/css">

	<script src="./My ASU - Schedule_files/jquery.dimensions-1.2-myasu.min.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/jquery.hoverIntent-r5.min.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/jquery.cluetip-1.0.7.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/jquery.popout.js.download" type="text/javascript"></script> 
	<link href="./My ASU - Schedule_files/jquery.cluetip.css" rel="stylesheet" type="text/css" media="all">
	
	<script src="./My ASU - Schedule_files/mustache.min.js.download" type="text/javascript"></script>
	
	<script src="./My ASU - Schedule_files/myasu-tabs.js.download" type="text/javascript"></script>
	<link href="./My ASU - Schedule_files/myasu-tabs.css" rel="stylesheet" type="text/css" media="all">
	<script src="./My ASU - Schedule_files/myasu-modal.js.download" type="text/javascript"></script>
	<link href="./My ASU - Schedule_files/myasu-modal.css" rel="stylesheet" type="text/css" media="all">

	<script type="text/javascript">
	    if (navigator.userAgent.match(/Trident\/7\./)) { // check if IE11 to load tippy polyfill
	        document.write('<script src="js/tippyjs/polyfill/array-prototype-find-polyfill.min.js" type="text/javascript"><\/script>');
	        document.write('<script src="js/tippyjs/polyfill/object-assign-polyfill.min.js" type="text/javascript"><\/script>');
	        document.write('<script src="js/tippyjs/polyfill/promise-polyfill.min.js" type="text/javascript"><\/script>');
	    }
	</script>
	<script src="./My ASU - Schedule_files/popper.min.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/tippy-bundle.umd.min.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/myasu-tippy.js.download" type="text/javascript"></script>
	<link rel="stylesheet" href="./My ASU - Schedule_files/scale.css">
	<link rel="stylesheet" href="./My ASU - Schedule_files/border.css">

	
		<link rel="stylesheet" href="./My ASU - Schedule_files/all.min.css" type="text/css">
		<link rel="stylesheet" href="./My ASU - Schedule_files/v4-shims.min.css" type="text/css">
	
	

	

	<link href="./My ASU - Schedule_files/myasu-common.css" rel="stylesheet" type="text/css" media="all">
	<link href="./My ASU - Schedule_files/myasu-common(1).css" rel="stylesheet" type="text/css" media="all">
	<link href="./My ASU - Schedule_files/responsive.css" rel="stylesheet" type="text/css" media="screen">

	<link rel="shortcut icon" href="https://webapp4.asu.edu/myasu/images/favicon.ico" type="image/x-icon">
	<link href="./My ASU - Schedule_files/jqModal.css" rel="stylesheet" type="text/css" media="all">
	<script src="./My ASU - Schedule_files/jqModal.js.download" type="text/javascript"></script>

	<script src="./My ASU - Schedule_files/myasu-common.js.download" type="text/javascript"></script>

	
	



	<link rel="stylesheet" href="./My ASU - Schedule_files/programs-box.css" type="text/css">

<link rel="stylesheet" href="./My ASU - Schedule_files/schedule.css" type="text/css" media="screen">
 	<link rel="stylesheet" href="./My ASU - Schedule_files/schedule-print.css" type="text/css" media="print">
	
	
	
	

	<meta name="robots" content="none">
	<meta name="googlebot" content="noarchive">

	




<script>
	var CLUETIP_CLOSE = '<img src="images/delete.gif" width="16" height="15"  alt="Close" title="Close"/>';
	var QTIP_LOADING = '<p style="text-align:center;"><img src="images/loader_all_yellow.gif" alt="Loading..."/></p>';
	var masq_need_to_show = false;
	var cluetipInitted = new Array();

	var maskEscHandler = function(e) {
		if (e.which == 27) {
			$('#maskDialog').jqmHide();
		}
	};
	$(document).ready(function() {
		MyASUJS.setDefaults(); //set qtip defaults
		
		$('#maskDialog').jqm({
			trigger: '#maskShow', 
			onShow: function(hash) {
				hash.w.show();
				$(document).bind('keydown', maskEscHandler);
			}, 
			onHide: function(hash) {
				hash.w.hide();
				hash.o.remove();
				$(document).unbind('keydown', maskEscHandler);
			}
		});
		if ($('#maskShow').length > 0 && typeof document.activeElement != "undefined") {
			$(document).keydown(function(e) {
				if (e.which == 86 && document.activeElement.nodeName != "INPUT" && document.activeElement.nodeName != "TEXTAREA") {
					$('#maskDialog').jqmShow();
					e.preventDefault();
				}
			});
		}
		
		if (masq_need_to_show) {
			$('#maskDialog').jqmShow();
		}
		$('.masq-info').cluetip({activation: 'click', width: 200, showTitle: true,
				sticky: true, closePosition: 'title', closeText: CLUETIP_CLOSE,
				arrows: true, local: true });
		$(document).keydown(function(e) {
			if (e.which == 27) {
				$(document).trigger('hideCluetip');
				$(document).trigger('hidepopout');
			}
		});
		// TODO: Do something similar to this for the qTip plugin
		/* add click listener */
		$(document).click(function(e) {
			var c = $("#cluetip:visible");
			if (c.length > 0) {
				if ($("div.ignoreClick", c).length > 0) {
					return;
				}
				var c_pos = c.offset();
				if (e.pageX < c_pos.left || e.pageX > c_pos.left+c.width() ||
					e.pageY < c_pos.top || e.pageY > c_pos.top+c.height() ) {
					// Click outside of cluetip, close it
					$(document).trigger('hideCluetip');
				}
			}
		});

		/* add sparky trigger */
		var k_state = 0, k_array = [38,38,40,40,37,39,37,39,66,65];
		$(document).keydown(function(e) {
			if ( e.keyCode == k_array[k_state] ) {
				k_state++;
			} else {
				k_state = 0;
				return;
			}
			if ( k_state == 10 ) {
				$(document).unbind('keydown',arguments.callee);
				$("#sparky").show();
				k_state = 0;
			}
		});
		
		// add link tracking to all links, now and in the future
		//$("a.tracking").live("click", trackLink); // Deprectated
		$(document).on("click", "a.tracking", trackLink); // Old way that needed attributes 'class' + 'rel' 
		//$("a[data-tracking]").live("click", trackLinkData); // Deprecated
		$(document).on("click", "[data-tracking]", trackLinkData); // Better way that uses dedicated attribute 'data-tracking' 

		
	});
	
	function addLinkTracking() {
		return;
	}
	
	var trackLink = function() {
		trackEvent("Link - student", this.rel);
	};

	var trackLinkData = function() {
		trackEvent("Link - student", $(this).attr('data-tracking'));
	};
	
	function trackEvent(category, description) {
		//console.debug("trackEvent: ["+category+", "+description+"]");
		if (category == null) {
			category = "undefined";
		}
		if (typeof gtag !== "undefined") {
			gtag('event', 'click', {
				'event_category': category,
				'event_label': "/analytics_links/myasu/student/" + description
			});
		}
		return true;
	}
	
	function urlValid(url) {
		var v = new RegExp();
		v.compile("^[A-Za-z]+://[A-Za-z0-9-_]+\\.[A-Za-z0-9-_%&~\?\/.=]+$");
	 	return v.test(url);
	}
	
	function updateUserPref(prefFunc, val, otherData, silentError, callback) {
		

		var token = '8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=';
		var ajaxData = { token: token, func: prefFunc, value: val };
		if (typeof otherData != "undefined" && otherData != null) {
			jQuery.extend(ajaxData, otherData);
		}
		var options = {
			cache: false,
			url: "pref",
			type: "POST",
			data: ajaxData 
		};
		if (typeof silentError == "undefined" || !silentError) {
			options['error'] = ajaxUpdateError;
		}
		if (typeof callback == "function") {
			options['success'] = callback;
		}
		$.ajax(options);
	}
	var ajaxUpdateError = function(XMLHttpRequest, textStatus, errorThrown) {
		//if (XMLHttpRequest.status == 403) {
		alert('There was an error updating settings. Please reload page.');
	};

	var AjaxLoaderErrorHandler = function(responseText, textStatus, XMLHttpRequest) {
	    if (textStatus != 'success') {
	    	this.innerHTML = '<i>Error retrieving your information.  Please reload the page to try again.</i><!--\n'+textStatus+'\n-->';
	    }
	};

	/*
	TODO Fix/replace this so that it doesn't assume specific HTML elements 'a', 'img', etc.
	Also, no magic strings built by appending suffixes and no assumption of 'experience'.
	*/
	function myASUToggle(baseID, hash, ajaxUpdate, callback) {
		if (typeof hash == "undefined" || hash == null) {
			hash = "";
		}
		if (typeof ajaxUpdate == "undefined" || ajaxUpdate == null) {
			ajaxUpdate = true;
		}
		
		var showHide;
		
		var $content = $('#'+baseID+'_content');
		var $contentTabs = $('#' + baseID + '_content_tablist');
		if ($content.css('display') != 'none') {
			showHide = 'hide';
			$content.slideUp('fast', callback);
			
			if ($contentTabs.length != 0) {
				$contentTabs.slideUp('fast');
			}
		} else {
			showHide = 'show';
			$content.slideDown('fast', callback);
			
			if ($contentTabs.length != 0) {
				$contentTabs.slideDown('fast');
			}
		}
		
		var $icon = $('#'+baseID+'_toggle img');
		if ($icon.length != 0) {
			var open = 'images/icons/minus.jpg';
			var closed = 'images/icons/plus.jpg';
			
			if ($icon.attr('src') == open) {
				$icon.attr('src', closed);
				$icon.attr('title', 'Open');
				$icon.attr('alt', 'Open');
			} else {
				$icon.attr('src', open);
				$icon.attr('title', 'Close');
				$icon.attr('alt', 'Close');
			}
		}
		
		var $faIcon = $('#'+baseID+'_toggle i');
		if ($faIcon.length != 0) {
			var open = 'fa-minus-circle';
			var closed = 'fa-plus-circle';
			
			if ($faIcon.hasClass(open)) {
				$faIcon.removeClass(open);
				$faIcon.addClass(closed);
				$faIcon.attr('title', 'Open');
			} else {
				$faIcon.removeClass(closed);
				$faIcon.addClass(open);
				$faIcon.attr('title', 'Close');
			}
		}

		var $link = $('#'+baseID+'_toggle a');
		if ($link.length != 0) {
			$link.toggleClass("treeopen");
		}

		if (ajaxUpdate) {
			updateUserPref('changeBoxDisplay', '', {key: 'student_'+baseID, display: showHide, hash: hash}, true);
			trackEvent('Toggle', 'box/'+baseID);
		}
		
		return false;
	}
	
	/*
	A different version of the box toggle function, but still, fix it all, see TODO for the other function.
	*/
	function myASUToggleNoExperience(baseID, hash, ajaxUpdate, callback) {
		if (typeof hash == "undefined" || hash == null) {
			hash = "";
		}
		if (typeof ajaxUpdate == "undefined" || ajaxUpdate == null) {
			ajaxUpdate = true;
		}
		
		var showHide;
		
		var $content = $('#'+baseID+'_content');
		if ($content.css('display') != 'none') {
			showHide = 'hide';
			$content.slideUp('fast', callback);
		} else {
			showHide = 'show';
			$content.slideDown('fast', callback);
		}
		
		var $icon = $('#'+baseID+'_toggle img');
		if ($icon.length != 0) {
			var open = 'images/icons/minus.jpg';
			var closed = 'images/icons/plus.jpg';
			
			if ($icon.attr('src') == open) {
				$icon.attr('src', closed);
				$icon.attr('title', 'Open');
				$icon.attr('alt', 'Open');
			} else {
				$icon.attr('src', open);
				$icon.attr('title', 'Close');
				$icon.attr('alt', 'Close');
			}
		}

		var $icon = $('#'+baseID+'_toggle i');
		if ($icon.length != 0) {
			$icon.toggleClass("fa-plus-circle fa-minus-circle");
		}

		var $link = $('#'+baseID+'_toggle a');
		if ($link.length != 0) {
			$link.toggleClass("treeopen");
		}

		if (ajaxUpdate) {
			updateUserPref('changeBoxDisplay', '', {key: baseID, display: showHide, hash: hash}, true);
			trackEvent('Toggle', 'box/'+baseID);
		}
		
		return false;
	}

	function updateCluetipShaddow() {
		if ($("#cluetip:visible").length > 0) {
			var height = ($("#cluetip-outer").height()+3)+"px";
			$("#cluetip > div").each(function() {
				if (this.id == "") {
					$(this).css("height", height);
				}
			});
		}
	}
	$(document).ready(function() {
		setInterval("updateCluetipShaddow()", 500);
	}); 
	
	/* set a custom signout url */
	var ASUHeader = {
		signout_url : 'Signout',
		signin_callback_url : 'https://webapp4.asu.edu/myasu/'
	}

	// Note: if called in view-as mode, will trigger error element
	function handlePinToHomepage(boxToPin, addPin, pinActionElement, saveInProgressElement, saveErrorElement, saveCompleteHideElement) {
		if (saveInProgressElement.is(":visible")) {
			//console.log('handlePinToHomepage already saving! ignoring');
			return;
		}
		pinActionElement.hide();
		saveInProgressElement.show();
		saveErrorElement.hide();

		var ajaxData = {
			token: "8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=", 
			func: "pin-to-homepage",
			boxToPin: boxToPin,
			addPin: addPin // 1 = add, 0 = remove
		};
		var options = {
			cache: false,
			url: "pref",
			type: "POST",
			data: ajaxData 
		};
		options['error'] = function() {
			//console.log('handlePinToHomepage: error');
			saveInProgressElement.hide();
			saveErrorElement.show();
		};
		options['success'] = function() {
			//console.log('handlePinToHomepage: success');
			saveInProgressElement.hide();
			saveErrorElement.hide();
			saveCompleteHideElement.hide(500);
			saveCompleteHideElement.remove();
		};
		$.ajax(options);
	}

</script>

	<!-- Begin GA4 ASU Google Analytics code -->
	
	<script async="" src="./My ASU - Schedule_files/js(2)"></script>
	<script type="text/javascript">
		window.dataLayer = window.dataLayer || [];

		function gtag(){dataLayer.push(arguments);}

		gtag('js', new Date());

		gtag('config', "G-3V83JF3V64");
	</script>

	
	
	
		<!-- Begin config GOOGLE_ANALYTICS_CODE -->
		<!-- Google Tag Manager -->
<script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-KDWN8Z');</script>
<!-- End Google Tag Manager -->

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-KDWN8Z"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<!-- Remove qs garbage from url on incoming links from canvas -->
<!-- GTM team: if this breaks your cross-domain GA tracking, have GTM do this once it is done reading the qs param and work with me to remove it from here -->
<script>
	function removeGaQsParam() {
		if (typeof window.URLSearchParams === 'function' && history.replaceState) {
			if (window.location.search.indexOf('_ga=') != -1 || window.location.search.indexOf('_gl=') != -1) {
				var qsParams = new URLSearchParams(window.location.search);
				qsParams.delete("_ga");
				qsParams.delete("_gl"); // _gl added 2024-10-01 jsh
				var qsStr = qsParams.toString();
				if (qsStr.length > 0) {
					qsStr = "?" + qsStr;
				}
				window.history.replaceState({}, document.title, `${window.location.pathname}${qsStr}`);
			}
		}
	}

	$(document).ready(function() {
		//removeGaQsParam();
		setTimeout(removeGaQsParam, 250); // give ga 250ms to gather the params before we remove them
	});
</script>

		<!-- End config GOOGLE_ANALYTICS_CODE -->
	


	<link href="./My ASU - Schedule_files/fix-asuthemes.css" rel="stylesheet" type="text/css" media="all">
	<link href="./My ASU - Schedule_files/asu-footer-5.0.css" rel="stylesheet" type="text/css" media="all">
	<script>
		var props = {
		  title: "My ASU",
		  baseUrl: "/myasu",
		  animateTitle: false
		};
	</script>
	
		
		<script>
			props.loggedIn = true;
			props.userName = document.head.getAttribute('data-ssoname');
		</script>
	
	
		<script type="text/javascript">
			props.navTree = [{"text":"Student Home","href":"/myasu/student","data-tracking":"/myasu/student","type":"icon-home"},{"text":"Finances","href":"/myasu/student/finances","data-tracking":"/myasu/student/finances"},{"text":"Resources","href":"/myasu/student/onlineresources","data-tracking":"/myasu/student/onlineresources"},{"text":"Profile","href":"/myasu/student/profile","data-tracking":"/myasu/student/profile"},{"text":"Help","href":"/myasu/student/servicecenter","data-tracking":"/myasu/student/servicecenter"}];
		</script>
	
	
		<script type="text/javascript">
			props.mobileNavTree = [{"text":"Student Home","href":"/myasu/student"},{"text":"Finances","href":"/myasu/student/finances"},{"text":"Resources","href":"/myasu/student/onlineresources"},{"text":"Profile","href":"/myasu/student/profile"},{"text":"Help","href":"/myasu/student/servicecenter"}];
		</script>
	
	<script src="./My ASU - Schedule_files/box-placement.js.download" type="text/javascript"></script>
	<script src="./My ASU - Schedule_files/quicklinks-icons.js.download" type="text/javascript"></script>
	
		<script src="./My ASU - Schedule_files/customize-background.js.download" type="text/javascript"></script>
		<script src="./My ASU - Schedule_files/quicklinks-edit.js.download" type="text/javascript"></script>
		<link href="./My ASU - Schedule_files/quicklinks-page.css" rel="stylesheet" type="text/css" media="all">
	
	
	
		<style type="text/css">
		.graduation-tab .rsvp-link-container {
	margin: 16px 0px 16px 16px;
}

.graduation-tab .rsvp-button-container {
	display: none
}
		</style>
	
<style data-emotion="css" data-s=""></style><script src="./My ASU - Schedule_files/loader.js.download"></script><script>

   //////////////////////////
   // START Set ucs cookie //
   //////////////////////////
 
function updateUCS(position,value){
    const ucs = getCookie('ucs');
    if(ucs == null) {
        setCookie('ucs','0|0|0',365);
    }
else {
if(ucs.includes(",")) {
ucs = ucs.replace(",", "|")
}
        const parts = ucs.split('|');
        parts[position] = value;
        const newUCS = parts.join('|');
        setCookie('ucs',newUCS,365);
    }
}
   function setCookie(cname, cvalue, exdays) {
      var d = new Date();
      d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
	  var expires = "expires=" + d.toUTCString();
	  var hostname = window.location.hostname;
	  var domainParts = hostname.split('.').slice(-2);
	  var baseDomain = domainParts.join('.'); 
	  var domainPart = ";domain=" + (hostname === baseDomain ? "." + baseDomain : baseDomain);
	  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/" + domainPart;
	  }

	function getCookie(name) {
	  const nameEQ = name + "=";
	  const ca = document.cookie.split(';');
	  for (let i = 0; i < ca.length; i++) {
		let c = ca[i];
		while (c.charAt(0) == ' ') c = c.substring(1, c.length);
			if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
	  }
	  return   
	 null;
	}
   
if (!CassieWidgetLoader.Widget.hasConsent()) {
      console.log('No existing consent');
      setCookie('ucs','0|0|0',365);
   }
   ///////////////////////////
   // FINISH Set ucs cookie //
   ///////////////////////////

   ////////////////////////////////////////
   // START non standard banner behavior //
   ////////////////////////////////////////
	
	addWidgetListeners();

	function addWidgetListeners() {
		document.addEventListener('CassieTemplateInitialized', () => {
			HideShowDescriptions();
			HideShowDescriptionsSN();		
		});
	}

	// Allow drilldown into strictly necessary description
	function HideShowDescriptionsSN() {
		const icon = document.getElementById('cassie_strictly_necessary_expand_icon');
		const targetElement = document.getElementById('cassie_expand_strictly_necessary');
		const otherElement = document.getElementById('cassie_strictly_necessary_description');
		const ClassUpdate = 'cassie-expand-cookies--icon--open';
		targetElement.addEventListener('click', () => {
		  if (otherElement.classList.contains(ClassUpdate)) {
			otherElement.classList.remove(ClassUpdate);
		  } else {
			otherElement.classList.add(ClassUpdate);
		  }
		});
		// Expand when page first loads
		otherElement.classList.add(ClassUpdate);
		icon.classList.add('cassie-expand-cookies--icon--open');
	}

	// Allow drilldown into Non-Strictly Necessary descriptions
	function HideShowDescriptions() {
	//Get all the category name IDs
		const expandCookieContainers = document.querySelectorAll('div[id^="cassie_expand_cookies_container_"]');
		const ClassUpdate = 'cassie-expandable-description--show';			

		// Loop through each
		expandCookieContainers.forEach(container => {
			const pId = container.id.replace('cassie_expand_cookies_container_', 'cassie_cookie_group_description_');

			// Add a click event listener to each name element
			container.addEventListener('click', () => {
				const p = document.getElementById(pId);
				if (p.classList.contains(ClassUpdate)) {
                    p.classList.remove(ClassUpdate);
                } else {
                    p.classList.add(ClassUpdate);  
                }  
            });
        });
    }

function GetTranslatedPrefCat () {
const CategoryID = 14;
let consentData = CassieWidgetLoader.Widget.widgetTemplate.Categories;
let CategoryName;
	consentData.forEach(channel => {
		if (channel.ChannelID === CategoryID) {
			CategoryName = channel.ChannelTypeParent;
			return false;
		}
	});
	return CategoryName;
}
    /////////////////////////////////////////
    // FINISH non standard banner behavior //
    /////////////////////////////////////////
</script><script>
   updateUCS(2,1);
</script></head>

<body><div id="cassie-widget" class="syrenis-cookie-widget" style="display: none;">  <meta name="cassie-viewport" content="width=device-width, initial-scale=1">   <div class="cassie-cookie-module cassie-d-none" role="region" aria-label="Cookie Preferences" data-nosnippet="" style="width: unset;">  <div class="cassie-pre-banner cassie-bottom cassie-bottom-fixed" role="dialog" aria-modal="true" style="display: none;"> <p class="cassie-pre-banner--text" id="cassie_pre_banner_text"><p>We use cookies to improve your experience and our services. This includes understanding your preferences and analyzing network usage. For more details, see our <a href="https://www.asu.edu/about/privacy" target="_blank" title="privacy statements">privacy statements</a>.</p><button class="close-button" aria-label="Close cookie consent banner"><i class="fas fa-times"></i></button></p> <div class="cassie-pre-banner--button--container"> <button class="cassie-pre-banner--button cassie-accept-all" id="cassie_accept_all_pre_banner">Accept all cookies</button> <button class="cassie-pre-banner--button cassie-reject-all" id="cassie_reject_all_pre_banner">Accept only required cookies</button> <button class="cassie-pre-banner--button cassie-view-all"> <span class="cassie-view-all--button--icon"> <svg xmlns="http://www.w3.org/2000/svg" enable-background="new 0 0 24 24" height="16" viewBox="0 0 24 24" width="16"> <g> <path d="M0,0h24v24H0V0z" fill="none"></path> <path class="cassie-view-all--button--icon" d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"></path> </g> </svg> </span> <span id="cassie_view_all_pre_banner">Manage cookies</span> </button> </div> </div> <div class="cassie-pre-banner cassie-gpc-pre-banner cassie-d-none-important cassie-gpc-bottom cassie-bottom-fixed" style="display:none" id="cassie_gpc_pre_banner" role="dialog" aria-modal="true"> <div class="cassie-gpc-pre-banner--text-container"> <h3 class="cassie-gpc-pre-banner-heading--text" id="gpc_banner_title">GPC Detected</h3> <p class="cassie-pre-banner--text" id="cassie_gpc_pre_banner_text">We have detected a Global Privacy Control (GPC) signal, please confirm if you wish to accept all cookies including or excluding those flagged as GPC.</p> <button class="close-button" aria-label="Close cookie consent banner"><i class="fas fa-times"></i></button></div> <div class="cassie-pre-banner--button--container"> <button class="cassie-pre-banner--button cassie-gpc-accept-all" id="cassie_gpc_accept_all_pre_banner" aria-label="Accept All">Accept All</button> <button class="cassie-pre-banner--button cassie-gpc-accept-all-excluding" id="cassie_gpc_accept_all_excluding_pre_banner" aria-label="Accept All Excluding GPC">Accept All Excluding GPC</button> <button class="cassie-pre-banner--button cassie-gpc-back" id="cassie_gpc_back_pre_banner" aria-label="Back">Back</button> </div> </div> <div class="cassie-cookie-modal cassie-cookie-modal--center cassie-cookie-modal--center-fixed" role="dialog" aria-modal="true" style="display: none;"> <div class="cassie-cookie-modal--header"> <div class="cassie-cookie-modal--top-header"> <h2 class="cassie-cookie-modal--header-image" id="cassie_header_image"></h2> <span class="cassie-close-modal--container"> <button class="cassie-close-modal-button" id="cassie_close_modal_button">×</button> </span> </div> <p class="cassie-cookie-modal--header--content" id="cassie_header_content"><h3 class="cassie-cookie-group--heading">Your privacy settings</h3><p class="cookieHeadersmall">On this website, we use cookies which allow our advertising partners to display ads tailored to your interests based on how you browse our site.</p></p> <div class="cassie-cookie-modal--accept-all" id="cassie_accept_all_cookies"> <span class="cassie-cookie-modal--accept-all--text" id="cassie_accept_all_cookies_text" for="cassie_accept_all_toggle_switch">Accept All</span> <div class="cassie-accept-all--toggle-switch cassie-toggle-switch" id="cassie_accept_all_toggle_switch" name="cassie_accept_all_toggle_switch" role="switch" aria-checked="true" tabindex="0" aria-label="Accept All"> <span aria-hidden="true" class="cassie-accept-all--toggle-switch--status cassie-toggle-switch--status" id="cassie_accept_all_toggle_switch_status">On</span> <span aria-hidden="false" class="cassie-accept-all--toggle-switch--slider cassie-toggle-switch--slider cassie-toggle-switch--slider--active" id="cassie_accept_all_toggle_slider"></span> </div> </div> </div> <div class="cassie-cookie-modal--main"> <div class="cassie-cookie-modal--cookies--container" id="cassie_cookie_modal_cookies_container"> <div class="cassie-cookie-modal--tabs--container" aria-hidden="true" style="display: none;"> <button class="cassie-tab--button cassie-active--button" id="cassie_consent_button" name="user consent tab button" aria-label="User Consent">User Consent</button> <button class="cassie-tab--button" id="cassie_legitimate_interests_button" name="legitimate interest tab button" aria-label="Legitimate Interests">Legitimate Interests</button> </div> <div class="cassie-cookie-modal--tab-group" id="cassie_consent_tab_cookies"> <div class="cassie-cookie-modal--strictly-necessary cassie-cookie-modal--group" id="cassie_strictly_necessary"> <div class="cassie-cookie-modal--group-head-container"> <div class="cassie-strictly-necessary--expand cassie-expand-cookies--container" tabindex="0" role="button" id="cassie_expand_strictly_necessary"> <span> <svg id="cassie_strictly_necessary_expand_icon" class="cassie-expand-cookies--icon cassie-expand-cookies--icon--open" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black" width="18px" height="18px"> <path d="M0 0h24v24H0z" fill="none"></path> <path class="cassie-expand-cookies--icon" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg> </span> <h3 class="cassie-strictly-necessary--heading cassie-cookie-group--heading" id="cassie_strictly_necessary_heading">Strictly Necessary Cookies</h3> </div> <p class="cassie-strictly-necessary--description cassie-cookie-group--description cassie-expand-cookies--icon--open" id="cassie_strictly_necessary_description"><p>These cookies are essential for you to browse the website and use its features, such as accessing secure areas of the site. Cookies that allow web shops to hold your items in your cart while you are shopping online are an example of strictly necessary cookies.</p><p>Examples:</p><ul><li>Maintain user state during a website visit.<ul><li>login information or session view of cart.</li></ul></li><li>Enable navigation through website pages.</li><li>Verify user identity and provide access to protected content.</li><li>Protect website data and user information from unauthorized access.</li><li>Detect and prevent fraudulent activity.</li><li>Distribute traffic across multiple servers for optimal performance.</li></ul></p> </div> <div class="cassie-strictly-necessary--children--container cassie-cookie-children--container" id="cassie_strictly_necessary_children_container" hidden="" aria-hidden="true">  <div class="cassie-strictly-necessary--child-cookie cassie-cookie--child" id="cassie_strictly_necessary_child_cookie_strictly_necessary cookie"><h4 class="cassie-strictly-necessary--child--heading cassie-cookie--child--heading" id="cassie_strictly_necessary_heading_strictly_necessary cookie">Strictly necessary cookie</h4><p class="cassie-strictly-necessary--child--description cassie-cookie--child--description" id="cassie_strictly_necessary_description_strictly_necessary cookie"><p><span style="color: rgb(65, 65, 65); font-family: Poppins, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.1px; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">These cookies are necessary for website functions such as setting your privacy preferences, logging in or completing forms. By using this site, you agree we may place these cookies on your device.</span></p></p></div></div> </div> <div class="cassie-cookie-modal--group cassie-cookie-modal--select--group" id="cassie_cookie_modal_group_14"> <div class="cassie-cookie-modal--group-head-container" id="cassie_cookie_group_header_14"> <div class="cassie-expand-cookies--container" tabindex="0" role="button" id="cassie_expand_cookies_container_14"> <span> <svg id="cassie_expand_cookies_icon_14" class="cassie-expand-cookies--icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="black" width="18px" height="18px"> <path d="M0 0h24v24H0z" fill="none"></path> <path class="cassie-expand-cookies--icon" d="M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"></path></svg> </span> <h3 class="cassie-cookie-group--heading" id="cassie_cookie_group_heading_14" for="cassie_cookie_group_toggle_switch_14">Marketing cookies</h3> </div><div class="cassie-toggle-switch cassie-cookie-group--toggle-switch" id="cassie_cookie_group_toggle_switch_14" name="cassie_cookie_group_toggle_switch_14" role="switch" aria-checked="true" tabindex="0" aria-label="Marketing cookies"> <span aria-hidden="true" class="cassie-toggle-switch--status cassie-cookie-group--toggle-switch--status" id="cassie_cookie_group_toggle_switch_status_14">On</span> <span aria-hidden="false" class="cassie-toggle-switch--slider cassie-cookie-group--toggle-switch--slider cassie-toggle-switch--slider--active" id="cassie_cookie_group_toggle_switch_slider_14"></span> </div> <p class="cassie-cookie-group--description" id="cassie_cookie_group_description_14"><p>These cookies track your online activity to help advertisers deliver more relevant advertising or to limit how many times you see an ad. These cookies can share that information with other organizations or advertisers.</p><p>Examples:</p><ul><li>Display ads for products or services a user has previously viewed on a website.</li><li>Track referrals and attribute sales to specific affiliates.</li><li>Track user interactions with social media platforms and websites.</li><li>Track user actions after clicking on an ad, such as making a purchase.</li><li>Personalize website content.</li></ul></p> </div> <div class="cassie-cookie-children--container" id="cassie_children_cookies_container_14" hidden="" aria-hidden="true">  <div class="cassie-cookie--child" id="cassie_cookie_child_14_27"> <h4 class="cassie-cookie--child--heading" id="cassie_cookie_child_heading_14_27" for="cassie_child_cookie_toggle_switch_27">Ad user data</h4><div class="cassie-toggle-switch cassie-child-cookie--toggle-switch" id="cassie_child_cookie_toggle_switch_27" name="cassie_child_cookie_toggle_switch_27" role="switch" aria-checked="true" tabindex="0" aria-label="Ad user data"> <span aria-hidden="true" class="cassie-toggle-switch--status cassie-child-cookie--toggle-switch--status" id="cassie_child_cookie_toggle_switch_status_27">On</span> <span aria-hidden="false" class="cassie-toggle-switch--slider cassie-child-cookie--toggle-switch--slider cassie-toggle-switch--slider--active" id="cassie_child_cookie_toggle_switch_slider_27" data-channel-id="20" data-statement-id="27"></span> </div> <p class="cassie-cookie--child--description" id="cassie_cookie_child_description_14_27"><p><span style="color: rgb(65, 65, 65); font-family: Poppins, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.1px; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">Ad User Data</span></p></p> </div><div class="cassie-cookie--child" id="cassie_cookie_child_14_26"> <h4 class="cassie-cookie--child--heading" id="cassie_cookie_child_heading_14_26" for="cassie_child_cookie_toggle_switch_26">Ad personalization</h4><div class="cassie-toggle-switch cassie-child-cookie--toggle-switch" id="cassie_child_cookie_toggle_switch_26" name="cassie_child_cookie_toggle_switch_26" role="switch" aria-checked="true" tabindex="0" aria-label="Ad personalization"> <span aria-hidden="true" class="cassie-toggle-switch--status cassie-child-cookie--toggle-switch--status" id="cassie_child_cookie_toggle_switch_status_26">On</span> <span aria-hidden="false" class="cassie-toggle-switch--slider cassie-child-cookie--toggle-switch--slider cassie-toggle-switch--slider--active" id="cassie_child_cookie_toggle_switch_slider_26" data-channel-id="19" data-statement-id="26"></span> </div> <p class="cassie-cookie--child--description" id="cassie_cookie_child_description_14_26"><p><span style="color: rgb(65, 65, 65); font-family: Poppins, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.1px; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">Ad Personalization</span></p></p> </div><div class="cassie-cookie--child" id="cassie_cookie_child_14_24"> <h4 class="cassie-cookie--child--heading" id="cassie_cookie_child_heading_14_24" for="cassie_child_cookie_toggle_switch_24">Marketing cookie</h4><div class="cassie-toggle-switch cassie-child-cookie--toggle-switch" id="cassie_child_cookie_toggle_switch_24" name="cassie_child_cookie_toggle_switch_24" role="switch" aria-checked="true" tabindex="0" aria-label="Marketing cookie"> <span aria-hidden="true" class="cassie-toggle-switch--status cassie-child-cookie--toggle-switch--status" id="cassie_child_cookie_toggle_switch_status_24">On</span> <span aria-hidden="false" class="cassie-toggle-switch--slider cassie-child-cookie--toggle-switch--slider cassie-toggle-switch--slider--active" id="cassie_child_cookie_toggle_switch_slider_24" data-channel-id="17" data-statement-id="24"></span> </div> <p class="cassie-cookie--child--description" id="cassie_cookie_child_description_14_24"><p><span style="color: rgb(65, 65, 65); font-family: Poppins, sans-serif; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: 0.1px; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; display: inline !important; float: none;">We use targeting/advertising cookies to create profiles and personalize content. We may use third party products, such as Google Analytics, to market ASU’s services to you on third party websites. For example, if you visit ASU websites, you may then see advertisements for ASU on other websites.</span></p></p> </div></div> </div></div> <div class="cassie-cookie-modal--tab-group" id="cassie_legitimate_interests_tab_cookies" aria-hidden="true" style="display: none;"></div> </div> </div> <div class="cassie-cookie-modal--footer" id="cassie_cookie_footer"> <p class="cassie-cookie-modal--footer-content" id="cassie_cookie_footer_content"><p>For more details, see our <a href="https://www.asu.edu/about/privacy" target="_blank" title="privacy statements">privacy statements</a>.</p></p> <div class="cassie-cookie-modal--footer-extra"> <div class="cassie-cookie-modal--save-preferences--container"> <button class="cassie-cookie-modal--save-preferences" id="cassie_save_preferences" name="save preferences button" aria-label="Submit preferences">Submit preferences</button> </div> <div class="cassie-cookie-modal--footer-image--container"> <a href="https://cassie.syrenis.com/" target="_blank"> <img class="cassie-cookie-modal--footer-image" alt="cookie-confidence-logo" src="./My ASU - Schedule_files/cassie_logo_white.svg"> </a> </div> </div> </div> </div> </div>  <div class="cassie-manage-cookies--container" style="display:none" data-nosnippet=""> <button class="cassie-manage-cookies--button" id="cassie_manage_cookies_button">Open Cookie Settings</button> <div class="cassie-arrow-down--hover"></div> </div>  </div>
<div class="asu_container">
	<div id="graduation-congrats-confetti-wrapper" style="display: none;"></div>
	<div id="admit-confetti-wrapper" style="display: none;"></div>
	
		<input type="hidden" id="global-user-form-token" value="8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=">
	
	

	<header>
		<!-- Header  will  be initialized in this container. If a different  ID needs to be targeted,  pass as 2nd argument to 'initHeader()' function -->
		<div id="headerContainer"><header class="css-10dnl2k" data-onclick-identifier="top-of-header"><div data-onclick-identifier="no-action"></div><div class="universal-nav"><div><div class="nav-grid css-9tk5jk"><a class="nav-link sr-only sr-only-focusable" href="https://webapp4.asu.edu/myasu/#skip-to-content">Skip to main content</a><a class="nav-link sr-only sr-only-focusable" href="http://asu.edu/accessibility/feedback?a11yref=unity-design-system">Report an accessibility problem</a><a href="https://www.asu.edu/">ASU Home</a><a href="https://my.asu.edu/">My ASU</a><a href="https://www.asu.edu/about/colleges-and-schools">Colleges and Schools</a><div class="login-status"><span class="name">Marca</span><a class="signout" href="https://webapp4.asu.edu/myasu/Signout">Sign Out</a></div></div><div class="asu-search-form" data-onclick-identifier="universal-search-bar"><form action="https://search.asu.edu/search" method="get" role="search" class=""><button type="submit" aria-label="Submit ASU Search"></button><input name="q" type="search" aria-labelledby="asu-search-label" required=""><label class="universal-search" id="asu-search-label">Search asu.edu</label></form></div></div></div><div class="navbar-component"><div><a href="https://www.asu.edu/" class="navbar-logo"><img class="vert" src="./My ASU - Schedule_files/arizona-state-university-logo-vertical.webp" alt="Arizona State University"><img class="horiz" src="./My ASU - Schedule_files/arizona-state-university-logo.webp" alt="Arizona State University"></a><button data-onclick-identifier="mobile-dropdown" aria-label="main menu" class="css-1m7y5q4 navbar-toggler"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="bars" class="svg-inline--fa fa-bars fa-w-14 " role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" href="#asu-header-nav"><path fill="currentColor" d="M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"></path></svg></button><div class="navbar-container"><a class="title subunit-name" href="https://webapp4.asu.edu/myasu">My ASU</a><nav id="asu-header-nav" class="header-nav css-mtr3ky"><ul aria-label="ASU" class="navlist"><li class="navicon"><a class="" href="https://webapp4.asu.edu/myasu/student" data-tracking="/myasu/student"><svg aria-hidden="true" focusable="false" data-prefix="fas" data-icon="home" class="svg-inline--fa fa-home fa-w-18 icon-nav-item" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" alt="Student Home icon"><path fill="currentColor" d="M280.37 148.26L96 300.11V464a16 16 0 0 0 16 16l112.06-.29a16 16 0 0 0 15.92-16V368a16 16 0 0 1 16-16h64a16 16 0 0 1 16 16v95.64a16 16 0 0 0 16 16.05L464 480a16 16 0 0 0 16-16V300L295.67 148.26a12.19 12.19 0 0 0-15.3 0zM571.6 251.47L488 182.56V44.05a12 12 0 0 0-12-12h-56a12 12 0 0 0-12 12v72.61L318.47 43a48 48 0 0 0-61 0L4.34 251.47a12 12 0 0 0-1.6 16.9l25.5 31A12 12 0 0 0 45.15 301l235.22-193.74a12.19 12.19 0 0 1 15.3 0L530.9 301a12 12 0 0 0 16.9-1.6l25.5-31a12 12 0 0 0-1.7-16.93z"></path></svg><span class="mobile-only">Student Home</span></a></li><li class="navlink"><a href="https://webapp4.asu.edu/myasu/student/finances" class="nav-item" data-tracking="/myasu/student/finances">Finances</a></li><li class="navlink"><a href="https://webapp4.asu.edu/myasu/student/onlineresources" class="nav-item" data-tracking="/myasu/student/onlineresources">Resources</a></li><li class="navlink"><a href="https://webapp4.asu.edu/myasu/student/profile" class="nav-item" data-tracking="/myasu/student/profile">Profile</a></li><li class="navlink"><a href="https://webapp4.asu.edu/myasu/student/servicecenter" class="nav-item" data-tracking="/myasu/student/servicecenter">Help</a></li></ul></nav></div></div></div></header></div>
		
		<!-- include bundled scripts from Preact packages -->
		<script src="./My ASU - Schedule_files/vendor.js.download"></script>
		<script src="./My ASU - Schedule_files/components-library.js.download"></script>
		
		<script>
			componentsLibrary.initHeader(props);

			$(document).ready(function() {
				$("#asu-header-nav .navlist").find("a").each(function(){
				    var href = $(this).attr("href");
				    $(this).attr('data-tracking', href);
				});
			});
		</script>
	</header>

	
		
		<div class="hidden" id="user-behavior-tracking-emplid">1239458054</div>
	

	<div id="myasu-header-top-container">
		<div id="myasu-header-top">
			





	<div id="utility-area">

		<div id="utility-area-a">
			<div id="user-info-container">
				
				
					<div id="blog-notification-container">
						<a id="blog-notification-icon" href="https://webapp4.asu.edu/myasu/#" role="button" style="display: none;" data-tracking="top/new-product"><i id="blog-notification" class="fas fa-gift" aria-hidden="true"></i></a>
						<div id="blog-notification-badge" style="display: none;"></div>
						<div id="blog-notification-popup-contents" class="user-info-popup" style="display: none">
							
						</div>
						<input id="blog-last-viewed-timestamp" type="hidden" value="">
						<script type="x-tmpl-mustache" id="blog_notifications_tpl">
							{{#items}}
								<div class="blog-notification-container">
									<div class="blog-notification-title">{{title}}</div>
									<div class="blog-notification-description">{{description}}</div>
									<div class="blog-notification-link"><a href="{{url}}" target="_blank" data-tracking="top/new-product/learn-more">Learn More</a></div>
								</div>
							{{/items}}
						</script>
					</div>
					<span class="user-name">
						<a class="nounderline" rel="#user-info-popup-contents" href="https://webapp4.asu.edu/myasu/#user-info" id="user-info-popup" data-tracking="student/profilename">Marca Cortez<i id="user-name-angle-down" class="user-name-chevron-toggle fas fa-chevron-down"></i><i id="user-name-angle-up" class="user-name-chevron-toggle fas fa-chevron-up" style="display: none;"></i></a>
						
					</span>
					<div id="user-info-popup-contents" class="user-info-popup" style="display: none;">
						<div id="user-info-profile-container">
							
								<div id="user-info-profile-image-container">
									<div class="profile-image">
										<img src="./My ASU - Schedule_files/f.txt" alt="User&#39;s profile image if one is set. If not set, this is a default image.">
									</div>
								</div>
							
							<div id="user-info-profile-information-container">
								<div id="user-info-profile-information-user-name">
									Marca Cortez

								</div>
								<div class="user-info-profile-information">
									<div class="user-info-profile-information-label">Primary Name:</div>
									<div class="user-info-profile-information-value">Marca   Cortez  </div>
								</div>

								<div class="user-info-profile-information">
									<div class="user-info-profile-information-label">ASU ID (emplid):</div>
									<div class="user-info-profile-information-value">12847593</div>
								</div>
								<div class="user-info-profile-information">
									<div class="user-info-profile-information-label">ASURITE User ID:</div>
									<div class="user-info-profile-information-value">arodr703</div>
								</div>
								
								
									<div id="user-info-profile-information-links">
										<a href="https://webapp4.asu.edu/myasu/student/profile" class="primary-btn unity-x-small-btn" aria-label="View Profile Link" data-tracking="student/profilename/view-profile">View Profile</a>
									</div>
								
							</div>
							<div class="clearfix"></div>
						</div>
						<div id="user-info-links-container">
							
								<div class="user-info-link-container">
									<a class="user-info-link" href="https://tech.asu.edu/security-policies/myasu-updates" data-tracking="student/profilename/new-features">
										<span class="user-info-link-icon"><i class="fas fa-lg fa-gift"></i></span>
										<span class="user-info-link-text">New Features</span>
									</a>
								</div>
							
							<div class="user-info-link-container">
								
									
										<a class="user-info-link" href="https://webapp4.asu.edu/myasu/student/servicecenter" data-tracking="student/profilename/help">
											<span class="user-info-link-icon"><i class="fas fa-lg fa-question-circle"></i></span>
											<span class="user-info-link-text">Help</span>
										</a>
									
									
									
									
								
								<div class="clearfix"></div>
							</div>
							
								<div class="user-info-link-container">
									<a class="user-info-link" href="https://webapp4.asu.edu/myasu/feedback" data-tracking="student/profilename/feedback">
										<span class="user-info-link-icon"><i class="fas fa-lg fa-comment"></i></span>
										<span class="user-info-link-text">Feedback</span>
									</a>
								</div>
							
						</div>
					</div>
				
				
			</div>
		</div>

		

		

		<div id="utility-area-b">
			<div id="top-switch">

				









				

				

			</div>
		</div>

	</div>

	
	

	<script>
		$(document).ready(function(){			
			$('#site-preferences').cluetip({activation: 'click', attribute:'rel', width: 260, showTitle: true,
				sticky: true, closePosition: 'title', closeText: CLUETIP_CLOSE, arrows: true,
				local: true, onShow: sitePreferencesOnShow
				});
				
			$(document).on("click", "#user-info-popup", function(e) {
				e.preventDefault();
				
				$("#user-info-popup-contents").toggle();
				$("#user-name-angle-up").toggle();
				$("#user-name-angle-down").toggle();
			});
			
			$(document).on("click", "#blog-notification-icon", function(e) {
				e.preventDefault();

				$("#blog-notification-popup-contents").toggle();
				
				if ($("#blog-notification-popup-contents").is(":visible")) {
					setBlogLastViewedTimestamp();
				}
			});
			
			$(document).mouseup(function(e) {
				var container1 = $("#user-info-popup-contents");
				var container2 = $("#user-info-popup");

			    // if the target of the click isn't the container nor a descendant of the container
			    if (!container1.is(e.target) && container1.has(e.target).length === 0
			    		&& !container2.is(e.target) && container2.has(e.target).length === 0) {
			        container1.hide();
					$("#user-name-angle-up").hide();
					$("#user-name-angle-down").show();
			    }
			});
			
			$(document).mouseup(function(e) {
				var container1 = $("#blog-notification-popup-contents");
				var container2 = $("#blog-notification-icon");

			    // if the target of the click isn't the container nor a descendant of the container
			    if (!container1.is(e.target) && container1.has(e.target).length === 0
			    		&& !container2.is(e.target) && container2.has(e.target).length === 0) {
			        container1.hide();
			    }
			});
			
			$.ajax({
		        url: 'myASUBlogFeed',
		        type: 'GET',
		        dataType: "xml"
		    }).done(function(xml) {
				var items = [];
				
				var blogLastViewedTimestamp = $("#blog-last-viewed-timestamp").val();
				var numberOfNewBlogPosts = 0;

				$.each($("item", xml), function(index, element) {
					var dontShowPost = false;

					var dontShowDate = new Date();
					dontShowDate.setDate(dontShowDate.getDate() - 90);

					var pubDate = $(element).find("pubDate").text(); // current pubDate format example: September 15, 2021
					var pubDateTimestamp = new Date(pubDate).getTime();

					if (isNaN(pubDateTimestamp)) {
						pubDate = pubDate.replace(/\s/, 'T');
						pubDateTimestamp = new Date(pubDate).getTime();
					}

					if (dontShowDate > pubDateTimestamp) {
						dontShowPost = true;
					}
					
					if (!dontShowPost) {
						if (blogLastViewedTimestamp != null && blogLastViewedTimestamp != "") {
							var twentyFourHoursAgo = new Date();
							twentyFourHoursAgo.setDate(twentyFourHoursAgo.getDate() - 1);
							
							if (pubDateTimestamp < blogLastViewedTimestamp && pubDateTimestamp < twentyFourHoursAgo.getTime()) {
								dontShowPost = true;
							}
						}
					}
					
					if (!dontShowPost) {
						var title = $(element).find("title").text();
						var description = $(element).find("description").text();
						var url = $(element).find("link").text();
						
						if ((blogLastViewedTimestamp == null || blogLastViewedTimestamp == "") || (blogLastViewedTimestamp != null && blogLastViewedTimestamp != "" && pubDateTimestamp > blogLastViewedTimestamp)) {
							numberOfNewBlogPosts++;
						}
						
						var item = {
							title: title,
							description: description,
							url: url,
							pubDate: pubDate
						};
						
						items.push(item);	
					}
		        });
				
				if (items.length > 0) {
					if (numberOfNewBlogPosts > 0) {
						$("#blog-notification-icon").prop("aria-label", "My ASU Blog Notifications (" + numberOfNewBlogPosts + " New Posts)");
						$("#blog-notification-icon").show();
						$("#blog-notification-badge").html(numberOfNewBlogPosts);
						$("#blog-notification-badge").show();
						$("#blog-notification").prop("title", "My ASU Blog Notifications (" + numberOfNewBlogPosts + " New)");
					} else {
						$("#blog-notification-icon").prop("aria-label", "My ASU Blog Notifications");
						$("#blog-notification-icon").show();
						$("#blog-notification").prop("title", "My ASU Blog Notifications");						
					}
					
					var blogNotificationsTemplate = $("#blog_notifications_tpl").html();
					Mustache.parse(blogNotificationsTemplate);
					$("#blog-notification-popup-contents").html(Mustache.render(blogNotificationsTemplate, {items: items}));
				}
		    }).fail(function(xhr, status, error) {
		    	console.log("There was a problem getting the My ASU blog feed: " + status);
		    });
		});

		var sitePreferencesState = {
			initialView: '',
			staffViewOverride: false,
			pgpDefaultStudent: '',
		};

		function sitePreferencesOnShow(ct, cluetipContext) {
			cluetipContext.find("#header-initial-view-selection").val(sitePreferencesState.initialView);
			cluetipContext.find('#header-staff-view-override').prop('checked', sitePreferencesState.staffViewOverride);
			if ($("#header-pgp-default-student-selection option[value='"+sitePreferencesState.pgpDefaultStudent+"']").length > 0) {
				cluetipContext.find('#header-pgp-default-student-selection').val(sitePreferencesState.pgpDefaultStudent);
			}
		}
		
		function setBlogLastViewedTimestamp() {
			var ajaxData = {
				token: "8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=", 
				func: "set-blog-last-viewed-timestamp"
			};
			var options = {
				cache: false,
				url: "pref",
				type: "POST",
				data: ajaxData 
			};
			options['error'] = function() {
				console.log('An error occurred while trying to update your latest notifications. Please try again or check back later.');
			};
			options['success'] = function() {
				$("#blog-notification").prop("title", "MY ASU Blog Notifications");
				$("#blog-notification-badge").hide();
			};
			$.ajax(options);
		}

	</script>

	



		</div>
	</div>

	








	
	<div class="hidden" id="spotlightModalWindow">
		<div id="jqmTitle">
			<button class="jqmClose">
        		Close X
    		</button>
     	</div>
    	<div id="spotlightModalWindowContent"></div>
	</div>

	<aside>
		<div id="user_alert">
			
		</div>
	</aside>

	

	<main>
		
			
			
		
		<div class="asu_content" id="myasu-main-content" name="myasu-main-content" style="">
			

			
			
			
			

			

			

			<!-- START CONTENT -->
			<div id="myasu-main-container" class="myasu-container">
			
				



<div id="myasu-main-top">

	<div id="myasu-main-top-button-container">
		<div id="quicklinks-container" class="quicklinks-container noprint">
			<span id="visible-quicklink-gmail" class="quicklink visible-quicklink">
				<a href="https://email.asu.edu/" class="quicklink-anchor" data-id="gmail" data-tracking="quicklinks/gmail/inbox">Gmail<span class="gmail-unread-badge-holder quicklink-icon-right" title="0 Unread Messages"><span title="Unread Messages" class="gmail-unread-badge hidden"><span aria-hidden="true" class="unread-count">0</span></span></span></a>
			</span>
			<span id="visible-quicklink-canvas" class="quicklink visible-quicklink">
				<a href="https://canvas.asu.edu/" class="quicklink-anchor" data-id="canvas" data-tracking="quicklinks/canvas">Canvas</a>
			</span>
			<span id="visible-quicklink-google-drive" class="quicklink visible-quicklink">
				<a href="https://drive.google.com/a/asu.edu/" class="quicklink-anchor" data-id="google-drive" data-tracking="quicklinks/mydocs">Google Drive</a>
			</span>
			<span id="visible-quicklink-library" class="quicklink visible-quicklink">
				<a href="https://lib.asu.edu/" class="quicklink-anchor" data-id="library" data-tracking="quicklinks/library">ASU Library</a>
			</span>
			<span id="visible-quicklink-myapps" class="quicklink visible-quicklink">
				<a href="https://www.asu.edu/myapps/" class="quicklink-anchor" data-id="myapps" data-tracking="quicklinks/myapps">My Apps</a>
			</span>
			<span id="visible-quicklink-calendar" class="quicklink visible-quicklink">
				<a href="https://calendar.google.com/a/asu.edu" class="quicklink-anchor" data-id="calendar" data-tracking="quicklinks/mycalendar">Calendar</a>
			</span>
			<span id="visible-quicklink-clubs-orgs" class="quicklink visible-quicklink">
				<a href="https://arizonau.campusgroups.com/cas/arizonau" class="quicklink-anchor" data-id="clubs-orgs" data-tracking="quicklinks/clubsorgs">Clubs and Orgs</a>
			</span>
			<span id="visible-quicklink-onedrive" class="quicklink visible-quicklink">
				<a href="https://office365.asu.edu/onedrive" class="quicklink-anchor" data-id="onedrive" data-tracking="quicklinks/onedrive">OneDrive</a>
			</span>
			
			
		
		<div id="has-more-quicklinks" style="">
		<div class="quicklink quicklinks-show-container">
			<button class="jslink quicklinks-show quicklink-anchor" data-tracking="quicklinks/view-more">View More<span class="fa fa-chevron-down fa-link-decoration gray quicklink-icon-right"></span></button>
			<div class="quicklinks-modal-list-container">
				<div class="quicklinks-modal-list hidden" id="quicklinks-modal-list" style="display: none;">
						<div id="view-more-quicklink-dropbox" class="quicklink view-more-quicklink">
							<a href="https://www.asu.edu/dropbox" class="quicklink-anchor" data-id="dropbox" data-tracking="quicklinks/dropbox">ASU Dropbox</a>		
						</div>
						<div id="view-more-quicklink-zoom" class="quicklink view-more-quicklink">
							<a href="https://asu.zoom.us/" class="quicklink-anchor" data-id="zoom" data-tracking="quicklinks/zoom">Zoom</a>		
						</div>
				</div>
			</div>
		</div>
	</div>
	</div>
		
			<div id="customize-myasu-button-container">
				<button id="customize-myasu-button" class="secondary-btn unity-small-btn" data-tracking="customize/customize-button"><i class="fas fa-pencil-alt icon-sm space-inline-right-xs" aria-hidden="true" title="Pencil"></i><span class="sr-only">Pencil</span>Customize</button>
			</div>
		
	</div>
	<script type="x-tmpl-mustache" id="quicklinks_container_tpl">
		{{#quicklinksVisibleList}}
			<span id="visible-quicklink-{{id}}" class="quicklink visible-quicklink">
				<a href="{{hrefUrl}}" class="quicklink-anchor" data-id="{{id}}" {{#isOpenQuickLinkInNewWin}}target="_blank" rel="noopener"{{/isOpenQuickLinkInNewWin}} {{#hasTrackingId}}data-tracking="{{trackingId}}"{{/hasTrackingId}} {{#isGmailLink}}class="reset-gmail"{{/isGmailLink}}>{{#imageSrc}}<img src="{{imageSrc}}" width="16" height="16" class="quicklink-anchor-img">{{/imageSrc}}{{text}}{{#isGmailLink}}<span class="gmail-unread-badge-holder quicklink-icon-right" title="{{unreadCount}} Unread Messages"><span title="Unread Messages" class="gmail-unread-badge {{^showUnreadCount}}hidden{{/showUnreadCount}}"><span aria-hidden="true" class="unread-count">{{unreadCount}}</span></span></span>{{/isGmailLink}}</a>
			</span>
		{{/quicklinksVisibleList}}
		
		<div id="has-more-quicklinks" style="display: none;"></div>
	</script>
	<script type="x-tmpl-mustache" id="quicklinks_container_more_tpl">
		<div class="quicklink quicklinks-show-container">
			<button class="jslink quicklinks-show quicklink-anchor" data-tracking="quicklinks/view-more">View More<span class="fa fa-chevron-down fa-link-decoration gray quicklink-icon-right"></span></button>
			<div class="quicklinks-modal-list-container">
				<div class="quicklinks-modal-list hidden" id="quicklinks-modal-list">
					{{#moreQuickLinksVisibleList}}
						<div id="view-more-quicklink-{{id}}" class="quicklink view-more-quicklink">
							<a href="{{hrefUrl}}" class="quicklink-anchor" data-id="{{id}}" {{#isOpenQuickLinkInNewWin}}target="_blank" rel="noopener"{{/isOpenQuickLinkInNewWin}} {{#hasTrackingId}}data-tracking="{{trackingId}}"{{/hasTrackingId}} {{#isGmailLink}}class="reset-gmail"{{/isGmailLink}}>{{#imageSrc}}<img src="{{imageSrc}}" width="16" height="16" class="quicklink-anchor-img">{{/imageSrc}}{{text}}{{#isGmailLink}}<span class="gmail-unread-badge-holder quicklink-icon-right" title="{{unreadCount}} Unread Messages"><span title="Unread Messages" class="gmail-unread-badge {{^showUnreadCount}}hidden{{/showUnreadCount}}"><span aria-hidden="true" class="unread-count">{{unreadCount}}</span></span></span>{{/isGmailLink}}</a>		
						</div>
					{{/moreQuickLinksVisibleList}}
				</div>
			</div>
		</div>
	</script>
</div>

<section class="box shortcuts-box">
	<div class="box-title-bar">
		<h2 class="box-title">My Shortcuts</h2>
		<a href="https://webapp4.asu.edu/myasu/#n" class="toggle" id="shortcuts_box_toggle" onclick="return myASUToggleNoExperience(&#39;shortcuts_box&#39;);">
			<i class="box-title-icon fas fa-plus-circle" title="Open"></i>
		</a>
	</div>
	<div class="box-content" style="display:none" id="shortcuts_box_content">
		<div class="box-padding">
			<div id="boxed-quicklinks-container">
					<div class="quicklink">
						<a href="https://email.asu.edu/" data-id="gmail" data-tracking="quicklinks/gmail/inbox" class="reset-gmail">Gmail<span class="gmail-unread-badge-holder quicklink-icon-right" title="0 Unread Messages"><span title="Unread Messages" class="gmail-unread-badge hidden"><span aria-hidden="true" class="unread-count">0</span></span></span></a>
					</div>
					<div class="quicklink">
						<a href="https://canvas.asu.edu/" data-id="canvas" data-tracking="quicklinks/canvas">Canvas</a>
					</div>
					<div class="quicklink">
						<a href="https://drive.google.com/a/asu.edu/" data-id="google-drive" data-tracking="quicklinks/mydocs">Google Drive</a>
					</div>
					<div class="quicklink">
						<a href="https://lib.asu.edu/" data-id="library" data-tracking="quicklinks/library">ASU Library</a>
					</div>
					<div class="quicklink">
						<a href="https://www.asu.edu/myapps/" data-id="myapps" data-tracking="quicklinks/myapps">My Apps</a>
					</div>

					<div id="boxed-quicklinks-show-container" class="quicklink quicklinks-show-container">
						<button class="jslink quicklinks-more" data-tracking="quicklinks/view-more">View More<span class="fa fa-chevron-down fa-link-decoration gray quicklink-icon-right"></span></button>
						<div class="quicklinks-modal-list-container">
							<div class="quicklinks-box-more-list hidden" id="quicklinks-box-more-list">
									<div class="quicklink">
										<a href="https://calendar.google.com/a/asu.edu" class="quicklink-anchor" data-id="calendar" data-tracking="quicklinks/mycalendar">Calendar</a>		
									</div>
									<div class="quicklink">
										<a href="https://arizonau.campusgroups.com/cas/arizonau" class="quicklink-anchor" data-id="clubs-orgs" data-tracking="quicklinks/clubsorgs">Clubs and Orgs</a>		
									</div>
									<div class="quicklink">
										<a href="https://office365.asu.edu/onedrive" class="quicklink-anchor" data-id="onedrive" data-tracking="quicklinks/onedrive">OneDrive</a>		
									</div>
									<div class="quicklink">
										<a href="https://www.asu.edu/dropbox" class="quicklink-anchor" data-id="dropbox" data-tracking="quicklinks/dropbox">ASU Dropbox</a>		
									</div>
									<div class="quicklink">
										<a href="https://asu.zoom.us/" class="quicklink-anchor" data-id="zoom" data-tracking="quicklinks/zoom">Zoom</a>		
									</div>
							</div>
							<div class="quicklink">
								<button class="jslink quicklinks-less">View Less 
									<span class="fa fa-chevron-up fa-link-decoration gray"></span>
								</button>
							</div>
						</div>
					</div>

				<div class="quicklink">
					<a id="customize-myasu-button" href="https://webapp4.asu.edu/myasu/#" data-tracking="customize/customize-button">Customize</a>
				</div>
			</div>
			
			<script type="x-tmpl-mustache" id="boxed_quicklinks_container_tpl">
				{{#bookmarksVisible}}
					<div class="quicklink">
						<a href="{{hrefUrl}}" data-id="{{id}}" {{#isOpenQuickLinkInNewWin}}target="_blank" rel="noopener"{{/isOpenQuickLinkInNewWin}} {{#hasTrackingId}}data-tracking="{{trackingId}}"{{/hasTrackingId}} {{#isGmailLink}}class="reset-gmail"{{/isGmailLink}}>{{text}}{{#isGmailLink}}<span class="gmail-unread-badge-holder quicklink-icon-right" title="{{unreadCount}} Unread Messages"><span title="Unread Messages" class="gmail-unread-badge {{^showUnreadCount}}hidden{{/showUnreadCount}}"><span aria-hidden="true" class="unread-count">{{unreadCount}}</span></span></span>{{/isGmailLink}}</a>
					</div>
				{{/bookmarksVisible}}

				{{#hasMore}}
					<div id="boxed-quicklinks-show-container" class="quicklink quicklinks-show-container">
						<button class="jslink quicklinks-more" data-tracking="quicklinks/view-more">View More<span class="fa fa-chevron-down fa-link-decoration gray quicklink-icon-right"></span></button>
						<div class="quicklinks-modal-list-container">
							<div class="quicklinks-box-more-list hidden" id="quicklinks-box-more-list">
								{{#moreBookmarksVisible}}
									<div class="quicklink">
										<a href="{{hrefUrl}}" class="quicklink-anchor" data-id="{{id}}" {{#isOpenQuickLinkInNewWin}}target="_blank" rel="noopener"{{/isOpenQuickLinkInNewWin}} {{#hasTrackingId}}data-tracking="{{trackingId}}"{{/hasTrackingId}} {{#isGmailLink}}class="reset-gmail"{{/isGmailLink}}>{{text}}{{#isGmailLink}}<span class="gmail-unread-badge-holder quicklink-icon-right" title="{{unreadCount}} Unread Messages"><span title="Unread Messages" class="gmail-unread-badge {{^showUnreadCount}}hidden{{/showUnreadCount}}"><span aria-hidden="true" class="unread-count">{{unreadCount}}</span></span></span>{{/isGmailLink}}</a>		
									</div>
								{{/moreBookmarksVisible}}
							</div>
							<div class="quicklink">
								<button class="jslink quicklinks-less">View Less 
									<span class="fa fa-chevron-up fa-link-decoration gray"></span>
								</button>
							</div>
						</div>
					</div>
				{{/hasMore}}

				<div class="quicklink">
					<a id="customize-myasu-button" href="#" data-tracking="customize/customize-button">Customize</a>
				</div>
			</script>
		</div>
	</div>
</section>

<input type="hidden" id="show-bookmark-icons-value" name="showBookmarkIconsValue" value="false">
<input type="hidden" id="bookmarks-open-new-win-value" name="booksmarksOpenNewWinValue" value="false">


	


<input type="hidden" id="is-masqueraded" value="false">
<input type="hidden" id="customize-background-user-form-token" value="8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=">
<input type="hidden" id="quicklinks-edit-user-form-token" name="quickLinksEditUserFormToken" value="8u[Oh14mB]Prrzvd2uLGTu4Ys3JEllzUqkBv6g2EhMrMlXAn]0r7MlW]Yd8]vxK8ujrD79[w3cQ=">
<div id="customize-background-modal" class="myasu-modal">
	
	
	<div id="customize-modal-content" class="myasu-modal-content-container">
		<!-- <span id="customize-background-modal-close" class="myasu-modal-close"><i class="fas fa-times"></i></span> -->
		<div id="customize-myasu-container-header" class="myasu-modal-header">
			<div id="customize-myasu-container-header-text" class="myasu-modal-header-text">Customize My ASU</div>
			<div class="box-tabs" role="tablist" aria-label="Customize My ASU" id="customize-myasu-tab-pane_tablist">
				<button role="tab" aria-selected="false" aria-controls="bacgkround_tab" id="background_link" data-tab-scope="customize-myasu-tab-pane" class="box-tab-selected">Backgrounds</button>
				<button role="tab" aria-selected="false" aria-controls="shortcuts_tab" id="shortcuts_link" data-tab-scope="customize-myasu-tab-pane" tabindex="-1">Shortcuts</button>
			</div>
		</div>
		<div class="tab-pane" id="customize-myasu-tab-pane">
			<div role="tabpanel" aria-labelledby="bacgkround_tab" id="bacgkround_tab" class="tab-page">
				<div id="customize-myasu-background-container-content" class="myasu-modal-content">
					<div id="customize-myasu-container-content-backgrounds-container">
						
							
								
									
										<!-- <div class="customize-myasu-background-header">Featured</div> -->
										<div id="customize-myasu-backgrounds-featured-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_1" data-background-value="images/custom_backgrounds/f2023_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_2" data-background-value="images/custom_backgrounds/f2023_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_3" data-background-value="images/custom_backgrounds/f2023_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_3"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_4" data-background-value="images/custom_backgrounds/f2023_4.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_4"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_5" data-background-value="images/custom_backgrounds/f2023_5.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_5"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="F2023_6" data-background-value="images/custom_backgrounds/f2023_6.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-f2023_6"></div>
												</div>
											
										</div>
									
								
								
								
								
								
							
						
							
								
								
									
										<!-- <div class="customize-myasu-background-header">Solid Colors</div> -->
										<div id="customize-myasu-backgrounds-solid-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="SOLID" data-value="WHITE" data-background-value="#FFFFFF" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-white"></div>
												</div>
											
												
												
												<div class="background-container " data-type="SOLID" data-value="MAROON" data-background-value="#8C1D40" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-maroon"></div>
												</div>
											
												
												
												<div class="background-container " data-type="SOLID" data-value="GOLD" data-background-value="#F8C126" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-gold"></div>
												</div>
											
												
												
												<div class="background-container " data-type="SOLID" data-value="BLACK" data-background-value="#000000" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-black"></div>
												</div>
											
										</div>
									
								
								
								
								
							
						
							
								
								
								
									
										<!-- <div class="customize-myasu-background-header">Photography</div> -->
										<div id="customize-myasu-backgrounds-photography-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="DOWNTOWN" data-background-value="images/custom_backgrounds/downtown.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-downtown"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="HAYDEN" data-background-value="images/custom_backgrounds/hayden.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-hayden"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="POLY" data-background-value="images/custom_backgrounds/poly.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-poly"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WEST" data-background-value="images/custom_backgrounds/west.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-west"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="DOWNTOWN_NIGHT" data-background-value="images/custom_backgrounds/downtown-night.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-downtown_night"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="GAMMAGE" data-background-value="images/custom_backgrounds/gammage.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-gammage"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="POLY_2" data-background-value="images/custom_backgrounds/poly_2.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-poly_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WEST_2" data-background-value="images/custom_backgrounds/west_2.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-west_2"></div>
												</div>
											
										</div>
									
								
								
								
							
						
							
								
								
								
								
									
										<!-- <div class="customize-myasu-background-header">Abstract Patterns</div> -->
										<div id="customize-myasu-backgrounds-abstract-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="ABSTRACT_PATTERNS" data-value="FUNKY" data-background-value="images/custom_backgrounds/funky.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-funky"></div>
												</div>
											
												
												
												<div class="background-container " data-type="ABSTRACT_PATTERNS" data-value="GOLD_CLOUDS" data-background-value="images/custom_backgrounds/gold_clouds.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-gold_clouds"></div>
												</div>
											
												
												
												<div class="background-container " data-type="ABSTRACT_PATTERNS" data-value="MAROON_CLOUDS" data-background-value="images/custom_backgrounds/maroon_clouds.jpg" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-maroon_clouds"></div>
												</div>
											
												
												
												<div class="background-container " data-type="ABSTRACT_PATTERNS" data-value="MAROON_GRADIENT" data-background-value="images/custom_backgrounds/maroon_gradient.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-maroon_gradient"></div>
												</div>
											
												
												
												<div class="background-container " data-type="ABSTRACT_PATTERNS" data-value="DARK_TOPO" data-background-value="images/custom_backgrounds/dark-topo.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-dark_topo"></div>
												</div>
											
										</div>
									
								
								
							
						
							
								
								
								
								
								
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="NAHM_1" data-background-value="images/custom_backgrounds/nahm_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-nahm_1"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="LTX_1" data-background-value="images/custom_backgrounds/ltx_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-ltx_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="LTX_2" data-background-value="images/custom_backgrounds/ltx_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-ltx_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="LTX_3" data-background-value="images/custom_backgrounds/ltx_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-ltx_3"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="LTX_4" data-background-value="images/custom_backgrounds/ltx_4.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-ltx_4"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="AAPIHM_1" data-background-value="images/custom_backgrounds/aapihm_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-aapihm_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="AAPIHM_2" data-background-value="images/custom_backgrounds/aapihm_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-aapihm_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="AAPIHM_3" data-background-value="images/custom_backgrounds/aapihm_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-aapihm_3"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="AAPIHM_4" data-background-value="images/custom_backgrounds/aapihm_4.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-aapihm_4"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="EARTHM_1" data-background-value="images/custom_backgrounds/earthm_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-earthm_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="EARTHM_2" data-background-value="images/custom_backgrounds/earthm_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-earthm_2"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="ICW_1" data-background-value="images/custom_backgrounds/icw_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-icw_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="ICW_2" data-background-value="images/custom_backgrounds/icw_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-icw_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="ICW_3" data-background-value="images/custom_backgrounds/icw_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-icw_3"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="PRIDEW_1" data-background-value="images/custom_backgrounds/pridew_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-pridew_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="PRIDEW_2" data-background-value="images/custom_backgrounds/pridew_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-pridew_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="PRIDEW_3" data-background-value="images/custom_backgrounds/pridew_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-pridew_3"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="PRIDEW_4" data-background-value="images/custom_backgrounds/pridew_4.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-pridew_4"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WHM_1" data-background-value="images/custom_backgrounds/whm_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-whm_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WHM_2" data-background-value="images/custom_backgrounds/whm_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-whm_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WHM_3" data-background-value="images/custom_backgrounds/whm_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-whm_3"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="WHM_4" data-background-value="images/custom_backgrounds/whm_4.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-whm_4"></div>
												</div>
											
										</div>
									
										<!-- <div class="customize-myasu-background-header">Limited Times</div> -->
										<div id="customize-myasu-backgrounds-limited-container" class="customize-myasu-backgrounds-type-container">
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="BHM_1" data-background-value="images/custom_backgrounds/bhm_1.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-bhm_1"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="BHM_2" data-background-value="images/custom_backgrounds/bhm_2.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-bhm_2"></div>
												</div>
											
												
												
												<div class="background-container " data-type="PHOTOGRAPHY" data-value="BHM_3" data-background-value="images/custom_backgrounds/bhm_3.png" data-tracking="customize/customize-background/option-click">
													<div class="background-solid background-solid-bhm_3"></div>
												</div>
											
										</div>
									
								
							
						
					</div>
				</div>
				<div id="customize-myasu-background-container-footer" class="myasu-modal-footer">
					<button id="customize-myasu-background-cancel" class="customize-myasu-button" data-tracking="customize/customize-background/cancel">Cancel</button>
					<button id="customize-myasu-background-save" class="customize-myasu-button customize-myasu-button-maroon" data-tracking="customize/customize-background/done">Done</button>
				</div>
			</div>
			<div role="tabpanel" aria-labelledby="shortcuts_tab" id="shortcuts_tab" class="tab-page" style="display: none;">
				<div id="customize-myasu-shortcuts-container-content" class="myasu-modal-content">
					<div id="quicklinks-modal">
								<div>Drag and drop, change the visibility, or edit your shortcuts.</div>
								<div>
									<p><input id="bookmarks-show-icons-checkbox" type="checkbox"> Icons make me happy</p>
								</div>
								<div>
									<p><input id="bookmarks-open-new-win-checkbox" type="checkbox"> Shortcuts opening in new tabs makes my day easier</p>
								</div>
								<div>
									<div id="bookmarks-add-bookmark-container" class="para"><button class="myasu-bttn" id="bookmarks-add-bookmark" onclick="showAddBookmarkPanel();"><span class="fa fa-plus"></span> Add Shortcut</button></div>
										
									<div id="add_bookmark_panel" class="add_bookmark_panel" style="display: none;">
										<div>
											<h3>Add A New Shortcut</h3>
											<form onsubmit="addMyLink(this); return false;" method="post" class="bookmarks-add-form" id="bookmarks-add-form">
												<div class="para">
													<label>Name</label> 
													<input type="text" class="name full" maxlength="36" id="new-bookmark-name">
												</div>
												<div>
													<label>URL</label>
													<input type="text" class="url full" id="new-bookmark-url" value="https://">
												</div>
												<div class="bookmark-form-request-status">
													<div id="add-bookmark-form-error" class="bookmark-form-error"></div>
													<div class="bookmark-form-saving"><img src="./My ASU - Schedule_files/icon-loading-spinner.gif" width="12"> Saving...</div>
												</div>
												<div class="bookmark-form-controls para">
													<div>
														<input id="close-add-bookmark-panel" class="myasu-bttn-gray" type="reset" value="Cancel">
														<input type="submit" value="Save" class="myasu-bttn">
													</div>
												</div>
											</form>
										</div>
									</div>
									
									<div id="edit_bookmark_panel" class="edit_bookmark_panel" style="display: none;">
										<div>
											<h3>Edit Shortcut</h3>
											<form onsubmit="updateMyLink(this); return false;" method="post" class="bookmarks-update-form" id="bookmarks-update-form">
												<input type="hidden" id="bookmark-id-to-edit" name="bookmarkid">
												<div class="para">
													<label>Name</label> <input type="text" class="name full" maxlength="36" id="edit-bookmark-name">
												</div>
												<div>
													<label>URL</label>
													<input type="text" class="url full" id="edit-bookmark-url">
												</div>
												<div class="bookmark-form-request-status">
													<div id="edit-bookmark-form-error" class="bookmark-form-error"></div>
													<div class="bookmark-form-saving"><img src="./My ASU - Schedule_files/icon-loading-spinner.gif" width="12"> Saving...</div>
												</div>
												<div class="bookmark-form-controls" style="margin: 1em 0;">
													<div>
														<button id="edit-remove-bookmark" type="button" onclick="removeMyLink(); return false;" class="myasu-bttn-gray">Remove</button>
													</div>
													<div>
														<input id="close-edit-bookmark-panel" class="myasu-bttn-gray" type="reset" value="Cancel">
														<input type="submit" value="Save" class="myasu-bttn">
													</div>
												</div>
											</form>
										</div>
									</div>
									<p class="quicklinks-table-update-error"></p>
									<div id="modal-quicklinks-table" class="table quicklinks-table">
										<div class="table-header-group">
											<div class="table-row">
												<div class="table-cell center">Drag &amp; Drop</div>
												<div class="table-cell">Shortcut</div>
												<div class="table-cell center">Show/Hide</div>
												<div class="table-cell center">Edit</div>
											</div>
										</div>
										<div id="quicklinks-entries" class="table-row-group sortable quicklinks-entries">
											<div id="bookmark-gmail" class="table-row" data-id="bookmark-gmail">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://email.asu.edu/" target="_blank" rel="noopener">Gmail</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;gmail&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-canvas" class="table-row" data-id="bookmark-canvas">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://canvas.asu.edu/" target="_blank" rel="noopener">Canvas</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;canvas&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-google-drive" class="table-row" data-id="bookmark-google-drive">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://drive.google.com/a/asu.edu/" target="_blank" rel="noopener">Google Drive</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;google-drive&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-library" class="table-row" data-id="bookmark-library">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://lib.asu.edu/" target="_blank" rel="noopener">ASU Library</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;library&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-myapps" class="table-row" data-id="bookmark-myapps">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://www.asu.edu/myapps/" target="_blank" rel="noopener">My Apps</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;myapps&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-calendar" class="table-row" data-id="bookmark-calendar">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://calendar.google.com/a/asu.edu" target="_blank" rel="noopener">Calendar</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;calendar&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-clubs-orgs" class="table-row" data-id="bookmark-clubs-orgs">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://arizonau.campusgroups.com/cas/arizonau" target="_blank" rel="noopener">Clubs and Orgs</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;clubs-orgs&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-onedrive" class="table-row" data-id="bookmark-onedrive">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://office365.asu.edu/onedrive" target="_blank" rel="noopener">OneDrive</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;onedrive&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-dropbox" class="table-row" data-id="bookmark-dropbox">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://www.asu.edu/dropbox" target="_blank" rel="noopener">ASU Dropbox</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;dropbox&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
											<div id="bookmark-zoom" class="table-row" data-id="bookmark-zoom">
												<div class="table-cell center"><button class="handle nowrap" aria-label="Move Shortcut"><span class="fa fa-grip-lines gray"></span></button></div>
												<div class="table-cell">
													<a href="https://asu.zoom.us/" target="_blank" rel="noopener">Zoom</a>
												</div>
												<div class="table-cell center">
													<button onclick="toggleLinkVisibility(&#39;zoom&#39;);" class="jslink nounderline" title="Hide This Shortcut">
														<span class="fa fa-eye gray" aria-hidden="true"></span>
													</button>
												</div>
												<div class="table-cell center">
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
				</div>
				<div id="customize-myasu-shortcuts-container-footer" class="myasu-modal-footer">
					<button id="customize-myasu-shortcuts-cancel" class="customize-myasu-button" data-tracking="customize/customize-shortcuts/close">Cancel</button>
					<button id="customize-myasu-shortcuts-save" class="customize-myasu-button customize-myasu-button-maroon" data-tracking="customize/customize-background/done">Done</button>
				</div>
				<span id="customize-myasu-shortcuts-tab-guard" style="user-select: none;" tabindex="0"></span>
			</div>
		</div>
	</div>
	
	<input type="hidden" id="quicklinks-edit-user-form-token" name="quickLinksEditUserFormToken" value="8u[Oh14mB]Prrzvd2uLGTjoJfSQXWVhL02dees14zH9nYPGIjn9pf5OjMMF8cH1RVW4OTzcW1ys=">
	<script src="./My ASU - Schedule_files/sortable.min.js.download"></script>
	<script src="./My ASU - Schedule_files/jquery-sortable.js.download"></script>
</div>




<script>
var resizeQuicklinkData = null;
var currentViewMoreIds = [];
$(document).ready(function() {
	// load quicklinks
	if ($("#quicklinks-container").length > 0) {
		loadQuicklinks();
	}
	
	$(window).resize(function() {
		if ($("#quicklinks-container").length > 0 && $("#quicklinks-container").is(":visible")) {
			updateQuicklinks(resizeQuicklinkData, false);
		}
	});

	$(document).on('click', '#myasu-main-top .quicklinks-show', function(event) {
		event.preventDefault();

		$('#myasu-main-top .quicklinks-modal-list').toggle();
		$("#myasu-main-top .quicklinks-show [class*='fa-chevron-']").toggleClass("fa-chevron-up fa-chevron-down");
	});

	$(document).on('click', '.shortcuts-box .quicklinks-more', function(event) {
		event.preventDefault();
		
		$("#boxed-quicklinks-show-container").css("height", "auto");

		$('.shortcuts-box .quicklinks-box-more-list').show();
		$('.shortcuts-box .quicklinks-less').show();
		$('.shortcuts-box .quicklinks-more').hide();
	});

	$(document).on('click', '.shortcuts-box .quicklinks-less', function(event) {
		event.preventDefault();
		
		$("#boxed-quicklinks-show-container").css("height", "45px");

		$('.shortcuts-box .quicklinks-box-more-list').hide();
		$('.shortcuts-box .quicklinks-more').show();
		$('.shortcuts-box .quicklinks-less').hide();
	});
	
	$(document).mouseup(function(e) {
		var container1 = $("#myasu-main-top .quicklinks-modal-list");
		var container2 = $("#myasu-main-top .quicklinks-show");

	    // if the target of the click isn't the container nor a descendant of the container
	    if (!container1.is(e.target) && container1.has(e.target).length === 0
	    		&& !container2.is(e.target) && container2.has(e.target).length === 0) {
	        container1.hide();
	        $('#myasu-main-top .quicklinks-modal-list').hide();
        	$("#myasu-main-top .quicklinks-show [class*='fa-chevron-']").removeClass("fa-chevron-up").addClass("fa-chevron-down");
	    }
	});
	
	$(document).on('keydown', function (e) {
		var container = $("#myasu-main-top .quicklinks-modal-list");
		
		if (container.is(":visible") && e.keyCode === 27) {
	  		$("#myasu-main-top .quicklinks-modal-list").hide();
	        	$("#myasu-main-top .quicklinks-show [class*='fa-chevron-']").removeClass("fa-chevron-up").addClass("fa-chevron-down");
		}
	});
	
	$(document).on('click', '.reset-gmail', function() {
		var options = {
			url: "gmail/clearcache",
			type: "POST",
			error: function(response) {
				console.log(response); 
			}
		};
		$.ajax(options);
	});
});

var quicklinksContainerView = {
	"hasTrackingId": function() {
		return this.trackingId != null && this.trackingId != ""
	},
	"isGmailLink": function() {
		return this.id == "gmail"
	},
	"isOpenQuickLinkInNewWin": function() {
		return (this.openNewWin && (this.curated || (!this.curated && !this.isViewAs)) && this.openQuickLinkInNewWin);
	}
};

function loadQuicklinks() {
	$.ajax({
		url: "customizeMyAsu",
		success: function(data) {
			resizeQuicklinkData = data;

			if (data.quicklinksVisibleList != null && data.quicklinksVisibleList != undefined) {
				for (var i = 0; i < data.quicklinksVisibleList.length; i++) {
					data.quicklinksVisibleList[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
					data.quicklinksVisibleList[i].isViewAs = data.isViewAs;
				}
			}

			if (data.bookmarksVisible != null && data.bookmarksVisible != undefined) {
				for (var i = 0; i < data.bookmarksVisible.length; i++) {
					data.bookmarksVisible[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
					data.bookmarksVisible[i].isViewAs = data.isViewAs;
				}
			}

			if (data.moreBookmarksVisible != null && data.moreBookmarksVisible != undefined) {	
				for (var i = 0; i < data.moreBookmarksVisible.length; i++) {
					data.moreBookmarksVisible[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
					data.moreBookmarksVisible[i].isViewAs = data.isViewAs;
				}
			}

			renderQuicklinkContainers(data);

			loadGmailCount();
			startGmailInterval();

			if ($("#quicklinks-container").is(":visible")) {
				determineCutOff(data);
			}
			
			if ($("#show-bookmark-icons-value").val() == "true") {
				populateQuicklinksFavicons($("#quicklinks-container"));
				populateQuicklinksFavicons($("#boxed-quicklinks-container"));
			}
		}
	});
}

function renderQuicklinkContainers(data) {
	var quicklinksContainerTemplate = $("#quicklinks_container_tpl").html();
	Mustache.parse(quicklinksContainerTemplate);
	$("#quicklinks-container").html(Mustache.render(quicklinksContainerTemplate, $.extend(data, quicklinksContainerView)));

	var boxedQuicklinksContainerTemplate = $("#boxed_quicklinks_container_tpl").html();
	Mustache.parse(boxedQuicklinksContainerTemplate);
	$("#boxed-quicklinks-container").html(Mustache.render(boxedQuicklinksContainerTemplate, $.extend(data, quicklinksContainerView)));
}

function updateQuicklinks(data, populateIcons) {
	if (data.quicklinksVisibleList != null && data.quicklinksVisibleList != undefined) {
		for (var i = 0; i < data.quicklinksVisibleList.length; i++) {
			data.quicklinksVisibleList[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
			data.quicklinksVisibleList[i].isViewAs = data.isViewAs;
		}
	}

	if (data.bookmarksVisible != null && data.bookmarksVisible != undefined) {
		for (var i = 0; i < data.bookmarksVisible.length; i++) {
			data.bookmarksVisible[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
			data.bookmarksVisible[i].isViewAs = data.isViewAs;
		}
	}

	if (data.moreBookmarksVisible != null && data.moreBookmarksVisible != undefined) {	
		for (var i = 0; i < data.moreBookmarksVisible.length; i++) {
			data.moreBookmarksVisible[i].openQuickLinkInNewWin = data.openQuickLinkInNewWin;
			data.moreBookmarksVisible[i].isViewAs = data.isViewAs;
		}
	}

	renderQuicklinkContainers(data);
	
	updateGmailBadge(gmailCount);
	
	determineCutOff(data);
	
	if ($("#show-bookmark-icons-value").val() == "true") {
		if (populateIcons) {
			populateQuicklinksFavicons($("#quicklinks-container"));
		}

		populateQuicklinksFavicons($("#boxed-quicklinks-container"));
	}
}

function determineCutOff(data) {
	var element = document.getElementById("quicklinks-container");
	
	data.moreQuickLinksVisibleList = [];

	var invisibleItems = [];
	var extraFaviconLeft = 0;

	for (var i = 0; i < element.childElementCount; i++) {
		var extraFaviconOffset = 0;
		
		if ($(element.children[i]).find("img").length == 0 && $("#show-bookmark-icons-value").val() == "true") {
			extraFaviconOffset = 20;
		}
		
		if ((element.children[i].offsetLeft + extraFaviconLeft) + (element.children[i].offsetWidth + extraFaviconOffset) > element.offsetLeft + element.offsetWidth) {
			invisibleItems.push(element.children[i]);
			data.moreQuickLinksVisibleList.push(data.quicklinksVisibleList[i]);
		}

		if ($(element.children[i]).find("img").length == 0 && $("#show-bookmark-icons-value").val() == "true") {
			extraFaviconLeft += 20;
		}
	}
	
	if (invisibleItems.length > 1) {
		var quicklinksContainerTemplate = $("#quicklinks_container_more_tpl").html();
		Mustache.parse(quicklinksContainerTemplate);
		$("#has-more-quicklinks").html(Mustache.render(quicklinksContainerTemplate, $.extend(data, quicklinksContainerView)));

		$("#has-more-quicklinks").show();

		for (var i = 0; i < invisibleItems.length; i++) {
			currentViewMoreIds.push($(invisibleItems[i]).find("a").data("id"));
			invisibleItems[i].remove();
		}
	}
}

var gmailInterval = null;
var gmailCount = null;
function startGmailInterval() {
	if (true) {
		gmailInterval = setInterval("loadGmailCount()", 1045000);
	}
};
function stopGmailInterval() {
	if (gmailInterval != null) {
		clearInterval(gmailInterval);
	}
};
function loadGmailCount() {
	var options = {
		url: "gmail/unreadcount",
		type: "GET",
		dataType: 'json',
		success: function(response) {
			updateGmailBadge(response.unreadCount);
		},
		statusCode: {
			403: function() {
				stopGmailInterval();
			}
		},
		error: function(response) {
			console.log(response);
		}
	};
	$.ajax(options);
};

function updateGmailBadge(count) {
	gmailCount = count;

	if (count > 0) {
		$('.gmail-unread-badge').prop('title', count+' Unread Messages');
		$('.gmail-unread-badge .unread-count').text(count);
		$('.gmail-unread-badge').show();

		resizeQuicklinkData.showUnreadCount = true;
	} else {
		$('.gmail-unread-badge').prop('title', count+' Unread Messages');
		$('.gmail-unread-badge').hide();

		resizeQuicklinkData.showUnreadCount = false;
	}

	resizeQuicklinkData.unreadCount = count;

	renderQuicklinkContainers(resizeQuicklinkData);

	determineCutOff(resizeQuicklinkData);
};

</script>




<script type="text/javascript">
	if (top.location != location) {
		top.location.replace(document.location.href);
	}
</script>

<div class="schedule-page">

<div id="single-column-boxes-container"></div>
<div class="column-container">
	<div class="column-container-row">
		<div class="content-column">
			<div class="schedule">
				<div class="printname">Marca&nbsp;Cortez</div>
				<section class="box">
					<div id="schedule_content_title_bar" class="box-title-bar box-title-has-tabs">
						<h2 class="box-title">My Schedule<span class="table-title-term doPrint">&nbsp;for&nbsp;Fall 2025</span></h2>
					</div>
					<div class="box-content">
						<div class="box-tabs" role="tablist" aria-label="Schedule" id="schedule_content_tablist" style=""><button role="tab" aria-selected="false" aria-controls="schedule_2251_tab" id="schedule-link-2251" data-tab-scope="schedule_content" onclick="return changeScheduleSection(&#39;student/classes?term=2251&#39;);" tabindex="-1">Spring '25</button><button role="tab" aria-selected="false" aria-controls="schedule_2254_tab" id="schedule-link-2254" data-tab-scope="schedule_content" onclick="return changeScheduleSection(&#39;student/classes?term=2254&#39;);" tabindex="-1">Summer '25</button><button role="tab" aria-selected="true" aria-controls="schedule_2257_tab" id="schedule-link-2257" data-tab-scope="schedule_content" onclick="return changeScheduleSection(&#39;student/classes?term=2257&#39;);" class="box-tab-selected">Fall '25</button><button id="schedule_dropdown" data-template="schedule_dropdown_content" class="dropdown" title="All Terms" aria-expanded="false">All Semesters<i class="schedule_dropdown_chevron fas fa-chevron-down" aria-hidden="true"></i></button></div>
						<div role="tabpanel" aria-labelledby="" id="schedule_content">
							
							

							
							
							
							
							
							
							
							
							
							
								
									
									
								
								
								
								
								
								
								
								
								
							
							
							
							<input type="hidden" id="print-class-schedule-grid-style" value="minmax(0, 1fr) minmax(0, 1.1fr) minmax(0, 3.2fr) minmax(0, .7fr) minmax(0, 2fr) minmax(0, .7fr) minmax(0, 2.2fr) minmax(0, 2fr) minmax(0, 2fr)">
							<input type="hidden" id="responsive-class-schedule-grid-style" value="minmax(0, 1fr) minmax(0, 1.1fr) minmax(0, 2.6fr) minmax(0, .7fr) minmax(0, 1.7fr) minmax(0, .7fr) minmax(0, 1.4fr) minmax(0, 1.3fr) minmax(0, 1fr)">
							<input type="hidden" id="class-schedule-grid-style" value="minmax(0, 1fr) minmax(0, 1.1fr) minmax(0, 3.2fr) minmax(0, .7fr) minmax(0, 2fr) minmax(0, .7fr) minmax(0, 2.2fr) minmax(0, 2fr) minmax(0, 2fr)">
							<div id="class-schedule" class="class-schedule card-collapse">
								<div id="class-schedule-header" class="class-schedule-header" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);">
									<div class="class-schedule-class-header-title-container">
										<div id="class" class="nowrap class-schedule-header-cell">Class #</div>
										<div id="course" class="class-schedule-header-cell">Course</div>
										<div id="title" class="class-schedule-header-cell">Title</div>
									</div>
	 								
									<div id="units">Units</div>
									
										
											<div id="instructor" class="class-schedule-header-cell">Instructor(s)</div>
										
										<div id="days" class="class-schedule-header-cell">Days</div>
										<div id="meets" class="class-schedule-header-cell">Times</div>
										<div id="dates" class="class-schedule-header-cell">Date(s)</div>
										<div id="location" class="class-schedule-header-cell">Location</div>
									
									
								</div>
								
								
									
									
									
									
										
											<div id="class-content-container-74588" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);" class="class-content-container" data-class-number="74588">
												<div class="class-schedule-class-header-container" data-class-number="74588">
													<div class="class-schedule-class-header-title-container">
														<div data-label="Class #" class="nowrap class-number-column class-content-cell">
															
																
																	<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D74588%26searchType%3Dall%26term%3D2257%23detailsOpen%3D74588">74588</a>
																
																
															
														</div>
														<div data-label="Course" class="nowrap course-column class-content-cell">
															
																
																	<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fcourses%2Fcourselist%3FcatalogNbr%3D101%26subject%3DENG%26term%3D2257">ENG&nbsp;101</a>
																
																
															
														</div>
														<div data-label="Title" class="title-column class-content-cell">
															<span class="class-title">
																First-Year Composition
																
																	
																	
																
																
															</span>
															
															
																
															
														</div>
														
														
														
														
															
														

														<div class="class-schedule-class-header-title-location-container">
															
																
																
																	
																		
																		
																		<div class="class-schedule-class-header-title-location-info">
																			LL 101
																		</div>
																	
																
															
														</div>
													</div>
													<div class="class-schedule-class-header-chevron-container"><i class="fas fa-chevron-up class-schedule-class-header-chevron" style="display: none;"></i><i class="fas fa-chevron-down class-schedule-class-header-chevron"></i></div>
												</div>
												<div id="class-schedule-class-info-container-74588" class="class-schedule-class-info-container" style="display: contents;">
													<div data-label="Class #" class="nowrap inner-class-number-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Class #</div>
														
															
																<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D74588%26searchType%3Dall%26term%3D2257%23detailsOpen%3D74588">74588</a>
															
															
														
													</div>
													
													<div data-label="Units" class="numerical units-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Units</div>
														<label title="Standard Grading">3.0</label>
													</div>
	
													
													
														
														
														
														
															<div class="class-schedule-class-container-date-content class-schedule-class-date-info-container-cell class-schedule-class-info-container-content" data-class-number="74588" data-date-id="0" data-expand-class-info="false">
																
																	
																	
																		<div class="class-schedule-class-info-container-content-label">Dates</div>
																			<div class="class-content-inner-cell">08/26/25&nbsp;- 12/12/25</div>
																	
																
															</div>
														
														<div id="class-schedule-class-container-date-content-container-74588-0" class="class-schedule-class-container-date-content-open-container">
															
															
																
																	<div data-label="Instructors" class="instructors-column class-content-cell class-schedule-column-start-five class-schedule-class-date-info-container-cell">
																		<div class="class-schedule-class-info-container-content-label">Instructor</div>
																		<div class="class-content-inner-cell">
																			
																					
																					<a href="https://webapp4.asu.edu/myasu/?action=directoryredir&amp;id=A5jjmHv_eE-r77iUuMhGwvQ" title="Anderson" data-tracking="schedule/directory-instructor">Anderson</a>
																					
																				
																			
																		</div>
																	</div>
																	
																
																
																
																<div data-label="Days" class="days-column class-content-cell  class-schedule-class-date-info-container-cell class-schedule-no-show">		
																	
																		
																		<div class="class-schedule-class-info-container-content-label">Day</div>
																			<div class="class-content-inner-cell"><span>MW</span></div>
																		
																		
																	
																	
																</div>
																<div data-label="Times" class="times-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
																	<div class="class-schedule-class-info-container-content-label">Time</div>
																	
																		<div class="class-content-inner-cell">9:00 AM - 10:15 AM</div>
																	
																</div>
																<div data-label="Date(s)" class="dates-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
																	<div class="class-schedule-class-info-container-content-label">Dates</div>
																	
																		<div class="class-content-inner-cell">10/15/25&nbsp;- 12/5/25</div>
																	
																</div>
																<div id="location-74588" data-label="Location" class="location-column class-content-cell class-schedule-class-date-info-container-cell">
																	<div class="class-schedule-class-info-container-content-label">Location</div>
																	<div class="class-content-inner-cell">
																		LL 101

																	</div>
																</div>
															
														</div>
													
													
													
												</div>
											</div>

											<div id="class-content-container-75234" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);" class="class-content-container" data-class-number="75234">
												<div class="class-schedule-class-header-container" data-class-number="75234">
													<div class="class-schedule-class-header-title-container">
														<div data-label="Class #" class="nowrap class-number-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D75234%26searchType%3Dall%26term%3D2257%23detailsOpen%3D75234">75234</a>
														</div>
														<div data-label="Course" class="nowrap course-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fcourses%2Fcourselist%3FcatalogNbr%3D142%26subject%3DMAT%26term%3D2257">MAT&nbsp;142</a>
														</div>
														<div data-label="Title" class="title-column class-content-cell">
															<span class="class-title">College Mathematics</span>
														</div>
														<div class="class-schedule-class-header-title-location-container">
															<div class="class-schedule-class-header-title-location-info">PSA 101</div>
														</div>
													</div>
													<div class="class-schedule-class-header-chevron-container"><i class="fas fa-chevron-up class-schedule-class-header-chevron" style="display: none;"></i><i class="fas fa-chevron-down class-schedule-class-header-chevron"></i></div>
												</div>
												<div id="class-schedule-class-info-container-75234" class="class-schedule-class-info-container" style="display: contents;">
													<div data-label="Class #" class="nowrap inner-class-number-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Class #</div>
														<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D75234%26searchType%3Dall%26term%3D2257%23detailsOpen%3D75234">75234</a>
													</div>
													<div data-label="Units" class="numerical units-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Units</div>
														<label title="Standard Grading">3.0</label>
													</div>
													<div class="class-schedule-class-container-date-content class-schedule-class-date-info-container-cell class-schedule-class-info-container-content" data-class-number="75234" data-date-id="0" data-expand-class-info="false">
														<div class="class-schedule-class-info-container-content-label">Dates</div>
														<div class="class-content-inner-cell">08/26/25&nbsp;- 12/12/25</div>
													</div>
													<div id="class-schedule-class-container-date-content-container-75234-0" class="class-schedule-class-container-date-content-open-container">
														<div data-label="Instructors" class="instructors-column class-content-cell class-schedule-column-start-five class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Instructor</div>
															<div class="class-content-inner-cell">
																<a href="https://webapp4.asu.edu/myasu/?action=directoryredir&amp;id=A5jjmHv_eE-r77iUuMhGwvQ" title="Mitchell" data-tracking="schedule/directory-instructor">Mitchell</a>
															</div>
														</div>
														<div data-label="Days" class="days-column class-content-cell  class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Day</div>
															<div class="class-content-inner-cell"><span>TTh</span></div>
														</div>
														<div data-label="Times" class="times-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Time</div>
															<div class="class-content-inner-cell">11:00 AM - 12:15 PM</div>
														</div>
														<div data-label="Date(s)" class="dates-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Dates</div>
															<div class="class-content-inner-cell">08/19/25&nbsp;- 12/5/25</div>
														</div>
														<div id="location-75234" data-label="Location" class="location-column class-content-cell class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Location</div>
															<div class="class-content-inner-cell">PSA 101</div>
														</div>
													</div>
												</div>
											</div>

											<div id="class-content-container-76891" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);" class="class-content-container" data-class-number="76891">
												<div class="class-schedule-class-header-container" data-class-number="76891">
													<div class="class-schedule-class-header-title-container">
														<div data-label="Class #" class="nowrap class-number-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D76891%26searchType%3Dall%26term%3D2257%23detailsOpen%3D76891">76891</a>
														</div>
														<div data-label="Course" class="nowrap course-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fcourses%2Fcourselist%3FcatalogNbr%3D113%26subject%3DCHM%26term%3D2257">CHM&nbsp;113</a>
														</div>
														<div data-label="Title" class="title-column class-content-cell">
															<span class="class-title">General Chemistry I</span>
														</div>
														<div class="class-schedule-class-header-title-location-container">
															<div class="class-schedule-class-header-title-location-info">PS A102</div>
														</div>
													</div>
													<div class="class-schedule-class-header-chevron-container"><i class="fas fa-chevron-up class-schedule-class-header-chevron" style="display: none;"></i><i class="fas fa-chevron-down class-schedule-class-header-chevron"></i></div>
												</div>
												<div id="class-schedule-class-info-container-76891" class="class-schedule-class-info-container" style="display: contents;">
													<div data-label="Class #" class="nowrap inner-class-number-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Class #</div>
														<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D76891%26searchType%3Dall%26term%3D2257%23detailsOpen%3D76891">76891</a>
													</div>
													<div data-label="Units" class="numerical units-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Units</div>
														<label title="Standard Grading">4.0</label>
													</div>
													<div class="class-schedule-class-container-date-content class-schedule-class-date-info-container-cell class-schedule-class-info-container-content" data-class-number="76891" data-date-id="0" data-expand-class-info="false">
														<div class="class-schedule-class-info-container-content-label">Dates</div>
														<div class="class-content-inner-cell">08/19/25&nbsp;- 11/28/25</div>
													</div>
													<div id="class-schedule-class-container-date-content-container-76891-0" class="class-schedule-class-container-date-content-open-container">
														<div data-label="Instructors" class="instructors-column class-content-cell class-schedule-column-start-five class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Instructor</div>
															<div class="class-content-inner-cell">
																<a href="https://webapp4.asu.edu/myasu/?action=directoryredir&amp;id=A5jjmHv_eE-r77iUuMhGwvQ" title="Parker" data-tracking="schedule/directory-instructor">Parker</a>
															</div>
														</div>
														<div data-label="Days" class="days-column class-content-cell  class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Day</div>
															<div class="class-content-inner-cell"><span>MWF</span></div>
														</div>
														<div data-label="Times" class="times-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Time</div>
															<div class="class-content-inner-cell">1:30 PM - 2:45 PM</div>
														</div>
														<div data-label="Date(s)" class="dates-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Dates</div>
															<div class="class-content-inner-cell">08/19/25&nbsp;- 11/28/25</div>
														</div>
														<div id="location-76891" data-label="Location" class="location-column class-content-cell class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Location</div>
															<div class="class-content-inner-cell">PS A102</div>
														</div>
													</div>
												</div>
											</div>

											<div id="class-content-container-77456" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);" class="class-content-container" data-class-number="77456">
												<div class="class-schedule-class-header-container" data-class-number="77456">
													<div class="class-schedule-class-header-title-container">
														<div data-label="Class #" class="nowrap class-number-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D77456%26searchType%3Dall%26term%3D2257%23detailsOpen%3D77456">77456</a>
														</div>
														<div data-label="Course" class="nowrap course-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fcourses%2Fcourselist%3FcatalogNbr%3D101%26subject%3DPSY%26term%3D2257">PSY&nbsp;101</a>
														</div>
														<div data-label="Title" class="title-column class-content-cell">
															<span class="class-title">Introduction to Psychology</span>
														</div>
														<div class="class-schedule-class-header-title-location-container">
															<div class="class-schedule-class-header-title-location-info">PSA 113</div>
														</div>
													</div>
													<div class="class-schedule-class-header-chevron-container"><i class="fas fa-chevron-up class-schedule-class-header-chevron" style="display: none;"></i><i class="fas fa-chevron-down class-schedule-class-header-chevron"></i></div>
												</div>
												<div id="class-schedule-class-info-container-77456" class="class-schedule-class-info-container" style="display: contents;">
													<div data-label="Class #" class="nowrap inner-class-number-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Class #</div>
														<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D77456%26searchType%3Dall%26term%3D2257%23detailsOpen%3D77456">77456</a>
													</div>
													<div data-label="Units" class="numerical units-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Units</div>
														<label title="Standard Grading">3.0</label>
													</div>
													<div class="class-schedule-class-container-date-content class-schedule-class-date-info-container-cell class-schedule-class-info-container-content" data-class-number="77456" data-date-id="0" data-expand-class-info="false">
														<div class="class-schedule-class-info-container-content-label">Dates</div>
														<div class="class-content-inner-cell">09/2/25&nbsp;- 12/5/25</div>
													</div>
													<div id="class-schedule-class-container-date-content-container-77456-0" class="class-schedule-class-container-date-content-open-container">
														<div data-label="Instructors" class="instructors-column class-content-cell class-schedule-column-start-five class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Instructor</div>
															<div class="class-content-inner-cell">
																<a href="https://webapp4.asu.edu/myasu/?action=directoryredir&amp;id=A5jjmHv_eE-r77iUuMhGwvQ" title="Bennett" data-tracking="schedule/directory-instructor">Bennett</a>
															</div>
														</div>
														<div data-label="Days" class="days-column class-content-cell  class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Day</div>
															<div class="class-content-inner-cell"><span>TTh</span></div>
														</div>
														<div data-label="Times" class="times-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Time</div>
															<div class="class-content-inner-cell">2:00 PM - 3:15 PM</div>
														</div>
														<div data-label="Date(s)" class="dates-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Dates</div>
															<div class="class-content-inner-cell">09/2/25&nbsp;- 12/5/25</div>
														</div>
														<div id="location-77456" data-label="Location" class="location-column class-content-cell class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Location</div>
															<div class="class-content-inner-cell">PSA 113</div>
														</div>
													</div>
												</div>
											</div>

											<div id="class-content-container-78123" style="grid-template-columns: minmax(0px, 1fr) minmax(0px, 1.1fr) minmax(0px, 2.6fr) minmax(0px, 0.7fr) minmax(0px, 1.7fr) minmax(0px, 0.7fr) minmax(0px, 1.4fr) minmax(0px, 1.3fr) minmax(0px, 1fr);" class="class-content-container" data-class-number="78123">
												<div class="class-schedule-class-header-container" data-class-number="78123">
													<div class="class-schedule-class-header-title-container">
														<div data-label="Class #" class="nowrap class-number-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D78123%26searchType%3Dall%26term%3D2257%23detailsOpen%3D78123">78123</a>
														</div>
														<div data-label="Course" class="nowrap course-column class-content-cell">
															<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fcourses%2Fcourselist%3FcatalogNbr%3D101%26subject%3DASU%26term%3D2257">ASU&nbsp;101</a>
														</div>
														<div data-label="Title" class="title-column class-content-cell">
															<span class="class-title">The ASU Experience</span>
														</div>
														<div class="class-schedule-class-header-title-location-container">
															<div class="class-schedule-class-header-title-location-info">MU 145</div>
														</div>
													</div>
													<div class="class-schedule-class-header-chevron-container"><i class="fas fa-chevron-up class-schedule-class-header-chevron" style="display: none;"></i><i class="fas fa-chevron-down class-schedule-class-header-chevron"></i></div>
												</div>
												<div id="class-schedule-class-info-container-78123" class="class-schedule-class-info-container" style="display: contents;">
													<div data-label="Class #" class="nowrap inner-class-number-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Class #</div>
														<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fkeywords%3D78123%26searchType%3Dall%26term%3D2257%23detailsOpen%3D78123">78123</a>
													</div>
													<div data-label="Units" class="numerical units-column class-content-cell class-schedule-class-info-container-cell">
														<div class="class-schedule-class-info-container-content-label">Units</div>
														<label title="Standard Grading">1.0</label>
													</div>
													<div class="class-schedule-class-container-date-content class-schedule-class-date-info-container-cell class-schedule-class-info-container-content" data-class-number="78123" data-date-id="0" data-expand-class-info="false">
														<div class="class-schedule-class-info-container-content-label">Dates</div>
														<div class="class-content-inner-cell">09/9/25&nbsp;- 11/21/25</div>
													</div>
													<div id="class-schedule-class-container-date-content-container-78123-0" class="class-schedule-class-container-date-content-open-container">
														<div data-label="Instructors" class="instructors-column class-content-cell class-schedule-column-start-five class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Instructor</div>
															<div class="class-content-inner-cell">
																<a href="https://webapp4.asu.edu/myasu/?action=directoryredir&amp;id=A5jjmHv_eE-r77iUuMhGwvQ" title="Foster" data-tracking="schedule/directory-instructor">Foster</a>
															</div>
														</div>
														<div data-label="Days" class="days-column class-content-cell  class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Day</div>
															<div class="class-content-inner-cell"><span>F</span></div>
														</div>
														<div data-label="Times" class="times-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Time</div>
															<div class="class-content-inner-cell">10:00 AM - 10:50 AM</div>
														</div>
														<div data-label="Date(s)" class="dates-column class-content-cell class-schedule-class-date-info-container-cell class-schedule-no-show">
															<div class="class-schedule-class-info-container-content-label">Dates</div>
															<div class="class-content-inner-cell">09/9/25&nbsp;- 11/21/25</div>
														</div>
														<div id="location-78123" data-label="Location" class="location-column class-content-cell class-schedule-class-date-info-container-cell">
															<div class="class-schedule-class-info-container-content-label">Location</div>
															<div class="class-content-inner-cell">MU 145</div>
														</div>
													</div>
												</div>
											</div>



							</div>
		
							<div class="noprint box-footer schedule-box-footer">
								
									<div>
										<span class="box-extra-link">Total Units: 14.0</span>
									</div>
								
								<div>
									
										<span class="box-extra-link">
											<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fwww.asu.edu%2Fgo%2Fweeklyschedule" data-tracking="schedule/weeklyview">Weekly View</a>
										</span>
									
									
										<span class="box-extra-link">
											<a href="https://webapp4.asu.edu/myasu/student/classes/grades?term=2257" data-tracking="schedule/gradesview">Grades View</a>
										</span>
									
									
									
										<span class="box-extra-link">
											<a href="https://webapp4.asu.edu/myasu/books?term=2257" title="View and order books for your Fall 2025 classes" data-tracking="schedule/books">Books</a>
										</span>
									
									
										<span class="box-extra-link">
											<a href="https://webapp4.asu.edu/myasu/student/schedule?term=2257&amp;format=ical" title="Download Schedule in iCal Format" data-tracking="schedule/icaldownload">iCal Download</a>
										</span>
									
									<span class="box-extra-link">
										<a href="javascript:window.print();">Print my Schedule</a>
									</span>
								</div>
							</div>
							
							<div class="noprint box-footer class-box-footer">
								
									<span class="box-extra-link">
										<a href="https://webapp4.asu.edu/myasu/?action=addclass&amp;strm=2257" title="Add classes" id="reg2257-add" data-tracking="schedule/reg-add">Add/Shopping Cart</a>
									</span>
									<span class="box-extra-link">
										<a href="https://webapp4.asu.edu/myasu/?action=dropclass&amp;strm=2257" title="Drop/Withdrawal from classes" data-tracking="schedule/reg-drop">Drop/Withdraw</a>
									</span>
									<span class="box-extra-link"> 
										<a href="https://webapp4.asu.edu/myasu/?action=swapclass&amp;strm=2257" title="Swap a class" data-tracking="schedule/reg-swap">Swap</a>
									</span>
									<span class="box-extra-link"> 
										<a href="https://webapp4.asu.edu/myasu/?action=editclass&amp;strm=2257" title="Edit hours or grading basis on a specific class" data-tracking="schedule/reg-edit">Edit/Update</a>
									</span> 
								
								
									<span class="box-extra-link">
										<a href="https://webapp4.asu.edu/myasu/?action=clearschedcache&amp;nextUrl=https%3A%2F%2Fcatalog.apps.asu.edu%2Fcatalog%2Fclasses%2Fclasslist%3Fterm%3D2257" title="Search for classes" id="class-search-2257" data-tracking="schedule/classsearch">Class Search</a>
									</span>
								
							</div>
						</div>
					</div>
				</section>
			</div>

			<div id="schedule-lower">
				
					<div id="schedule-textbooks" class="schedule-lower-left">
						<section class="box">
							<div class="box-title-bar">
								<h2 class="box-title">Textbooks for Fall 2025</h2>	
							</div>
							<!-- textbooks -->
							<div id="schedule-textbooks-content">










	    <div id="test">
	        
		                
		                <div class="list odd">
		                    <div class="course">ENG 101 (74588) - Mallory Branco</div>
		                    
									<div class="no-books">No ASU Bookstore materials required.<br>Refer to syllabus for additional details.</div>
								
							
					    </div>
				    
        </div>
   
</div>
						</section>
					</div>
				
			</div>
			<div class="clr"></div>

			

		</div>
	</div>
</div><!-- column-container -->

<div id="schedule_dropdown_content" style="display: none;">
	<div id="schedule-dropdown-tippy-content">
		<div class="myasu-tippy-options">
			
				<a href="https://webapp4.asu.edu/myasu/student/classes?term=2257" class="myasu-tippy-option">Fall 2025</a>
			
				<a href="https://webapp4.asu.edu/myasu/student/classes?term=2254" class="myasu-tippy-option">Summer 2025</a>
			
				<a href="https://webapp4.asu.edu/myasu/student/classes?term=2251" class="myasu-tippy-option">Spring 2025</a>
			
		</div>
	</div>
</div>


	<div id="in-person-class-popout" style="display: none;"><div class="sync-class-popout-content-container">This class is not available via ASU Sync. If you cannot attend this class in-person during the semester, please contact your instructor directly to explain your circumstances. They will advise you on any possible adjustments to your attendance.</div></div>


	<div id="asu-sync-class-popout" style="display: none;"><div class="sync-class-popout-content-container">Your instructor will communicate your options for attending in-person or via <a href="https://provost.asu.edu/sync/students" target="_blank" class="asu_sync_anchor" data-tracking="asu-sync-label-popup/asu-sync">ASU Sync</a>.</div></div>


	<div id="asu-sync-only-class-popout" style="display: none;"><div class="sync-class-popout-content-container">All sessions for this course will be delivered remotely via ASU Sync. Do not plan to attend in-person unless your instructor communicates otherwise.</div></div>


</div>

<script type="text/javascript">
	var termCYYT = '2257';

	var allTerms = [];
	var allTermAbbreviations = {};

	var defaultTermTab = '2257';

	var termNames = [];
	var currentTerms = [];

	$(".student-term-info").each(function() {
		var termInfoCYYT = $(this).data("cyyt").toString();
		var termInfoAbbreviatedDescription = $(this).data(
				"abbreviated-description");

		allTerms.push(termInfoCYYT);
		allTermAbbreviations[termInfoCYYT] = termInfoAbbreviatedDescription;
	});

	function switchToTerm() {
		/* If we have 5 or less terms, skip processing and show */
		if (allTerms.length <= 5) {
			showTerms(allTerms);
			return;
		}
		var showBefore = 2;
		var showAfter = 2;
		var indexOf = array_indexOf(defaultTermTab, allTerms);
		if (indexOf == -1) {
			return;
		}
		if (indexOf < 2) {
			showBefore -= 2 - indexOf;
			showAfter += 2 - indexOf;
		} else if (indexOf > allTerms.length - 3) {
			showBefore = 4 - ((allTerms.length - 1) - indexOf);
			showAfter = ((allTerms.length - 1) - indexOf);
		}

		termsToShow = [];
		i = 0;
		beforeCounter = showBefore;
		while (beforeCounter != 0) {
			termsToShow[i++] = allTerms[indexOf - beforeCounter];
			beforeCounter--;
		}
		termsToShow[i++] = allTerms[indexOf];

		var afterCounter = 0;
		while (afterCounter != showAfter) {
			termsToShow[i++] = allTerms[indexOf + afterCounter + 1];
			afterCounter++;
		}

		showTerms(termsToShow);
	}

	function showTerms(terms) {
		currentTerms = terms;

		$("#schedule_content_tablist").css("display", "none").empty();

		var defaultTab = 0;
		for (i = 0; i < terms.length; i++) {
			term = terms[i];
			$("#schedule_content_tablist")
					.append(
							"<button role=\"tab\" aria-selected=\"false\" aria-controls=\"schedule_"
									+ term
									+ "_tab\" id=\"schedule-link-"
									+ term
									+ "\" data-tab-scope=\"schedule_content\" onclick=\"return changeScheduleSection('student/classes?term="
									+ term
									+ "');\" tabindex=\"-1\">"
									+ allTermAbbreviations[term] + "</button>");
			if (defaultTermTab == term) {
				defaultTab = i;
			}
		}

		$('.deadline-icon').cluetip({
			activation : 'click',
			width : 380,
			showTitle : false,
			sticky : true,
			closePosition : 'title',
			closeText : CLUETIP_CLOSE,
			arrows : true,
			local : false
		});

		$('.zoom-links-popup').cluetip({
			activation : 'click',
			showTitle : false,
			sticky : true,
			closePosition : 'title',
			closeText : CLUETIP_CLOSE,
			arrows : true,
			local : true,
			onShow : function(ct, c) {
				addLinkTracking(c);
				$("a:eq(0)", c).focus();
			}
		});

		$("#schedule-textbooks-content").load("/myasu/booksbox?term=" + termCYYT + "&amp;format=ajaxhtml&amp;rnd=" + (new Date()).getTime());

		// Add dropdown
		$("#schedule_content_tablist")
				.append(
						"<button id=\"schedule_dropdown\" data-template='schedule_dropdown_content' class=\"dropdown\" title=\"All Terms\">All Semesters<i class=\"schedule_dropdown_chevron fas fa-chevron-down\" aria-hidden=\"true\"></i></button>");

		$("#schedule_content_tablist").css("display", "");

		initializeTablist("schedule_content");

		if (terms.length) {
			manualActiveTab(document.getElementById("schedule-link-"
					+ defaultTermTab), false);

			// init tippy instances
			initMyAsuTippy("#schedule_dropdown", {
				placement : "bottom",
				addLinkTracking : true,
				arrow : false,
				offset : [ 0, 0 ],
				disablePadding : true,
				onShowCallback : function() {
					$("#schedule_dropdown").removeClass("dropdown-focused");
				},
				onHidden : function() {
					$("#schedule_dropdown").focus();
				}
			});

			// init tippy instances
			if ($("#in-person-class-popout").length) {
				initMyAsuTippy(".class-remote-classification-in-person-popup",
						{
							placement : "auto"
						});
			}

			initMyAsuTippy(".class-remote-classification-asu-sync-popup", {placement : "auto"});
			// end of init tippy instances
		}

		var lastGridValue = null;

		function monitorMobileSchedule(x) {
			if (x.matches) { // If media query matches
				$(".class-schedule-class-info-container").css("display", "none");
				$(".class-content-container").removeClass("open");
				$(".class-schedule-class-header-chevron-container").find(".fa-chevron-down").show();
				$(".class-schedule-class-header-chevron-container").find(".fa-chevron-up").hide();
				
				$(".class-schedule-class-container-date-content-container").css("display", "none");
				$(".class-schedule-class-info-container-content-label-chevron-container").find(".fa-chevron-down").show();
				$(".class-schedule-class-info-container-content-label-chevron-container").find(".fa-chevron-up").hide();

				$(document).on("click", ".class-schedule-class-header-container", function() {
					var classNumber = $(this).data("class-number");

					if ($("#class-schedule-class-info-container-" + classNumber).css("display") == "contents") {
						$("#class-schedule-class-info-container-" + classNumber).css("display", "block");
					}

					$("#class-schedule-class-info-container-" + classNumber).slideToggle(function() {
						if ($(this).is(":visible")) {
					        $(this).css("display", "contents");
						}
					});

					$(this).find(".class-schedule-class-header-chevron").toggle();
					
					$("#class-content-container-" + classNumber).toggleClass("open");
				});

				$(document).on("click",".class-schedule-class-container-date-content", function() {
					var classNumber = $(this).data("class-number");
					var dateId = $(this).data("date-id");
					var expandClassInfo = $(this).data("expand-class-info");

					if (expandClassInfo == true) {
						if ($("#class-schedule-class-container-date-content-container-" + classNumber + "-" + dateId).css("display") == "contents") {
							$("#class-schedule-class-container-date-content-container-" + classNumber + "-" + dateId).css("display", "block");
						}
	
						$("#class-schedule-class-container-date-content-container-" + classNumber + "-" + dateId).slideToggle(function() {
							if ($(this).is(":visible")) {
						        $(this).css("display", "contents");
							}
						});
	
						$(this).find(".class-schedule-class-info-container-content-label-chevron").toggle();
					}
				});
			} else {
				$(".class-schedule-class-info-container").css("display", "contents");
				$(".class-content-container").removeClass("open");
				$(".class-schedule-class-header-chevron-container").find(".fa-chevron-down").show();
				$(".class-schedule-class-header-chevron-container").find(".fa-chevron-up").hide();
				
				$(".class-schedule-class-container-date-content-container").css("display", "contents");
				$(".class-schedule-class-info-container-content-label-chevron-container").find(".fa-chevron-down").show();
				$(".class-schedule-class-info-container-content-label-chevron-container").find(".fa-chevron-up").hide();
				
				$(document).off("click", ".class-schedule-class-header-container");
				$(document).off("click",".class-schedule-class-container-date-content");
			}
		}

		function monitorTabletSchedule(x) {
			if (x.matches) { // If media query matches
				$("#class-schedule-header").css("grid-template-columns", $("#responsive-class-schedule-grid-style").val());
				$(".class-content-container").css("grid-template-columns", $("#responsive-class-schedule-grid-style").val());
			} else {
				$("#class-schedule-header").css("grid-template-columns", $("#class-schedule-grid-style").val());
				$(".class-content-container").css("grid-template-columns", $("#class-schedule-grid-style").val());
			}
		}

		window.onbeforeprint = function() {
			lastGridValue = $(".class-content-container").css("grid-template-columns");

			$("#class-schedule-header").css("grid-template-columns", $("#print-class-schedule-grid-style").val());
			$(".class-content-container").css("grid-template-columns", $("#print-class-schedule-grid-style").val());
			$(".action-column").hide();
			scheduleMatchMedia.removeListener(monitorMobileSchedule);
			responsiveScheduleMatchMedia.removeListener(monitorTabletSchedule);
		};

		window.onafterprint = function() {
			$("#class-schedule-header").css("grid-template-columns", lastGridValue);
			$(".class-content-container").css("grid-template-columns", lastGridValue);
			$(".action-column").show();
			scheduleMatchMedia.addListener(monitorMobileSchedule);
			responsiveScheduleMatchMedia.addListener(monitorTabletSchedule);
		};

		var responsiveScheduleMatchMedia = window.matchMedia("(max-width: 1224px)");
		monitorTabletSchedule(responsiveScheduleMatchMedia) // Call listener function at run time
		responsiveScheduleMatchMedia.addListener(monitorTabletSchedule); // Attach listener function on state changes

		var scheduleMatchMedia = window.matchMedia("(max-width: 768px)");
		monitorMobileSchedule(scheduleMatchMedia) // Call listener function at run time
		scheduleMatchMedia.addListener(monitorMobileSchedule); // Attach listener function on state changes
	}

	$(document).ready(function() {
		switchToTerm(defaultTermTab);
	});

	function changeScheduleSection(url) {
		if (url != null) {
			window.location.href = url;
		}
	}

	function array_indexOf(item, arr) {
		for (i = 0; i < arr.length; i++) {
			if (arr[i] == item) {
				return i;
			}
		}
		return -1;
	}
</script>






				<aside>
					<div id="bottom-utility">
					
						 
							<span>
								<a href="https://webapp4.asu.edu/myasu/?action=clearcache&amp;nextUrl=%2Fmyasu%2Fstudent%2Fschedule%3Fterm%3D2257" class="refresh-btn secondary-btn unity-small-btn" aria-label="Refresh the data on this page" data-tracking="refresh-page"><span class="fa fa-refresh" title="Refresh the data on this page"></span></a>
							</span>
						

						
							<a href="https://webapp4.asu.edu/myasu/feedback" class="primary-btn unity-small-btn" title="Please leave your feedback" aria-label="Feedback link"><i class="fas fa fa-comment icon-md space-inline-right-sm" aria-hidden="true" title="Comment Bubble"></i><span class="sr-only">Comment Bubble</span>Feedback</a>
						

						

						

					</div>
				</aside>
				<div id="graduationBackgroundImagePhotoCreditPlaceholder"></div>
			<!-- END CONTENT -->
			</div>															<!--close myasu-main-container-->

		</div>																	<!--close asu_content-->
	</main>
	
	<!-- END MIDDLE CONTENT AREA -->
	<footer>
		<div class="myasu-footer-container">
			<div class="myasu-container" id="myasu-footer">
				<div id="asu-footer" role="contentinfo">
	<div class="wrapper" id="wrapper-footer-innovation">
		<div class="container" id="footer-innovation">
			<div class="row">
				<div class="col">
					<div class="d-flex footer-innovation-links">
						<nav class="nav" aria-label="University Services">
							<a class="nav-link" href="https://www.asu.edu/about/locations-maps">Maps and Locations</a> 
							<a class="nav-link" href="https://cfo.asu.edu/applicant">Jobs</a> 
							<a class="nav-link" href="https://search.asu.edu/?search-tabs=web_dir_faculty_staff">Directory</a>
							<a class="nav-link" href="https://www.asu.edu/about/contact">Contact ASU</a> 
							<a class="nav-link" href="https://my.asu.edu/">My ASU</a>
						</nav>
						<a class="img-link" href="https://www.asu.edu/rankings"> 
							<img src="./My ASU - Schedule_files/240917_ASU_Rankings_GOLD.png" loading="lazy" height="392" width="1798" alt="Repeatedly ranked #1 on 20+ lists in the last 3 years">
						</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="wrapper" id="wrapper-footer-colophon">
		<div class="container" id="footer-colophon">
			<div class="row">
				<div class="col">
					<div class="footer-legal-links">
						<nav class="nav colophon" aria-label="University Legal and Compliance">
							<a class="nav-link" href="https://www.asu.edu/about/copyright-trademark">Copyright and Trademark</a> 
							<a class="nav-link" href="https://accessibility.asu.edu/report">Accessibility</a>
							<a class="nav-link" href="https://www.asu.edu/about/privacy">Privacy</a>
							<a class="nav-link" href="https://www.asu.edu/about/terms-of-use">Terms of Use</a> 
							<a class="nav-link" href="https://www.asu.edu/emergency">Emergency</a>
						<button id="manualConsentoptout">Manage my privacy settings</button></nav>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

			</div>
		</div>
	</footer>
	<!-- END FOOTER -->

</div>																		<!--close asu_container-->

<script defer="" src="./My ASU - Schedule_files/vcd15cbe7772f49c399c6a5babf22c1241717689176015" integrity="sha512-ZpsOmlRQV6y907TI0dKBHq9Md29nnaEIPlkf84rnaERnq6zvWvPUqr2ft8M1aS28oN72PdrCzSjY4U6VaAw1EQ==" data-cf-beacon="{&quot;rayId&quot;:&quot;9625bd036af02ec0&quot;,&quot;serverTiming&quot;:{&quot;name&quot;:{&quot;cfExtPri&quot;:true,&quot;cfEdge&quot;:true,&quot;cfOrigin&quot;:true,&quot;cfL4&quot;:true,&quot;cfSpeedBrain&quot;:true,&quot;cfCacheStatus&quot;:true}},&quot;version&quot;:&quot;2025.7.0&quot;,&quot;token&quot;:&quot;e64fdfc25a574723a13d1f77e07bc18c&quot;}" crossorigin="anonymous"></script>



<script id="" text="" charset="" type="text/javascript" src="./My ASU - Schedule_files/cassie-fullwidth.js.download"></script>
<iframe height="0" width="0" style="display: none; visibility: hidden;" data-gtm-yt-inspected-13="true" data-gtm-yt-inspected-658644_1548="true" src="./My ASU - Schedule_files/saved_resource.html"></iframe><iframe height="0" width="0" style="display: none; visibility: hidden;" data-gtm-yt-inspected-13="true" data-gtm-yt-inspected-658644_1548="true" src="./My ASU - Schedule_files/saved_resource(1).html"></iframe><link rel="stylesheet" type="text/css" href="./My ASU - Schedule_files/cassie-fullwidth.css"><script>!function(){var e={669:function(e,t,n){e.exports=n(609)},448:function(e,t,n){"use strict";var i=n(867),s=n(26),r=n(372),o=n(327),a=n(97),c=n(109),l=n(985),d=n(61);e.exports=function(e){return new Promise((function(t,n){var S=e.data,E=e.headers;i.isFormData(S)&&delete E["Content-Type"];var u=new XMLHttpRequest;if(e.auth){var h=e.auth.username||"",g=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";E.Authorization="Basic "+btoa(h+":"+g)}var _=a(e.baseURL,e.url);if(u.open(e.method.toUpperCase(),o(_,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,u.onreadystatechange=function(){if(u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))){var i="getAllResponseHeaders"in u?c(u.getAllResponseHeaders()):null,r={data:e.responseType&&"text"!==e.responseType?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:i,config:e,request:u};s(t,n,r),u=null}},u.onabort=function(){u&&(n(d("Request aborted",e,"ECONNABORTED",u)),u=null)},u.onerror=function(){n(d("Network Error",e,null,u)),u=null},u.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(d(t,e,"ECONNABORTED",u)),u=null},i.isStandardBrowserEnv()){var T=(e.withCredentials||l(_))&&e.xsrfCookieName?r.read(e.xsrfCookieName):void 0;T&&(E[e.xsrfHeaderName]=T)}if("setRequestHeader"in u&&i.forEach(E,(function(e,t){void 0===S&&"content-type"===t.toLowerCase()?delete E[t]:u.setRequestHeader(t,e)})),i.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),e.responseType)try{u.responseType=e.responseType}catch(t){if("json"!==e.responseType)throw t}"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){u&&(u.abort(),n(e),u=null)})),S||(S=null),u.send(S)}))}},609:function(e,t,n){"use strict";var i=n(867),s=n(849),r=n(321),o=n(185);function a(e){var t=new r(e),n=s(r.prototype.request,t);return i.extend(n,r.prototype,t),i.extend(n,t),n}var c=a(n(655));c.Axios=r,c.create=function(e){return a(o(c.defaults,e))},c.Cancel=n(263),c.CancelToken=n(972),c.isCancel=n(502),c.all=function(e){return Promise.all(e)},c.spread=n(713),c.isAxiosError=n(268),e.exports=c,e.exports.default=c},263:function(e){"use strict";function t(e){this.message=e}t.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},t.prototype.__CANCEL__=!0,e.exports=t},972:function(e,t,n){"use strict";var i=n(263);function s(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new i(e),t(n.reason))}))}s.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},s.source=function(){var e;return{token:new s((function(t){e=t})),cancel:e}},e.exports=s},502:function(e){"use strict";e.exports=function(e){return!(!e||!e.__CANCEL__)}},321:function(e,t,n){"use strict";var i=n(867),s=n(327),r=n(782),o=n(572),a=n(185);function c(e){this.defaults=e,this.interceptors={request:new r,response:new r}}c.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=a(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=[o,void 0],n=Promise.resolve(e);for(this.interceptors.request.forEach((function(e){t.unshift(e.fulfilled,e.rejected)})),this.interceptors.response.forEach((function(e){t.push(e.fulfilled,e.rejected)}));t.length;)n=n.then(t.shift(),t.shift());return n},c.prototype.getUri=function(e){return e=a(this.defaults,e),s(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},i.forEach(["delete","get","head","options"],(function(e){c.prototype[e]=function(t,n){return this.request(a(n||{},{method:e,url:t,data:(n||{}).data}))}})),i.forEach(["post","put","patch"],(function(e){c.prototype[e]=function(t,n,i){return this.request(a(i||{},{method:e,url:t,data:n}))}})),e.exports=c},782:function(e,t,n){"use strict";var i=n(867);function s(){this.handlers=[]}s.prototype.use=function(e,t){return this.handlers.push({fulfilled:e,rejected:t}),this.handlers.length-1},s.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},s.prototype.forEach=function(e){i.forEach(this.handlers,(function(t){null!==t&&e(t)}))},e.exports=s},97:function(e,t,n){"use strict";var i=n(793),s=n(303);e.exports=function(e,t){return e&&!i(t)?s(e,t):t}},61:function(e,t,n){"use strict";var i=n(481);e.exports=function(e,t,n,s,r){var o=new Error(e);return i(o,t,n,s,r)}},572:function(e,t,n){"use strict";var i=n(867),s=n(527),r=n(502),o=n(655);function a(e){e.cancelToken&&e.cancelToken.throwIfRequested()}e.exports=function(e){return a(e),e.headers=e.headers||{},e.data=s(e.data,e.headers,e.transformRequest),e.headers=i.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),i.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||o.adapter)(e).then((function(t){return a(e),t.data=s(t.data,t.headers,e.transformResponse),t}),(function(t){return r(t)||(a(e),t&&t.response&&(t.response.data=s(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))}},481:function(e){"use strict";e.exports=function(e,t,n,i,s){return e.config=t,n&&(e.code=n),e.request=i,e.response=s,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e}},185:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t){t=t||{};var n={},s=["url","method","data"],r=["headers","auth","proxy","params"],o=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],a=["validateStatus"];function c(e,t){return i.isPlainObject(e)&&i.isPlainObject(t)?i.merge(e,t):i.isPlainObject(t)?i.merge({},t):i.isArray(t)?t.slice():t}function l(s){i.isUndefined(t[s])?i.isUndefined(e[s])||(n[s]=c(void 0,e[s])):n[s]=c(e[s],t[s])}i.forEach(s,(function(e){i.isUndefined(t[e])||(n[e]=c(void 0,t[e]))})),i.forEach(r,l),i.forEach(o,(function(s){i.isUndefined(t[s])?i.isUndefined(e[s])||(n[s]=c(void 0,e[s])):n[s]=c(void 0,t[s])})),i.forEach(a,(function(i){i in t?n[i]=c(e[i],t[i]):i in e&&(n[i]=c(void 0,e[i]))}));var d=s.concat(r).concat(o).concat(a),S=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===d.indexOf(e)}));return i.forEach(S,l),n}},26:function(e,t,n){"use strict";var i=n(61);e.exports=function(e,t,n){var s=n.config.validateStatus;n.status&&s&&!s(n.status)?t(i("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}},527:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t,n){return i.forEach(n,(function(n){e=n(e,t)})),e}},655:function(e,t,n){"use strict";var i=n(867),s=n(16),r={"Content-Type":"application/x-www-form-urlencoded"};function o(e,t){!i.isUndefined(e)&&i.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var a,c={adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(a=n(448)),a),transformRequest:[function(e,t){return s(t,"Accept"),s(t,"Content-Type"),i.isFormData(e)||i.isArrayBuffer(e)||i.isBuffer(e)||i.isStream(e)||i.isFile(e)||i.isBlob(e)?e:i.isArrayBufferView(e)?e.buffer:i.isURLSearchParams(e)?(o(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):i.isObject(e)?(o(t,"application/json;charset=utf-8"),JSON.stringify(e)):e}],transformResponse:[function(e){if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*"}}};i.forEach(["delete","get","head"],(function(e){c.headers[e]={}})),i.forEach(["post","put","patch"],(function(e){c.headers[e]=i.merge(r)})),e.exports=c},849:function(e){"use strict";e.exports=function(e,t){return function(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];return e.apply(t,n)}}},327:function(e,t,n){"use strict";var i=n(867);function s(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}e.exports=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(i.isURLSearchParams(t))r=t.toString();else{var o=[];i.forEach(t,(function(e,t){null!=e&&(i.isArray(e)?t+="[]":e=[e],i.forEach(e,(function(e){i.isDate(e)?e=e.toISOString():i.isObject(e)&&(e=JSON.stringify(e)),o.push(s(t)+"="+s(e))})))})),r=o.join("&")}if(r){var a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e}},303:function(e){"use strict";e.exports=function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}},372:function(e,t,n){"use strict";var i=n(867);e.exports=i.isStandardBrowserEnv()?{write:function(e,t,n,s,r,o){var a=[];a.push(e+"="+encodeURIComponent(t)),i.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),i.isString(s)&&a.push("path="+s),i.isString(r)&&a.push("domain="+r),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}}},793:function(e){"use strict";e.exports=function(e){return/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(e)}},268:function(e){"use strict";e.exports=function(e){return"object"==typeof e&&!0===e.isAxiosError}},985:function(e,t,n){"use strict";var i=n(867);e.exports=i.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function s(e){var i=e;return t&&(n.setAttribute("href",i),i=n.href),n.setAttribute("href",i),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=s(window.location.href),function(t){var n=i.isString(t)?s(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0}},16:function(e,t,n){"use strict";var i=n(867);e.exports=function(e,t){i.forEach(e,(function(n,i){i!==t&&i.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[i])}))}},109:function(e,t,n){"use strict";var i=n(867),s=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"];e.exports=function(e){var t,n,r,o={};return e?(i.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=i.trim(e.substr(0,r)).toLowerCase(),n=i.trim(e.substr(r+1)),t){if(o[t]&&s.indexOf(t)>=0)return;o[t]="set-cookie"===t?(o[t]?o[t]:[]).concat([n]):o[t]?o[t]+", "+n:n}})),o):o}},713:function(e){"use strict";e.exports=function(e){return function(t){return e.apply(null,t)}}},867:function(e,t,n){"use strict";var i=n(849),s=Object.prototype.toString;function r(e){return"[object Array]"===s.call(e)}function o(e){return void 0===e}function a(e){return null!==e&&"object"==typeof e}function c(e){if("[object Object]"!==s.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function l(e){return"[object Function]"===s.call(e)}function d(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),r(e))for(var n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.call(null,e[s],s,e)}e.exports={isArray:r,isArrayBuffer:function(e){return"[object ArrayBuffer]"===s.call(e)},isBuffer:function(e){return null!==e&&!o(e)&&null!==e.constructor&&!o(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:a,isPlainObject:c,isUndefined:o,isDate:function(e){return"[object Date]"===s.call(e)},isFile:function(e){return"[object File]"===s.call(e)},isBlob:function(e){return"[object Blob]"===s.call(e)},isFunction:l,isStream:function(e){return a(e)&&l(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&"undefined"!=typeof window&&"undefined"!=typeof document},forEach:d,merge:function e(){var t={};function n(n,i){c(t[i])&&c(n)?t[i]=e(t[i],n):c(n)?t[i]=e({},n):r(n)?t[i]=n.slice():t[i]=n}for(var i=0,s=arguments.length;i<s;i++)d(arguments[i],n);return t},extend:function(e,t,n){return d(t,(function(t,s){e[s]=n&&"function"==typeof t?i(t,n):t})),e},trim:function(e){return e.replace(/^\s*/,"").replace(/\s*$/,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}}}},t={};function n(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={exports:{}};return e[i](r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=function(e){function t(e){try{const t=new URL(e).hostname.split("."),n=t.length-1,i=t.length>=3&&(t[n]+t[n-1]).length<=5;return t.splice(i?-3:-2).join(".")}catch(e){return null}}function n(e,t){let n=e||window.location.hostname;const i=t||!1;let s=0;if(n.includes(".co.")||n.includes(".com.")){const e=n.split(".");e[e.length-1].length<=3&&(s=1)}if(n=n.replace(/(https?:\/\/)?(www.)?/i,""),!i){const e=n.split(".");n=e.slice(e.length-(2+s)).join(".")}return-1!==n.indexOf("/")?n.split("/")[0]:n}return e&&(e.environment="production"===e.environment?"":e.environment),{getDomainName:n,getDefaultSubmissionUrl:function(){const t=e.recordUrlDomain;if(t||t?.length>0)return t;const{region:i}=e;let s=e?.environment;return s&&s?.length>0&&(s=`-${s}`),`https://cscript-${i}${s}.${n()}/cookiesapi/submit`},getSubmissionUrl:function(){const t=""===e.environment?"":`-${e.environment}`,n=window?.cassieResourceRootDomain??"cassiecloud.com";return`https://cscript-${e.region}${t}.${n}/cookiesapi/submit`},determineJsonUrl:function(){return`${e.baseUrl}/${e.licenseKey}/V2/${e.profileSettings.WidgetId}_${e.languageCode}.json`},checkDomain:function(e){if("all"===e||"N/A"===e)return!0;const n=e.trim().split(",");for(let e=0;e<n.length;e++){let i=n[e].trim();if("all"===i)return!0;-1!==i.indexOf("*.")&&(i=i.replace("*.",""));const s=document.domain;if(-1!==s.indexOf(i))return!0;const r=t(`https://${i}`);if(r&&-1!==s.indexOf(r))return!0}return!1}}},t=function(e,t){e&&0!==e.length&&function(e,t){const n=t?"head":"body",i=document.createElement("body"),s=["script","link","meta","title","style","base","noscript"];i.innerHTML=e;const{children:r}=i;for(let e=0;e<r.length;e++){const i=r[e],o=document.createElement(i.tagName.toLocaleLowerCase());for(let e=0;e<i.attributes.length;e++){const t=i.attributes[e];o.setAttribute(t.name,t.value)}o.innerHTML=i.innerHTML,s.includes(o.tagName.toLocaleLowerCase())||!t?document.getElementsByTagName(n)[0].appendChild(o):document.getElementsByTagName("body")[0].appendChild(o)}i.remove()}(e,t)},i=n(669),s=n.n(i);s().defaults.withCredentials=!1;var r=function(e){return new Promise(((t,n)=>{s().get(e).then((e=>{t(e.data)})).catch((e=>{n(e)}))}))},o=function(e,t){return new Promise(((n,i)=>{s().get(e,t).then((e=>{n(e.data)})).catch((e=>{i(e)}))}))},a=function(e,t,n){return new Promise(((i,r)=>{try{s().post(e,t,n).then((e=>{i(e.data)})).catch((e=>{r(e)}))}catch(e){r(e)}}))},c=async function(e,t,n,i,s,r,a,c,l,d,S,E){const u=`${l}/Home/SaveConsent?accessKey=${t}&domain=${d}&consentedCookieIds=${n}&cookieFormConsent=${i}&runFirstCookieIds=${s}&privacyCookieIds=${r}&custom1stPartyData=${a}&privacyLink=${c}${S?`&userProfile=${S}`:""}&cookieCollectionId=${E}&expiryDays=${e}`,h=await o(u,{withCredentials:!0});return JSON.parse(h.CassieConsent)},l=async function(e,t,n,i,s,r){const a=`${i}/Home/Index?accessKey=${t}&domain=${n}${s?`&userProfile=${s}`:""}&cookieCollectionId=${r}&expiryDays=${e}`;return await o(a,{withCredentials:!0})},d=async function(e,t,n){const i=`${n}/Home/CdcPropagate?accessKey=${e}&domain=${t}`;return await o(i,{withCredentials:!0})},S=function(){return/Trident\/|MSIE/.test(window.navigator.userAgent)};function E(e){return e&&"string"==typeof e&&""!==e.trim()?e.split(".").map((e=>{const t=e.split("-");if(2!==t.length)return null;const n=t[0].match(/s(\d+)c(\d+)/);return n?{FieldID:`s${n[1]}_c${n[2]}`,IsChecked:parseInt(t[1],10)}:null})).filter((e=>null!==e)):[]}function u(e){try{return JSON.parse(e),!0}catch(e){return!1}}var h=class{constructor(e,t=null){this.accessKey=e,this.cookies=this.getAllCookies(),this.defaultCookieExpiry=t??365,this.maxTimeout=2e3}getAllCookies(){for(var e={},t=document.cookie.split(";"),n=1;n<=t.length;n++){var i=t[n-1].split("="),s=i[0],r=i[1];void 0!==s&&s.length>0&&(s=s.trim()),void 0!==r&&r.length>0&&(r=r.trim()),e[s]=r}return e}getCookieValueByName=function(e){const t=`; ${document.cookie}`.split(`; ${e}=`);return 2===t.length?t.pop().split(";").shift().trim():null};sleep(e){return new Promise((t=>setTimeout(t,e)))}getProtectedCookieByName=function(e,t,n=2e3){return new Promise((async i=>{let s=n/100;for(let n=1;n<s;n++){const n=`; ${document.cookie}`.split(`; ${e}=`);if(2===n.length){let s=n.pop().split(";").shift();i({Key:e,Value:s,Expiry:t})}await this.sleep(100)}setTimeout(i(null),n)}))};getFormConsent=function(){let e="SyrenisCookieFormConsent_"+this.accessKey,t=this.getCookieValueByName(e);if(!t)return[];try{return u(t)?JSON.parse(t)??[]:E(t)}catch(e){return[]}};getProtectedCookies=async function(e,t){let n=[],i=[];if(0==e?.length)return i;e.includes(",")?n=e.split(","):n[0]=e.trim();for(let e=0;e<n.length;e++){const r=n[e]?.trim();if(!r)continue;const o=t.find((e=>{let t=e.browserCookieName.trim().toLowerCase(),n=[];return t.includes(",")?n=t.split(",").map((e=>e.trim())):n.push(t),n.includes(r.toLowerCase())}));if(o&&0!=o?.expiry){var s=new Date;s.setDate(s.getDate()+o.expiry),i.push(this.getProtectedCookieByName(r,s.getTime().toString(),this.maxTimeout))}}return i=(await Promise.all(i)).filter((e=>e)),i.filter((e=>e))};recreateProtectedCookies(){try{const e=this.getCookieValueByName("SyrenisCustom_"+this.accessKey);let t=[];t=u(e)?JSON.parse(e):function(e){if(!e||"string"!=typeof e||""===e.trim())return[];let t=null;try{t=function(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");for(;t.length%4!=0;)t+="=";return decodeURIComponent(Array.from(atob(t)).map((e=>`%${e.charCodeAt(0).toString(16).padStart(2,"0")}`)).join(""))}(e)}catch(e){return console.error("Error decoding base64 string:",e),[]}return t.split("|$|").map((e=>{const t=e.split("|*|");return{Key:t[0],Value:t[1],Expiry:t[2]}}))}(e);for(let e=0;e<t.length;e++){const n=t[e];this.getAllCookies()[n.Key]||this.storeCookieWithTimeExpiry(n.Key,n.Value,null,n.Expiry)}}catch(e){}}storeCookie=function(e,t,n,i){i=i??this.defaultCookieExpiry;let s=new Date;s.setTime(s.getTime()+24*i*60*60*1e3);let r="expires="+s.toUTCString();n=n??window.document.domain;let o=S()?"":";domain="+n;try{t=JSON.parse(t)}catch{}let a=t;var c;Array.isArray(t)&&0===t.length||"string"==typeof t&&"[]"===t.trim()?a="":"object"==typeof t&&function(e){if(!e||"string"!=typeof e)return!1;const t=e.toLowerCase();return t.startsWith("syrenis")||t.startsWith("cassie")}(e)?a=Array.isArray(t)&&t.length>0&&t[0].FieldID?(c=t)&&Array.isArray(c)&&0!==c.length?c.map((e=>`${e.FieldID.replace("_","")}-${e.IsChecked}`)).join("."):"":Array.isArray(t)&&t.length>0&&t[0].Key?function(e){return e&&Array.isArray(e)&&0!==e.length?(t=e.map((e=>`${e.Key}|*|${e.Value}|*|${e.Expiry}`)).join("|$|"),btoa(Array.from((new TextEncoder).encode(t)).map((e=>String.fromCharCode(e))).join("")).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")):"";var t}(t):e.includes("GtmConsent")?function(e){return e&&Array.isArray(e)&&0!==e.length?e.map((e=>Object.entries(e).map((([e,t])=>`${e}:${t}`)).join("|"))).join("|"):""}(t):JSON.stringify(t):"object"==typeof t&&(a=JSON.stringify(t));let l=e+"="+a+";"+r+o+";path=/";document.cookie=l};storeCookieWithTimeExpiry=function(e,t,n,i){let s=new Date;s.setTime(i);let r=e+"="+t+";expires="+s.toUTCString()+";domain="+(n=n??window.document.domain)+";path=/";document.cookie=r};hasConsent=function(){let e=this.getAllCookies(),t=e["SyrenisCookieFormConsent_"+this.accessKey],n=e["SyrenisGuid_"+this.accessKey];return!(!t||!n)};getVisitGuid=function(){return this.getCookieValueByName("SyrenisGuid_"+this.accessKey)};getConsentDate=function(){let e=this.getCookieValueByName("SyrenisCookieConsentDate_"+this.accessKey);return e||(e=new Date(1970,0,1).getTime()),e};getPrivacyPolicyId=function(){let e=this.getCookieValueByName("SyrenisCookiePrivacyLink_"+this.accessKey);return e&&0!=e.length?e:null};getProtectedCookiesContainer=function(){return this.getCookieValueByName("SyrenisCustom_"+this.accessKey)}};let g,_,T;function O(e){const t=e.split(",").map((e=>e.trim().replace("*","").replace("https://","").replace("http://",""))).filter((e=>(e||e.length>0)&&"all"!==e.trim())).map((e=>e.trim().replace("*","")));return 0===t.length&&t.push(`.${document.domain}`),t}var p,N,I,C,A,P,D=function(e,n){function i(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}))}function s(e){e.sort(((e,t)=>e.executionOrder-t.executionOrder)),e.sort(((e,t)=>t.isRunFirst-e.isRunFirst)),e.forEach((e=>{e.isStrictlyNecessary||(e.isOptIn?(t(e.bodyOptIn,!1),t(e.headOptIn,!0)):(t(e.bodyOptOut,!1),t(e.headOptOut,!0)))}))}function r(e){for(const[t,n]of Object.entries(e))O(_.supportedDomains).forEach((e=>{g.storeCookie(t.replace("Cassie","Syrenis"),n,e)}))}function o(e,t){e.forEach((e=>{const n=t.find((t=>t.channelId==e.channelId));e.isStrictlyNecessary?(e.hasConsent=!0,e.isOptIn=!0):n&&(e.hasConsent=!0,e.isOptIn=n.isOptIn)})),s(e.filter((e=>e.hasConsent)))}return _=e,T=n,g=new h(_.profileSettings.AccessKey,T),{runStrictlyNecessaryScripts:function(e){e.sort(((e,t)=>e.executionOrder-t.executionOrder)),e.sort(((e,t)=>t.isRunFirst-e.isRunFirst)),e.forEach((e=>{e.isStrictlyNecessary&&(t(e.bodyOptIn,!1),t(e.headOptIn,!0))}))},runOptInScripts:s,placeSyrenisCookies:r,processConsent:async function(e,t,n){const s=_.profileSettings.AccessKey,a=e.map((e=>({FieldID:`s${e.statementId}_c${e.channelId}`,IsChecked:e.isOptIn?1:0})));this.runStrictlyNecessaryScripts(t),o(t,e);const l=await g.getProtectedCookies(n.persistCookies,t);_.identityServiceUrl?r(await c(T,s,JSON.stringify([]),JSON.stringify(a),JSON.stringify([]),JSON.stringify([]),JSON.stringify(l),n.privacyId,_.identityServiceUrl,_.supportedDomains,_.crossDomainConsent,_?.profileSettings?.CookieCollectionId??1)):function(e,t){let n=_.profileSettings.AccessKey,s=O(_.supportedDomains);var r=i(),o=(new Date).getTime();s.forEach((i=>{null===g.getCookieValueByName("SyrenisGuid_"+n)&&g.storeCookie("SyrenisGuid_"+n,r,i),g.storeCookie("SyrenisCookieFormConsent_"+n,e,i),g.storeCookie("SyrenisCookiePrivacyLink_"+n,t,i),g.storeCookie("SyrenisCookieConsentDate_"+n,o,i)}))}(a,n.privacyId)},runConsent:async function(e){let t=_?.profileSettings?.CookieCollectionId??1;if(_.identityServiceUrl&&await async function(e){let t=(!g.hasConsent()||_.crossDomainConsent)&&await l(T,_.profileSettings.AccessKey,_.supportedDomains,_.identityServiceUrl,_.crossDomainConsent,e);if(null!==t){const e=JSON.parse(t.CassieConsent),n=t.Restored,i="boolean"!=typeof e&&e;if(i&&r(i),!1===n&&_.crossDomainConsent){const e=await d(_.profileSettings.AccessKey,_.supportedDomains,_.identityServiceUrl);document.getElementsByTagName("body")[0].insertAdjacentHTML("beforeend",e)}}}(t),!g.hasConsent())return;const n=g.getCookieValueByName("SyrenisCookieFormConsent_"+_.profileSettings.AccessKey);let i;try{i=u(n)?JSON.parse(n):E(n)}catch(e){return void console.error("Error parsing cookie consent:",e)}let s=i.map((e=>{let t=e.FieldID.indexOf("c")+1;return{channelId:e.FieldID.substring(t,e.FieldID.length),isOptIn:1==e.IsChecked}}));g.recreateProtectedCookies(),this.runStrictlyNecessaryScripts(e),o(e,s)},getCurrentConsent:function(){let e=g.getFormConsent(),t=[];for(let n=0;n<e.length;n++){const i=e[n],s=i.FieldID.indexOf("_"),r=i.FieldID.substring(1,s),o=i.FieldID.substr(s+2),a=1==i.IsChecked;t.push({statementId:r,channelId:o,isOptIn:a})}return t},postConsentToCassie:async function(e,t,n,s){return new Promise((async(r,o)=>{let c={CookieFormID:_?.profileSettings?.WidgetId??null,LicenseID:_?.licenseKey,DivID:s,Preferences:g.getFormConsent(),appCodeName:navigator.appCodeName,appName:navigator.appName,appVersion:navigator.appVersion,cookieEnabled:navigator.cookieEnabled,geolocation:"",language:navigator.language,platform:navigator.platform,referrer:document.referrer.replace(/&/g,"%26"),submissionSource:t??"undefined_source",visitGUID:g.getVisitGuid()??i(),WebsiteURL:document.URL.replace(/&/g,"%26"),PrivacyPolicyID:n,custom1stPartyData:g.getProtectedCookiesContainer()};try{return r(await a(e,c,{timeout:5e3}))}catch(e){return o(e)}}))},dropStrictlyNecessaryCookies:function(e,t){let n=_.profileSettings.AccessKey,s=O(_.supportedDomains);var r=i(),o=(new Date).getTime();s.forEach((t=>{null===g.getCookieValueByName("SyrenisGuid_"+n)&&g.storeCookie("SyrenisGuid_"+n,r,t),g.storeCookie("SyrenisCookiePrivacyLink_"+n,e,t),g.storeCookie("SyrenisCookieConsentDate_"+n,o,t)})),this.runStrictlyNecessaryScripts(t)},saveGtmConsent:function(e){let t=_.profileSettings.AccessKey,n=O(_.supportedDomains),i=e;n.forEach((e=>{g.storeCookie("SyrenisGtmConsent_"+t,i,e)}))}}},w={emit:function(e,t){!function(e,t){const n=t||{bubbles:!0,cancelable:!1},i=document.createEvent("CustomEvent");i.initCustomEvent(e,n.bubbles,n.cancelable,n.detail),document.dispatchEvent(i)}(e,{detail:t,bubbles:!0,cancelable:!0})}};class f{gppVersion;cmpStatus;cmpDisplayStatus;signalStatus;supportedAPIs;cmpId;sectionList;applicableSections;gppString;parsedSections;constructor(e){this.gppVersion=e.gppVersion,this.cmpStatus=e.cmpStatus,this.cmpDisplayStatus=e.cmpDisplayStatus,this.signalStatus=e.signalStatus,this.supportedAPIs=e.supportedAPIs,this.cmpId=e.cmpId,this.sectionList=e.gppModel.getSectionIds(),this.applicableSections=e.applicableSections,this.gppString=e.gppModel.encode(),this.parsedSections=e.gppModel.toObject()}}class m{callback;parameter;success=!0;cmpApiContext;constructor(e,t,n){this.cmpApiContext=e,Object.assign(this,{callback:t,parameter:n})}execute(){try{return this.respond()}catch(e){return this.invokeCallback(null),null}}invokeCallback(e){const t=null!==e;this.callback&&this.callback(e,t)}}class V extends m{respond(){let e=new f(this.cmpApiContext);this.invokeCallback(e)}}class R extends m{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>.<field> parameter required");let e=this.parameter.split(".");if(2!=e.length)throw new Error("Field name must be in the format <section>.<fieldName>");let t=e[0],n=e[1],i=this.cmpApiContext.gppModel.getFieldValue(t,n);this.invokeCallback(i)}}class M extends m{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section> parameter required");let e=null;this.cmpApiContext.gppModel.hasSection(this.parameter)&&(e=this.cmpApiContext.gppModel.getSection(this.parameter)),this.invokeCallback(e)}}class U extends m{respond(){if(!this.parameter||0===this.parameter.length)throw new Error("<section>[.version] parameter required");let e=this.cmpApiContext.gppModel.hasSection(this.parameter);this.invokeCallback(e)}}!function(e){e.ADD_EVENT_LISTENER="addEventListener",e.GET_FIELD="getField",e.GET_SECTION="getSection",e.HAS_SECTION="hasSection",e.PING="ping",e.REMOVE_EVENT_LISTENER="removeEventListener"}(p||(p={}));class G{eventName;listenerId;data;pingData;constructor(e,t,n,i){this.eventName=e,this.listenerId=t,this.data=n,this.pingData=i}}class y extends m{respond(){let e=this.cmpApiContext.eventQueue.add({callback:this.callback,parameter:this.parameter}),t=new G("listenerRegistered",e,!0,new f(this.cmpApiContext));this.invokeCallback(t)}}class b extends m{respond(){let e=this.parameter,t=this.cmpApiContext.eventQueue.remove(e),n=new G("listenerRemoved",e,t,new f(this.cmpApiContext));this.invokeCallback(n)}}class L{static[p.ADD_EVENT_LISTENER]=y;static[p.GET_FIELD]=R;static[p.GET_SECTION]=M;static[p.HAS_SECTION]=U;static[p.PING]=V;static[p.REMOVE_EVENT_LISTENER]=b}!function(e){e.STUB="stub",e.LOADING="loading",e.LOADED="loaded",e.ERROR="error"}(N||(N={})),function(e){e.VISIBLE="visible",e.HIDDEN="hidden",e.DISABLED="disabled"}(I||(I={})),function(e){e.GPP_LOADED="gpploaded",e.CMP_UI_SHOWN="cmpuishown",e.USER_ACTION_COMPLETE="useractioncomplete"}(C||(C={})),function(e){e.NOT_READY="not ready",e.READY="ready"}(A||(A={}));class v{callQueue;customCommands;cmpApiContext;constructor(e,t){if(this.cmpApiContext=e,t){let e=p.ADD_EVENT_LISTENER;if(t?.[e])throw new Error(`Built-In Custom Commmand for ${e} not allowed`);if(e=p.REMOVE_EVENT_LISTENER,t?.[e])throw new Error(`Built-In Custom Commmand for ${e} not allowed`);this.customCommands=t}try{this.callQueue=window.__gpp()||[]}catch(e){this.callQueue=[]}finally{window.__gpp=this.apiCall.bind(this),this.purgeQueuedCalls()}}apiCall(e,t,n,i){if("string"!=typeof e)t(null,!1);else{if(t&&"function"!=typeof t)throw new Error("invalid callback function");this.isCustomCommand(e)?this.customCommands[e](t,n):this.isBuiltInCommand(e)?new L[e](this.cmpApiContext,t,n).execute():t&&t(null,!1)}}purgeQueuedCalls(){const e=this.callQueue;this.callQueue=[],e.forEach((e=>{window.__gpp(...e)}))}isCustomCommand(e){return this.customCommands&&"function"==typeof this.customCommands[e]}isBuiltInCommand(e){return void 0!==L[e]}}class F{eventQueue=new Map;queueNumber=1e3;cmpApiContext;constructor(e){this.cmpApiContext=e;try{let e=window.__gpp("events")||[];for(var t=0;t<e.length;t++){let n=e[t];this.eventQueue.set(n.id,{callback:n.callback,parameter:n.parameter})}}catch(e){console.log(e)}}add(e){return this.eventQueue.set(this.queueNumber,e),this.queueNumber++}get(e){return this.eventQueue.get(e)}remove(e){return this.eventQueue.delete(e)}exec(e,t){this.eventQueue.forEach(((n,i)=>{let s=new G(e,i,t,new f(this.cmpApiContext));n.callback(s,!0)}))}clear(){this.queueNumber=1e3,this.eventQueue.clear()}get size(){return this.eventQueue.size}}class x extends Error{constructor(e){super(e),this.name="InvalidFieldError"}}class k{segments;encodedString=null;dirty=!1;decoded=!0;constructor(){this.segments=this.initializeSegments()}hasField(e){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let t=0;t<this.segments.length;t++){let n=this.segments[t];if(n.getFieldNames().includes(e))return n.hasField(e)}return!1}getFieldValue(e){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let t=0;t<this.segments.length;t++){let n=this.segments[t];if(n.hasField(e))return n.getFieldValue(e)}throw new x("Invalid field: '"+e+"'")}setFieldValue(e,t){this.decoded||(this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!0);for(let n=0;n<this.segments.length;n++){let i=this.segments[n];if(i.hasField(e))return void i.setFieldValue(e,t)}throw new x("Invalid field: '"+e+"'")}toObj(){let e={};for(let t=0;t<this.segments.length;t++){let n=this.segments[t].toObj();for(const[t,i]of Object.entries(n))e[t]=i}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.encodedString=this.encodeSection(this.segments),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.segments=this.decodeSection(this.encodedString),this.dirty=!1,this.decoded=!1}setIsDirty(e){this.dirty=e}}class H extends Error{constructor(e){super(e),this.name="DecodingError"}}class B extends Error{constructor(e){super(e),this.name="EncodingError"}}class K{static encode(e,t){let n=[];if(e>=1)for(n.push(1);e>=2*n[0];)n.unshift(2*n[0]);let i="";for(let t=0;t<n.length;t++){let s=n[t];e>=s?(i+="1",e-=s):i+="0"}if(i.length>t)throw new B("Numeric value '"+e+"' is too large for a bit string length of '"+t+"'");for(;i.length<t;)i="0"+i;return i}static decode(e){if(!/^[0-1]*$/.test(e))throw new H("Undecodable FixedInteger '"+e+"'");let t=0,n=[];for(let t=0;t<e.length;t++)n[e.length-(t+1)]=0===t?1:2*n[e.length-t];for(let i=0;i<e.length;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}class W{static DICT="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";static REVERSE_DICT=new Map([["A",0],["B",1],["C",2],["D",3],["E",4],["F",5],["G",6],["H",7],["I",8],["J",9],["K",10],["L",11],["M",12],["N",13],["O",14],["P",15],["Q",16],["R",17],["S",18],["T",19],["U",20],["V",21],["W",22],["X",23],["Y",24],["Z",25],["a",26],["b",27],["c",28],["d",29],["e",30],["f",31],["g",32],["h",33],["i",34],["j",35],["k",36],["l",37],["m",38],["n",39],["o",40],["p",41],["q",42],["r",43],["s",44],["t",45],["u",46],["v",47],["w",48],["x",49],["y",50],["z",51],["0",52],["1",53],["2",54],["3",55],["4",56],["5",57],["6",58],["7",59],["8",60],["9",61],["-",62],["_",63]]);encode(e){if(!/^[0-1]*$/.test(e))throw new B("Unencodable Base64Url '"+e+"'");e=this.pad(e);let t="",n=0;for(;n<=e.length-6;){let i=e.substring(n,n+6);try{let e=K.decode(i);t+=W.DICT.charAt(e),n+=6}catch(t){throw new B("Unencodable Base64Url '"+e+"'")}}return t}decode(e){if(!/^[A-Za-z0-9\-_]*$/.test(e))throw new H("Undecodable Base64URL string '"+e+"'");let t="";for(let n=0;n<e.length;n++){let i=e.charAt(n),s=W.REVERSE_DICT.get(i);t+=K.encode(s,6)}return t}}class j extends W{static instance=new j;constructor(){super()}static getInstance(){return this.instance}pad(e){for(;e.length%8>0;)e+="0";for(;e.length%6>0;)e+="0";return e}}class z{static instance=new z;constructor(){}static getInstance(){return this.instance}encode(e,t){let n="";for(let i=0;i<t.length;i++){let s=t[i];if(!e.containsKey(s))throw new Error("Field not found: '"+s+"'");n+=e.get(s).encode()}return n}decode(e,t,n){let i=0;for(let s=0;s<t.length;s++){let r=t[s];if(!n.containsKey(r))throw new Error("Field not found: '"+r+"'");{let t=n.get(r);try{let n=t.substring(e,i);t.decode(n),i+=n.length}catch(e){if("SubstringError"!==e.name||t.getHardFailIfMissing())throw new H("Unable to decode field '"+r+"'");return}}}}}class ${static encode(e){let t=[];if(e>=1&&(t.push(1),e>=2)){t.push(2);let n=2;for(;e>=t[n-1]+t[n-2];)t.push(t[n-1]+t[n-2]),n++}let n="1";for(let i=t.length-1;i>=0;i--){let s=t[i];e>=s?(n="1"+n,e-=s):n="0"+n}return n}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<2||e.indexOf("11")!==e.length-2)throw new H("Undecodable FibonacciInteger '"+e+"'");let t=0,n=[];for(let t=0;t<e.length-1;t++)0===t?n.push(1):1===t?n.push(2):n.push(n[t-1]+n[t-2]);for(let i=0;i<e.length-1;i++)"1"===e.charAt(i)&&(t+=n[i]);return t}}class Y{static encode(e){if(!0===e)return"1";if(!1===e)return"0";throw new B("Unencodable Boolean '"+e+"'")}static decode(e){if("1"===e)return!0;if("0"===e)return!1;throw new H("Undecodable Boolean '"+e+"'")}}class q{static encode(e){e=e.sort(((e,t)=>e-t));let t=[],n=0,i=0;for(;i<e.length;){let n=i;for(;n<e.length-1&&e[n]+1===e[n+1];)n++;t.push(e.slice(i,n+1)),i=n+1}let s=K.encode(t.length,12);for(let e=0;e<t.length;e++)if(1==t[e].length){let i=t[e][0]-n;n=t[e][0],s+="0"+$.encode(i)}else{let i=t[e][0]-n;n=t[e][0];let r=t[e][t[e].length-1]-n;n=t[e][t[e].length-1],s+="1"+$.encode(i)+$.encode(r)}return s}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new H("Undecodable FibonacciIntegerRange '"+e+"'");let t=[],n=K.decode(e.substring(0,12)),i=0,s=12;for(let r=0;r<n;r++){let n=Y.decode(e.substring(s,s+1));if(s++,!0===n){let n=e.indexOf("11",s),r=$.decode(e.substring(s,n+2))+i;i=r,s=n+2,n=e.indexOf("11",s);let o=$.decode(e.substring(s,n+2))+i;i=o,s=n+2;for(let e=r;e<=o;e++)t.push(e)}else{let n=e.indexOf("11",s),r=$.decode(e.substring(s,n+2))+i;i=r,t.push(r),s=n+2}}return t}}class J extends Error{constructor(e){super(e),this.name="ValidationError"}}class Q{hardFailIfMissing;validator;value;constructor(e=!0){this.hardFailIfMissing=e}withValidator(e){return this.validator=e,this}hasValue(){return void 0!==this.value&&null!==this.value}getValue(){return this.value}setValue(e){if(this.validator&&!this.validator.test(e))throw new J("Invalid value '"+e+"'");this.value=e}getHardFailIfMissing(){return this.hardFailIfMissing}}class X extends H{constructor(e){super(e),this.name="SubstringError"}}class Z{static substring(e,t,n){if(n>e.length||t<0||t>n)throw new X("Invalid substring indexes "+t+":"+n+" for string of length "+e.length);return e.substring(t,n)}}class ee extends Q{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return q.encode(this.value)}catch(e){throw new B(e)}}decode(e){try{this.value=q.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{let n=K.decode(Z.substring(e,t,t+12)),i=t+12;for(let t=0;t<n;t++)i="1"===e.charAt(i)?e.indexOf("11",e.indexOf("11",i+1)+2)+2:e.indexOf("11",i+1)+2;return Z.substring(e,t,i)}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class te extends Q{bitStringLength;constructor(e,t,n=!0){super(n),this.bitStringLength=e,this.setValue(t)}encode(){try{return K.encode(this.value,this.bitStringLength)}catch(e){throw new B(e)}}decode(e){try{this.value=K.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+this.bitStringLength)}catch(e){throw new X(e)}}}class ne{fields=new Map;containsKey(e){return this.fields.has(e)}put(e,t){this.fields.set(e,t)}get(e){return this.fields.get(e)}getAll(){return new Map(this.fields)}reset(e){this.fields.clear(),e.getAll().forEach(((e,t)=>{this.fields.set(t,e)}))}}!function(e){e.ID="Id",e.VERSION="Version",e.SECTION_IDS="SectionIds"}(P||(P={}));const ie=[P.ID,P.VERSION,P.SECTION_IDS];class se{fields;encodedString=null;dirty=!1;decoded=!0;constructor(){this.fields=this.initializeFields()}validate(){}hasField(e){return this.fields.containsKey(e)}getFieldValue(e){if(this.decoded||(this.decodeSegment(this.encodedString,this.fields),this.dirty=!1,this.decoded=!0),this.fields.containsKey(e))return this.fields.get(e).getValue();throw new x("Invalid field: '"+e+"'")}setFieldValue(e,t){if(this.decoded||(this.decodeSegment(this.encodedString,this.fields),this.dirty=!1,this.decoded=!0),!this.fields.containsKey(e))throw new x(e+" not found");this.fields.get(e).setValue(t),this.dirty=!0}toObj(){let e={},t=this.getFieldNames();for(let n=0;n<t.length;n++){let i=t[n],s=this.getFieldValue(i);e[i]=s}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.validate(),this.encodedString=this.encodeSegment(this.fields),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.dirty=!1,this.decoded=!1}}class re extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ie}initializeFields(){let e=new ne;return e.put(P.ID.toString(),new te(6,oe.ID)),e.put(P.VERSION.toString(),new te(6,oe.VERSION)),e.put(P.SECTION_IDS.toString(),new ee([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode HeaderV1CoreSegment '"+e+"'")}}}class oe extends k{static ID=3;static VERSION=1;static NAME="header";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return oe.ID}getName(){return oe.NAME}getVersion(){return oe.VERSION}initializeSegments(){let e=[];return e.push(new re),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var ae;!function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.POLICY_VERSION="PolicyVersion",e.IS_SERVICE_SPECIFIC="IsServiceSpecific",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_OPTINS="SpecialFeatureOptins",e.PURPOSE_CONSENTS="PurposeConsents",e.PURPOSE_LEGITIMATE_INTERESTS="PurposeLegitimateInterests",e.PURPOSE_ONE_TREATMENT="PurposeOneTreatment",e.PUBLISHER_COUNTRY_CODE="PublisherCountryCode",e.VENDOR_CONSENTS="VendorConsents",e.VENDOR_LEGITIMATE_INTERESTS="VendorLegitimateInterests",e.PUBLISHER_RESTRICTIONS="PublisherRestrictions",e.PUBLISHER_PURPOSES_SEGMENT_TYPE="PublisherPurposesSegmentType",e.PUBLISHER_CONSENTS="PublisherConsents",e.PUBLISHER_LEGITIMATE_INTERESTS="PublisherLegitimateInterests",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.PUBLISHER_CUSTOM_CONSENTS="PublisherCustomConsents",e.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS="PublisherCustomLegitimateInterests",e.VENDORS_ALLOWED_SEGMENT_TYPE="VendorsAllowedSegmentType",e.VENDORS_ALLOWED="VendorsAllowed",e.VENDORS_DISCLOSED_SEGMENT_TYPE="VendorsDisclosedSegmentType",e.VENDORS_DISCLOSED="VendorsDisclosed"}(ae||(ae={}));const ce=[ae.VERSION,ae.CREATED,ae.LAST_UPDATED,ae.CMP_ID,ae.CMP_VERSION,ae.CONSENT_SCREEN,ae.CONSENT_LANGUAGE,ae.VENDOR_LIST_VERSION,ae.POLICY_VERSION,ae.IS_SERVICE_SPECIFIC,ae.USE_NON_STANDARD_STACKS,ae.SPECIAL_FEATURE_OPTINS,ae.PURPOSE_CONSENTS,ae.PURPOSE_LEGITIMATE_INTERESTS,ae.PURPOSE_ONE_TREATMENT,ae.PUBLISHER_COUNTRY_CODE,ae.VENDOR_CONSENTS,ae.VENDOR_LEGITIMATE_INTERESTS,ae.PUBLISHER_RESTRICTIONS],le=[ae.PUBLISHER_PURPOSES_SEGMENT_TYPE,ae.PUBLISHER_CONSENTS,ae.PUBLISHER_LEGITIMATE_INTERESTS,ae.NUM_CUSTOM_PURPOSES,ae.PUBLISHER_CUSTOM_CONSENTS,ae.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS],de=[ae.VENDORS_ALLOWED_SEGMENT_TYPE,ae.VENDORS_ALLOWED],Se=[ae.VENDORS_DISCLOSED_SEGMENT_TYPE,ae.VENDORS_DISCLOSED];class Ee extends W{static instance=new Ee;constructor(){super()}static getInstance(){return this.instance}pad(e){for(;e.length%24>0;)e+="0";return e}}class ue{static encode(e){e.sort(((e,t)=>e-t));let t=[],n=0;for(;n<e.length;){let i=n;for(;i<e.length-1&&e[i]+1===e[i+1];)i++;t.push(e.slice(n,i+1)),n=i+1}let i=K.encode(t.length,12);for(let e=0;e<t.length;e++)1===t[e].length?i+="0"+K.encode(t[e][0],16):i+="1"+K.encode(t[e][0],16)+K.encode(t[e][t[e].length-1],16);return i}static decode(e){if(!/^[0-1]*$/.test(e)||e.length<12)throw new H("Undecodable FixedIntegerRange '"+e+"'");let t=[],n=K.decode(e.substring(0,12)),i=12;for(let s=0;s<n;s++){let n=Y.decode(e.substring(i,i+1));if(i++,!0===n){let n=K.decode(e.substring(i,i+16));i+=16;let s=K.decode(e.substring(i,i+16));i+=16;for(let e=n;e<=s;e++)t.push(e)}else{let n=K.decode(e.substring(i,i+16));t.push(n),i+=16}}return t}}class he extends Q{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return ue.encode(this.value)}catch(e){throw new B(e)}}decode(e){try{this.value=ue.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{let n=K.decode(Z.substring(e,t,t+12)),i=t+12;for(let t=0;t<n;t++)"1"===e.charAt(i)?i+=33:i+=17;return Z.substring(e,t,i)}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class ge{key;type;ids;constructor(e,t,n){this.key=e,this.type=t,this.ids=n}getKey(){return this.key}setKey(e){this.key=e}getType(){return this.type}setType(e){this.type=e}getIds(){return this.ids}setIds(e){this.ids=e}}class _e extends Q{keyBitStringLength;typeBitStringLength;constructor(e,t,n,i=!0){super(i),this.keyBitStringLength=e,this.typeBitStringLength=t,this.setValue(n)}encode(){try{let e=this.value,t="";t+=K.encode(e.length,12);for(let n=0;n<e.length;n++){let i=e[n];t+=K.encode(i.getKey(),this.keyBitStringLength),t+=K.encode(i.getType(),this.typeBitStringLength),t+=ue.encode(i.getIds())}return t}catch(e){throw new B(e)}}decode(e){try{let t=[],n=K.decode(Z.substring(e,0,12)),i=12;for(let s=0;s<n;s++){let n=K.decode(Z.substring(e,i,i+this.keyBitStringLength));i+=this.keyBitStringLength;let s=K.decode(Z.substring(e,i,i+this.typeBitStringLength));i+=this.typeBitStringLength;let r=new he([]).substring(e,i),o=ue.decode(r);i+=r.length,t.push(new ge(n,s,o))}this.value=t}catch(e){throw new H(e)}}substring(e,t){try{let n="";n+=Z.substring(e,t,t+12);let i=K.decode(n.toString()),s=t+n.length;for(let t=0;t<i;t++){let t=Z.substring(e,s,s+this.keyBitStringLength);s+=t.length,n+=t;let i=Z.substring(e,s,s+this.typeBitStringLength);s+=i.length,n+=i;let r=new he([]).substring(e,s);s+=r.length,n+=r}return n}catch(e){throw new X(e)}}}class Te extends Q{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return Y.encode(this.value)}catch(e){throw new B(e)}}decode(e){try{this.value=Y.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+1)}catch(e){throw new X(e)}}}class Oe{static encode(e){return e?K.encode(Math.round(e.getTime()/100),36):K.encode(0,36)}static decode(e){if(!/^[0-1]*$/.test(e)||36!==e.length)throw new H("Undecodable Datetime '"+e+"'");return new Date(100*K.decode(e))}}class pe extends Q{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{return Oe.encode(this.value)}catch(e){throw new B(e)}}decode(e){try{this.value=Oe.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+36)}catch(e){throw new X(e)}}}class Ne{static encode(e,t){if(e.length>t)throw new B("Too many values '"+e.length+"'");let n="";for(let t=0;t<e.length;t++)n+=Y.encode(e[t]);for(;n.length<t;)n+="0";return n}static decode(e){if(!/^[0-1]*$/.test(e))throw new H("Undecodable FixedBitfield '"+e+"'");let t=[];for(let n=0;n<e.length;n++)t.push(Y.decode(e.substring(n,n+1)));return t}}class Ie extends Q{numElements;constructor(e,t=!0){super(t),this.numElements=e.length,this.setValue(e)}encode(){try{return Ne.encode(this.value,this.numElements)}catch(e){throw new B(e)}}decode(e){try{this.value=Ne.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+this.numElements)}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=[...e];for(let e=t.length;e<this.numElements;e++)t.push(!1);t.length>this.numElements&&(t=t.slice(0,this.numElements)),super.setValue(t)}}class Ce{static encode(e,t){for(;e.length<t;)e+=" ";let n="";for(let t=0;t<e.length;t++){let i=e.charCodeAt(t);if(32===i)n+=K.encode(63,6);else{if(!(i>=65))throw new B("Unencodable FixedString '"+e+"'");n+=K.encode(e.charCodeAt(t)-65,6)}}return n}static decode(e){if(!/^[0-1]*$/.test(e)||e.length%6!=0)throw new H("Undecodable FixedString '"+e+"'");let t="";for(let n=0;n<e.length;n+=6){let i=K.decode(e.substring(n,n+6));t+=63===i?" ":String.fromCharCode(i+65)}return t.trim()}}class Ae extends Q{stringLength;constructor(e,t,n=!0){super(n),this.stringLength=e,this.setValue(t)}encode(){try{return Ce.encode(this.value,this.stringLength)}catch(e){throw new B(e)}}decode(e){try{this.value=Ce.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+6*this.stringLength)}catch(e){throw new X(e)}}}class Pe extends Q{constructor(e,t=!0){super(t),this.setValue(e)}encode(){try{let e=this.value.length>0?this.value[this.value.length-1]:0,t=ue.encode(this.value),n=t.length,i=e;if(n<=i)return K.encode(e,16)+"1"+t;{let t=[],n=0;for(let i=0;i<e;i++)i===this.value[n]-1?(t[i]=!0,n++):t[i]=!1;return K.encode(e,16)+"0"+Ne.encode(t,i)}}catch(e){throw new B(e)}}decode(e){try{if("1"===e.charAt(16))this.value=ue.decode(e.substring(17));else{let t=[],n=Ne.decode(e.substring(17));for(let e=0;e<n.length;e++)!0===n[e]&&t.push(e+1);this.value=t}}catch(e){throw new H(e)}}substring(e,t){try{let n=K.decode(Z.substring(e,t,t+16));return"1"===e.charAt(t+16)?Z.substring(e,t,t+17)+new he([]).substring(e,t+17):Z.substring(e,t,t+17+n)}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){super.setValue(Array.from(new Set(e)).sort(((e,t)=>e-t)))}}class De extends se{base64UrlEncoder=Ee.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ce}initializeFields(){let e=new Date,t=new ne;return t.put(ae.VERSION.toString(),new te(6,Re.VERSION)),t.put(ae.CREATED.toString(),new pe(e)),t.put(ae.LAST_UPDATED.toString(),new pe(e)),t.put(ae.CMP_ID.toString(),new te(12,0)),t.put(ae.CMP_VERSION.toString(),new te(12,0)),t.put(ae.CONSENT_SCREEN.toString(),new te(6,0)),t.put(ae.CONSENT_LANGUAGE.toString(),new Ae(2,"EN")),t.put(ae.VENDOR_LIST_VERSION.toString(),new te(12,0)),t.put(ae.POLICY_VERSION.toString(),new te(6,2)),t.put(ae.IS_SERVICE_SPECIFIC.toString(),new Te(!1)),t.put(ae.USE_NON_STANDARD_STACKS.toString(),new Te(!1)),t.put(ae.SPECIAL_FEATURE_OPTINS.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ae.PURPOSE_CONSENTS.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ae.PURPOSE_LEGITIMATE_INTERESTS.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(ae.PURPOSE_ONE_TREATMENT.toString(),new Te(!1)),t.put(ae.PUBLISHER_COUNTRY_CODE.toString(),new Ae(2,"AA")),t.put(ae.VENDOR_CONSENTS.toString(),new Pe([])),t.put(ae.VENDOR_LEGITIMATE_INTERESTS.toString(),new Pe([])),t.put(ae.PUBLISHER_RESTRICTIONS.toString(),new _e(6,2,[],!1)),t}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfEuV2CoreSegment '"+e+"'")}}}class we extends Q{getLength;constructor(e,t,n=!0){super(n),this.getLength=e,this.setValue(t)}encode(){try{return Ne.encode(this.value,this.getLength())}catch(e){throw new B(e)}}decode(e){try{this.value=Ne.decode(e)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+this.getLength())}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=this.getLength(),n=[...e];for(let e=n.length;e<t;e++)n.push(!1);n.length>t&&(n=n.slice(0,t)),super.setValue([...n])}}class fe extends se{base64UrlEncoder=Ee.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return le}initializeFields(){let e=new ne;e.put(ae.PUBLISHER_PURPOSES_SEGMENT_TYPE.toString(),new te(3,3)),e.put(ae.PUBLISHER_CONSENTS.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),e.put(ae.PUBLISHER_LEGITIMATE_INTERESTS.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));let t=new te(6,0);return e.put(ae.NUM_CUSTOM_PURPOSES.toString(),t),e.put(ae.PUBLISHER_CUSTOM_CONSENTS.toString(),new we((()=>t.getValue()),[])),e.put(ae.PUBLISHER_CUSTOM_LEGITIMATE_INTERESTS.toString(),new we((()=>t.getValue()),[])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfEuV2PublisherPurposesSegment '"+e+"'")}}}class me extends se{base64UrlEncoder=Ee.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return de}initializeFields(){let e=new ne;return e.put(ae.VENDORS_ALLOWED_SEGMENT_TYPE.toString(),new te(3,2)),e.put(ae.VENDORS_ALLOWED.toString(),new Pe([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfEuV2VendorsAllowedSegment '"+e+"'")}}}class Ve extends se{base64UrlEncoder=Ee.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Se}initializeFields(){let e=new ne;return e.put(ae.VENDORS_DISCLOSED_SEGMENT_TYPE.toString(),new te(3,1)),e.put(ae.VENDORS_DISCLOSED.toString(),new Pe([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfEuV2VendorsDisclosedSegment '"+e+"'")}}}class Re extends k{static ID=2;static VERSION=2;static NAME="tcfeuv2";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Re.ID}getName(){return Re.NAME}getVersion(){return Re.VERSION}initializeSegments(){let e=[];return e.push(new De),e.push(new fe),e.push(new me),e.push(new Ve),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<n.length;e++){let i=n[e];if(0!==i.length){let s=i.charAt(0);if(s>="A"&&s<="H")t[0].decode(n[e]);else if(s>="I"&&s<="P")t[3].decode(n[e]);else if(s>="Q"&&s<="X")t[2].decode(n[e]);else{if(!(s>="Y"&&s<="Z"||s>="a"&&s<="f"))throw new H("Unable to decode TcfEuV2 segment '"+i+"'");t[1].decode(n[e])}}}}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),this.getFieldValue(ae.IS_SERVICE_SPECIFIC)?e.length>=2&&t.push(e[1].encode()):e.length>=2&&(t.push(e[2].encode()),e.length>=3&&t.push(e[3].encode()))),t.join(".")}setFieldValue(e,t){if(super.setFieldValue(e,t),e!==ae.CREATED&&e!==ae.LAST_UPDATED){let e=new Date;super.setFieldValue(ae.CREATED,e),super.setFieldValue(ae.LAST_UPDATED,e)}}}var Me;!function(e){e.VERSION="Version",e.CREATED="Created",e.LAST_UPDATED="LastUpdated",e.CMP_ID="CmpId",e.CMP_VERSION="CmpVersion",e.CONSENT_SCREEN="ConsentScreen",e.CONSENT_LANGUAGE="ConsentLanguage",e.VENDOR_LIST_VERSION="VendorListVersion",e.TCF_POLICY_VERSION="TcfPolicyVersion",e.USE_NON_STANDARD_STACKS="UseNonStandardStacks",e.SPECIAL_FEATURE_EXPRESS_CONSENT="SpecialFeatureExpressConsent",e.PUB_PURPOSES_SEGMENT_TYPE="PubPurposesSegmentType",e.PURPOSES_EXPRESS_CONSENT="PurposesExpressConsent",e.PURPOSES_IMPLIED_CONSENT="PurposesImpliedConsent",e.VENDOR_EXPRESS_CONSENT="VendorExpressConsent",e.VENDOR_IMPLIED_CONSENT="VendorImpliedConsent",e.PUB_RESTRICTIONS="PubRestrictions",e.PUB_PURPOSES_EXPRESS_CONSENT="PubPurposesExpressConsent",e.PUB_PURPOSES_IMPLIED_CONSENT="PubPurposesImpliedConsent",e.NUM_CUSTOM_PURPOSES="NumCustomPurposes",e.CUSTOM_PURPOSES_EXPRESS_CONSENT="CustomPurposesExpressConsent",e.CUSTOM_PURPOSES_IMPLIED_CONSENT="CustomPurposesImpliedConsent",e.DISCLOSED_VENDORS_SEGMENT_TYPE="DisclosedVendorsSegmentType",e.DISCLOSED_VENDORS="DisclosedVendors"}(Me||(Me={}));const Ue=[Me.VERSION,Me.CREATED,Me.LAST_UPDATED,Me.CMP_ID,Me.CMP_VERSION,Me.CONSENT_SCREEN,Me.CONSENT_LANGUAGE,Me.VENDOR_LIST_VERSION,Me.TCF_POLICY_VERSION,Me.USE_NON_STANDARD_STACKS,Me.SPECIAL_FEATURE_EXPRESS_CONSENT,Me.PURPOSES_EXPRESS_CONSENT,Me.PURPOSES_IMPLIED_CONSENT,Me.VENDOR_EXPRESS_CONSENT,Me.VENDOR_IMPLIED_CONSENT,Me.PUB_RESTRICTIONS],Ge=[Me.PUB_PURPOSES_SEGMENT_TYPE,Me.PUB_PURPOSES_EXPRESS_CONSENT,Me.PUB_PURPOSES_IMPLIED_CONSENT,Me.NUM_CUSTOM_PURPOSES,Me.CUSTOM_PURPOSES_EXPRESS_CONSENT,Me.CUSTOM_PURPOSES_IMPLIED_CONSENT],ye=[Me.DISCLOSED_VENDORS_SEGMENT_TYPE,Me.DISCLOSED_VENDORS];class be extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ue}initializeFields(){let e=new Date,t=new ne;return t.put(Me.VERSION.toString(),new te(6,Fe.VERSION)),t.put(Me.CREATED.toString(),new pe(e)),t.put(Me.LAST_UPDATED.toString(),new pe(e)),t.put(Me.CMP_ID.toString(),new te(12,0)),t.put(Me.CMP_VERSION.toString(),new te(12,0)),t.put(Me.CONSENT_SCREEN.toString(),new te(6,0)),t.put(Me.CONSENT_LANGUAGE.toString(),new Ae(2,"EN")),t.put(Me.VENDOR_LIST_VERSION.toString(),new te(12,0)),t.put(Me.TCF_POLICY_VERSION.toString(),new te(6,2)),t.put(Me.USE_NON_STANDARD_STACKS.toString(),new Te(!1)),t.put(Me.SPECIAL_FEATURE_EXPRESS_CONSENT.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Me.PURPOSES_EXPRESS_CONSENT.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Me.PURPOSES_IMPLIED_CONSENT.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),t.put(Me.VENDOR_EXPRESS_CONSENT.toString(),new Pe([])),t.put(Me.VENDOR_IMPLIED_CONSENT.toString(),new Pe([])),t.put(Me.PUB_RESTRICTIONS.toString(),new _e(6,2,[],!1)),t}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfCaV1CoreSegment '"+e+"'")}}}class Le extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ge}initializeFields(){let e=new ne;e.put(Me.PUB_PURPOSES_SEGMENT_TYPE.toString(),new te(3,3)),e.put(Me.PUB_PURPOSES_EXPRESS_CONSENT.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1])),e.put(Me.PUB_PURPOSES_IMPLIED_CONSENT.toString(),new Ie([!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1,!1]));let t=new te(6,0);return e.put(Me.NUM_CUSTOM_PURPOSES.toString(),t),e.put(Me.CUSTOM_PURPOSES_EXPRESS_CONSENT.toString(),new we((()=>t.getValue()),[])),e.put(Me.CUSTOM_PURPOSES_IMPLIED_CONSENT.toString(),new we((()=>t.getValue()),[])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode TcfCaV1PublisherPurposesSegment '"+e+"'")}}}class ve extends se{base64UrlEncoder=Ee.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ye}initializeFields(){let e=new ne;return e.put(Me.DISCLOSED_VENDORS_SEGMENT_TYPE.toString(),new te(3,1)),e.put(Me.DISCLOSED_VENDORS.toString(),new Pe([])),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode HeaderV1CoreSegment '"+e+"'")}}}class Fe extends k{static ID=5;static VERSION=1;static NAME="tcfcav1";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Fe.ID}getName(){return Fe.NAME}getVersion(){return Fe.VERSION}initializeSegments(){let e=[];return e.push(new be),e.push(new Le),e.push(new ve),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<n.length;e++){let i=n[e];if(0!==i.length){let s=i.charAt(0);if(s>="A"&&s<="H")t[0].decode(n[e]);else if(s>="I"&&s<="P")t[2].decode(n[e]);else{if(!(s>="Y"&&s<="Z"||s>="a"&&s<="f"))throw new H("Unable to decode TcfCaV1 segment '"+i+"'");t[1].decode(n[e])}}}}return t}encodeSection(e){let t=[];return t.push(e[0].encode()),t.push(e[1].encode()),this.getFieldValue(Me.DISCLOSED_VENDORS).length>0&&t.push(e[2].encode()),t.join(".")}setFieldValue(e,t){if(super.setFieldValue(e,t),e!==Me.CREATED&&e!==Me.LAST_UPDATED){let e=new Date;super.setFieldValue(Me.CREATED,e),super.setFieldValue(Me.LAST_UPDATED,e)}}}class xe{validator;value=null;constructor(e,t){this.validator=t||new class{test(e){return!0}},this.setValue(e)}hasValue(){return null!=this.value}getValue(){return this.value}setValue(e){e?this.value=e.charAt(0):e=null}}class ke{validator;value=null;constructor(e,t){this.validator=t||new class{test(e){return!0}},this.setValue(e)}hasValue(){return null!=this.value}getValue(){return this.value}setValue(e){this.value=e}}class He{fields=new Map;containsKey(e){return this.fields.has(e)}put(e,t){this.fields.set(e,t)}get(e){return this.fields.get(e)}getAll(){return new Map(this.fields)}reset(e){this.fields.clear(),e.getAll().forEach(((e,t)=>{this.fields.set(t,e)}))}}var Be;!function(e){e.VERSION="Version",e.NOTICE="Notice",e.OPT_OUT_SALE="OptOutSale",e.LSPA_COVERED="LspaCovered"}(Be||(Be={}));const Ke=[Be.VERSION,Be.NOTICE,Be.OPT_OUT_SALE,Be.LSPA_COVERED];class We extends se{constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ke}initializeFields(){const e=new class{test(e){return"-"===e||"Y"===e||"N"===e}};let t=new He;return t.put(Be.VERSION,new ke(je.VERSION)),t.put(Be.NOTICE,new xe("-",e)),t.put(Be.OPT_OUT_SALE,new xe("-",e)),t.put(Be.LSPA_COVERED,new xe("-",e)),t}encodeSegment(e){let t="";return t+=e.get(Be.VERSION).getValue(),t+=e.get(Be.NOTICE).getValue(),t+=e.get(Be.OPT_OUT_SALE).getValue(),t+=e.get(Be.LSPA_COVERED).getValue(),t}decodeSegment(e,t){if(null==e||4!=e.length)throw new H("Unable to decode UspV1CoreSegment '"+e+"'");try{t.get(Be.VERSION).setValue(parseInt(e.substring(0,1))),t.get(Be.NOTICE).setValue(e.charAt(1)),t.get(Be.OPT_OUT_SALE).setValue(e.charAt(2)),t.get(Be.LSPA_COVERED).setValue(e.charAt(3))}catch(t){throw new H("Unable to decode UspV1CoreSegment '"+e+"'")}}}class je extends k{static ID=6;static VERSION=1;static NAME="uspv1";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return je.ID}getName(){return je.NAME}getVersion(){return je.VERSION}initializeSegments(){let e=[];return e.push(new We),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var ze;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(ze||(ze={}));const $e=[ze.VERSION,ze.SHARING_NOTICE,ze.SALE_OPT_OUT_NOTICE,ze.SHARING_OPT_OUT_NOTICE,ze.TARGETED_ADVERTISING_OPT_OUT_NOTICE,ze.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE,ze.SENSITIVE_DATA_LIMIT_USE_NOTICE,ze.SALE_OPT_OUT,ze.SHARING_OPT_OUT,ze.TARGETED_ADVERTISING_OPT_OUT,ze.SENSITIVE_DATA_PROCESSING,ze.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,ze.PERSONAL_DATA_CONSENTS,ze.MSPA_COVERED_TRANSACTION,ze.MSPA_OPT_OUT_OPTION_MODE,ze.MSPA_SERVICE_PROVIDER_MODE],Ye=[ze.GPC_SEGMENT_TYPE,ze.GPC];class qe{static encode(e,t,n){if(e.length>n)throw new B("Too many values '"+e.length+"'");let i="";for(let n=0;n<e.length;n++)i+=K.encode(e[n],t);for(;i.length<t*n;)i+="0";return i}static decode(e,t,n){if(!/^[0-1]*$/.test(e))throw new H("Undecodable FixedInteger '"+e+"'");if(e.length>t*n)throw new H("Undecodable FixedIntegerList '"+e+"'");if(e.length%t!=0)throw new H("Undecodable FixedIntegerList '"+e+"'");for(;e.length<t*n;)e+="0";e.length>t*n&&(e=e.substring(0,t*n));let i=[];for(let n=0;n<e.length;n+=t)i.push(K.decode(e.substring(n,n+t)));for(;i.length<n;)i.push(0);return i}}class Je extends Q{elementBitStringLength;numElements;constructor(e,t,n=!0){super(n),this.elementBitStringLength=e,this.numElements=t.length,this.setValue(t)}encode(){try{return qe.encode(this.value,this.elementBitStringLength,this.numElements)}catch(e){throw new B(e)}}decode(e){try{this.value=qe.decode(e,this.elementBitStringLength,this.numElements)}catch(e){throw new H(e)}}substring(e,t){try{return Z.substring(e,t,t+this.elementBitStringLength*this.numElements)}catch(e){throw new X(e)}}getValue(){return[...super.getValue()]}setValue(e){let t=[...e];for(let e=t.length;e<this.numElements;e++)t.push(0);t.length>this.numElements&&(t=t.slice(0,this.numElements)),super.setValue(t)}}class Qe extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return $e}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(ze.VERSION.toString(),new te(6,Ze.VERSION)),i.put(ze.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.SHARING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ze.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(ze.SHARING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(ze.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(ze.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(ze.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(ze.PERSONAL_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(ze.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(ze.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(ze.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);66==n.length&&(n=n.substring(0,48)+"00000000"+n.substring(48,52)+"00"+n.substring(52,62)),this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNatCoreSegment '"+e+"'")}}}class Xe extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ye}initializeFields(){let e=new ne;return e.put(ze.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(ze.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(ze.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNatGpcSegment '"+e+"'")}}}class Ze extends k{static ID=7;static VERSION=1;static NAME="usnat";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Ze.ID}getName(){return Ze.NAME}getVersion(){return Ze.VERSION}initializeSegments(){let e=[];return e.push(new Qe),e.push(new Xe),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(ze.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(ze.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(ze.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var et;!function(e){e.VERSION="Version",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.SHARING_OPT_OUT_NOTICE="SharingOptOutNotice",e.SENSITIVE_DATA_LIMIT_USE_NOTICE="SensitiveDataLimitUseNotice",e.SALE_OPT_OUT="SaleOptOut",e.SHARING_OPT_OUT="SharingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.PERSONAL_DATA_CONSENTS="PersonalDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(et||(et={}));const tt=[et.VERSION,et.SALE_OPT_OUT_NOTICE,et.SHARING_OPT_OUT_NOTICE,et.SENSITIVE_DATA_LIMIT_USE_NOTICE,et.SALE_OPT_OUT,et.SHARING_OPT_OUT,et.SENSITIVE_DATA_PROCESSING,et.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,et.PERSONAL_DATA_CONSENTS,et.MSPA_COVERED_TRANSACTION,et.MSPA_OPT_OUT_OPTION_MODE,et.MSPA_SERVICE_PROVIDER_MODE],nt=[et.GPC_SEGMENT_TYPE,et.GPC];class it extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return tt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(et.VERSION.toString(),new te(6,rt.VERSION)),i.put(et.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(et.SHARING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(et.SENSITIVE_DATA_LIMIT_USE_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(et.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(et.SHARING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(et.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(et.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0]).withValidator(n)),i.put(et.PERSONAL_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(et.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(et.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(et.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCaCoreSegment '"+e+"'")}}}class st extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return nt}initializeFields(){let e=new ne;return e.put(et.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(et.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(et.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCaGpcSegment '"+e+"'")}}}class rt extends k{static ID=8;static VERSION=1;static NAME="usca";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return rt.ID}getName(){return rt.NAME}getVersion(){return rt.VERSION}initializeSegments(){let e=[];return e.push(new it),e.push(new st),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(et.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(et.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(et.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var ot;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(ot||(ot={}));const at=[ot.VERSION,ot.SHARING_NOTICE,ot.SALE_OPT_OUT_NOTICE,ot.TARGETED_ADVERTISING_OPT_OUT_NOTICE,ot.SALE_OPT_OUT,ot.TARGETED_ADVERTISING_OPT_OUT,ot.SENSITIVE_DATA_PROCESSING,ot.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,ot.MSPA_COVERED_TRANSACTION,ot.MSPA_OPT_OUT_OPTION_MODE,ot.MSPA_SERVICE_PROVIDER_MODE];class ct extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return at}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(ot.VERSION.toString(),new te(6,lt.VERSION)),i.put(ot.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ot.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ot.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(ot.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(ot.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(ot.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(ot.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(ot.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(ot.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(ot.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsVaCoreSegment '"+e+"'")}}}class lt extends k{static ID=9;static VERSION=1;static NAME="usva";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return lt.ID}getName(){return lt.NAME}getVersion(){return lt.VERSION}initializeSegments(){let e=[];return e.push(new ct),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var dt;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(dt||(dt={}));const St=[dt.VERSION,dt.SHARING_NOTICE,dt.SALE_OPT_OUT_NOTICE,dt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,dt.SALE_OPT_OUT,dt.TARGETED_ADVERTISING_OPT_OUT,dt.SENSITIVE_DATA_PROCESSING,dt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,dt.MSPA_COVERED_TRANSACTION,dt.MSPA_OPT_OUT_OPTION_MODE,dt.MSPA_SERVICE_PROVIDER_MODE],Et=[dt.GPC_SEGMENT_TYPE,dt.GPC];class ut extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return St}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(dt.VERSION.toString(),new te(6,gt.VERSION)),i.put(dt.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(dt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(dt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(dt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(dt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(dt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0]).withValidator(n)),i.put(dt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(dt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(dt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(dt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCoCoreSegment '"+e+"'")}}}class ht extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Et}initializeFields(){let e=new ne;return e.put(dt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(dt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(dt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCoGpcSegment '"+e+"'")}}}class gt extends k{static ID=10;static VERSION=1;static NAME="usco";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return gt.ID}getName(){return gt.NAME}getVersion(){return gt.VERSION}initializeSegments(){let e=[];return e.push(new ut),e.push(new ht),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(dt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(dt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(dt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var _t;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE="SensitiveDataProcessingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(_t||(_t={}));const Tt=[_t.VERSION,_t.SHARING_NOTICE,_t.SALE_OPT_OUT_NOTICE,_t.TARGETED_ADVERTISING_OPT_OUT_NOTICE,_t.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE,_t.SALE_OPT_OUT,_t.TARGETED_ADVERTISING_OPT_OUT,_t.SENSITIVE_DATA_PROCESSING,_t.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,_t.MSPA_COVERED_TRANSACTION,_t.MSPA_OPT_OUT_OPTION_MODE,_t.MSPA_SERVICE_PROVIDER_MODE];class Ot extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Tt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(_t.VERSION.toString(),new te(6,pt.VERSION)),i.put(_t.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(_t.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(_t.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(_t.SENSITIVE_DATA_PROCESSING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(_t.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(_t.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(_t.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(_t.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(_t.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(_t.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(_t.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsUtCoreSegment '"+e+"'")}}}class pt extends k{static ID=11;static VERSION=1;static NAME="usut";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return pt.ID}getName(){return pt.NAME}getVersion(){return pt.VERSION}initializeSegments(){let e=[];return e.push(new Ot),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var Nt;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Nt||(Nt={}));const It=[Nt.VERSION,Nt.SHARING_NOTICE,Nt.SALE_OPT_OUT_NOTICE,Nt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Nt.SALE_OPT_OUT,Nt.TARGETED_ADVERTISING_OPT_OUT,Nt.SENSITIVE_DATA_PROCESSING,Nt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Nt.MSPA_COVERED_TRANSACTION,Nt.MSPA_OPT_OUT_OPTION_MODE,Nt.MSPA_SERVICE_PROVIDER_MODE],Ct=[Nt.GPC_SEGMENT_TYPE,Nt.GPC];class At extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return It}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Nt.VERSION.toString(),new te(6,Dt.VERSION)),i.put(Nt.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Nt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Nt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Nt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Nt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Nt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Nt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(Nt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Nt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Nt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCtCoreSegment '"+e+"'")}}}class Pt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ct}initializeFields(){let e=new ne;return e.put(Nt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Nt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Nt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsCtGpcSegment '"+e+"'")}}}class Dt extends k{static ID=12;static VERSION=1;static NAME="usct";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Dt.ID}getName(){return Dt.NAME}getVersion(){return Dt.VERSION}initializeSegments(){let e=[];return e.push(new At),e.push(new Pt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Nt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Nt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Nt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var wt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode"}(wt||(wt={}));const ft=[wt.VERSION,wt.PROCESSING_NOTICE,wt.SALE_OPT_OUT_NOTICE,wt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,wt.SALE_OPT_OUT,wt.TARGETED_ADVERTISING_OPT_OUT,wt.SENSITIVE_DATA_PROCESSING,wt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,wt.ADDITIONAL_DATA_PROCESSING_CONSENT,wt.MSPA_COVERED_TRANSACTION,wt.MSPA_OPT_OUT_OPTION_MODE,wt.MSPA_SERVICE_PROVIDER_MODE];class mt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ft}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(wt.VERSION.toString(),new te(6,Vt.VERSION)),i.put(wt.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(wt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(wt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(wt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(wt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(wt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(wt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(wt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(wt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(wt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(wt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsFlCoreSegment '"+e+"'")}}}class Vt extends k{static ID=13;static VERSION=1;static NAME="usfl";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Vt.ID}getName(){return Vt.NAME}getVersion(){return Vt.VERSION}initializeSegments(){let e=[];return e.push(new mt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");for(let e=0;e<t.length;e++)n.length>e&&t[e].decode(n[e])}return t}encodeSection(e){let t=[];for(let n=0;n<e.length;n++){let i=e[n];t.push(i.encode())}return t.join(".")}}var Rt;!function(e){e.VERSION="Version",e.SHARING_NOTICE="SharingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Rt||(Rt={}));const Mt=[Rt.VERSION,Rt.SHARING_NOTICE,Rt.SALE_OPT_OUT_NOTICE,Rt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Rt.SALE_OPT_OUT,Rt.TARGETED_ADVERTISING_OPT_OUT,Rt.SENSITIVE_DATA_PROCESSING,Rt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Rt.ADDITIONAL_DATA_PROCESSING_CONSENT,Rt.MSPA_COVERED_TRANSACTION,Rt.MSPA_OPT_OUT_OPTION_MODE,Rt.MSPA_SERVICE_PROVIDER_MODE],Ut=[Rt.GPC_SEGMENT_TYPE,Rt.GPC];class Gt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Mt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Rt.VERSION.toString(),new te(6,bt.VERSION)),i.put(Rt.SHARING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Rt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Rt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Rt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Rt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Rt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Rt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(Rt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(Rt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Rt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Rt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsMtCoreSegment '"+e+"'")}}}class yt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ut}initializeFields(){let e=new ne;return e.put(Rt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Rt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Rt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsMtGpcSegment '"+e+"'")}}}class bt extends k{static ID=14;static VERSION=1;static NAME="usmt";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return bt.ID}getName(){return bt.NAME}getVersion(){return bt.VERSION}initializeSegments(){let e=[];return e.push(new Gt),e.push(new yt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Rt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Rt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Rt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Lt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Lt||(Lt={}));const vt=[Lt.VERSION,Lt.PROCESSING_NOTICE,Lt.SALE_OPT_OUT_NOTICE,Lt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Lt.SALE_OPT_OUT,Lt.TARGETED_ADVERTISING_OPT_OUT,Lt.SENSITIVE_DATA_PROCESSING,Lt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Lt.ADDITIONAL_DATA_PROCESSING_CONSENT,Lt.MSPA_COVERED_TRANSACTION,Lt.MSPA_OPT_OUT_OPTION_MODE,Lt.MSPA_SERVICE_PROVIDER_MODE],Ft=[Lt.GPC_SEGMENT_TYPE,Lt.GPC];class xt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return vt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Lt.VERSION.toString(),new te(6,Ht.VERSION)),i.put(Lt.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Lt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Lt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Lt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Lt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Lt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Lt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(Lt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(Lt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Lt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Lt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsOrCoreSegment '"+e+"'")}}}class kt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Ft}initializeFields(){let e=new ne;return e.put(Lt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Lt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Lt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsOrGpcSegment '"+e+"'")}}}class Ht extends k{static ID=15;static VERSION=1;static NAME="usor";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Ht.ID}getName(){return Ht.NAME}getVersion(){return Ht.VERSION}initializeSegments(){let e=[];return e.push(new xt),e.push(new kt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Lt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Lt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Lt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Bt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Bt||(Bt={}));const Kt=[Bt.VERSION,Bt.PROCESSING_NOTICE,Bt.SALE_OPT_OUT_NOTICE,Bt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Bt.SALE_OPT_OUT,Bt.TARGETED_ADVERTISING_OPT_OUT,Bt.SENSITIVE_DATA_PROCESSING,Bt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Bt.ADDITIONAL_DATA_PROCESSING_CONSENT,Bt.MSPA_COVERED_TRANSACTION,Bt.MSPA_OPT_OUT_OPTION_MODE,Bt.MSPA_SERVICE_PROVIDER_MODE],Wt=[Bt.GPC_SEGMENT_TYPE,Bt.GPC];class jt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Kt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Bt.VERSION.toString(),new te(6,$t.VERSION)),i.put(Bt.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Bt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Bt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Bt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Bt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Bt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Bt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(Bt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(Bt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Bt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Bt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsTxCoreSegment '"+e+"'")}}}class zt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Wt}initializeFields(){let e=new ne;return e.put(Bt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Bt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Bt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsTxGpcSegment '"+e+"'")}}}class $t extends k{static ID=16;static VERSION=1;static NAME="ustx";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return $t.ID}getName(){return $t.NAME}getVersion(){return $t.VERSION}initializeSegments(){let e=[];return e.push(new jt),e.push(new zt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Bt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Bt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Bt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Yt;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Yt||(Yt={}));const qt=[Yt.VERSION,Yt.PROCESSING_NOTICE,Yt.SALE_OPT_OUT_NOTICE,Yt.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Yt.SALE_OPT_OUT,Yt.TARGETED_ADVERTISING_OPT_OUT,Yt.SENSITIVE_DATA_PROCESSING,Yt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Yt.ADDITIONAL_DATA_PROCESSING_CONSENT,Yt.MSPA_COVERED_TRANSACTION,Yt.MSPA_OPT_OUT_OPTION_MODE,Yt.MSPA_SERVICE_PROVIDER_MODE],Jt=[Yt.GPC_SEGMENT_TYPE,Yt.GPC];class Qt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return qt}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Yt.VERSION.toString(),new te(6,Zt.VERSION)),i.put(Yt.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Yt.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Yt.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Yt.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Yt.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Yt.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Yt.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0,0,0]).withValidator(n)),i.put(Yt.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(Yt.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Yt.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Yt.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsDeCoreSegment '"+e+"'")}}}class Xt extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Jt}initializeFields(){let e=new ne;return e.put(Yt.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Yt.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Yt.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsDeGpcSegment '"+e+"'")}}}class Zt extends k{static ID=17;static VERSION=1;static NAME="usde";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Zt.ID}getName(){return Zt.NAME}getVersion(){return Zt.VERSION}initializeSegments(){let e=[];return e.push(new Qt),e.push(new Xt),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Yt.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Yt.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Yt.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var en;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SENSITIVE_DATA_OPT_OUT_NOTICE="SensitiveDataOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(en||(en={}));const tn=[en.VERSION,en.PROCESSING_NOTICE,en.SALE_OPT_OUT_NOTICE,en.TARGETED_ADVERTISING_OPT_OUT_NOTICE,en.SENSITIVE_DATA_OPT_OUT_NOTICE,en.SALE_OPT_OUT,en.TARGETED_ADVERTISING_OPT_OUT,en.SENSITIVE_DATA_PROCESSING,en.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,en.MSPA_COVERED_TRANSACTION,en.MSPA_OPT_OUT_OPTION_MODE,en.MSPA_SERVICE_PROVIDER_MODE],nn=[en.GPC_SEGMENT_TYPE,en.GPC];class sn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return tn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(en.VERSION.toString(),new te(6,on.VERSION)),i.put(en.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(en.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(en.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(en.SENSITIVE_DATA_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(en.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(en.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(en.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(en.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(en.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(en.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(en.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsIaCoreSegment '"+e+"'")}}}class rn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return nn}initializeFields(){let e=new ne;return e.put(en.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(en.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(en.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsIaGpcSegment '"+e+"'")}}}class on extends k{static ID=18;static VERSION=1;static NAME="usia";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return on.ID}getName(){return on.NAME}getVersion(){return on.VERSION}initializeSegments(){let e=[];return e.push(new sn),e.push(new rn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(en.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(en.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(en.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var an;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(an||(an={}));const cn=[an.VERSION,an.PROCESSING_NOTICE,an.SALE_OPT_OUT_NOTICE,an.TARGETED_ADVERTISING_OPT_OUT_NOTICE,an.SALE_OPT_OUT,an.TARGETED_ADVERTISING_OPT_OUT,an.SENSITIVE_DATA_PROCESSING,an.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,an.ADDITIONAL_DATA_PROCESSING_CONSENT,an.MSPA_COVERED_TRANSACTION,an.MSPA_OPT_OUT_OPTION_MODE,an.MSPA_SERVICE_PROVIDER_MODE],ln=[an.GPC_SEGMENT_TYPE,an.GPC];class dn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return cn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(an.VERSION.toString(),new te(6,En.VERSION)),i.put(an.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(an.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(an.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(an.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(an.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(an.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(an.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(an.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(an.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(an.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(an.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNeCoreSegment '"+e+"'")}}}class Sn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return ln}initializeFields(){let e=new ne;return e.put(an.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(an.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(an.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNeGpcSegment '"+e+"'")}}}class En extends k{static ID=19;static VERSION=1;static NAME="usne";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return En.ID}getName(){return En.NAME}getVersion(){return En.VERSION}initializeSegments(){let e=[];return e.push(new dn),e.push(new Sn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(an.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(an.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(an.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var un;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(un||(un={}));const hn=[un.VERSION,un.PROCESSING_NOTICE,un.SALE_OPT_OUT_NOTICE,un.TARGETED_ADVERTISING_OPT_OUT_NOTICE,un.SALE_OPT_OUT,un.TARGETED_ADVERTISING_OPT_OUT,un.SENSITIVE_DATA_PROCESSING,un.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,un.ADDITIONAL_DATA_PROCESSING_CONSENT,un.MSPA_COVERED_TRANSACTION,un.MSPA_OPT_OUT_OPTION_MODE,un.MSPA_SERVICE_PROVIDER_MODE],gn=[un.GPC_SEGMENT_TYPE,un.GPC];class _n extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return hn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(un.VERSION.toString(),new te(6,On.VERSION)),i.put(un.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(un.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(un.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(un.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(un.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(un.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(un.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0]).withValidator(n)),i.put(un.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(un.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(un.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(un.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNhCoreSegment '"+e+"'")}}}class Tn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return gn}initializeFields(){let e=new ne;return e.put(un.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(un.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(un.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNhGpcSegment '"+e+"'")}}}class On extends k{static ID=20;static VERSION=1;static NAME="usnh";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return On.ID}getName(){return On.NAME}getVersion(){return On.VERSION}initializeSegments(){let e=[];return e.push(new _n),e.push(new Tn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(un.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(un.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(un.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var pn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(pn||(pn={}));const Nn=[pn.VERSION,pn.PROCESSING_NOTICE,pn.SALE_OPT_OUT_NOTICE,pn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,pn.SALE_OPT_OUT,pn.TARGETED_ADVERTISING_OPT_OUT,pn.SENSITIVE_DATA_PROCESSING,pn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,pn.ADDITIONAL_DATA_PROCESSING_CONSENT,pn.MSPA_COVERED_TRANSACTION,pn.MSPA_OPT_OUT_OPTION_MODE,pn.MSPA_SERVICE_PROVIDER_MODE],In=[pn.GPC_SEGMENT_TYPE,pn.GPC];class Cn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return Nn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(pn.VERSION.toString(),new te(6,Pn.VERSION)),i.put(pn.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(pn.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(pn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(pn.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(pn.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(pn.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0,0,0]).withValidator(n)),i.put(pn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new Je(2,[0,0,0,0,0]).withValidator(n)),i.put(pn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(pn.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(pn.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(pn.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNjCoreSegment '"+e+"'")}}}class An extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return In}initializeFields(){let e=new ne;return e.put(pn.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(pn.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(pn.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsNjGpcSegment '"+e+"'")}}}class Pn extends k{static ID=21;static VERSION=1;static NAME="usnj";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Pn.ID}getName(){return Pn.NAME}getVersion(){return Pn.VERSION}initializeSegments(){let e=[];return e.push(new Cn),e.push(new An),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(pn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(pn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(pn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}var Dn;!function(e){e.VERSION="Version",e.PROCESSING_NOTICE="ProcessingNotice",e.SALE_OPT_OUT_NOTICE="SaleOptOutNotice",e.TARGETED_ADVERTISING_OPT_OUT_NOTICE="TargetedAdvertisingOptOutNotice",e.SALE_OPT_OUT="SaleOptOut",e.TARGETED_ADVERTISING_OPT_OUT="TargetedAdvertisingOptOut",e.SENSITIVE_DATA_PROCESSING="SensitiveDataProcessing",e.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS="KnownChildSensitiveDataConsents",e.ADDITIONAL_DATA_PROCESSING_CONSENT="AdditionalDataProcessingConsent",e.MSPA_COVERED_TRANSACTION="MspaCoveredTransaction",e.MSPA_OPT_OUT_OPTION_MODE="MspaOptOutOptionMode",e.MSPA_SERVICE_PROVIDER_MODE="MspaServiceProviderMode",e.GPC_SEGMENT_TYPE="GpcSegmentType",e.GPC_SEGMENT_INCLUDED="GpcSegmentIncluded",e.GPC="Gpc"}(Dn||(Dn={}));const wn=[Dn.VERSION,Dn.PROCESSING_NOTICE,Dn.SALE_OPT_OUT_NOTICE,Dn.TARGETED_ADVERTISING_OPT_OUT_NOTICE,Dn.SALE_OPT_OUT,Dn.TARGETED_ADVERTISING_OPT_OUT,Dn.SENSITIVE_DATA_PROCESSING,Dn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS,Dn.ADDITIONAL_DATA_PROCESSING_CONSENT,Dn.MSPA_COVERED_TRANSACTION,Dn.MSPA_OPT_OUT_OPTION_MODE,Dn.MSPA_SERVICE_PROVIDER_MODE],fn=[Dn.GPC_SEGMENT_TYPE,Dn.GPC];class mn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return wn}initializeFields(){const e=new class{test(e){return e>=0&&e<=2}},t=new class{test(e){return e>=1&&e<=2}},n=new class{test(e){for(let t=0;t<e.length;t++){let n=e[t];if(n<0||n>2)return!1}return!0}};let i=new ne;return i.put(Dn.VERSION.toString(),new te(6,Rn.VERSION)),i.put(Dn.PROCESSING_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Dn.SALE_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Dn.TARGETED_ADVERTISING_OPT_OUT_NOTICE.toString(),new te(2,0).withValidator(e)),i.put(Dn.SALE_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Dn.TARGETED_ADVERTISING_OPT_OUT.toString(),new te(2,0).withValidator(e)),i.put(Dn.SENSITIVE_DATA_PROCESSING.toString(),new Je(2,[0,0,0,0,0,0,0,0]).withValidator(n)),i.put(Dn.KNOWN_CHILD_SENSITIVE_DATA_CONSENTS.toString(),new te(2,0).withValidator(e)),i.put(Dn.ADDITIONAL_DATA_PROCESSING_CONSENT.toString(),new te(2,0).withValidator(e)),i.put(Dn.MSPA_COVERED_TRANSACTION.toString(),new te(2,1).withValidator(t)),i.put(Dn.MSPA_OPT_OUT_OPTION_MODE.toString(),new te(2,0).withValidator(e)),i.put(Dn.MSPA_SERVICE_PROVIDER_MODE.toString(),new te(2,0).withValidator(e)),i}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsTnCoreSegment '"+e+"'")}}}class Vn extends se{base64UrlEncoder=j.getInstance();bitStringEncoder=z.getInstance();constructor(e){super(),e&&this.decode(e)}getFieldNames(){return fn}initializeFields(){let e=new ne;return e.put(Dn.GPC_SEGMENT_TYPE.toString(),new te(2,1)),e.put(Dn.GPC_SEGMENT_INCLUDED.toString(),new Te(!0)),e.put(Dn.GPC.toString(),new Te(!1)),e}encodeSegment(e){let t=this.bitStringEncoder.encode(e,this.getFieldNames());return this.base64UrlEncoder.encode(t)}decodeSegment(e,t){null!=e&&0!==e.length||this.fields.reset(t);try{let n=this.base64UrlEncoder.decode(e);this.bitStringEncoder.decode(n,this.getFieldNames(),t)}catch(t){throw new H("Unable to decode UsTnGpcSegment '"+e+"'")}}}class Rn extends k{static ID=22;static VERSION=1;static NAME="ustn";constructor(e){super(),e&&e.length>0&&this.decode(e)}getId(){return Rn.ID}getName(){return Rn.NAME}getVersion(){return Rn.VERSION}initializeSegments(){let e=[];return e.push(new mn),e.push(new Vn),e}decodeSection(e){let t=this.initializeSegments();if(null!=e&&0!==e.length){let n=e.split(".");n.length>0&&t[0].decode(n[0]),n.length>1?(t[1].setFieldValue(Dn.GPC_SEGMENT_INCLUDED,!0),t[1].decode(n[1])):t[1].setFieldValue(Dn.GPC_SEGMENT_INCLUDED,!1)}return t}encodeSection(e){let t=[];return e.length>=1&&(t.push(e[0].encode()),e.length>=2&&!0===e[1].getFieldValue(Dn.GPC_SEGMENT_INCLUDED)&&t.push(e[1].encode())),t.join(".")}}class Mn{static SECTION_ID_NAME_MAP=new Map([[Re.ID,Re.NAME],[Fe.ID,Fe.NAME],[je.ID,je.NAME],[Ze.ID,Ze.NAME],[rt.ID,rt.NAME],[lt.ID,lt.NAME],[gt.ID,gt.NAME],[pt.ID,pt.NAME],[Dt.ID,Dt.NAME],[Vt.ID,Vt.NAME],[bt.ID,bt.NAME],[Ht.ID,Ht.NAME],[$t.ID,$t.NAME],[Zt.ID,Zt.NAME],[on.ID,on.NAME],[En.ID,En.NAME],[On.ID,On.NAME],[Pn.ID,Pn.NAME],[Rn.ID,Rn.NAME]]);static SECTION_ORDER=[Re.NAME,Fe.NAME,je.NAME,Ze.NAME,rt.NAME,lt.NAME,gt.NAME,pt.NAME,Dt.NAME,Vt.NAME,bt.NAME,Ht.NAME,$t.NAME,Zt.NAME,on.NAME,En.NAME,On.NAME,Pn.NAME,Rn.NAME]}class Un{sections=new Map;encodedString=null;decoded=!0;dirty=!1;constructor(e){e&&this.decode(e)}setFieldValue(e,t,n){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let i=null;if(this.sections.has(e)?i=this.sections.get(e):e===Fe.NAME?(i=new Fe,this.sections.set(Fe.NAME,i)):e===Re.NAME?(i=new Re,this.sections.set(Re.NAME,i)):e===je.NAME?(i=new je,this.sections.set(je.NAME,i)):e===Ze.NAME?(i=new Ze,this.sections.set(Ze.NAME,i)):e===rt.NAME?(i=new rt,this.sections.set(rt.NAME,i)):e===lt.NAME?(i=new lt,this.sections.set(lt.NAME,i)):e===gt.NAME?(i=new gt,this.sections.set(gt.NAME,i)):e===pt.NAME?(i=new pt,this.sections.set(pt.NAME,i)):e===Dt.NAME?(i=new Dt,this.sections.set(Dt.NAME,i)):e===Vt.NAME?(i=new Vt,this.sections.set(Vt.NAME,i)):e===bt.NAME?(i=new bt,this.sections.set(bt.NAME,i)):e===Ht.NAME?(i=new Ht,this.sections.set(Ht.NAME,i)):e===$t.NAME?(i=new $t,this.sections.set($t.NAME,i)):e===Zt.NAME?(i=new Zt,this.sections.set(Zt.NAME,i)):e===on.NAME?(i=new on,this.sections.set(on.NAME,i)):e===En.NAME?(i=new En,this.sections.set(En.NAME,i)):e===On.NAME?(i=new On,this.sections.set(On.NAME,i)):e===Pn.NAME?(i=new Pn,this.sections.set(Pn.NAME,i)):e===Rn.NAME&&(i=new Rn,this.sections.set(Rn.NAME,i)),!i)throw new x(e+"."+t+" not found");i.setFieldValue(t,n),this.dirty=!0,i.setIsDirty(!0)}setFieldValueBySectionId(e,t,n){this.setFieldValue(Mn.SECTION_ID_NAME_MAP.get(e),t,n)}getFieldValue(e,t){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).getFieldValue(t):null}getFieldValueBySectionId(e,t){return this.getFieldValue(Mn.SECTION_ID_NAME_MAP.get(e),t)}hasField(e,t){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),!!this.sections.has(e)&&this.sections.get(e).hasField(t)}hasFieldBySectionId(e,t){return this.hasField(Mn.SECTION_ID_NAME_MAP.get(e),t)}hasSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)}hasSectionId(e){return this.hasSection(Mn.SECTION_ID_NAME_MAP.get(e))}deleteSection(e){!this.decoded&&null!=this.encodedString&&this.encodedString.length>0&&this.decode(this.encodedString),this.sections.delete(e),this.dirty=!0}deleteSectionById(e){this.deleteSection(Mn.SECTION_ID_NAME_MAP.get(e))}clear(){this.sections.clear(),this.encodedString="DBAA",this.decoded=!1,this.dirty=!1}getHeader(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e=new oe;return e.setFieldValue("SectionIds",this.getSectionIds()),e.toObj()}getSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).toObj():null}getSectionIds(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e=[];for(let t=0;t<Mn.SECTION_ORDER.length;t++){let n=Mn.SECTION_ORDER[t];if(this.sections.has(n)){let t=this.sections.get(n);e.push(t.getId())}}return e}encodeModel(e){let t=[],n=[];for(let i=0;i<Mn.SECTION_ORDER.length;i++){let s=Mn.SECTION_ORDER[i];if(e.has(s)){let i=e.get(s);i.setIsDirty(!0),t.push(i.encode()),n.push(i.getId())}}let i=new oe;return i.setFieldValue("SectionIds",n),t.unshift(i.encode()),t.join("~")}decodeModel(e){if(!e||0==e.length||e.startsWith("DB")){let t=e.split("~"),n=new Map;if(t[0].startsWith("D")){let i=new oe(t[0]).getFieldValue("SectionIds");if(i.length!==t.length-1)throw new H("Unable to decode '"+e+"'. The number of sections does not match the number of sections defined in the header.");for(let s=0;s<i.length;s++){if(""===t[s+1].trim())throw new H("Unable to decode '"+e+"'. Section "+(s+1)+" is blank.");if(i[s]===Fe.ID){let e=new Fe(t[s+1]);n.set(Fe.NAME,e)}else if(i[s]===Re.ID){let e=new Re(t[s+1]);n.set(Re.NAME,e)}else if(i[s]===je.ID){let e=new je(t[s+1]);n.set(je.NAME,e)}else if(i[s]===Ze.ID){let e=new Ze(t[s+1]);n.set(Ze.NAME,e)}else if(i[s]===rt.ID){let e=new rt(t[s+1]);n.set(rt.NAME,e)}else if(i[s]===lt.ID){let e=new lt(t[s+1]);n.set(lt.NAME,e)}else if(i[s]===gt.ID){let e=new gt(t[s+1]);n.set(gt.NAME,e)}else if(i[s]===pt.ID){let e=new pt(t[s+1]);n.set(pt.NAME,e)}else if(i[s]===Dt.ID){let e=new Dt(t[s+1]);n.set(Dt.NAME,e)}else if(i[s]===Vt.ID){let e=new Vt(t[s+1]);n.set(Vt.NAME,e)}else if(i[s]===bt.ID){let e=new bt(t[s+1]);n.set(bt.NAME,e)}else if(i[s]===Ht.ID){let e=new Ht(t[s+1]);n.set(Ht.NAME,e)}else if(i[s]===$t.ID){let e=new $t(t[s+1]);n.set($t.NAME,e)}else if(i[s]===Zt.ID){let e=new Zt(t[s+1]);n.set(Zt.NAME,e)}else if(i[s]===on.ID){let e=new on(t[s+1]);n.set(on.NAME,e)}else if(i[s]===En.ID){let e=new En(t[s+1]);n.set(En.NAME,e)}else if(i[s]===On.ID){let e=new On(t[s+1]);n.set(On.NAME,e)}else if(i[s]===Pn.ID){let e=new Pn(t[s+1]);n.set(Pn.NAME,e)}else if(i[s]===Rn.ID){let e=new Rn(t[s+1]);n.set(Rn.NAME,e)}}}return n}if(e.startsWith("C")){let t=new Map,n=new Re(e);return t.set(Re.NAME,n),(new oe).setFieldValue(P.SECTION_IDS,[2]),t.set(oe.NAME,n),t}throw new H("Unable to decode '"+e+"'")}encodeSection(e){return this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0),this.sections.has(e)?this.sections.get(e).encode():null}encodeSectionById(e){return this.encodeSection(Mn.SECTION_ID_NAME_MAP.get(e))}decodeSection(e,t){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let n=null;this.sections.has(e)?n=this.sections.get(e):e===Fe.NAME?(n=new Fe,this.sections.set(Fe.NAME,n)):e===Re.NAME?(n=new Re,this.sections.set(Re.NAME,n)):e===je.NAME?(n=new je,this.sections.set(je.NAME,n)):e===Ze.NAME?(n=new Ze,this.sections.set(Ze.NAME,n)):e===rt.NAME?(n=new rt,this.sections.set(rt.NAME,n)):e===lt.NAME?(n=new lt,this.sections.set(lt.NAME,n)):e===gt.NAME?(n=new gt,this.sections.set(gt.NAME,n)):e===pt.NAME?(n=new pt,this.sections.set(pt.NAME,n)):e===Dt.NAME?(n=new Dt,this.sections.set(Dt.NAME,n)):e===Vt.NAME?(n=new Vt,this.sections.set(Vt.NAME,n)):e===bt.NAME?(n=new bt,this.sections.set(bt.NAME,n)):e===Ht.NAME?(n=new Ht,this.sections.set(Ht.NAME,n)):e===$t.NAME?(n=new $t,this.sections.set($t.NAME,n)):e===Zt.NAME?(n=new Zt,this.sections.set(Zt.NAME,n)):e===on.NAME?(n=new on,this.sections.set(on.NAME,n)):e===En.NAME?(n=new En,this.sections.set(En.NAME,n)):e===On.NAME?(n=new On,this.sections.set(On.NAME,n)):e===Pn.NAME?(n=new Pn,this.sections.set(Pn.NAME,n)):e===Rn.NAME&&(n=new Rn,this.sections.set(Rn.NAME,n)),n&&(n.decode(t),this.dirty=!0)}decodeSectionById(e,t){this.decodeSection(Mn.SECTION_ID_NAME_MAP.get(e),t)}toObject(){this.decoded||(this.sections=this.decodeModel(this.encodedString),this.dirty=!1,this.decoded=!0);let e={};for(let t=0;t<Mn.SECTION_ORDER.length;t++){let n=Mn.SECTION_ORDER[t];this.sections.has(n)&&(e[n]=this.sections.get(n).toObj())}return e}encode(){return(null==this.encodedString||0===this.encodedString.length||this.dirty)&&(this.encodedString=this.encodeModel(this.sections),this.dirty=!1,this.decoded=!0),this.encodedString}decode(e){this.encodedString=e,this.dirty=!1,this.decoded=!1}}class Gn{gppVersion="1.1";supportedAPIs=[];eventQueue=new F(this);cmpStatus=N.LOADING;cmpDisplayStatus=I.HIDDEN;signalStatus=A.NOT_READY;applicableSections=[];gppModel=new Un;cmpId;cmpVersion;eventStatus;reset(){this.eventQueue.clear(),this.cmpStatus=N.LOADING,this.cmpDisplayStatus=I.HIDDEN,this.signalStatus=A.NOT_READY,this.applicableSections=[],this.supportedAPIs=[],this.gppModel=new Un,delete this.cmpId,delete this.cmpVersion,delete this.eventStatus}}class yn extends Error{constructor(e){super(e),this.name="GVLError"}}class bn{static langSet=new Set(["AR","BG","BS","CA","CS","CY","DA","DE","EL","EN","ES","ET","EU","FI","FR","GL","HE","HR","HU","ID","IT","JA","KA","KO","LT","LV","MK","MS","MT","NL","NO","PL","PT-BR","PT-PT","RO","RU","SK","SL","SQ","SR-LATN","SR-CYRL","SV","SW","TH","TL","TR","UK","VI","ZH"]);has(e){return bn.langSet.has(e)}forEach(e){bn.langSet.forEach(e)}get size(){return bn.langSet.size}}class Ln{static absCall(e,t,n,i){return new Promise(((s,r)=>{const o=new XMLHttpRequest;o.withCredentials=n,o.addEventListener("load",(()=>{if(o.readyState==XMLHttpRequest.DONE)if(o.status>=200&&o.status<300){let e=o.response;if("string"==typeof e)try{e=JSON.parse(e)}catch(e){}s(e)}else r(new Error(`HTTP Status: ${o.status} response type: ${o.responseType}`))})),o.addEventListener("error",(()=>{r(new Error("error"))})),o.addEventListener("abort",(()=>{r(new Error("aborted"))})),null===t?o.open("GET",e,!0):o.open("POST",e,!0),o.responseType="json",o.timeout=i,o.ontimeout=()=>{r(new Error("Timeout "+i+"ms "+e))},o.send(t)}))}static post(e,t,n=!1,i=0){return this.absCall(e,JSON.stringify(t),n,i)}static fetch(e,t=!1,n=0){return this.absCall(e,null,t,n)}}class vn{vendors;static DEFAULT_LANGUAGE="EN";consentLanguages=new bn;gvlSpecificationVersion;vendorListVersion;tcfPolicyVersion;lastUpdated;purposes;specialPurposes;features;specialFeatures;stacks;dataCategories;language=vn.DEFAULT_LANGUAGE;vendorIds;ready=!1;fullVendorList;byPurposeVendorMap;bySpecialPurposeVendorMap;byFeatureVendorMap;bySpecialFeatureVendorMap;baseUrl;languageFilename="purposes-[LANG].json";static fromVendorList(e){let t=new vn;return t.populate(e),t}static async fromUrl(e){let t=e.baseUrl;if(!t||0===t.length)throw new yn("Invalid baseUrl: '"+t+"'");if(/^https?:\/\/vendorlist\.consensu\.org\//.test(t))throw new yn("Invalid baseUrl!  You may not pull directly from vendorlist.consensu.org and must provide your own cache");t.length>0&&"/"!==t[t.length-1]&&(t+="/");let n=new vn;if(n.baseUrl=t,e.languageFilename?n.languageFilename=e.languageFilename:n.languageFilename="purposes-[LANG].json",e.version>0){let i=e.versionedFilename;i||(i="archives/vendor-list-v[VERSION].json");let s=t+i.replace("[VERSION]",String(e.version));n.populate(await Ln.fetch(s))}else{let i=e.latestFilename;i||(i="vendor-list.json");let s=t+i;n.populate(await Ln.fetch(s))}return n}async changeLanguage(e){const t=e.toUpperCase();if(!this.consentLanguages.has(t))throw new yn(`unsupported language ${e}`);if(t!==this.language){this.language=t;const n=this.baseUrl+this.languageFilename.replace("[LANG]",e);try{this.populate(await Ln.fetch(n))}catch(e){throw new yn("unable to load language: "+e.message)}}}getJson(){return JSON.parse(JSON.stringify({gvlSpecificationVersion:this.gvlSpecificationVersion,vendorListVersion:this.vendorListVersion,tcfPolicyVersion:this.tcfPolicyVersion,lastUpdated:this.lastUpdated,purposes:this.purposes,specialPurposes:this.specialPurposes,features:this.features,specialFeatures:this.specialFeatures,stacks:this.stacks,dataCategories:this.dataCategories,vendors:this.fullVendorList}))}isVendorList(e){return void 0!==e&&void 0!==e.vendors}populate(e){this.purposes=e.purposes,this.specialPurposes=e.specialPurposes,this.features=e.features,this.specialFeatures=e.specialFeatures,this.stacks=e.stacks,this.dataCategories=e.dataCategories,this.isVendorList(e)&&(this.gvlSpecificationVersion=e.gvlSpecificationVersion,this.tcfPolicyVersion=e.tcfPolicyVersion,this.vendorListVersion=e.vendorListVersion,this.lastUpdated=e.lastUpdated,"string"==typeof this.lastUpdated&&(this.lastUpdated=new Date(this.lastUpdated)),this.vendors=e.vendors,this.fullVendorList=e.vendors,this.mapVendors(),this.ready=!0)}mapVendors(e){this.byPurposeVendorMap={},this.bySpecialPurposeVendorMap={},this.byFeatureVendorMap={},this.bySpecialFeatureVendorMap={},Object.keys(this.purposes).forEach((e=>{this.byPurposeVendorMap[e]={legInt:new Set,impCons:new Set,consent:new Set,flexible:new Set}})),Object.keys(this.specialPurposes).forEach((e=>{this.bySpecialPurposeVendorMap[e]=new Set})),Object.keys(this.features).forEach((e=>{this.byFeatureVendorMap[e]=new Set})),Object.keys(this.specialFeatures).forEach((e=>{this.bySpecialFeatureVendorMap[e]=new Set})),Array.isArray(e)||(e=Object.keys(this.fullVendorList).map((e=>+e))),this.vendorIds=new Set(e),this.vendors=e.reduce(((e,t)=>{const n=this.vendors[String(t)];return n&&void 0===n.deletedDate&&(n.purposes.forEach((e=>{this.byPurposeVendorMap[String(e)].consent.add(t)})),n.specialPurposes.forEach((e=>{this.bySpecialPurposeVendorMap[String(e)].add(t)})),n.legIntPurposes&&n.legIntPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].legInt.add(t)})),n.impConsPurposes&&n.impConsPurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].impCons.add(t)})),n.flexiblePurposes&&n.flexiblePurposes.forEach((e=>{this.byPurposeVendorMap[String(e)].flexible.add(t)})),n.features.forEach((e=>{this.byFeatureVendorMap[String(e)].add(t)})),n.specialFeatures.forEach((e=>{this.bySpecialFeatureVendorMap[String(e)].add(t)})),e[t]=n),e}),{})}getFilteredVendors(e,t,n,i){const s=e.charAt(0).toUpperCase()+e.slice(1);let r;const o={};return r="purpose"===e&&n?this["by"+s+"VendorMap"][String(t)][n]:this["by"+(i?"Special":"")+s+"VendorMap"][String(t)],r.forEach((e=>{o[String(e)]=this.vendors[String(e)]})),o}getVendorsWithConsentPurpose(e){return this.getFilteredVendors("purpose",e,"consent")}getVendorsWithLegIntPurpose(e){return this.getFilteredVendors("purpose",e,"legInt")}getVendorsWithFlexiblePurpose(e){return this.getFilteredVendors("purpose",e,"flexible")}getVendorsWithSpecialPurpose(e){return this.getFilteredVendors("purpose",e,void 0,!0)}getVendorsWithFeature(e){return this.getFilteredVendors("feature",e)}getVendorsWithSpecialFeature(e){return this.getFilteredVendors("feature",e,void 0,!0)}narrowVendorsTo(e){this.mapVendors(e)}get isReady(){return this.ready}static isInstanceOf(e){return"object"==typeof e&&"function"==typeof e.narrowVendorsTo}}class Fn{callResponder;cmpApiContext;constructor(e,t,n){this.cmpApiContext=new Gn,this.cmpApiContext.cmpId=e,this.cmpApiContext.cmpVersion=t,this.callResponder=new v(this.cmpApiContext,n)}fireEvent(e,t){this.cmpApiContext.eventQueue.exec(e,t)}fireErrorEvent(e){this.cmpApiContext.eventQueue.exec("error",e)}fireSectionChange(e){this.cmpApiContext.eventQueue.exec("sectionChange",e)}getEventStatus(){return this.cmpApiContext.eventStatus}setEventStatus(e){this.cmpApiContext.eventStatus=e}getCmpStatus(){return this.cmpApiContext.cmpStatus}setCmpStatus(e){this.cmpApiContext.cmpStatus=e,this.cmpApiContext.eventQueue.exec("cmpStatus",e)}getCmpDisplayStatus(){return this.cmpApiContext.cmpDisplayStatus}setCmpDisplayStatus(e){this.cmpApiContext.cmpDisplayStatus=e,this.cmpApiContext.eventQueue.exec("cmpDisplayStatus",e)}getSignalStatus(){return this.cmpApiContext.signalStatus}setSignalStatus(e){this.cmpApiContext.signalStatus=e,this.cmpApiContext.eventQueue.exec("signalStatus",e)}getApplicableSections(){return this.cmpApiContext.applicableSections}setApplicableSections(e){this.cmpApiContext.applicableSections=e}getSupportedAPIs(){return this.cmpApiContext.supportedAPIs}setSupportedAPIs(e){this.cmpApiContext.supportedAPIs=e}setGppString(e){this.cmpApiContext.gppModel.decode(e)}getGppString(){return this.cmpApiContext.gppModel.encode()}setSectionString(e,t){this.cmpApiContext.gppModel.decodeSection(e,t)}setSectionStringById(e,t){this.setSectionString(Mn.SECTION_ID_NAME_MAP.get(e),t)}getSectionString(e){return this.cmpApiContext.gppModel.encodeSection(e)}getSectionStringById(e){return this.getSectionString(Mn.SECTION_ID_NAME_MAP.get(e))}setFieldValue(e,t,n){this.cmpApiContext.gppModel.setFieldValue(e,t,n)}setFieldValueBySectionId(e,t,n){this.setFieldValue(Mn.SECTION_ID_NAME_MAP.get(e),t,n)}getFieldValue(e,t){return this.cmpApiContext.gppModel.getFieldValue(e,t)}getFieldValueBySectionId(e,t){return this.getFieldValue(Mn.SECTION_ID_NAME_MAP.get(e),t)}getSection(e){return this.cmpApiContext.gppModel.getSection(e)}getSectionById(e){return this.getSection(Mn.SECTION_ID_NAME_MAP.get(e))}hasSection(e){return this.cmpApiContext.gppModel.hasSection(e)}hasSectionId(e){return this.hasSection(Mn.SECTION_ID_NAME_MAP.get(e))}deleteSection(e){this.cmpApiContext.gppModel.deleteSection(e)}deleteSectionById(e){this.deleteSection(Mn.SECTION_ID_NAME_MAP.get(e))}clear(){this.cmpApiContext.gppModel.clear()}getObject(){return this.cmpApiContext.gppModel.toObject()}getGvlFromVendorList(e){return vn.fromVendorList(e)}async getGvlFromUrl(e){return vn.fromUrl(e)}}let xn=null,kn=null,Hn=null;function Bn(e=200,t=1){return xn&&kn===e&&Hn===t||(xn=new Fn(e,t),kn=e,Hn=t),xn}function Kn(e){const t=Bn();if(t&&e)try{t.setGppString(e);const n=t.getApplicableSections();"function"==typeof t.fireSectionChange&&n.forEach((e=>{try{const n={2:"tcfeuv2",7:"usnat",8:"usca"}[e];n&&t.fireSectionChange(n)}catch(e){console.warn("Could not fire section change event:",e)}}))}catch(t){throw console.error("Failed to update GPP API instance with string:",{gppString:e,error:t.message,stack:t.stack}),new Error(`GPP API update failed: ${t.message}. Unable to set GPP string through official API.`)}}function Wn(e){return"number"==typeof e&&Number.isInteger(e)&&e>=0&&e<=2?e:0}function jn(e,t,n){const i=e.ChannelID,s=t.Scripts&&t.Scripts.find((e=>e.ChannelID===i)),r=t.Template&&t.Template.Categories&&t.Template.Categories.find((e=>e.ChannelID===i));let o;if(s&&s.IsStrictlyNecessary)o=e.False;else if(n&&Array.isArray(n)){const t=n.find((e=>String(e.channelId)===String(i)));o=t?t.isOptIn?e.False:e.True:r&&"boolean"==typeof r.DefaultValue?r.DefaultValue?e.False:e.True:0}else o=r&&"boolean"==typeof r.DefaultValue?r.DefaultValue?e.False:e.True:0;return Wn(o)}function zn(e){return!(!e?.MSPAConfig&&!e?.TCFconfig)}window.CassieWidget=class{constructor(t){this.eventManager=w,this.datetimestamp=(new Date).getTime(),this.domain=(new e).getDomainName();let n=t.languageCode??"";(t.profileSettings?.SupportedLanguages??[]).includes(n)&&""!==n||(n="Default"),this.cassieSettings=function(e,t,n,i,s,r,o,a,c,l,d){return{licenseKey:e,profileSettings:t,baseUrl:n,languageCode:i,supportedDomains:s,environment:r,region:o,recordUrlDomain:a,identityServiceUrl:c,crossDomainConsent:l,customOptions:d}}(t.licenseKey,t.profileSettings,t.baseUrl,n,t.supportedDomains??`*.${this.domain}`,t.environment,t.region,t.recordUrlDomain,t.identityServiceUrl,t.crossDomainConsent,t.customOptions),this.cassieSettings.profileSettings?.TemplateOptions||(this.cassieSettings.profileSettings.TemplateOptions={},this.cassieSettings.profileSettings.TemplateOptions.PreBannerPosition=this.cassieSettings.profileSettings?.TemplateOptions?.PreBannerPosition??1,this.cassieSettings.profileSettings.TemplateOptions.WidgetPosition=this.cassieSettings.profileSettings?.TemplateOptions?.WidgetPosition??2),this.urlHelper=new e(this.cassieSettings),this.cookieWidgetDivId="cassie-widget",this.cassieScripts=[],this.widgetTemplate={},this.cassieWidgetFileSettings=null,this.gpcEnabled=(t.profileSettings.GpcEnabled&&navigator.globalPrivacyControl)??!1,this.gppString=null,this.bannerIsVisible=!1,this.modalIsVisible=!1,this.handlerUrl=this.urlHelper.getSubmissionUrl(),this.forceReconsent=!1,this.extentions=[{event:"Sample",extentionFunction:function(){}}],this.init()}async init(){this.createWidgetElement(),await this.getWidgetJson(),this.initializeGppWithConfig(),this.cassieWidgetFileSettings.identityServiceUrl&&!this.cassieSettings?.customOptions?.isPreview&&(this.cassieSettings.identityServiceUrl=this.cassieWidgetFileSettings.identityServiceUrl),this.cassieSettings?.crossDomainConsent&&this.cassieWidgetFileSettings.identityServiceUrlPrefix&&(this.cassieSettings.identityServiceUrl=`https://${this.cassieWidgetFileSettings.identityServiceUrlPrefix}.${this.domain}`),this.syrenisHelper=new D(this.cassieSettings,this.cassieWidgetFileSettings.CassieCookiesExpiry),this.cookieHelper=new h(this.cassieSettings.profileSettings.AccessKey,this.cassieWidgetFileSettings.CassieCookiesExpiry),this.loadExistingGppString();const e=this.cassieWidgetFileSettings.reconsentDate.toString(),t=e.endsWith("Z")?new Date(e):new Date(`${e}Z`);this.forceReconsent=t.getTime()>this.cookieHelper.getConsentDate()&&this.cookieHelper.hasConsent(),!this.forceReconsent&&await this.syrenisHelper.runConsent(this.cassieScripts),this.widgetTemplate.hasConsent=this.cookieHelper.hasConsent()&&!this.forceReconsent,this.widgetTemplate.useRadioButtons=this.cassieSettings.profileSettings.UseRadioButtons??!1,this.widgetTemplate.hasConsent||this.dropCookiesOnLoad(),await this.getTemplateFiles(),w.emit("CassieTemplateFilesLoaded")}registerExtention(e,t){this.extentions.push({event:e,extentionFunction:t})}runExtentions(e){const t=this.extentions.filter((t=>t.event===e));for(const e of t)e.extentionFunction()}dropCookiesOnLoad(){const e="auto_save",t=this.widgetTemplate.CookieDropBehaviourType??1;if(3===t)this.acceptAll(e);else if(2===t){const e=this.cookieHelper.getPrivacyPolicyId()??this.cassieWidgetFileSettings.privacyId;this.syrenisHelper.dropStrictlyNecessaryCookies(e,this.cassieScripts)}else if(4===t)this.acceptAll({respectGpc:!0,source:e});else if(1===t)return}getCurrentConsent(){return this.syrenisHelper.getCurrentConsent()}getGtmConsent(){const e=this.syrenisHelper.getCurrentConsent(),t=this.cassieScripts.filter((e=>null!==e.gtmConsentType&&""!==e.gtmConsentType)).reduce(((e,t)=>{const n=t.gtmConsentType;return e[n]||(e[n]=[]),e[n].push(t.channelId),e}),{});return Object.keys(t).map((n=>{const i=t[n].every((t=>e.find((e=>e.channelId==t))?.isOptIn))?"granted":"denied",s={};return s[n]=i,s}))}storeGtmConsent(){const e=this.getGtmConsent();e.length>0&&this.syrenisHelper.saveGtmConsent(e)}initializeTemplate(){w.emit("CassieTemplateInitialized")}hideBanner(){this.runExtentions("hideBanner"),this.bannerIsVisible=!1,w.emit("CassieBannerVisibility",!1)}showBanner(){this.runExtentions("showBanner"),this.bannerIsVisible=!0,w.emit("CassieBannerVisibility",!0)}showModal(){this.runExtentions("showModal"),this.modalIsVisible=!0,w.emit("CassieModalVisibility",!0)}hideModal(){this.runExtentions("hideModal"),this.modalIsVisible=!1,w.emit("CassieModalVisibility",!1)}getPreferences(){const e=this.widgetTemplate.Categories,t=[];return e.forEach((e=>{const n=e.Cookies.map((e=>({channelId:e.ChannelID,gpcEnabled:e.GpcEnabled,statementId:e.Statements[0].StatementID,isOptIn:e.DefaultValue})));t.push(...n)})),t}async submitConsent(e,t){if(w.emit("CassieSubmitConsent",e),await this.syrenisHelper.processConsent(e,this.cassieScripts,this.cassieWidgetFileSettings),this.storeGtmConsent(),this.widgetTemplate.hasConsent=!0,w.emit("CassieProcessedConsent",e),this.generateAndStoreGPPString(e),this.handlerUrl)try{const n=this.cookieHelper.getPrivacyPolicyId()??this.cassieWidgetFileSettings.privacyId;this.syrenisHelper.postConsentToCassie(this.handlerUrl,t,n,this.cookieWidgetDivId).then((t=>(w.emit("CassieSubmittedConsent",e),t))).catch((async()=>{const i=await this.syrenisHelper.postConsentToCassie(this.defaultSubmissionUrl,t,n,this.cookieWidgetDivId);return w.emit("CassieSubmittedConsent",e),i}))}catch(e){return console.error(e),e}}addConsentListener(e){document.addEventListener("CassieProcessedConsent",e)}hasConsent(){return this.widgetTemplate.hasConsent=this.cookieHelper.hasConsent(),this.widgetTemplate.hasConsent}getGppString(){return this.gppString}acceptAll(e){if("string"==typeof e||null==e){var t=e;(e={}).source=t,e.respectGpc=!1}let n=this.getPreferences();n.forEach((t=>{t.isOptIn=!0,e.respectGpc&&this.gpcEnabled&&t.gpcEnabled&&(t.isOptIn=!1)})),this.submitConsent(n,e.source)}rejectAll(e){const t=this.getPreferences();t.forEach((e=>e.isOptIn=!1)),this.submitConsent(t,e)}createWidgetElement(){if(!this.cassieSettings.profileSettings.LoadTemplateHtml)return;const e=this.cookieWidgetDivId;if(null===document.getElementById(e)||0===document.getElementById(e).length){const t=document.createElement("div");t.setAttribute("id",e),t.style.display="none",t.classList.add("syrenis-cookie-widget"),document.body.appendChild(t)}}async getWidgetJson(){const e=this.urlHelper.determineJsonUrl(),t=await r(e);this.populateWidget(t),w.emit("CassieWidgetFileLoaded",null)}async getTemplateFiles(){const e=`${this.cassieSettings.baseUrl}/templates/${this.cassieSettings.profileSettings.Template}/`;if(this.cassieSettings.profileSettings.LoadTemplateCss&&this.AddCssToPage(`${e}template.min.css`),this.cassieSettings.profileSettings.LoadTemplateHtml){const t=await r(`${e}index.min.html`);this.loadHtmlToElement(this.cookieWidgetDivId,t)}if(this.cassieSettings.profileSettings.LoadTemplateJs){const t=await r(`${e}template.min.js`);this.loadScriptToDOM(t)}}AddCssToPage(e){const{head:t}=document,n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.insertBefore(n,t.childNodes[0])}loadHtmlToElement(e,t){const n=document.getElementById(e);n.innerHTML=t;const i=document.getElementsByTagName("body")[0];i.insertBefore(n,i.firstChild)}loadScriptToDOM(e){const t=document.createElement("script");t.innerHTML=e,document.body.appendChild(t)}loadExistingGppString(){if((!this.cassieWidgetFileSettings||!this.cassieWidgetFileSettings.widgetResponse||zn(this.cassieWidgetFileSettings.widgetResponse))&&this.cookieHelper){const e=this.cookieHelper.getCookieValueByName("__gpp");e&&(this.gppString=e,function(e,t=200,n=1){const i=Bn(t,n);if(e)try{Kn(e),"function"==typeof i.setCmpStatus&&i.setCmpStatus("loaded"),"function"==typeof i.setSignalStatus&&i.setSignalStatus("ready"),"function"==typeof i.fireEvent&&(i.fireEvent("cmpStatus","loaded"),i.fireEvent("signalStatus","ready"))}catch(e){console.error("Failed to initialize GPP for returning user:",e)}}(e))}}initializeGppWithConfig(){if(this.cassieWidgetFileSettings&&this.cassieWidgetFileSettings.widgetResponse){const{widgetResponse:e}=this.cassieWidgetFileSettings;if(!zn(e))return;!function(e,t=200,n=1){!!zn(e)&&function(e=200,t=1){const n=Bn(e,t);if("undefined"!=typeof window&&(window.__gpp=function(t,i,s){if(!n)return window.__gpp.queue||(window.__gpp.queue=[]),void window.__gpp.queue.push({command:t,callback:i,parameter:s});try{switch(t){case"addEventListener":"function"==typeof i&&n.addEventListener(s,i);break;case"removeEventListener":"function"==typeof i&&n.removeEventListener(s,i);break;case"hasSection":{const e=n.hasSection(s);return"function"==typeof i&&i(e,!0),e}case"getSection":{const e=n.getSection(s);return"function"==typeof i&&i(e,!0),e}case"getGPPString":{const e=n.getGppString();return"function"==typeof i&&i(e,!0),e}case"getField":{const e=n.getFieldValue(s);return"function"==typeof i&&i(e,!0),e}case"ping":{const t=n.getApplicableSections()||[],s=n.getGppString()||"",r=n.getObject()||{},o={gppVersion:"1.1",cmpStatus:"loaded",cmpDisplayStatus:"hidden",signalStatus:"ready",supportedAPIs:["2:tcfeuv2","7:usnat"],cmpId:e,sectionList:t,applicableSections:t,gppString:s,parsedSections:r};return"function"==typeof i&&i(o,!0),o}default:"function"==typeof i&&i(null,!1)}}catch(e){console.error("GPP API Error:",e),"function"==typeof i&&i(null,!1)}},window.__gpp.queue&&Array.isArray(window.__gpp.queue))){const e=window.__gpp.queue.slice();window.__gpp.queue=[],e.forEach((e=>{window.__gpp(e.command,e.callback,e.parameter)}))}}(t,n)}(e)}}generateAndStoreGPPString(e){if(!this.cassieWidgetFileSettings||!this.cassieWidgetFileSettings.widgetResponse)return void console.error("CassieWidget: widgetResponse not available for GPP string generation.");const{widgetResponse:t}=this.cassieWidgetFileSettings;if(zn(t)){e||console.warn("CassieWidget: formConsent not available for GPP string generation.");try{const n=200,i=1;this.gppString=function(e,t,n,i=null){if(!e?.MSPAConfig&&!e?.TCFconfig)return null;const s=Bn(t,n),r=[];if(e.MSPAConfig){const t=e.MSPAConfig,n="usnat";if(t.Core)for(const[r,o]of Object.entries(t.Core))if("SaleOptOut"===r||"SharingOptOut"===r||"TargetedAdvertisingOptOut"===r){const t=o;if("object"==typeof t&&null!==t&&"ChannelID"in t&&"True"in t&&"False"in t){const o=jn(t,e,i);s.setFieldValue(n,r,o)}else{const e=Wn(o);s.setFieldValue(n,r,e)}}else s.setFieldValue(n,r,o);if(t.SensitiveDataProcessing&&t.SensitiveDataProcessing.items){const r=new Array(16).fill(0);for(let n=1;n<=16;n++){const s=n.toString();if(void 0!==t.SensitiveDataProcessing.items[s]){const o=t.SensitiveDataProcessing.items[s];if("object"==typeof o&&null!==o&&"ChannelID"in o&&"True"in o&&"False"in o){const t=jn(o,e,i);r[n-1]=t}else r[n-1]=Wn(o)}}s.setFieldValue(n,"SensitiveDataProcessing",r)}if(t.KnownChildSensitiveDataConsents&&t.KnownChildSensitiveDataConsents.items){const r=new Array(10).fill(0);for(let n=1;n<=10;n++){const s=n.toString();if(void 0!==t.KnownChildSensitiveDataConsents.items[s]){const o=t.KnownChildSensitiveDataConsents.items[s];if("object"==typeof o&&null!==o&&"ChannelID"in o&&"True"in o&&"False"in o){const t=jn(o,e,i);r[n-1]=t}else r[n-1]=Wn(o)}}s.setFieldValue(n,"KnownChildSensitiveDataConsents",r)}if(void 0!==t.PersonalDataConsents){const r=t.PersonalDataConsents;if("object"==typeof r&&null!==r&&"ChannelID"in r&&"True"in r&&"False"in r){const t=jn(r,e,i);s.setFieldValue(n,"PersonalDataConsents",t)}else s.setFieldValue(n,"PersonalDataConsents",Wn(r))}void 0!==t.MspaCoveredTransaction&&s.setFieldValue(n,"MspaCoveredTransaction",t.MspaCoveredTransaction),void 0!==t.MspaOptOutOptionMode&&s.setFieldValue(n,"MspaOptOutOptionMode",t.MspaOptOutOptionMode),void 0!==t.MspaServiceProviderMode&&s.setFieldValue(n,"MspaServiceProviderMode",t.MspaServiceProviderMode),r.includes(7)||r.push(7)}if(e.TCFconfig){const t=e.TCFconfig,n="tcfeuv2";for(const[e,i]of Object.entries(t))null!=i&&("Created"===e||"LastUpdated"===e?s.setFieldValue(n,e,new Date(i)):s.setFieldValue(n,e,i));r.includes(2)||r.push(2)}return r.sort(((e,t)=>e-t)),s.setApplicableSections(r),s.getGppString()}(t,n,i,e),this.gppString&&this.cookieHelper?(this.cookieHelper.storeCookie("__gpp",this.gppString,null,null),Kn(this.gppString)):this.gppString||this.cookieHelper&&this.cookieHelper.storeCookie("__gpp","",null,null)}catch(e){console.error("CassieWidget: Error generating or storing GPP string:",e),this.gppString=null}}}populateWidget(e){if(this.cassieWidgetFileSettings=new class{constructor(e){this.persistCookies=e.Custom1stPartyData?.length>0?e.Custom1stPartyData:[],this.privacyId=function(e){if(!e)return 1;const t=e.substring(0,e.length-1).split("|"),n=[];for(let e=0;e<t.length;e++){const i=t[e].split(","),s="1"===i[1],r={PrivacyId:Number(i[0]),Default:s,ValidFrom:i[2]};n.push(r)}n.sort((function(e,t){return Date.parse(t.ValidFrom)-Date.parse(e.ValidFrom)}));const i=n.find((e=>!1===e.Default));return i?i.PrivacyId:1}(e.Template.PrivacyPolicies),this.identityServiceUrl=e.IdentityServiceEndpoint??null,this.reconsentDate=e.ReconsentDate??"1970-01-01T00:00:00.000Z",this.cookieCollectionId=e.CookieCollectionId??1,this.identityServiceUrlPrefix=e.IdentityServiceUrlPrefix??null,this.CassieCookiesExpiry=e.CassieCookiesExpiry??365}}(e),this.cassieWidgetFileSettings.widgetResponse=e,e.Scripts.forEach((e=>{const t=new class{constructor(e){this.cookieId=e.CookieID,this.scriptName=e.CookieName,this.scriptDescription=e.CookieDescription??new String(""),this.isStrictlyNecessary=e.IsStrictlyNecessary??!1,this.isRunFirst=e.IsRunFirst??!1,this.executionOrder=e.ExecutionOrder??100,this.bodyOptIn=e.CookieBodyDivID,this.bodyOptOut=e.CookieBodyDivIDOptOut,this.headOptIn=e.CookieScript,this.headOptOut=e.CookieScriptOptOut,this.cookieUrl=e.CookieURL,this.channelId=e.ChannelID,this.expiry=e.Expiry,this.browserCookieName=e.BrowserCookieName,this.gtmConsentType=e.GtmConsentType}CreateScript(e){this.cookieId=e.cookieId,this.scriptName=e.scriptName,this.scriptDescription=e.scriptDescription,this.isStrictlyNecessary=e.isStrictlyNecessary,this.isRunFirst=e.isRunFirst,this.executionOrder=e.executionOrder,this.bodyOptIn=e.bodyOptIn,this.bodyOptOut=e.bodyOptOut,this.headOptIn=e.headOptIn,this.headOptOut=e.headOptOut,this.cookieUrl=e.cookieUrl,this.channelId=e.channelID,this.gtmConsentType=e.gtmConsentType}}(e);this.cassieScripts.push(t)})),!(e.AccessKeys?.split(",").map((e=>e.toLowerCase().trim()))??[]).includes(this.cassieSettings.profileSettings.AccessKey.toLowerCase().trim())&&"n/a"!==this.cassieSettings.profileSettings.AccessKey&&!this.cassieSettings?.customOptions?.isPreview)throw new Error(`Access Key: ${this.cassieSettings.profileSettings.AccessKey}\n        can't access widget with id: ${this.cassieSettings.widgetId}`);const t=e.SupportedDomains;if(t&&!this.urlHelper.checkDomain(t)&&!this.cassieSettings?.customOptions?.isPreview)throw new Error(`Not supported domain: ${this.domain} the supported domains are ${t}.\n        Please add this domain to your Cassie system`);this.cassieSettings.supportedDomains=t,this.widgetTemplate=e.Template,this.widgetTemplate.strictlyNecessary=e.Scripts.filter((e=>e.IsStrictlyNecessary)).map((e=>({cookieName:e.CookieName,cookieDescription:e.CookieDescription}))),this.widgetTemplate.Services=e.Scripts}}}()}();</script>
        <iframe width="0" height="0" frameborder="0" src="./My ASU - Schedule_files/saved_resource(2).html" data-gtm-yt-inspected-13="true" data-gtm-yt-inspected-658644_1548="true"></iframe>
        <iframe width="0" height="0" frameborder="0" src="./My ASU - Schedule_files/saved_resource(3).html" data-gtm-yt-inspected-13="true" data-gtm-yt-inspected-658644_1548="true"></iframe>
        <iframe width="0" height="0" frameborder="0" src="./My ASU - Schedule_files/saved_resource(4).html" data-gtm-yt-inspected-13="true" data-gtm-yt-inspected-658644_1548="true"></iframe>
<script>!function(){if(void 0!==window.Element&&!("classList"in document.documentElement)){var e,t,s,i=Array.prototype,o=i.push,r=i.splice,c=i.join;a.prototype={add:function(e){this.contains(e)||(o.call(this,e),this.el.className=this.toString())},contains:function(e){return-1!=this.el.className.indexOf(e)},item:function(e){return this[e]||null},remove:function(e){if(this.contains(e)){for(var t=0;t<this.length&&this[t]!=e;t++);r.call(this,t,1),this.el.className=this.toString()}},toString:function(){return c.call(this," ")},toggle:function(e){return this.contains(e)?this.remove(e):this.add(e),this.contains(e)}},window.DOMTokenList=a,e=Element.prototype,t="classList",s=function(){return new a(this)},Object.defineProperty?Object.defineProperty(e,t,{get:s}):e.__defineGetter__(t,s)}function a(e){this.el=e;for(var t=e.className.replace(/^\s+|\s+$/g,"").split(/\s+/),s=0;s<t.length;s++)o.call(this,t[s])}}(),function(){"use strict";var e=document.createElement("i");if(e.style.setProperty("--x","y"),"y"!==e.style.getPropertyValue("--x")&&e.msMatchesSelector){Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector);var t,s=[],i=document,o=!1;document.addEventListener("DOMContentLoaded",(function(){o=!0})),"classList"in Element.prototype||M("classList",HTMLElement.prototype,Element.prototype),"innerHTML"in Element.prototype||M("innerHTML",HTMLElement.prototype,Element.prototype),"runtimeStyle"in Element.prototype||M("runtimeStyle",HTMLElement.prototype,Element.prototype),"sheet"in SVGStyleElement.prototype||Object.defineProperty(SVGStyleElement.prototype,"sheet",{get:function(){for(var e,t=document.styleSheets,s=0;e=t[s++];)if(e.ownerNode===this)return e}});var r={},c=new Set,a=!1,n=!1,l=/([\s{;])(--([A-Za-z0-9-_]*)\s*:([^;!}{]+)(!important)?)(?=\s*([;}]|$))/g,d=/([{;]\s*)([A-Za-z0-9-_]+\s*:[^;}{]*var\([^!;}{]+)(!important)?(?=\s*([;}$]|$))/g,u=/-ieVar-([^:]+):/g,y=/-ie-([^};]+)/g,p=/:(hover|active|focus|target|visited|link|:before|:after|:first-letter|:first-line)/;P("style:not([iecp-ignore])",W),P("link[rel=stylesheet]:not([iecp-ignore])",W),P("[ie-style]",(function(e){var t=H("{"+e.getAttribute("ie-style")).substr(1);e.style.cssText+=";"+t;var s=O(e.style);s.getters&&G(e,s.getters,"%styleAttr"),s.setters&&j(e,s.setters)}));var g={hover:{on:"mouseenter",off:"mouseleave"},focus:{on:"focusin",off:"focusout"},active:{on:"CSSActivate",off:"CSSDeactivate"}},_=null;document.addEventListener("mousedown",(function(e){setTimeout((function(){if(e.target===document.activeElement){var t=document.createEvent("Event");t.initEvent("CSSActivate",!0,!0),(_=e.target).dispatchEvent(t)}}))})),document.addEventListener("mouseup",(function(){if(_){var e=document.createEvent("Event");e.initEvent("CSSDeactivate",!0,!0),_.dispatchEvent(e),_=null}}));var h=0,m=new MutationObserver((function(e){if(!n)for(var t,s=0;t=e[s++];)"iecp-needed"!==t.attributeName&&$(t.target)}));setTimeout((function(){m.observe(document,{attributes:!0,subtree:!0})}));var v=location.hash;addEventListener("hashchange",(function(e){var t=document.getElementById(location.hash.substr(1));if(t){var s=document.getElementById(v.substr(1));$(t),$(s)}else $(document);v=location.hash}));var f=Object.getOwnPropertyDescriptor(HTMLElement.prototype,"style"),C=f.get;f.get=function(){const e=C.call(this);return e.owningElement=this,e},Object.defineProperty(HTMLElement.prototype,"style",f);var k=getComputedStyle;window.getComputedStyle=function(e){var t=k.apply(this,arguments);return t.computedFor=e,t};var S=CSSStyleDeclaration.prototype,b=S.getPropertyValue;S.getPropertyValue=function(e){if(this.lastPropertyServedBy=!1,"-"!==(e=e.trim())[0]||"-"!==e[1])return b.apply(this,arguments);const t=e.substr(2),s="-ie-"+t,i="-ie-❗"+t;let o=this[i]||this[s];if(this.computedFor){if(void 0===o||L[o]){if(L[o]||!q[e]||q[e].inherits){let e=this.computedFor.parentNode;for(;e&&1===e.nodeType;){var r=getComputedStyle(e),c=r[i]||r[s];if(void 0!==c){o=X(this,c),this.lastPropertyServedBy=e;break}e=e.parentNode}}}else o=X(this,o),this.lastPropertyServedBy=this.computedFor;if("initial"===o)return""}return void 0===o&&q[e]&&(o=q[e].initialValue),void 0===o?"":o};var L={inherit:1,revert:1,unset:1},T=S.setProperty;S.setProperty=function(e,t,s){if("-"!==e[0]||"-"!==e[1])return T.apply(this,arguments);const i=this.owningElement;i&&(i.ieCP_setters||(i.ieCP_setters={}),i.ieCP_setters[e]=1),e="-ie-"+("important"===s?"❗":"")+e.substr(2),this.cssText+="; "+e+":"+t+";",i&&$(i)},window.CSS||(window.CSS={});var q={};CSS.registerProperty=function(e){q[e.name]=e}}function w(e,t){try{return e.querySelectorAll(t)}catch(e){return[]}}function P(e,o){for(var r,c={selector:e,callback:o,elements:new WeakMap},a=w(i,c.selector),n=0;r=a[n++];)c.elements.set(r,!0),c.callback.call(r,r);s.push(c),t||(t=new MutationObserver(B)).observe(i,{childList:!0,subtree:!0}),A(c)}function A(e,t){var s,r=0,c=[];try{t&&t.matches(e.selector)&&c.push(t)}catch(e){}for(o&&Array.prototype.push.apply(c,w(t||i,e.selector));s=c[r++];)e.elements.has(s)||(e.elements.set(s,!0),e.callback.call(s,s))}function E(e){for(var t,i=0;t=s[i++];)A(t,e)}function B(e){for(var t,s,i,o,r=0;s=e[r++];)for(i=s.addedNodes,t=0;o=i[t++];)1===o.nodeType&&E(o)}function M(e,t,s){var i=Object.getOwnPropertyDescriptor(t,e);Object.defineProperty(s,e,i)}function W(e){if(!e.ieCP_polyfilled&&!e.ieCP_elementSheet&&e.sheet){if(e.href)return t=e.href,s=function(t){var s=H(t);t!==s&&I(e,s)},(i=new XMLHttpRequest).open("GET",t),i.overrideMimeType("text/css"),i.onload=function(){i.status>=200&&i.status<400&&s(i.responseText)},void i.send();var t,s,i,o=e.innerHTML,r=H(o);o!==r&&I(e,r)}}function H(e){return e.replace(l,(function(e,t,s,i,o,r){return t+"-ie-"+(r?"❗":"")+i+":"+o})).replace(d,(function(e,t,s,i){return t+"-ieVar-"+(i?"❗":"")+s+"; "+s}))}function O(e){e["z-index"]===e&&x();const t=e.cssText;var s,i,o=t.match(u);if(o){var c=[];for(s=0;i=o[s++];){let t=i.slice(7,-1);"❗"===t[0]&&(t=t.substr(1)),c.push(t),r[t]||(r[t]=[]),r[t].push(e)}}var a=t.match(y);if(a){var n={};for(s=0;i=a[s++];){let e=i.substr(4).split(":"),t=e[0],s=e[1];"❗"===t[0]&&(t=t.substr(1)),n[t]=s}}return{getters:c,setters:n}}function I(e,t){e.sheet.cssText=t,e.ieCP_polyfilled=!0;for(var s,i=e.sheet.rules,o=0;s=i[o++];){const e=O(s.style);e.getters&&D(s.selectorText,e.getters),e.setters&&N(s.selectorText,e.setters);const t=s.parentRule&&s.parentRule.media&&s.parentRule.media.mediaText;t&&(e.getters||e.setters)&&matchMedia(t).addListener((function(){$(document.documentElement)}))}V()}function D(e,t){R(e),P(F(e),(function(s){G(s,t,e),z(s)}))}function G(e,t,s){var i,o,r=0;const c=s.split(",");for(e.setAttribute("iecp-needed",!0),e.ieCPSelectors||(e.ieCPSelectors={});i=t[r++];)for(o=0;s=c[o++];){const t=s.trim().split("::");e.ieCPSelectors[i]||(e.ieCPSelectors[i]=[]),e.ieCPSelectors[i].push({selector:t[0],pseudo:t[1]?"::"+t[1]:""})}}function N(e,t){R(e),P(F(e),(function(e){j(e,t)}))}function j(e,t){for(var s in e.ieCP_setters||(e.ieCP_setters={}),t)e.ieCP_setters["--"+s]=1;$(e)}function V(){for(var e in r){let o=r[e];for(var t,s=0;t=o[s++];)if(!t.owningElement){var i=t["-ieVar-"+e];if(i&&""!==(i=X(getComputedStyle(document.documentElement),i)))try{t[e]=i}catch(e){}}}}function R(e){for(var t in e=e.split(",")[0],g){var s=e.split(":"+t);if(s.length>1){var i=s[1].match(/^[^\s]*/);let e=F(s[0]+i);const o=g[t];P(e,(function(e){e.addEventListener(o.on,Z),e.addEventListener(o.off,Z)}))}}}function F(e){return e.replace(p,"").replace(":not()","")}function z(e){c.add(e),a||(a=!0,requestAnimationFrame((function(){a=!1,n=!0,c.forEach(U),c.clear(),setTimeout((function(){n=!1}))})))}function U(e){e.ieCP_unique||(e.ieCP_unique=++h,e.classList.add("iecp-u"+e.ieCP_unique));var t=getComputedStyle(e);let s="";for(var i in e.runtimeStyle.cssText="",e.ieCPSelectors){var o=t["-ieVar-❗"+i];let l=o||t["-ieVar-"+i];if(l){var r={},c=X(t,l,r);o&&(c+=" !important");for(var a,n=0;a=e.ieCPSelectors[i][n++];)"%styleAttr"===a.selector&&(e.style[i]=c),(o||!1===r.allByRoot)&&(a.pseudo?s+=a.selector+".iecp-u"+e.ieCP_unique+a.pseudo+"{"+i+":"+c+"}\n":e.runtimeStyle[i]=c)}}!function(e,t){if(!e.ieCP_styleEl&&t){const t=document.createElement("style");t.ieCP_elementSheet=1,document.head.appendChild(t),e.ieCP_styleEl=t}e.ieCP_styleEl&&(e.ieCP_styleEl.innerHTML=t)}(e,s)}function $(e){if(e){e===document.documentElement&&V();var t=e.querySelectorAll("[iecp-needed]");e.hasAttribute&&e.hasAttribute("iecp-needed")&&z(e);for(var s,i=0;s=t[i++];)z(s)}}function Z(e){$(e.target)}function X(e,t,s){return function(e,t){let s,i,o=0,r=null,c=0,a="",n=0;for(;s=e[n++];){if("("===s&&(++o,null===r&&e[n-4]+e[n-3]+e[n-2]==="var"&&(r=o,a+=e.substring(c,n-4),c=n),e[n-5]+e[n-4]+e[n-3]+e[n-2]==="calc"&&(i=o)),")"===s&&r===o){let s,o=e.substring(c,n-1).trim(),l=o.indexOf(",");-1!==l&&(s=o.slice(l+1),o=o.slice(0,l)),a+=t(o,s,i),c=n,r=null}")"===s&&(--o,i===o&&(i=null))}return a+=e.substring(c),a}(t,(function(t,i,o){var r=e.getPropertyValue(t);return o&&(r=r.replace(/^calc\(/,"(")),s&&e.lastPropertyServedBy!==document.documentElement&&(s.allByRoot=!1),""===r&&i&&(r=X(e,i,s)),r}))}}(),window.cassieTemplateModule=function(){var e,t,s="#cassie_accept_all_pre_banner",i="#cassie_reject_all_pre_banner",o=".cassie-view-all",r="#cassie_save_preferences",c="#cassie_manage_cookies_button",a="#cassie_expand_strictly_necessary",n="#cassie_gpc_accept_all_pre_banner",l="#cassie_gpc_accept_all_excluding_pre_banner",d="#cassie_gpc_back_pre_banner",u=CassieWidgetLoader.Widget.widgetTemplate,y=document.getElementById("cassie-widget"),p=CassieWidgetLoader.Widget.gpcEnabled,g=CassieWidgetLoader.Widget.eventManager,_=u.ForceBannerInteraction;function h(e){var t=y.querySelector("#cassie_consent_tab_cookies"),s=y.querySelector("#cassie_strictly_necessary").cloneNode(!0);s.classList.add("cassie-cookie-modal--select--group"),s.classList.remove("cassie-cookie-modal--strictly-necessary"),s.id="cassie_cookie_modal_group_"+e.ChannelID,s.style.display="";var i=s.querySelector(".cassie-cookie-modal--group-head-container");i.id="cassie_cookie_group_header_"+e.ChannelID;var o=s.querySelector(".cassie-expand-cookies--container");o.classList.remove("cassie-strictly-necessary--expand"),o.id="cassie_expand_cookies_container_"+e.ChannelID;var r=o.querySelector(".cassie-expand-cookies--icon");r.id="cassie_expand_cookies_icon_"+e.ChannelID;var c=s.querySelector(".cassie-cookie-group--heading");c.classList.remove("cassie-strictly-necessary--heading"),c.id="cassie_cookie_group_heading_"+e.ChannelID,c.innerHTML=e.ChannelTypeParent;var a=s.querySelector(".cassie-cookie-group--description");a.classList.remove("cassie-strictly-necessary--description"),a.id="cassie_cookie_group_description_"+e.ChannelID,a.innerHTML=e.Statements[0].StatementText,t.appendChild(s);var n=e.Cookies,l=s.querySelector(".cassie-cookie-children--container");l.classList.remove("cassie-strictly-necessary--children--container"),l.id="cassie_children_cookies_container_"+e.ChannelID,m(i,o,e.ChannelID,e.ChannelTypeParent),function(e,t,s){for(var i=s.querySelector(".cassie-cookie--child"),o=0;o<t.length;o++){var r=i.cloneNode(!0);childCookie=t[o];var c=e.ChannelID+"_"+childCookie.Statements[0].StatementID;r.classList.remove("cassie-strictly-necessary--child-cookie"),r.id="cassie_cookie_child_"+c;var a=r.querySelector(".cassie-cookie--child--heading");a.classList.remove("cassie-strictly-necessary--child--heading"),a.id="cassie_cookie_child_heading_"+c,a.innerHTML=childCookie.ChannelType,p&&childCookie.GpcEnabled&&S(childCookie,a),p&&childCookie.GPCEnabled&&S(childCookie,a);var n=r.querySelector(".cassie-cookie--child--description");n.classList.remove("cassie-strictly-necessary--child--description"),n.id="cassie_cookie_child_description_"+c,n.innerHTML=childCookie.Statements[0].StatementText,s.appendChild(r),v(r,childCookie.Statements[0].StatementID,a,childCookie.ChannelID,a.id,childCookie.ChannelType)}u.strictlyNecessary&&k(s)}(e,n,l),E(o,l,r)}function m(e,t,s,i){var o=y.querySelector("#cassie_accept_all_toggle_switch").cloneNode(!0),r=e.id,c="_li_";o.classList.remove("cassie-accept-all--toggle-switch"),o.classList.add("cassie-cookie-group--toggle-switch"),o.ariaLabel=i,r.includes(c)?o.id="cassie_cookie_group_toggle_switch_li_"+s:o.id="cassie_cookie_group_toggle_switch_"+s,o.setAttribute("name","cassie_cookie_group_toggle_switch_"+s),e.querySelector("h3").setAttribute("for","cassie_cookie_group_toggle_switch_"+s);var a=o.querySelector(".cassie-toggle-switch--status");a.classList.remove("cassie-accept-all--toggle-switch--status"),a.classList.add("cassie-cookie-group--toggle-switch--status"),r.includes(c)?a.id="cassie_cookie_group_toggle_switch_status_li_"+s:a.id="cassie_cookie_group_toggle_switch_status_"+s;var n,l=o.querySelector(".cassie-toggle-switch--slider");l.classList.remove("cassie-accept-all--toggle-switch--slider"),l.classList.add("cassie-cookie-group--toggle-switch--slider"),r.includes(c)?l.id="cassie_cookie_group_toggle_switch_slider_li_"+s:l.id="cassie_cookie_group_toggle_switch_slider_"+s,e.insertBefore(o,t.nextSibling),(n=o).addEventListener("click",(function(){L(n)})),n.addEventListener("keydown",(function(e){13!=e.keyCode&&32!=e.keyCode||L(n)}))}function v(e,t,s,i,o,r){var c=y.querySelector("#cassie_accept_all_toggle_switch").cloneNode(!0);c.classList.remove("cassie-accept-all--toggle-switch"),c.classList.add("cassie-child-cookie--toggle-switch"),c.ariaLabel=r,c.id="cassie_child_cookie_toggle_switch_"+t,c.setAttribute("name","cassie_child_cookie_toggle_switch_"+t),s.setAttribute("for","cassie_child_cookie_toggle_switch_"+t);var a=c.querySelector(".cassie-toggle-switch--status");a.classList.remove("cassie-accept-all--toggle-switch--status"),a.classList.add("cassie-child-cookie--toggle-switch--status"),a.id="cassie_child_cookie_toggle_switch_status_"+t;var n=c.querySelector(".cassie-toggle-switch--slider");n.classList.remove("cassie-accept-all--toggle-switch--slider"),n.classList.add("cassie-child-cookie--toggle-switch--slider"),n.id="cassie_child_cookie_toggle_switch_slider_"+t,n.dataset.channelId=i,n.dataset.statementId=t,e.insertBefore(c,s.nextSibling),T(c,0,n)}function f(e){var t=y.querySelector("#cassie_legitimate_interests_tab_cookies"),s=y.querySelector("#cassie_strictly_necessary").cloneNode(!0);s.classList.add("cassie-cookie-modal--select--group"),s.classList.remove("cassie-cookie-modal--strictly-necessary"),s.id="cassie_cookie_modal_group_li_"+e.ChannelID,s.style.display="";var i=s.querySelector(".cassie-cookie-modal--group-head-container");i.id="cassie_cookie_group_header_li_"+e.ChannelID;var o=s.querySelector(".cassie-expand-cookies--container");o.classList.remove("cassie-strictly-necessary--expand"),o.id="cassie_expand_cookies_container_li_"+e.ChannelID;var r=o.querySelector(".cassie-expand-cookies--icon");r.id="cassie_expand_cookies_icon_li_"+e.ChannelID;var c=s.querySelector(".cassie-cookie-group--heading");c.classList.remove("cassie-strictly-necessary--heading"),c.id="cassie_cookie_group_heading_li_"+e.ChannelID,c.innerHTML=e.ChannelTypeParent;var a=s.querySelector(".cassie-cookie-group--description");a.classList.remove("cassie-strictly-necessary--description"),a.id="cassie_cookie_group_description_li_"+e.ChannelID,a.innerHTML=e.Statements[0].StatementText,t.appendChild(s);var n=e.Cookies,l=s.querySelector(".cassie-cookie-children--container");l.classList.remove("cassie-strictly-necessary--children--container"),l.id="cassie_children_cookies_container_li_"+e.ChannelID,m(i,o,e.ChannelID,e.ChannelTypeParent),function(e,t,s){for(var i=s.querySelector(".cassie-cookie--child"),o=0;o<t.length;o++){var r=i.cloneNode(!0);childCookie=t[o];var c=e.ChannelID+"_"+childCookie.Statements[0].StatementID;r.classList.remove("cassie-strictly-necessary--child-cookie"),r.id="cassie_cookie_child_"+c;var a=r.querySelector(".cassie-cookie--child--heading");a.classList.remove("cassie-strictly-necessary--child--heading"),a.id="cassie_cookie_child_heading_"+c,a.innerHTML=childCookie.ChannelType,p&&childCookie.GpcEnabled&&S(childCookie,a);var n=r.querySelector(".cassie-cookie--child--description");n.classList.remove("cassie-strictly-necessary--child--description"),n.id="cassie_cookie_child_description_"+c,n.innerHTML=childCookie.Statements[0].StatementText,s.appendChild(r),C(r,childCookie.Statements[0].StatementID,a,childCookie.ChannelID,a.id,childCookie.GpcEnabled,childCookie.ChannelType)}u.strictlyNecessary&&k(s)}(e,n,l),E(o,l,r)}function C(e,t,s,i,o,r,c){var a=y.querySelector("#cassie_accept_all_toggle_switch").cloneNode(!0);a.classList.remove("cassie-accept-all--toggle-switch"),a.classList.add("cassie-child-cookie--toggle-switch"),a.ariaLabel=c,a.id="cassie_child_cookie_toggle_switch_"+t;var n=a.querySelector(".cassie-toggle-switch--status");n.classList.remove("cassie-accept-all--toggle-switch--status"),n.classList.add("cassie-child-cookie--toggle-switch--status"),n.id="cassie_child_cookie_toggle_switch_status_"+t;var l=a.querySelector(".cassie-toggle-switch--slider");l.classList.remove("cassie-accept-all--toggle-switch--slider"),l.classList.add("cassie-child-cookie--toggle-switch--slider"),l.classList.add("cassie-toggle-switch--slider--active"),p&&r&&l.classList.remove("cassie-toggle-switch--slider--active"),l.id="cassie_child_cookie_toggle_switch_slider_"+t,l.dataset.channelId=i,l.dataset.statementId=t,e.insertBefore(a,s.nextSibling),T(a,0,l)}function k(e){for(var t=e.querySelectorAll(".cassie-strictly-necessary--child-cookie"),s=0;s<t.length;s++){var i=t[s];e.removeChild(i)}}function S(e,t){var s=document.createElement("span");s.classList.add("cassie-gpc-flag"),s.innerHTML="GPC",t.appendChild(s)}function b(){var e=y.querySelector("#cassie_accept_all_toggle_switch"),t=y.querySelector("#cassie_accept_all_toggle_slider"),s=y.querySelectorAll(".cassie-toggle-switch--slider"),i=y.querySelectorAll(".cassie-toggle-switch--status"),o=y.querySelectorAll(".cassie-toggle-switch"),r=y.querySelector("#cassie_accept_all_toggle_switch_status"),c=u.ConsentOptInText,a=u.ConsentOptOutText;t.classList.toggle("cassie-toggle-switch--slider--active"),t.classList.contains("cassie-toggle-switch--slider--active")?e.ariaChecked="true":e.ariaChecked="false",t.classList.contains("cassie-toggle-switch--slider--active")?(s.forEach((function(e){e.classList.add("cassie-toggle-switch--slider--active")})),r.innerHTML=c,i.forEach((function(e){e.innerHTML=c})),o.forEach((function(e){e.ariaChecked="true"}))):(s.forEach((function(e){e.classList.remove("cassie-toggle-switch--slider--active")})),r.innerHTML=a,i.forEach((function(e){e.innerHTML=a})),o.forEach((function(e){e.ariaChecked="false"})))}function L(e){var t=e.querySelector(".cassie-cookie-group--toggle-switch--slider"),s="cassie-toggle-switch--slider--active",i=function(e,t){for(;(e=e.parentElement)&&!e.classList.contains("cassie-cookie-modal--select--group"););return e}(e).querySelectorAll(".cassie-child-cookie--toggle-switch--slider");t.classList.toggle(s);var o=t.classList.contains(s),r=e.querySelector(".cassie-toggle-switch--status");t.classList.contains(s)?(r.innerHTML=u.ConsentOptInText,e.ariaChecked="true"):(r.innerHTML=u.ConsentOptOutText,e.ariaChecked="false");for(var c=0;c<i.length;c++){var a=i[c];o&&a.classList.add(s),!o&&a.classList.remove(s);var n=a.parentNode.firstElementChild,l=a.parentElement;o?(n.innerHTML=u.ConsentOptInText,l.ariaChecked=!0):(n.innerHTML=u.ConsentOptOutText,l.ariaChecked=!1)}I()}function T(e,t,s){e.addEventListener("click",(function(){q(s,e)})),e.addEventListener("keydown",(function(t){13!=t.keyCode&&32!=t.keyCode||q(s,e)}))}function q(e,t){e.classList.toggle("cassie-toggle-switch--slider--active");var s=t.querySelector(".cassie-toggle-switch--status");e.classList.contains("cassie-toggle-switch--slider--active")?(s.innerHTML=u.ConsentOptInText,t.ariaChecked="true"):(s.innerHTML=u.ConsentOptOutText,t.ariaChecked="false"),I()}function w(){var e=y.querySelector("#cassie_strictly_necessary_children_container"),t=y.querySelector("#cassie_strictly_necessary_expand_icon");e.classList.toggle("cassie-cookie-children--container--open"),t.classList.toggle("cassie-expand-cookies--icon--open"),e.previousElementSibling,t.classList.contains("cassie-expand-cookies--icon--open")?(e.ariaHidden="false",e.removeAttribute("hidden")):(e.ariaHidden="true",e.setAttribute("hidden",!0))}function x(){for(var e=!0,t=("none"!=y.querySelector("#cassie_consent_tab_cookies").style.display?y.querySelector("#cassie_consent_tab_cookies"):y.querySelector("#cassie_legitimate_interests_tab_cookies")).querySelectorAll(".cassie-cookie-group--toggle-switch--slider"),s=0;s<t.length;s++)t[s].classList.contains("cassie-toggle-switch--slider--active")||(e=!1);e?(y.querySelector("#cassie_accept_all_toggle_slider").classList.add("cassie-toggle-switch--slider--active"),y.querySelector("#cassie_accept_all_toggle_switch_status").innerHTML=u.ConsentOptInText,y.querySelector("#cassie_accept_all_toggle_switch").ariaChecked="true"):(y.querySelector("#cassie_accept_all_toggle_slider").classList.remove("cassie-toggle-switch--slider--active"),y.querySelector("#cassie_accept_all_toggle_switch_status").innerHTML=u.ConsentOptOutText,y.querySelector("#cassie_accept_all_toggle_switch").ariaChecked="false")}function P(){var e=y.querySelector("#cassie_legitimate_interests_tab_cookies"),t=y.querySelector("#cassie_consent_tab_cookies");y.querySelector("#cassie_consent_button").classList.add("cassie-active--button"),y.querySelector("#cassie_legitimate_interests_button").classList.remove("cassie-active--button"),e.style.display="none",t.style.display="block",x()}function A(){var e=y.querySelector("#cassie_legitimate_interests_tab_cookies"),t=y.querySelector("#cassie_consent_tab_cookies");y.querySelector("#cassie_legitimate_interests_button").classList.add("cassie-active--button"),y.querySelector("#cassie_consent_button").classList.remove("cassie-active--button"),t.style.display="none",e.style.display="block",x()}function E(e,t,s){e.addEventListener("click",(function(){t.classList.toggle("cassie-cookie-children--container--open"),s.classList.toggle("cassie-expand-cookies--icon--open"),e.parentNode,s.classList.contains("cassie-expand-cookies--icon--open")?(t.ariaHidden="false",t.removeAttribute("hidden")):(t.ariaHidden="true",t.setAttribute("hidden",!0))})),e.addEventListener("keydown",(function(e){32!=e.keyCode&&13!=e.keyCode||(t.classList.toggle("cassie-cookie-children--container--open"),s.classList.toggle("cassie-expand-cookies--icon--open"))}))}function B(){D(CassieWidgetLoader.Widget.cassieSettings.profileSettings.LoadTemplateCss);var e=y.querySelector(".cassie-gpc-pre-banner");e.classList.remove("cassie-d-none-important"),e.style.removeProperty("display"),g.emit("CassieGPCBannerDisplayed");var t=y.querySelector(".cassie-pre-banner");t.classList.add("cassie-d-none-important"),e.querySelector("#cassie_gpc_back_pre_banner").addEventListener("click",(function(s){g.emit("CassieGPCBackClick"),e.classList.add("cassie-d-none-important"),e.style.display="none",t.classList.remove("cassie-d-none-important"),g.emit("CassieGPCOnClose")})),e.querySelector("#cassie_gpc_accept_all_pre_banner").addEventListener("click",(function(e){g.emit("CassieGPCAcceptAllClick"),CassieWidgetLoader.Widget.hideBanner(),CassieWidgetLoader.Widget.acceptAll("prebanner_accept_all"),g.emit("CassieGPCOnClose"),O()})),e.querySelector("#cassie_gpc_accept_all_excluding_pre_banner").addEventListener("click",(function(e){g.emit("CassieGPCAcceptAllExGPCCLick"),CassieWidgetLoader.Widget.hideBanner(),CassieWidgetLoader.Widget.acceptAll({respectGpc:!0,source:"accept_all_button"}),g.emit("CassieGPCOnClose"),O()}))}function M(){var e=y.querySelector(".cassie-cookie-module");e.classList.contains("cassie-d-none")&&e.classList.remove("cassie-d-none")}function W(){var e=y.querySelector(".cassie-cookie-module");!e.classList.contains("cassie-d-none")&&e.classList.add("cassie-d-none")}function H(){for(var e=CassieWidgetLoader.Widget.getCurrentConsent(),t=y.querySelectorAll("[data-channel-id]"),s=0;s<e.length;s++)for(var i=e[s],o=0;o<t.length;o++){var r=t[o],c=r.parentNode,a=r.parentNode.firstElementChild;r.dataset.channelId==i.channelId&&r.dataset.statementId==i.statementId&&(i.isOptIn?(r.classList.add("cassie-toggle-switch--slider--active"),a.innerHTML=u.ConsentOptInText,c.ariaChecked=!0):(r.classList.remove("cassie-toggle-switch--slider--active"),a.innerHTML=u.ConsentOptOutText,c.ariaChecked=!1))}I()}function O(){var e=u.DisplayCookieSettingsButton,t=y.querySelector(".cassie-manage-cookies--container"),s=y.querySelector(".cassie-cookie-module").classList.contains("cassie-d-none");t.style.display=e&&s?"block":"none"}function I(){for(var e=y.querySelectorAll(".cassie-cookie-modal--select--group"),t=0;t<e.length;t++){for(var s=e[t],i=s.querySelectorAll(".cassie-child-cookie--toggle-switch--slider"),o=!0,r=0;r<i.length;r++){var c=i[r];c.parentNode,c.classList.contains("cassie-toggle-switch--slider--active")||(o=!1)}o?(s.querySelector(".cassie-cookie-group--toggle-switch--slider").classList.add("cassie-toggle-switch--slider--active"),s.querySelector(".cassie-toggle-switch--status").innerHTML=u.ConsentOptInText,s.querySelector(".cassie-cookie-group--toggle-switch").ariaChecked="true"):(s.querySelector(".cassie-cookie-group--toggle-switch--slider").classList.remove("cassie-toggle-switch--slider--active"),s.querySelector(".cassie-toggle-switch--status").innerHTML=u.ConsentOptOutText,s.querySelector(".cassie-cookie-group--toggle-switch").ariaChecked="false"),x()}}function D(e){var t=document.querySelector(":root"),s=CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.GpcBannerColourScheme;s&&e&&p&&(t.style.setProperty("--gpc-banner-background-color",s?.BackgroundColour),t.style.setProperty("--gpc-banner-secondary-color",s?.SecondaryColour),t.style.setProperty("--gpc-banner-text-color",s?.TextColour),t.style.setProperty("--gpc-banner-accept-all-button-color",s?.AcceptAllButtonColour),t.style.setProperty("--gpc-banner-accept-all-button-text-color",s?.AcceptAllButtonTextColour),t.style.setProperty("--gpc-banner-accept-all-exc-gpc-button-color",s?.AcceptAllExcGpcButtonColour),t.style.setProperty("--gpc-banner-accept-all-exc-gpc-button-text-color",s?.AcceptAllExcGpcButtonTextColour),t.style.setProperty("--gpc-banner-back-button-color",s?.BackButtonColour),t.style.setProperty("--gpc-banner-back-button-text-color",s?.BackButtonTextColour))}function G(e){var t=document.querySelector(":root"),s=CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.BannerColourScheme;s&&e&&(t.style.setProperty("--banner-background-color",s?.BackgroundColour),t.style.setProperty("--banner-secondary-color",s?.SecondaryColour),t.style.setProperty("--banner-text-color",s?.TextColour),t.style.setProperty("--banner-submit-preferences-button-color",s?.SubmitPreferencesButtonColour),t.style.setProperty("--banner-submit-preferences-button-text-color",s?.SubmitPreferencesButtonTextColour),t.style.setProperty("--banner-slider-background-off",s?.SliderBackgroundOff),t.style.setProperty("--banner-slider-background-on",s?.SliderBackgroundOn),t.style.setProperty("--banner-slider-toggle",s?.SliderToggle),t.style.setProperty("--scrollbar-background-color",s?.SecondaryColour),t.style.setProperty("--scrollbar-thumb-color",s?.TextColour),t.style.setProperty("--scrollbar-thumb-hover-color",s?.AcceptAllButtonColour),t.style.setProperty("--cookie-modal-background-color",s?.BackgroundColour),t.style.setProperty("--cookie-modal-text-color",s?.TextColour),t.style.setProperty("--close-modal-button-color",s?.SecondaryColour),t.style.setProperty("--header-content-top-border-color",s?.SecondaryColour),t.style.setProperty("--cookie-modal-text-color",s?.TextColour),t.style.setProperty("--main-content-border-colour",s?.SecondaryColour),t.style.setProperty("--tab-button-background-color",s?.SecondaryColour),t.style.setProperty("--tab-button-text-color",s?.TextColour),t.style.setProperty("--tab-button-border-color",s?.SecondaryColour),t.style.setProperty("--active-button-border-color",s?.SecondaryColour),t.style.setProperty("--cookie-group-bottom-border-colour",s?.SecondaryColour),t.style.setProperty("--expand-cookies-icon-color",s?.AcceptAllButtonColour),t.style.setProperty("--cookie-children-container-background-color",s?.TextColour),t.style.setProperty("--cookie-children-container-text-color",s?.SecondaryColour),t.style.setProperty("--slider-background-colour-unchecked",s?.SliderBackgroundOff),t.style.setProperty("--toggle-background-color",s?.SliderToggle),t.style.setProperty("--slider-background-colour-checked",s?.SliderBackgroundOn),t.style.setProperty("--manage-cookies-button-background-color",s?.BackgroundColour),t.style.setProperty("--manage-cookies-button-text-color",s?.TextColour),t.style.setProperty("----manage-cookies-button-border-color",s?.AcceptAllButtonColour))}function N(){var e=document.documentElement,t=document.body;e.style.margin="0",e.style.overflow="hidden",t.style.margin="0",t.style.overflow="hidden"}function j(){var e=document.documentElement,t=document.body;e.hasAttribute("style")&&(e.style.removeProperty("margin"),e.style.removeProperty("height"),e.style.removeProperty("overflow")),t.hasAttribute("style")&&t.removeAttribute("style")}null==_&&(_=!0),CassieWidgetLoader.Widget.registerExtention("showModal",(function(){var e=CassieWidgetLoader.Widget.cassieSettings.profileSettings.LoadTemplateCss;H(),M(),G(e),y.querySelector(".cassie-cookie-modal").style.display="flex";var t=y.querySelector(".cassie-overlay");t&&(t.style.display="block"),y.querySelector(".cassie-pre-banner").style.display="none",y.style.display="block",y.querySelector(".cassie-accept-all--toggle-switch").focus(),_&&N()})),CassieWidgetLoader.Widget.registerExtention("hideModal",(function(){W(),y.querySelector(".cassie-cookie-modal").style.display="none";var e=y.querySelector(".cassie-overlay");e&&(e.style.display="none"),j()})),CassieWidgetLoader.Widget.registerExtention("showBanner",(function(){CassieWidgetLoader.Widget.hideModal(),M();var e=y.querySelector(".cassie-overlay");e&&(e.style.display="block"),y.querySelector(".cassie-pre-banner").style.display="flex",_&&N()})),CassieWidgetLoader.Widget.registerExtention("hideBanner",(function(){var e=CassieWidgetLoader.Widget.widgetTemplate.hasConsent,t=y.querySelector(".cassie-close-modal-button");e||!_?t.classList.add("cassie-d-block"):t.classList.add("cassie-d-none"),y.querySelector(".cassie-pre-banner").style.display="none",y.querySelector(".cassie-gpc-pre-banner").style.display="none";var s=y.querySelector(".cassie-overlay");s&&(s.style.display="none"),O(),j(),W()})),function(){y.querySelector("#cassie_pre_banner_text").innerHTML=u.PreBannerText,y.querySelector(s).innerHTML=u.AcceptAllCookiesText,y.querySelector(i).innerHTML=u.RejectAllCookiesText,y.querySelector("#cassie_view_all_pre_banner").innerHTML=u.ViewAllOptionsText,y.querySelector("#cassie_strictly_necessary_heading").innerHTML=u.StrictlyNecessaryName,y.querySelector("#cassie_strictly_necessary_description").innerHTML=u.StrictlyNecessaryDescription,y.querySelector("#cassie_cookie_footer_content").innerHTML=u.FooterHTML;var e=y.querySelector(r);e.innerHTML=u.SavePreferenceText,e.setAttribute("aria-label",u.SavePreferenceText),y.querySelector("#cassie_header_image").innerHTML=u.ImageUrl;var t=y.querySelector("#cassie_header_content");u.HeaderHTML?t.innerHTML=u.HeaderHTML:t.classList.add("cassie-d-none"),y.querySelector("#cassie_accept_all_cookies_text").innerHTML=u.BannerAcceptAllButtonText?u.BannerAcceptAllButtonText:u.AcceptAllCookiesText,y.querySelector(c).innerHTML=u.OpenCookieSettingsText;var o=y.querySelector("#cassie_consent_button");o.innerHTML=u.UserConsentTabText,o.setAttribute("aria-label",u.UserConsentTabText);var a=y.querySelector("#cassie_legitimate_interests_button");a.innerHTML=u.LegitimateInterestTabText,a.setAttribute("aria-label",u.LegitimateInterestTabText),y.querySelector("#gpc_banner_title").innerHTML=u.GpcBannerTitle,y.querySelector("#cassie_gpc_pre_banner_text").innerHTML=u.GpcBannerText;var p=y.querySelector(n);p.innerHTML=u.GpcBannerAcceptAllCookiesButtonText,p.setAttribute("aria-label",u.GpcBannerAcceptAllCookiesButtonText);var g=y.querySelector(l);g.innerHTML=u.GpcBannerAcceptAllExGpcButtonText,g.setAttribute("aria-label",u.GpcBannerAcceptAllExGpcButtonText);var _=y.querySelector(d);_.innerHTML=u.GpcBackButtonText,_.setAttribute("aria-label",u.GpcBackButtonText)}(),function(){var e=u.strictlyNecessary,t=y.querySelector("#cassie_strictly_necessary_children_container"),s=y.querySelector("#cassie_strictly_necessary");0===e.length&&(s.style.display="none");for(var i=0;i<e.length;i++){var o=document.createElement("div"),r=document.createElement("h4"),c=document.createElement("p"),n=e[i].cookieName.toLowerCase();n=n.replace(" ","_"),o.setAttribute("class","cassie-strictly-necessary--child-cookie cassie-cookie--child"),o.setAttribute("id","cassie_strictly_necessary_child_cookie_"+n),r.setAttribute("class","cassie-strictly-necessary--child--heading cassie-cookie--child--heading"),r.setAttribute("id","cassie_strictly_necessary_heading_"+n),c.setAttribute("class","cassie-strictly-necessary--child--description cassie-cookie--child--description"),c.setAttribute("id","cassie_strictly_necessary_description_"+n);var l=document.createTextNode(e[i].cookieName);document.createTextNode(e[i].cookieDescription),r.appendChild(l),c.innerHTML=e[i].cookieDescription,o.appendChild(r),o.appendChild(c),t.appendChild(o),y.querySelector(a)}}(),function(){"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(e,t){"use strict";if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var s=Object(e),i=1;i<arguments.length;i++){var o=arguments[i];if(null!=o)for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(s[r]=o[r])}return s},writable:!0,configurable:!0});for(var e=u.Categories,t=e.filter((function(e){return e.Cookies.some((function(e){return false===e.IsLegitimateInterest}))})).map((function(e){return Object.assign({},e,{Cookies:e.Cookies.filter((function(e){return false===e.IsLegitimateInterest}))})})),s=e.filter((function(e){return e.Cookies.some((function(e){return true===e.IsLegitimateInterest}))})).map((function(e){return Object.assign({},e,{Cookies:e.Cookies.filter((function(e){return true===e.IsLegitimateInterest}))})})),i=0;i<t.length;i++)h(t[i]);for(var o=0;o<s.length;o++)f(s[o]);var r=y.querySelector("#cassie_strictly_necessary_children_container"),c=y.querySelector("#cassie_strictly_necessary_child_cookie");r.removeChild(c)}(),function(){var e=y.querySelector("#cassie_accept_all_toggle_switch");e.addEventListener("click",b),e.addEventListener("keydown",(function(e){32!=e.keyCode&&13!=e.keyCode||b()}));var t=y.querySelector(a);t.addEventListener("click",w),t.addEventListener("keydown",(function(e){32!=e.keyCode&&13!=e.keyCode||w()})),y.querySelector("#cassie_consent_button").addEventListener("click",P),y.querySelector("#cassie_legitimate_interests_button").addEventListener("click",A);var n=CassieWidgetLoader.Widget.widgetTemplate.hasConsent;y.querySelector(r).addEventListener("click",(function(){var e=CassieWidgetLoader.Widget.gpcEnabled,t=document.querySelector("#cassie_accept_all_toggle_slider").classList.contains("cassie-toggle-switch--slider--active");if(e&&t&&!n)document.querySelector(".cassie-cookie-modal").style.display="none",B();else{var s=function(e,t){for(var s=y.querySelectorAll(".cassie-child-cookie--toggle-switch--slider"),i=[],o=0;o<s.length;o++){var r=s[o],c=r.classList.contains("cassie-toggle-switch--slider--active"),a={channelId:r.dataset.channelId,statementId:r.dataset.statementId,isOptIn:c};i.push(a)}return i}();CassieWidgetLoader.Widget.hideModal(),n=!0,O(),CassieWidgetLoader.Widget.submitConsent(s,"save_pref_button")}H()})),y.querySelector("#cassie_close_modal_button").addEventListener("click",(function(){CassieWidgetLoader.Widget.hideModal(),O()})),y.querySelector(s).addEventListener("click",(function(e){p?B():(CassieWidgetLoader.Widget.hideBanner(),O(),CassieWidgetLoader.Widget.acceptAll("prebanner_accept_all")),H()})),y.querySelector(i).addEventListener("click",(function(e){CassieWidgetLoader.Widget.hideBanner(),CassieWidgetLoader.Widget.rejectAll("prebanner_reject_all"),O(),H()})),y.querySelector(o).addEventListener("click",(function(){CassieWidgetLoader.Widget.hideBanner(),CassieWidgetLoader.Widget.showModal()})),y.querySelector(c).addEventListener("click",(function(){CassieWidgetLoader.Widget.hideBanner(),CassieWidgetLoader.Widget.showModal(),O()}))}(),function(){var e=CassieWidgetLoader.Widget.widgetTemplate.hasConsent,t=CassieWidgetLoader.Widget.widgetTemplate.HidePreBanner,r=y.querySelector(".cassie-pre-banner");r.style.display=e||t?"none":"flex",!e&&!t&&M(),"flex"==r.style.display&&(g.emit("CassieBannerVisibility",!0),_&&N()),y.querySelector(".cassie-cookie-modal").style.display=!e&&t?"flex":"none";var c=y.querySelector(".cassie-overlay");c&&(c.style.display=e&&"none");var a=y.querySelector(".cassie-close-modal-button");e||a.classList.add("cassie-d-none");var h,m,v=CassieWidgetLoader.Widget.cassieSettings.baseUrl+"/templates/"+CassieWidgetLoader.Widget.cassieSettings.profileSettings.Template+"/Images/cassie_logo_white.svg";y.querySelector(".cassie-cookie-modal--footer-image").src=v,y.querySelector("#cassie_legitimate_interests_tab_cookies").style.display="none";for(var f=y.querySelectorAll(".cassie-child-cookie--toggle-switch--slider"),C=0;C<f.length;C++){var k=f[C],S=k.parentNode.firstChild;k.classList.contains("cassie-toggle-switch--slider--active")?S.innerHTML=u.ConsentOptInText:S.innerHTML=u.ConsentOptOutText}(function(){var e=u.AutoShowModal,t=u.DisplayCookieSettingsButton,r=u.HideAcceptAllButton,c=u.HidePreBanner,a=u.HideRejectAllButton,p=u.HideViewOptionsButton,h=u.hasConsent,m=u.HideGpcBannerAcceptAllCookiesButton,v=u.HideGpcBannerAcceptAllExGpcButton,f=u.HideGpcBannerBackButton,C=y.querySelector(".cassie-cookie-modal"),k=y.querySelector(".cassie-manage-cookies--container"),S=y.querySelector(".cassie-overlay");_||S.remove();var b=y.querySelector(s),L=y.querySelector(".cassie-pre-banner"),T=y.querySelector(i),q=y.querySelector(o),w=document.getElementById("cassie-widget");1==r&&(b.style.display="none"),1==a&&(T.style.display="none"),1==p&&(q.style.display="none");var x=y.querySelector(n),P=y.querySelector(l),A=y.querySelector(d);1==m&&(x.style.display="none"),1==v&&(P.style.display="none"),1==f&&(A.style.display="none"),0==t&&(k.style.display="none"),1==c&&1==e&&0==h&&(g.emit("CassieModalVisibility",!0),L.style.display="none",C.style.display="flex",(S=y.querySelector(".cassie-overlay"))&&(S.style.display="block"),y.querySelector(".cassie-cookie-module").classList.remove("cassie-d-none"),_&&N()),1==c&&0==e&&(w.style.display="none")})(),h=y.querySelector(".cassie-cookie-modal--tabs--container"),m=y.querySelector("#cassie_legitimate_interests_tab_cookies"),u.ShowLegitimateInterestTab||(h.style.display="none",h.setAttribute("aria-hidden","true"),m.style.display="none",m.setAttribute("aria-hidden","true")),H(),I(),function(){const e=Object.freeze({Top:0,Middle:1,Bottom:2});var t=y.querySelector(".cassie-cookie-module"),s=CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.PreBannerPosition,i=y.querySelector(".cassie-pre-banner"),o=y.querySelector(".cassie-gpc-pre-banner"),r="cassie-top-fixed",c="cassie-bottom-fixed",a="cassie-middle-fixed";switch(_||(t.style.width="unset"),s){case e.Top:i.classList.add("cassie-top"),_||i.classList.add(r);break;case e.Middle:_||i.classList.add(a);break;case e.Bottom:i.classList.add("cassie-bottom"),_||i.classList.add(c)}switch(o=y.querySelector(".cassie-gpc-pre-banner"),CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.GpcBannerPosition){case e.Top:o.classList.add("cassie-gpc-top"),_||o.classList.add(r);break;case e.Middle:_||o.classList.add(a);break;case e.Bottom:o.classList.add("cassie-gpc-bottom"),_||o.classList.add(c)}const n=Object.freeze({Middle:0,Right:1,Left:2,Panels:3});var l=CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.WidgetPosition,d=y.querySelector(".cassie-cookie-modal");switch(l){case n.Middle:d.classList.add("cassie-cookie-modal--center"),_||d.classList.add("cassie-cookie-modal--center-fixed");break;case n.Right:d.classList.add("cassie-cookie-modal--right"),_||d.classList.add("cassie-cookie-modal--right-fixed");break;case n.Left:_||d.classList.add("cassie-cookie-modal--left-fixed");case n.Panels:}var u=CassieWidgetLoader.Widget.cassieSettings.profileSettings.LoadTemplateCss;(function(e){var t=document.querySelector(":root"),s=CassieWidgetLoader.Widget.cassieSettings.profileSettings.TemplateOptions.PreBannerColourScheme;s&&e&&(t.style.setProperty("--prebanner-background-color",s?.BackgroundColour),t.style.setProperty("--prebanner-secondary-color",s?.SecondaryColour),t.style.setProperty("--prebanner-text-color",s?.TextColour),t.style.setProperty("--prebanner-accept-all-button-color",s?.AcceptAllButtonColour),t.style.setProperty("--prebanner-accept-all-button-text-color",s?.AcceptAllButtonTextColour),t.style.setProperty("--prebanner-reject-all-button-color",s?.RejectAllButtonColour),t.style.setProperty("--prebanner-reject-all-button-text-color",s?.RejectAllButtonTextColour),t.style.setProperty("--prebanner-customise-all-button-color",s?.CustomiseAllButtonColour),t.style.setProperty("--prebanner-customise-all-button-text-color",s?.CustomiseAllButtonTextColour),t.style.setProperty("--prebanner-border-color",s?.SecondaryColour),t.style.setProperty("--accept-all-button-background-color)",s?.AcceptAllButtonColour),t.style.setProperty("--accept-all-button-text-color)",s?.AcceptAllButtonTextColour),t.style.setProperty("--accept-all-button-border-color)",s?.AcceptAllButtonColour),t.style.setProperty("--reject-all-button-background-color)",s?.RejectAllButtonColour),t.style.setProperty("--reject-all-button-border-color)",s?.RejectAllButtonColour),t.style.setProperty("--view-all-button-background-color)",s?.CustomiseAllButtonColour),t.style.setProperty("--view-all-button-text-color)",s?.CustomiseAllButtonTextColour),t.style.setProperty("--view-all-button-border-color)",s?.CustomiseAllButtonColour),t.style.setProperty("--view-all-button-icon-color)",s?.CustomiseAllButtonTextColour))})(u),p&&D(u),G(u)}(),function(){var e=u.ConsentOptInText,t=u.ConsentOptOutText,s=y.querySelectorAll(".cassie-toggle-switch");if(e.length>=12||t.length>=12)for(var i=0;i<s.length;i++){var o=s[i];o.classList.add("cassie-long-labels--parent"),o.querySelector(".cassie-toggle-switch--status").classList.add("cassie-long-labels");var r=o.parentElement.querySelector(".cassie-expand-cookies--container");r&&r.classList.add("cassie-short--expand-cookies--container")}}(),O()}(),e=document.getElementById("cassie-widget"),t=u.AutoShowModal,1==u.HidePreBanner&&0==t?e.style.display="none":e.style.display="block",function(){for(var e=document.querySelectorAll(".cassie-toggle-switch.cassie-child-cookie--toggle-switch"),t=0;t<e.length;t++){var s=e[t],i=s.querySelector(".cassie-toggle-switch--status"),o=s.querySelector(".cassie-toggle-switch--slider");if(null!=o){var r=o.classList.contains("cassie-toggle-switch--slider--active"),c=u.ConsentOptInText,a=u.ConsentOptOutText;i.innerHTML=r?c:a}}}(),CassieWidgetLoader.Widget.initializeTemplate()}();</script><script type="text/javascript" id="gtm-youtube-tracking" charset="">(function(h,f,k){function m(){var b=[].slice.call(h.getElementsByTagName("iframe")).concat([].slice.call(h.getElementsByTagName("embed"))),a;for(a=0;a<b.length;a++){var d=n(b[a]);if(d){d=b[a];var e=f.location,c=h.createElement("a");c.href=d.src;c.hostname="www.youtube.com";c.protocol=e.protocol;var g="/"===c.pathname.charAt(0)?c.pathname:"/"+c.pathname;-1<c.search.indexOf("enablejsapi")||(c.search=(0<c.search.length?c.search+"\x26":"")+"enablejsapi\x3d1");if(!(-1<c.search.indexOf("origin"))&&-1===
e.hostname.indexOf("localhost")){var w=e.port?":"+e.port:"";e=e.protocol+"%2F%2F"+e.hostname+w;c.search=c.search+"\x26origin\x3d"+e}"application/x-shockwave-flash"===d.type&&(e=h.createElement("iframe"),e.height=d.height,e.width=d.width,g=g.replace("/v/","/embed/"),d.parentNode.parentNode.replaceChild(e,d.parentNode),d=e);c.pathname=g;d.src!==c.href+c.hash&&(d.src=c.href+c.hash);p(d)}}"addEventListener"in h&&h.addEventListener("load",x,!0)}function n(b){b=b.src||"";return-1<b.indexOf("youtube.com/embed/")||
-1<b.indexOf("youtube.com/v/")?!0:!1}function p(b){var a=YT.get(b.id);a||(a=new YT.Player(b,{}));"undefined"===typeof b.pauseFlag&&(b.pauseFlag=!1,a.addEventListener("onStateChange",function(a){y(a,b)}))}function z(b){var a={};g.events["Watch to End"]&&(a["Watch to End"]=Math.min(b-3,Math.floor(.99*b)));if(g.percentageTracking){var d=[],e;g.percentageTracking.each&&(d=d.concat(g.percentageTracking.each));if(g.percentageTracking.every){var c=parseInt(g.percentageTracking.every,10),f=100/c;for(e=1;e<
f;e++)d.push(e*c)}for(e=0;e<d.length;e++)f=d[e],c=f+"%",f=b*f/100,a[c]=Math.floor(f)}return a}function y(b,a){var d=b.data,e=b.target,c=e.getVideoUrl();c=c.match(/[?&]v=([^&#]*)/)[1];var f=e.getPlayerState(),g=Math.floor(e.getDuration()),h=z(g);g={1:"Play",2:"Pause"};g=g[d];a.playTracker=a.playTracker||{};1!==f||a.timer?(clearInterval(a.timer),a.timer=!1):(clearInterval(a.timer),a.timer=setInterval(function(){var b=e,d=h,c=a.videoId,g=b.getCurrentTime(),f;b[c]=b[c]||{};for(f in d)d[f]<=g&&!b[c][f]&&
(b[c][f]=!0,q(c,f))},1E3));1===d&&(a.playTracker[c]=!0,a.videoId=c,a.pauseFlag=!1);if(!a.playTracker[a.videoId])return!1;if(2===d){if(a.pauseFlag)return!1;a.pauseFlag=!0}r[g]&&q(a.videoId,g)}function q(b,a){var d="https://www.youtube.com/watch?v\x3d"+b,e=f.GoogleAnalyticsObject;if("undefined"===typeof f[t]||g.forceSyntax)if("function"===typeof f[e]&&"function"===typeof f[e].getAll&&2!==g.forceSyntax)f[e]("send","event","Videos",a,d);else"undefined"!==typeof f._gaq&&1!==A&&f._gaq.push(["_trackEvent",
"Videos",a,d]);else f[t].push({event:"youTubeTrack",attributes:{videoUrl:d,videoAction:a}})}function u(b,a,d){if(b.addEventListener)b.addEventListener(a,d);else if(b.attachEvent)b.attachEvent("on"+a,function(a){a.target=a.target||a.srcElement;d.call(b,a)});else if("undefined"===typeof b["on"+a]||null===b["on"+a])b["on"+a]=function(a){a.target=a.target||a.srcElement;d.call(b,a)}}function x(b){b=b.target||b.srcElement;var a=n(b);"IFRAME"===b.tagName&&a&&-1<b.src.indexOf("enablejsapi")&&-1<b.src.indexOf("origin")&&
p(b)}var g=k||{},A=g.forceSyntax||0,t=g.dataLayerName||"dataLayer",r={Play:!0,Pause:!0,"Watch to End":!0},l;k=h.createElement("script");k.src="//www.youtube.com/iframe_api";var v=h.getElementsByTagName("script")[0];v.parentNode.insertBefore(k,v);for(l in g.events)g.events.hasOwnProperty(l)&&(r[l]=g.events[l]);f.onYouTubeIframeAPIReady=function(){var b=f.onYouTubeIframeAPIReady;return function(){b&&b.apply(this,arguments);navigator.userAgent.match(/MSIE [67]\./gi)||("loading"!==h.readyState?m():h.addEventListener?
u(h,"DOMContentLoaded",m):u(f,"load",m))}}()})(document,window,{events:{Play:!0,Pause:!0,"Watch to End":!0},percentageTracking:{every:25,each:[10,90]}});</script><script type="text/javascript" id="" charset="">(function(){if(window.dataLayer){var c=window.dataLayer=window.dataLayer||[],a=window.SI_dataLayer=window.SI_dataLayer||[];a.push=function(){for(var a=0;a<arguments.length;a++)c.push(arguments[a]);return Array.prototype.push.apply(this,arguments)};for(var b=0;b<a.length;b++)c.push(a[b])}})();</script></body></html>