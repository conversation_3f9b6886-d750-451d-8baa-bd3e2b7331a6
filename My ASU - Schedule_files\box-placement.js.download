/**
 * This script sets box order according to one column vs multicolumn mode
 */
var resizeTimer;

var elemsLeft = [];
var elemsRight = [];
var elemsDefinedOrder = [];
var leftColumnSelector = '.column-container .column-container-row .content-column  > .splitcontentleft';
var rightColumnSelector = '.column-container .column-container-row .content-column  > .splitcontentright';
var singleColumnDiv = null;
var multicolumn = true;
var breakWidth = 890;
var cataloged = false;

if (typeof console === "undefined") {
    console = {
        log: function() {},
        debug: function() {},
        trace: function() {}
    };
};

if (typeof performance === "undefined") {
	performance = {
        now: function() {}
    };
};

$(document).ready( function() {
	catalogElements();
	placeBoxes(true);
	$(window).on("resize", onResize);	
});

function onResize() {
	// Optimize this not to run on every resize
    clearTimeout(resizeTimer);
    resizeTimer = setTimeout(placeBoxes(), 50);
};

function catalogElements (callback) {
	//console.log("Cataloging elements...");
	//console.log("document ready? "+document.readyState);

	var startTime = performance.now();

	$(leftColumnSelector).children().each(function(index, val) {

		//console.log($('.header:first', val).text().trim());
		elemsLeft.push(val);					

		//console.log($(val).attr('data-box-order'));
		if ($(val).attr('data-box-order') != null) {
			//console.log("found ordered box "+order+": "+$('.header:first', orderElem).text().trim());
			elemsDefinedOrder.push(val);
		}
	});	
	
	$(rightColumnSelector).children().each(function(index, val) {
	
		//console.log($('.header:first', val).text().trim());
		elemsRight.push(val);					

		//console.log($(val).attr('data-box-order'));
		if ($(val).attr('data-box-order') != null) {
			//console.log("found ordered box "+order+": "+$('.header:first', orderElem).text().trim());
			elemsDefinedOrder.push(val);
		}
	});
	
	elemsDefinedOrder.sort(compareBoxes);
	cataloged = true;
	//console.log("Cataloging elements took "+(performance.now()-startTime )+"ms: " +
	//		"[defined order: "+elemsDefinedOrder.length+", left: "+elemsLeft.length+", right: "+elemsRight.length+"]");
	if (typeof callback === "function") {
		callback();		
	}
};

function placeBoxes(force) {
	
	//console.log((force ? "Force " : "") + "Placing boxes...");
	//console.log("document ready? "+document.readyState);

	/*
	 * First put in the defined-order boxes, in their order, from orderdboxes array
	 * Next loop through the two left and right arrays together, skipping over those items also found in ordered boxes
	 * and append each one to the single column
	 */
	
	//console.log("Currently "+ (multicolumn ? "multi column" : "single column"));

	var startTime = performance.now();
	
	if (!cataloged) {
		//console.trace("placeBoxes() was called before elements were cataloged!  Doing it now.")
		catalogElements();
	}

	var viewportWidth = window.innerWidth;
	var modeChanged = false;
	
	if (viewportWidth > breakWidth) {
		if (!multicolumn) {
			modeChanged = true;
		}
		multicolumn = true;
	} else {
		if (multicolumn) {
			modeChanged = true;
		}
		multicolumn = false;
	}
	
	if (force || modeChanged) {
		//console.log("Single <-> Multi column mode changed");
		if (multicolumn) {
			//console.log("Going to multi-column mode: "+viewportWidth+"px");
			for (var i = elemsLeft.length; i > 0; --i) { // Reverse to prepend above all the non-box elements like spacers
				var leftElem = elemsLeft[i-1];
				$(leftColumnSelector).prepend(leftElem);					
			};
			for (var i = elemsRight.length; i > 0; --i) { // Reverse to prepend above all the non-box elements like spacers
				var rightElem = elemsRight[i-1];
				$(rightColumnSelector).prepend(rightElem);					
			};
			
		}
		
		else {
			//console.log("Going to single-column mode: "+viewportWidth+"px");
			
			singleColumnDiv = $('#single-column-boxes-container');
			if (singleColumnDiv.length == 0) {
				//console.log('singleColumnDiv is missing; creating it')
				singleColumnDiv = $('<div/>', {"id":"single-column-boxes-container"}).prependTo('#myasu-main-container');
			}
			
			
			for (var i=0; i<elemsDefinedOrder.length; i++) {
				var elem = elemsDefinedOrder[i];
				//console.log("appending: "+$('.header:first', elem).text().trim());
				singleColumnDiv.append(elem);
			}
			
			var maxIndex = Math.max(elemsLeft.length, elemsRight.length);
			// loop over both, excluding nulls, defined orders, etc
			for (var i=0; i<maxIndex; i++) {
				if (i <= elemsLeft.length-1) {
					var elem = elemsLeft[i];
					var append = true;
					// Check if box has defined order
					for (var j=0; j<elemsDefinedOrder.length; j++) {
						var definedBox = elemsDefinedOrder[j];
						//console.log("comparing: "+$('.header:first', definedBox).text());
						if (elem === definedBox) {
							append = false;
						}
					}
					if (append) {
						//console.log("appending: "+$('.header:first', elem).text().trim());
						singleColumnDiv.append(elem);					
					} else {
						//console.log("not appending: "+$('.header:first', elem).text().trim());
					}
				}
				if (i <= elemsRight.length-1) {
					var elem = elemsRight[i];
					var append = true;
					for (var j=0; j<elemsDefinedOrder.length; j++) {
						// Check if box has defined order
						var definedBox = elemsDefinedOrder[j];
						//console.log("comparing: "+$('.header:first', definedBox).text());
						if (elem === definedBox) {
							append = false;
						}
					}				
					if (append) {
						//console.log("appending: "+$('.header:first', elem).text().trim());
						singleColumnDiv.append(elem);					
					} else {
						//console.log("not appending: "+$('.header:first', elem).text().trim());
					}
				}

			}
		}		
	}
	
	else {
		//console.log("No change");
	}
	
	//console.log((modeChanged ? "Entered " : "Remained ") 
	//		+ (multicolumn ? "multicolumn" : "single column") 
	//		+ " ("+viewportWidth+"px)" 
	//		+ "; Took "+(performance.now()-startTime )+"ms");
};

function compareBoxes(a, b) {
	var compares = 0;
	var boxOrderA = $(a).attr('data-box-order');
	var boxOrderB = $(b).attr('data-box-order');
	if(!isFinite(boxOrderA-boxOrderB)) {  // handle NaN from 'undefined - undefined' compares
		if (!isFinite(boxOrderA) && !isFinite(boxOrderB)) {
			return -1; // 'undefined == undefined', order by traversal
		} else {
			return !isFinite(boxOrderA) ? 1 : -1; // box-order > undefined
		}
	} else {
		return (boxOrderA - boxOrderB); // compare by box-order
	}
	
	return compares;
};
