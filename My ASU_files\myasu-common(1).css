/*
ASU standard palette: https://brandguide.asu.edu/brand-elements/design/color
Maroon: #8C1D40
Hover Maroon: #D23153
Gold: #FFC627
Navbar hover gold: #FFB310
Red: #CC2F2F
Green: #78BE20
Blue: #00A3E0
New darker blue: #007BB6
Orange: #FF7F32
Gray: #5C6670
*/

.text-page {
	max-width: 60em;
}

.text-page a {
	color: #8C1D40;
	border-bottom: 1px dotted #8C1D40;
}

.text-page a:hover{
	color: #D23153;
	border-bottom: 1px solid #D23153;
}

.text-page a.myasu-bttn {
	color: #fff;
	border: none;
}

.inline-table {
	display: inline-table;
}

.float-right {
	float: right;
}

.table {
	display: table;
	/* width: 100% */
}

.table.full-width {
	width: 100%;
}

.table-row {
	display: table-row;
}

.table-cell {
	display: table-cell;
}

.table-cell.center {
	text-align: center;
}

.table-column {
	display: table-column;
}

.table-header-group {
	display: table-header-group;
}

.table-footer-group {
	display: table-footer-group;
}

.table-row-group {
	display: table-row-group;
}


#error-page {
	font-size: 1rem;
}

#error-page .error-message {
	max-width: 30em;
	margin: auto;
}

#error-page .error-link {
	margin: 1em;
	display: inline-block;
}

#error-page .error-link .error-link-text::before {
	white-space: pre;
	content: '\A';
}

.box .box-divider {
	border-bottom: 1px solid #E8E8E8;
}

.btn-maroon, .cluetip-default a.btn-maroon {
	box-sizing: border-box;
	display: inline-block;
	background-color: #8C1D40;
	color: #ffffff ;
	text-decoration: none;
    vertical-align: middle;
    white-space: nowrap;
    border-radius: 2px;
    padding: 0.5em 0.75em;
    border: 0;
}

.btn-maroon:hover {
	background-color: #D23153;
	color: #ffffff;
	border: 0;
}

input[type=submit].btn-maroon {
	padding-top: 7px;
	padding-bottom: 8px;
	padding-left: 12px;
	padding-right: 12px;
	/* font-size: 1.1em; */
}

.highlight-orange
, .highlight-green
, .highlight-gray
, .highlight-red
, .highlight-gold {
	border-radius: 0.2em;
	padding-left: 0.5em;
	padding-right: 0.5em;
}

.highlight-gold {
	background-color: #FFC627;
	color: #000;
}

.highlight-orange {
	background-color: #FF7F32;
	color: black;
}

.highlight-green {
	background-color: #088A08;
	color: white;
}

.highlight-gray {
	background-color: #A0A0A0;
	color: white;
}

.highlight-red {
	background-color: #CC2F2F;
	color: white;
}

.highlight-maroon {
	background-color: #8C1D40;
	color: white;	
}

.uppercase {
	text-transform: uppercase;
}

.inline-block {
	display: inline-block;
}

.text-align-right {
	text-align: right;
}

.text-align-center {
	text-align: center;
}

.drawer-face {
	
}

.task-list {
	padding-left: 0;
}

.task-list .task {
	display: block;
}

.task-list .task:not(:last-child) {
	border-bottom: 1px solid #d0d0d0;
}

.task-list .task .task-summary {
	display: flex;
	align-items: center;
	padding: 0.5rem 1rem;
	outline: none;
	cursor: pointer;
}

.task-list .task-summary-focused {
	outline: auto 5px -webkit-focus-ring-color !important;
}


.task-list .task .task-summary *[class^="highlight-"] {
	font-size: 0.9em;
	border-radius: 0.25em;
	padding: 4px 12px 4px 12px;
}

.task-list .task .task-summary > .checkbox-holder {
	margin-right: 0.5rem;
	color: var(--myasu-gray-70);
	font-size: 1.714em; /* 24px */
}

.task-list .task .task-summary > .checkbox-holder .task-checked {
	color: #8C1D40;
}

.task-list .task .task-summary .drawer-caret-holder {
	color: var(--myasu-gray-80);
	font-size: 1.286em; /* 18px */
}

.task-list .task .task-summary .drawer-caret-holder a {
	border-bottom: none;
}

.task-list .task .task-summary > .task-label-wrapper {
	flex: 1;
}

.task-list .task .task-summary .task-label-wrapper > .prefix-labels {
	margin-left: 0.5rem;
}

.task-list .task .task-summary .task-label-wrapper > .task-title {
	white-space: nowrap;
	overflow: hidden;
	border-bottom: none;
	font-weight: 400;
}

.task-list .task .task-summary .task-label-wrapper > .task-title > a {
	overflow: hidden;
	text-overflow: ellipsis;
	display: inline-block;
	border-bottom: none;
}

.task-list .task .task-summary .task-label-wrapper > .task-title a:hover {
	color: #333;
}

.task-list .task .task-details {
	padding: 16px;
	white-space: normal;
}

.task-list .task .task-details .task-action
, .task-list .task .task-details .task-dismiss {
	margin-top: 1em;
}

.task-list .task .task-details .task-dismiss .task-dismiss-button {
	background-color: #000;
	color: #FFF;
	padding: 4px 12px 4px 12px;
}

.task-list .task .task-details .task-dismiss .task-dismiss-button:hover {
	background-color: #484848;
}

.task-list .task .task-details > .task-details-title {
	margin-bottom: 1em;
	font-weight: bold;
}

.task-list .task .fa-status-text {
	color: #ec5937;
	font-weight: bold;
	padding-left: 4px;
}

.task-list .task .fa-status-text-received {
	color: var(--myasu-info);
}

.task-list .task .task-dismiss-processing {
	font-weight: bold;
}

.task-list .task .task-dismiss-error {
	font-weight: bold;
}

.task-list .task .task-dismiss-error .icon {
	color: #bf0711;	
}

.graybg {
	background-color: #eee;
}

.graybg a {
	color: #8C1D40;
	text-decoration: underline;
	border-bottom: none;
}

.graybg a:hover {
	text-decoration: none;
}

.task-list > .task > .task-details ul {
	list-style-image: initial;
	list-style-type: square;
	list-style-position: initial;
	margin: 0 10px;
}

.drawer-caret .fa {
	color: #999;
}

.subsection {
	display: block;
	margin: 1em 20px;
}

.unpadded {
	padding-left: 0;
}

td.min-width {
    width: 1%;
    white-space: nowrap;
}

/* BEGIN box-section styling */

.box-section {
	border-bottom: 1px solid #E8E8E8;
	min-height: 2.25em;
}

.box-section:last-child {
	border-bottom: none;
}

.box-section-banner {
    padding: 8px 10px;
	background-color: #007BB6;
	color: white;
}

.box-section-error-banner {
	background-color: #cb4848;
	color: white;
	padding-top: 8px;
	padding-right: 10px;
	padding-bottom: 8px;
	padding-left: 40px;
	position: relative;
	/* font-size: 1.1em; */
}

.box-section-error-banner .error-icon {
	font-weight: 900;
	left: 14px;
	font-size: 1.3em;
	position: absolute;
}

.box-section-head-group {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.box-section-title {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	font-size: 1em;
	margin: 0 0 0.25em 0;
}

.box-section-title a, .box-section-title button {
    color: #8C1D40;
	text-decoration: none;
    border-bottom: none;
}

.box-section-title a:hover, .box-section-title button:hover {
	text-decoration: none;
	border-bottom: 1px solid;
	margin-bottom: -1px;
}

.box-section-descr
, .box-section .lighttext {
	color: #757575;
}

.box-section-head-group > .right-margin {
	margin-left: 1em;
}

.box-section-padding {
	padding: 6px 10px;
}

.box-section .button-group {
	margin: 1em 0
}

/* END box-section styling */

/* BEGIN NEW 2020 box-section styling */

.box-section-2020 {
	border-top: 1px solid #d0d0d0;
}

.box-section-2020:first-of-type {
	border-top: none;
}

.box-section-title-2020 {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	padding-top: 16px;
	padding-bottom: 16px;
}

.box-section-title-2020 .icon {
	margin-right: 8px;
	color: #8C1D40;
}

button.box-section-title-2020 {
	display: block;
	cursor: pointer;
	color: inherit;
	text-align: initial;
	background: transparent;
	border: none;
	width: 100%;
	-moz-appearance: none;
	-webkit-appearance: none;
	appearance: none;
}

@media (hover: hover) {
	button:hover.box-section-title-2020 {
		color: #d23153;
	}
}


/* END NEW 2020 box-section styling */


/* BEGIN box-drawer styling */
/* .box-drawer:not(:first-child) {
	border-top: 1px solid #E8E8E8;	
}
 */

.box-section-title-detail {
	font-weight: 400;
	font-weight: var(--myasu-font-regular);
	display: block;
}

.box-section-title-center {
	display: flex;
	align-items: center;
}

.box-drawer-row:last-child {
	border-bottom:none;
}

.box-drawer-row {
	border-bottom:1px solid #E6E6E6;
	padding: 6px 10px;
	min-height: 27px;
	padding-left: 25px;
}

.box-drawer-row-title {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	font-size: 1em;
}

.box-drawer-descr, .box-drawer-link-descr, .box-drawer-row {
	color: #767676;
}

.box-drawer-table-contact tr td:first-child {
	float: right;
	padding-right: 10px;
}

.box-drawer-table-contact tr th {
	padding-bottom: 7px;
}

.box-drawer-body {
	display: none;
	background-color: #f2f2f2;
}

.box-drawer-body .box-section-padding {
	padding-left: 25px;
}

.box-drawer-bodycontent {
	padding: 16px;
}

.box-drawer-caret-container :hover.box-drawer-action-blue {
	border-bottom: none;
}

.box-section-title .box-drawer-action-blue
, .box-drawer-caret-container .box-drawer-action-blue {
	color: #007BB6;
	border-bottom: none;
}

[tabindex="-1"]:focus {
	outline: none;
}

.box-drawer-body-table {
	padding-left: 16px;
	line-height: 1.5em;
	/*font-size: 0.9em;*/
}

.box-drawer-body-2020 .table-cell {
	padding: 2px 0;
}

.box-drawer-body-table .label {
	color: gray;
	padding-right: 16px;
}

.box-drawer-body-table .full-width {
	width: 100%;
}

.contact-box-table-2024 {
	padding-left: 0;
	margin-bottom: 1em;
}

.contact-box-table-2024 .label {
	color: unset;
	padding-right: 16px;
	min-width: 180px;
}

/* BEGIN NEW 2020 box-drawer styling */

.box-drawer-body-2020 {
	display: none;
	background-color: #fafafa;
	border-top: 1px solid #d0d0d0;
}

.box-drawer-body-2020 .box-drawer-body-2020 {
	background-color: var(--myasu-gray-10);
}

.box-drawer-body-2020 .box-drawer-body-2020 a {
	border-bottom-color: inherit;
}

.box-drawer-body-2020 .box-drawer-body-2020 a:hover {
	border-bottom-color: inherit;
}

/* END NEW 2020 box-drawer styling */

/* END box-drawer styling */

.link-button {
	color: #8C1D40 ;
	border: 1px solid ;
	border-radius: 3px;
	padding: 3px;
	line-height: 2.5em;
}

a.link-button-solid-primary {
	color: white;
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	border: 1px solid;
	border-radius: 3px;
	border-color: #8c1d40;
	background-color: #8c1d40;
	padding: 4px;
	text-decoration: none;
}

a.link-button-solid-primary:hover {
	color: white;
	border-bottom: 1px solid #8c1d40;
	background-color: #D23153; 
}

a.link-button-solid-secondary {
	color: black;
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	border: 1px solid;
	border-radius: 3px;
	border-color: #dbdbdb;
	background-color: #dbdbdb;
	padding: 4px;
	text-decoration: none;
}

a.link-button-solid-secondary:hover {
	color: black;
	border-bottom: 1px solid #dbdbdb;
}

.starbucks-box .box-header {
	background-color: #f2f2f2;
	line-height: 1.5em;
}

.starbucks-box .box-section {
	min-height: initial;
	border-bottom: none;
	border-top: 1px solid #e8e8e8;
}
.starbucks-box .box-padding {
	padding: 8px;
}

.starbucks-box .box-section-padding {
	padding: 16px;
}

.starbucks-box .scap-section {
	margin: 0;
	/* font-size: 0.875rem; */
	padding-top: 16px;
	padding-bottom: 16px;
}

.starbucks-box .box-section-title {
	margin: 0;
	/* font-size: 0.875rem; */
	padding: 16px;
}

.starbucks-box .box-section-title .icon {
	margin-right: 8px;
	color: #8C1D40;
}

.starbucks-box .box-header .icon.fa-exclamation-triangle {
	vertical-align: text-bottom;
	color: #BF0711;
	padding-right: 1.3em;
}

.starbucks-box .box-section-title .fa {
	float: right;
	padding-right: 5px;
}

.starbucks-box .progress-table .label {
	color: gray;
	text-transform: uppercase;
	font-weight: bold;
	padding-right: 15px;
}

.starbucks-box .link-descr {
	color: var(--myasu-gray-95);
	/* font-size: .875rem; */
}

.starbucks-box .link-descr .label {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	/* font-size: 0.875rem; */
	color: #555;
}

.starbucks-box .link-descr .description {
	margin-top: 4px;
}

.starbucks-box .link-descr a {
	border-bottom: 1px solid #BBB;
	color: black;
	font-size: 1.0em;
	font-weight: 400;
}

.starbucks-box .box-header a {
	border-bottom: 1px solid #BBB;
	color: black;
	font-size: 1.0em;
	font-weight: 400;
}

.starbucks-box .warn {
	color: #BF0711;
	margin-top: 4px;
}

.starbucks-box a:hover {
	color: #D23153;
}

.starbucks-box-credit-value {
	/*color: #007BB6;*/
	font-weight: bold;
	/* font-size: 1.1em; */
}

.starbucks-box-status-eligible {
	color: #436d12;
}

.starbucks-box-status-ineligible {
	color: #bc4700;
}

.starbucks-box .box-drawer-body-table {
	overflow: auto;	
}

.starbucks-box .scap-box-section-text {
	font-weight: 400;
	padding-top: 4px;
	line-height: 1.4;
}

.starbucks-box .box-drawer-body {
	border-top: 1px solid #e8e8e8;
	background-color: #f9faf9;
}

.starbucks-box .box-drawer-body .scap-drawer-item {
	margin-top: 16px;
	margin-bottom: 16px;
}

#sbux-support-team a {
	border: 0;
}

#sbux-benefits-center-link {
	font-weight: 700;
	font-weight: var(--myasu-font-bold);
	border: 0;
}

#sbux-support-team .scap-drawer-item-info {
	display: inline-block;
}

/* Content background image */

#enable_earth_month_background_modal {
position: fixed;
	width: 350px;
	left: 50%;
	top: 50%;
	background-color: white;
	padding: 16px;
	transform: translate(-50%, -50%);
	border-radius: 5px;
}

#enable_earth_month_background_modal .modal_title {
	font-size: 1.333em;
	color: #2A2A2A;
}

#earth_month_accept {
	cursor: pointer;
}

.background-earth-month:after {
	content: '';
	position: 'absolute';
	background-size: cover;
	background-image: url(/myasu/images/background-earth-month.png);
	opacity: 0.2;
	width:100%;
    height:100%;
    pointer-events: none;
    position:absolute;
    top:0;
    left:0;
	z-index: -1
}

.earth_month_background_preview {
	text-align: center;
	padding: 1em;
}

.modal-content {
	padding-top: 1em;
	line-height:1.5;
}

.modal-footer {
	padding-top: 1em;
}

.modal-footer-right {
	display: inline;
	float: right;
}

#earth_month_decline {
	margin-right: 1em;
}

#earth-month-enabled {
	
}

.earth-month-background-enable-caption {
	display: inline-block;
    top: -30px;
    transform: translateY(20%);
}

.earth-month-background-enable-switch {
	display: inline-block;
	margin-left: 10px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 4.5em;
  height: 2em;
}

.toggle-switch input { 
  opacity: 0;
  width: 0;
  height: 0;
}

.togle-switch-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  color: #FFF;
  box-shadow: inset 0px 1px 2px #555;
  -webkit-transition: .4s;
  transition: .4s;
}

.togle-switch-slider:before {
  position: absolute;
  content: "\00a0\00a0Off";
  color: #000;
  padding-left: 1.7em;
  padding-top: 0.3em;
  height: 1.7em;
  width: 1.7em;
  left: 0.2em;
  bottom: 0.15em;
  box-shadow: none;
  background-color: white;
}

input:checked + .togle-switch-slider {
  background-color: #8C1D40;
}

input:focus + .togle-switch-slider {
  box-shadow: 0 0 3px #2196F3;
} 

input:checked + .togle-switch-slider:before {
  -webkit-transform: translateX(2.4em);
  -ms-transform: translateX(2.4em);
  transform: translateX(2.4em);
  content: ""
}

input:checked + .togle-switch-slider:after {
  -webkit-transform: translateX(2.4em);
  -ms-transform: translateX(2.4em);
  transform: translateX(2.4em);
  position: absolute;
  content: "On";
  margin-left: -1.5em;
  padding-top: 0.4em;
}

/* Rounded sliders */
.togle-switch-slider.round {
  border-radius: 2em;
}

.togle-switch-slider.round:before {
  border-radius: 50%;
}

.new-link-styling a:not(.primary-btn):not(.secondary-btn) {
	color: var(--myasu-maroon);
	text-decoration: underline;
	border-bottom: none;
}

.new-link-styling a:hover:not(.primary-btn):not(.secondary-btn) {
	text-decoration: none;
}

.suppress-link-styling a:not(.primary-btn):not(.secondary-btn) {
	color: unset;
	text-decoration: unset;
}

.suppress-link-styling a:hover:not(.primary-btn):not(.secondary-btn) {
	color: var(--myasu-maroon);
	text-decoration: underline;
}
