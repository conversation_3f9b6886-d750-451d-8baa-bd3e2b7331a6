function quicklinksPageInit() {
	var userFormToken = $("#quicklinks-edit-user-form-token").val();
	
	var saveSortTimeout = null;
	
	$(document).on("keydown", function(event) {
		var keycode = (event.keyCode ? event.keyCode : event.which);

		if (keycode == "38" || keycode == "40") {
			clearTimeout(saveSortTimeout);

			var indexToMove = 1;
			if (keycode == "38") {
				indexToMove *= -1;
			}

			var i = 0;
			$("#quicklinks-entries").find(".handle").each(function(index) {
				if ($(this).is(":focus")) {
					event.preventDefault();
					
					var newOrder = arrayMove(sortableQuicklinks.toArray(), i, (i + indexToMove));
					sortableQuicklinks.sort(newOrder);
					
					saveSortTimeout = setTimeout(saveSort, 2000);
					
					$(this).focus();
					
					return false;
				}

				i++;
			});
		}
	});
	
	$('#add_bookmark_modal').jqm({
		//trigger: '#bookmarks-add-bookmark', 
		onShow: function(hash) {
			hash.w.show();
			
			document.addEventListener("keydown", hideAddBookmarkModal);
		}, 
		onHide: function(hash) {
			hash.w.hide();
			hash.o.remove();
			$('#add_bookmark_modal form:first').trigger("reset");
			$('#add_bookmark_modal .bookmark-form-error').text('');
			
			document.removeEventListener("keydown", hideAddBookmarkModal);
		}
	});
	
	$('#edit_bookmark_modal').jqm({
		//trigger: '.bookmarks-edit-bookmark', 
		onShow: function(hash) {
			hash.w.show();
		}, 
		onHide: function(hash) {
			hash.w.hide();
			hash.o.remove();
			$('#edit_bookmark_modal form:first').trigger("reset");
			$('#edit_bookmark_modal .bookmark-form-error').text('');
		}
	});
	
	var el = document.getElementById("quicklinks-entries");
	if (el) {
		var sortableQuicklinks = Sortable.create(el, {
			handle: '.handle',
			delay: 100, // time in milliseconds to define when the sorting should start
			delayOnTouchOnly: true, // only delay if user is using touch
			onChoose: function (evt) {
				var itemEl = evt.item;
				itemEl.classList.add("quicklinks-entry-sortable-chosen");
			},
			onUnchoose: function (evt) {
				var itemEl = evt.item;
				itemEl.classList.remove("quicklinks-entry-sortable-chosen");
			},
			onEnd: function (evt) {
				var itemEl = evt.item;
				itemEl.classList.remove("quicklinks-entry-sortable-chosen");
			},
			onSort: function (evt) {
				saveSort();
			}
		});
	}
	
	function saveSort() {
		var ajaxData = {
			token: userFormToken, 
			func: "reorder-bookmarks",
			order: sortableQuicklinks.toArray()
		};
		var options = {
			cache: false,
			url: "pref",
			type: "POST",
			data: ajaxData 
		};
		options['success'] = function(data) {
			if (typeof updateQuicklinks === "function") { 
				if ($("#quicklinks-container").length > 0) {
					resizeQuicklinkData = data;
					updateQuicklinks(data, true);
				}
			}
		};
		options['error'] = function() {
			alert('An error occurred while trying to save your change.  Please try again or check back later.');
		};
		$.ajax(options);
	}
	
	$(document).on("change", "#bookmarks-show-icons-checkbox", function() {
		toggleBooksmarksShowIconsPref($(this));

		if (!$(this).is(":checked")) {
			trackEvent("Toggle", "customize/customize-shortcuts/icons-on");
		} else {
			trackEvent("Toggle", "customize/customize-shortcuts/icons-off");
		}
	});

	$(document).on("change", "#bookmarks-open-new-win-checkbox", function() {
		toggleBooksmarksOpenNewWinPref($(this));

		if (!$(this).is(":checked")) {
			trackEvent("Toggle", "customize/customize-shortcuts/tabs-on");
		} else {
			trackEvent("Toggle", "customize/customize-shortcuts/tabs-off");
		}
	});
	
	if ($("#show-bookmark-icons-value").val() == "true") {
		populateQuicklinksFavicons($("#quicklinks-entries"));
	}
	
	$(document).on("click", "#close-add-bookmark-panel", function() {
		$('.bookmark-form-error').text('');	
		$('.bookmark-form-saving').hide();
		$('#add_bookmark_panel').hide();
		
		$("#bookmarks-add-bookmark-container").show();
		$("#modal-quicklinks-table").show();
	});
	
	$(document).on("click", "#close-edit-bookmark-panel", function() {
		$('#edit_bookmark_panel form:first').trigger("reset");
		$('#edit_bookmark_panel .bookmark-form-error').text('');
		$('#edit_bookmark_panel').hide();
		
		$("#bookmarks-add-bookmark-container").show();
		$("#modal-quicklinks-table").show();
	});
}

function showAddBookmarkPanel() {
	$('.bookmark-form-error').text('');	
	$('.bookmark-form-saving').hide();
	$('#add_bookmark_panel').show();
	
	$("#bookmarks-add-bookmark-container").hide();
	$("#modal-quicklinks-table").hide();
}

function showAddBookmarkModal() {
	//$('.bookmark-form-error').hide();
	$('.bookmark-form-error').text('');	
	$('.bookmark-form-saving').hide();
	$('#add_bookmark_modal').jqmShow();
}

function hideAddBookmarkModal(e) {
	if (e.keyCode === 27) {
		$('#add_bookmark_modal').jqmHide();
	}
}

function addMyLink(form) {
	var name_box = $(form).find(".name:first");
	var name = name_box.val();
	var url_box = $(form).find(".url:first");
	var url = url_box.val();
	var errorMessage;
	
	// check the user inputs
	if (url.length == 0) {
		errorMessage = "Please enter a bookmark URL.";
		onSaveLinkError(errorMessage);
		return;
	}
	if (url.indexOf('feed://') == 0) {
		url = 'http://'+url.substring(7);
	}
	if (!urlValid(url)) {
		errorMessage = "Please enter a valid URL.";
		onSaveLinkError(errorMessage);
		return;
	}
	
	// now check the name
	// remove any html characters
	name = name.replace(/<\/?[^>]+(>|$)/g, "");
	if (name.length == 0) {
		errorMessage = "Please name the new bookmark.";
		onSaveLinkError(errorMessage);
		return;
	}
	
	// Lets send the ajax call to save
	var bookmarkId = getNextCustomId();
	//updateUserPref('updateMyLinks', 'addLink', {key: 'mylink_'+bookmarkId, url: url, name: name});
	var inProgressElement = $('.bookmark-form-saving');
	updateQuicklink('updateMyLinks', 
			'addLink', 
			{key: 'mylink_'+bookmarkId, url: url, name: name, customId: bookmarkId}, 
			inProgressElement, 
			onSaveLinkError, 
			onAddLinkSuccess);
}

function updateQuicklink(prefFunc, val, bookmarkData, saveInProgressElement, ajaxErrorFunction, ajaxSuccessFunction) {
	var isMasqueraded = $("#is-masqueraded").val();
	var userFormToken = $("#quicklinks-edit-user-form-token").val();

	if (isMasqueraded == "true") {
		userFormToken = "";
	}
	
	if (saveInProgressElement != null) {
		if (saveInProgressElement.is(":visible")) {
			return;
		}
		saveInProgressElement.show();
	}

	var ajaxData = { 
		token: userFormToken, 
		func: prefFunc, 
		value: val 
	};

	if (typeof bookmarkData != "undefined" && bookmarkData != null) {
		jQuery.extend(ajaxData, bookmarkData);
	}

	var options = {
		cache: false,
		url: "pref",
		type: "POST",
		data: ajaxData 
	};
	
	if (typeof ajaxErrorFunction == "undefined" || !ajaxErrorFunction) {
		options['error'] = function() {ajaxErrorFunction()};
	}

	options['success'] = function(data) {
		if (typeof updateQuicklinks === "function") { 
			if ($("#quicklinks-container").length > 0) {
				resizeQuicklinkData = data;
				updateQuicklinks(data, true);
			}
		}

		if (typeof bookmarkData != "undefined" && bookmarkData != null) {
			if (typeof ajaxSuccessFunction == "function") {
				ajaxSuccessFunction(bookmarkData);
			}
		}
	};

	$.ajax(options);

	if (saveInProgressElement != null) {
		saveInProgressElement.hide();
	}
}

var onSaveLinkError = function(errorMessage, bookmarkData) {
	//console.trace("Error saving link change");
	$(".bookmark-form-error").text(errorMessage);

	if (errorMessage) {
		$(".bookmark-form-error").text(errorMessage);
	} else {
		$(".bookmark-form-error").text("An error occurred while trying to save.  Please try again or check back later.");		
	}
	$(".bookmark-form-error").show();

}

var onUpdateLinkSuccess = function(bookmarkData) {
	var link = $('#bookmark-'+bookmarkData.key).find("a:first");
	link.attr("href", bookmarkData.url);
	link.text(bookmarkData.name);

	if ($("#show-bookmark-icons-value").val() == "true") {
		populateQuicklinksFavicons($("#bookmark-" + bookmarkData.key));
	}

	$('#edit_bookmark_modal').jqmHide();
	$('#edit_bookmark_panel').hide();
	
	$("#bookmarks-add-bookmark-container").show();
	$("#modal-quicklinks-table").show();
	$('#bookmarks-update-form').trigger("reset");
}

var onAddLinkSuccess = function(bookmarkData) {
	// add the new bookmark to dom
	var bookmarkId = bookmarkData.customId;
	//console.log("Adding new row to dom for bookmark ID = "+JSON.stringify(bookmarkData));
	var url = bookmarkData.url;
	var name = bookmarkData.name
	$(".quicklinks-entries").each(function() {
		$(this).append('<div id="bookmark-mylink_'+bookmarkId+'" class="table-row" data-id="bookmark-mylink_'+bookmarkId+'">'
				+'<div class="table-cell center"><button class="handle nowrap"><span class="fa fa-grip-lines gray"></span></button></div>'
				+'<div class="table-cell"><a href='+url+' title='+url+' target="_blank" rel="noopener">'+name+'</a> <span class="fa fa-external-link fa-link-decoration gray" title="Opens in a new window"></span></div>'
				+'<div class="table-cell center"><button onclick="toggleLinkVisibility(\'mylink_'+bookmarkId+'\');" class="jslink nounderline" title="Hide This Shortcut"><span class="fa fa-eye gray" aria-hidden="true"></span></button></div>'
				+'<div class="table-cell center"><button onclick="showUpdateMyLinkForm(\'mylink_'+bookmarkId+'\');"class="jslink nounderline" title="Edit or Delete This Shortcut"><span class="fa fa-edit gray" aria-hidden="true"></span></button></div>'
				+'</div>');
		
		if ($("#show-bookmark-icons-value").val() == "true") {
			populateQuicklinksFavicons($("#bookmark-mylink_" + bookmarkId));
		}
	});

	$('#add_bookmark_modal').jqmHide();
	$('#add_bookmark_panel').hide();
	
	$("#bookmarks-add-bookmark-container").show();
	$("#modal-quicklinks-table").show();
	$('#bookmarks-add-form').trigger("reset");
	
}

function removeMyLink() {
	var linkKey = $('#bookmark-id-to-edit').val();
	updateQuicklink('updateMyLinks', 
			'removeLink', 
			{key: linkKey}, 
			null, 
			null, 
			null);

	$("#bookmark-"+linkKey).each(function() {
		$(this).remove();
	});
	$('#edit_bookmark_modal').jqmHide();
	$('#edit_bookmark_panel').hide();
	
	$("#bookmarks-add-bookmark-container").show();
	$("#modal-quicklinks-table").show();
	
}

function showUpdateMyLinkForm(linkKey) {
	//$('.bookmark-form-error').hide();
	$('.bookmark-form-error').val('');	
	$('.bookmark-form-saving').hide();
	$('#edit_bookmark_modal').jqmShow();
	$('#bookmark-id-to-edit').val(linkKey);
	var link = $('#bookmark-'+linkKey).find("a:first");
	$('#edit-bookmark-url').val(link.attr("href"));
	$('#edit-bookmark-name').val(link.text());
	
	if ($("#edit_bookmark_modal").length > 0) {
		$('#edit_bookmark_modal').jqmShow();
	} else if ($("#edit_bookmark_panel").length > 0) {
		$('#edit_bookmark_panel').show();
		
		$("#bookmarks-add-bookmark-container").hide();
		$("#modal-quicklinks-table").hide();
	}
}

function updateMyLink(form) {
	var bookmarkId = $('#bookmark-id-to-edit').val();
	var name_box = $(form).find(".name:first");
	var name = name_box.val();
	var url_box = $(form).find(".url:first");
	var url = url_box.val();
	
	// check the user inputs
	if (url.length == 0) {
		errorMessage = "Please enter a bookmark URL.";
		onSaveLinkError(errorMessage);
		return;
	}
	if (url.indexOf('feed://') == 0) {
		url = 'http://'+url.substring(7);
	}
	if (!urlValid(url)) {
		errorMessage = "Please enter a valid URL.";
		onSaveLinkError(errorMessage);
		return;
	}
	
	// now check the name
	// remove any html characters
	name = name.replace(/<\/?[^>]+(>|$)/g, "");
	if (name.length == 0) {
		errorMessage = "Please name the new bookmark.";
		onSaveLinkError(errorMessage);
		return;
	}

	
	// Lets send the ajax call to save
	//updateUserPref('updateMyLinks', 'updateLink', {key: bookmarkId, url: url, name: name});
	updateQuicklink('updateMyLinks', 
			'updateLink', 
			{key: bookmarkId, url: url, name: name}, 
			$('.bookmark-form-saving'), 
			onSaveLinkError, 
			onUpdateLinkSuccess);

}

function toggleLinkVisibility(linkKey) {
	var userFormToken = $("#quicklinks-edit-user-form-token").val();
	
	var ajaxData = {
		token: userFormToken, 
		func: "hide-show-bookmark",
		bookmark: linkKey
	};
	var options = {
		cache: false,
		url: "pref",
		type: "POST",
		data: ajaxData 
	};
	options['error'] = function() {
		alert('An error occurred while trying to save your change.  Please try again or check back later.');
	};
	options['success'] = function(data) {
		$("#bookmark-"+linkKey).each(function() {
			$(this).find("[class*='fa-eye']").toggleClass("fa-eye fa-eye-slash");
		});

		if (typeof updateQuicklinks === "function") { 
			if ($("#quicklinks-container").length > 0) {
				resizeQuicklinkData = data;

				if (currentViewMoreIds.includes(linkKey) && $(".view-more-quicklink").length > $(".removed-quicklink").length && (($(".view-more-quicklink").length - $(".removed-quicklink").length) > 2)) {
					$("#view-more-quicklink-" + linkKey).toggleClass("removed-quicklink");
				} else {
					updateQuicklinks(data, true);
				}
			}
		}
	};
	$.ajax(options);
}

function toggleBooksmarksShowIconsPref(bookmarksShowIconsCheckbox) {
	var userFormToken = $("#quicklinks-edit-user-form-token").val();
	
	var checkbox = $(bookmarksShowIconsCheckbox);
	
	var bookmarksShowIconsPref = $(checkbox).is(":checked");
	
	var ajaxData = {
		token: userFormToken, 
		func: "set-booksmarks-show-icons",
		bookmarksShowIconsPref: bookmarksShowIconsPref
	};
	var options = {
		cache: false,
		url: "pref",
		type: "POST",
		data: ajaxData 
	};
	options['error'] = function() {
		checkbox.attr("checked", !$(checkbox).is(":checked"));
		
		alert('An error occurred while trying to save your change. Please try again or check back later.');
	};
	options['success'] = function(data) {
		if ($(checkbox).is(":checked")) {
			$("#show-bookmark-icons-value").val(true);
			populateQuicklinksFavicons($("#quicklinks-entries"));

			if (typeof updateQuicklinks === "function") { 
				if ($("#quicklinks-container").length > 0) {
					resizeQuicklinkData = data;
					updateQuicklinks(data, true);
				}
			}
		} else {
			$("#show-bookmark-icons-value").val(false);
			depopulateQuicklinksFavicons($("#quicklinks-entries"));

			if (typeof updateQuicklinks === "function") { 
				if ($("#quicklinks-container").length > 0) {
					resizeQuicklinkData = data;
					updateQuicklinks(data, true);
				}
			}
		}
	};
	$.ajax(options);
}

function toggleBooksmarksOpenNewWinPref(bookmarksOpenNewTabCheckbox) {
	var userFormToken = $("#quicklinks-edit-user-form-token").val();
	
	var checkbox = $(bookmarksOpenNewTabCheckbox);
	
	var bookmarksOpenNewWinPref = $(checkbox).is(":checked");

	var ajaxData = {
		token: userFormToken, 
		func: "set-booksmarks-open-new-win",
		bookmarksOpenNewWinPref: bookmarksOpenNewWinPref
	};
	var options = {
		cache: false,
		url: "pref",
		type: "POST",
		data: ajaxData 
	};
	options['error'] = function() {
		checkbox.attr("checked", !$(checkbox).is(":checked"));
		
		alert('An error occurred while trying to save your change. Please try again or check back later.');
	};
	options['success'] = function(data) {
		if (typeof updateQuicklinks === "function") { 
			if ($("#quicklinks-container").length > 0) {
				resizeQuicklinkData = data;
				updateQuicklinks(data, true);
			}
		}
	};
	$.ajax(options);
}

function getNextCustomId() {
	var highest = -1;
	var ids = $("*[id^=bookmark-mylink_]").each (function() {
		var parts = $(this).attr('id').split("_");
		var id = parseInt(parts[parts.length-1]);
		if (!isNaN(id)) {
			if (id > highest) {
				highest = id;
			}			
		}
		
	});
	return highest+1;
}

function arrayMove(arr, old_index, new_index) {
	while (old_index < 0) {
		old_index += arr.length;
	}
	while (new_index < 0) {
		new_index += arr.length;
	}
	if (new_index >= arr.length) {
		var k = new_index - arr.length;
		while ((k--) + 1) {
			arr.push(undefined);
		}
	}
	arr.splice(new_index, 0, arr.splice(old_index, 1)[0]);
	return arr;
}