var ajaxRnd=(new Date()).getTime();function AddCollegeBox(g,h,f,c,e){var d='<div class="box"><div id="tab-pane-mycollege-'+c+'_title_bar" class="box-title-bar"><h2 class="box-title">'+h+"</h2>";if(e!==undefined){d+='<a href="#" class="nounderline" id="dynbox_helptip_'+c+'" title="'+h+' - Frequently Asked Questions" data-template="publickb?cat=college-box&college='+g+'"><i class="box-title-icon fas fa-question-circle" title="Info"></i></a>';}d+='</div><div class="mycollege" id="dynbox_content_'+c+'"><p class="textcenter loading"><img src="images/loader_all_yellow.gif" alt="Loading your data..."/></p></div></div>';$("#dynbox-holder-"+f).append(d);$("#dynbox_content_"+c).load("collegebox",{code:g,title:h,id:c},function(){if($("#tab-pane-mycollege-"+c).length){initializeTablist("tab-pane-mycollege-"+c);if(e){initMyAsuTippy("#dynbox_helptip_"+c,{placement:"right-start",addLinkTracking:true,ajax:true,showTitle:true,onShowCallback:function(){trackEvent("Popup","college-box/faq");}});}}});}$(document).ready(function(){helptips();$(".financing_links_popup").cluetip({activation:"click",width:270,showTitle:false,sticky:true,cluezIndex:103,closePosition:"title",closeText:CLUETIP_CLOSE,arrows:true,local:true});if(document.getElementById("tasks_content")){$("#tasks_content").load("tasks?format=ajaxhtml&_="+ajaxRnd+"&env=student",null,AjaxLoaderErrorHandler);}if(document.getElementById("classes_content")){$("#classes_content").load("classes-box?format=ajaxhtml&_="+ajaxRnd,null,AjaxLoaderErrorHandler);}initMenus();var c=document.getElementById("my-agenda-box");if(c!=null){initializeDocketWidget("my-agenda-box");}var d=document.getElementById("narrowcasting_carousel_widget");if(d!=null){initNarrowcastingCarouselWidget(d);}});function helptips(){$(".helptip").cluetip({activation:"click",width:400,showTitle:false,sticky:true,closePosition:"title",closeText:CLUETIP_CLOSE,arrows:true,local:false,onShow:function(d,e){addLinkTracking(e);trackEvent("Popup","home/helptip-popup");}});initMyAsuTippy("#myclasses-helptip",{placement:"right-start",addLinkTracking:true,ajax:true,showTitle:true,onShowCallback:function(){trackEvent("Popup","myclasses/faq");}});initMyAsuTippy("#myprograms-helptip",{placement:"right-start",addLinkTracking:true,ajax:true,showTitle:true,onShowCallback:function(){trackEvent("Popup","myprograms/faq");}});}function logLibrarySearch(c){trackEvent("Search","library/"+c.searchtype.value);}function setHeights(d){if(d){d=document.getElementById(d);}else{d=document.getElementById("cluetip-outer");}var c=0;if(d&&d.previousSibling){c=d.clientHeight;}else{return;}while(d&&d.previousSibling){d=d.previousSibling;d.style.height=String(c)+"px";}}function togglePaymentTasks(){refreshLink="?action=clearcache&amp;nextUrl="+encodeURIComponent(window.location.pathname);$("#my-tasks-box").html('<div class="box-padding"><a href="'+refreshLink+'">Click to Refresh</a></div>');}function initializeDocketWidget(d){var c=$("#docket-widget-url").val();var e=$("#myasu-docket-jwt").val();if(c!=null&&c!=""&&e!=null&&e!=""){loadDocketWidget(d,c,e);}}function loadDocketWidget(e,d,f){var c=document.getElementById(e);if(c!=null){var g=$("#my-agenda-emplid").val();if(g!=null&&g!=""){(function(i,n,j,l,k,m){i.DocketObject=m;i[m]=function(){i[m].q=arguments;i[m].h=l;};a=n.createElement(j);b=n.getElementsByTagName(j)[0];a.async=1;a.src=l+k;b.parentNode.insertBefore(a,b);}(window,document,"script",d,"js/agenda.js","docket"));docket(e,f,g,new Date(),"agenda","student/docket",generateDocketJwtToken);$("#my-agenda-section").show();}}}function generateDocketJwtToken(c){var d=this;if(c!=null){$.ajax({method:"GET",url:"generateDocketJwtToken",success:function(f){var e=f.myasuJwt;c(d,e);},error:function(f){c(d,null);}});}}