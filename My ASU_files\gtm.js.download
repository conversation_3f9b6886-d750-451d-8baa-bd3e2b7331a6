
// Copyright 2012 Google Inc. All rights reserved.
 
(function(){

var data = {
"resource": {
  "version":"5",
  
  "macros":[{"function":"__u","vtp_component":"URL","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"HOST","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__u","vtp_component":"PATH","vtp_enableMultiQueryKeys":false,"vtp_enableIgnoreEmptyQueryParam":false},{"function":"__f","vtp_component":"URL"},{"function":"__e"}],
  "tags":[],
  "predicates":[],
  "rules":[]
},
"runtime":[ [50,"__e",[46,"a"],[36,[13,[41,"$0"],[3,"$0",["require","internal.getEventData"]],["$0","event"]]]]
 ,[50,"__f",[46,"a"],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","getReferrerUrl"]],[52,"d",["require","makeString"]],[52,"e",["require","parseUrl"]],[52,"f",[15,"__module_legacyUrls"]],[52,"g",[30,["b","gtm.referrer",1],["c"]]],[22,[28,[15,"g"]],[46,[36,["d",[15,"g"]]]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"f"],"B",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"C",[7,[15,"g"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"f"],"D",[7,[15,"g"]]]]]],[5,[46,[36,[2,[15,"f"],"E",[7,[15,"g"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[22,[17,[15,"a"],"queryKey"],[46,[53,[36,[2,[15,"f"],"H",[7,[15,"g"],[17,[15,"a"],"queryKey"]]]]]]],[52,"h",["e",[15,"g"]]],[36,[2,[17,[15,"h"],"search"],"replace",[7,"?",""]]]]],[5,[46,[36,[2,[15,"f"],"G",[7,[15,"g"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"f"],"A",[7,["d",[15,"g"]]]]]]]]]]
 ,[50,"__u",[46,"a"],[50,"k",[46,"l","m"],[52,"n",[17,[15,"m"],"multiQueryKeys"]],[52,"o",[30,[17,[15,"m"],"queryKey"],""]],[52,"p",[17,[15,"m"],"ignoreEmptyQueryParam"]],[22,[20,[15,"o"],""],[46,[53,[52,"r",[2,[17,["i",[15,"l"]],"search"],"replace",[7,"?",""]]],[36,[39,[1,[28,[15,"r"]],[15,"p"]],[44],[15,"r"]]]]]],[41,"q"],[22,[15,"n"],[46,[53,[22,[20,["e",[15,"o"]],"array"],[46,[53,[3,"q",[15,"o"]]]],[46,[53,[52,"r",["c","\\s+","g"]],[3,"q",[2,[2,["f",[15,"o"]],"replace",[7,[15,"r"],""]],"split",[7,","]]]]]]]],[46,[53,[3,"q",[7,["f",[15,"o"]]]]]]],[65,"r",[15,"q"],[46,[53,[52,"s",[2,[15,"h"],"H",[7,[15,"l"],[15,"r"]]]],[22,[29,[15,"s"],[44]],[46,[53,[22,[1,[15,"p"],[20,[15,"s"],""]],[46,[53,[6]]]],[36,[15,"s"]]]]]]]],[36,[44]]],[52,"b",["require","copyFromDataLayer"]],[52,"c",["require","internal.createRegex"]],[52,"d",["require","getUrl"]],[52,"e",["require","getType"]],[52,"f",["require","makeString"]],[52,"g",["require","parseUrl"]],[52,"h",[15,"__module_legacyUrls"]],[52,"i",["require","internal.legacyParseUrl"]],[41,"j"],[22,[17,[15,"a"],"customUrlSource"],[46,[53,[3,"j",[17,[15,"a"],"customUrlSource"]]]],[46,[53,[3,"j",["b","gtm.url",1]]]]],[3,"j",[30,[15,"j"],["d"]]],[38,[17,[15,"a"],"component"],[46,"PROTOCOL","HOST","PORT","PATH","EXTENSION","QUERY","FRAGMENT","URL"],[46,[5,[46,[36,[2,[15,"h"],"B",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"C",[7,[15,"j"],[17,[15,"a"],"stripWww"]]]]]],[5,[46,[36,[2,[15,"h"],"D",[7,[15,"j"]]]]]],[5,[46,[36,[2,[15,"h"],"E",[7,[15,"j"],[17,[15,"a"],"defaultPages"]]]]]],[5,[46,[36,[2,[15,"h"],"F",[7,[15,"j"]]]]]],[5,[46,[36,["k",[15,"j"],[15,"a"]]]]],[5,[46,[36,[2,[15,"h"],"G",[7,[15,"j"]]]]]],[5,[46]],[9,[46,[36,[2,[15,"h"],"A",[7,["f",[15,"j"]]]]]]]]]]
 ,[52,"__module_legacyUrls",[13,[41,"$0"],[3,"$0",[51,"",[7],[50,"a",[46],[50,"h",[46,"p"],[52,"q",[2,[15,"p"],"indexOf",[7,"#"]]],[36,[39,[23,[15,"q"],0],[15,"p"],[2,[15,"p"],"substring",[7,0,[15,"q"]]]]]],[50,"i",[46,"p"],[52,"q",[17,["e",[15,"p"]],"protocol"]],[36,[39,[15,"q"],[2,[15,"q"],"replace",[7,":",""]],""]]],[50,"j",[46,"p","q"],[41,"r"],[3,"r",[17,["e",[15,"p"]],"hostname"]],[22,[28,[15,"r"]],[46,[36,""]]],[52,"s",["b",":[0-9]+"]],[3,"r",[2,[15,"r"],"replace",[7,[15,"s"],""]]],[22,[15,"q"],[46,[53,[52,"t",["b","^www\\d*\\."]],[52,"u",[2,[15,"r"],"match",[7,[15,"t"]]]],[22,[1,[15,"u"],[16,[15,"u"],0]],[46,[3,"r",[2,[15,"r"],"substring",[7,[17,[16,[15,"u"],0],"length"]]]]]]]]],[36,[15,"r"]]],[50,"k",[46,"p"],[52,"q",["e",[15,"p"]]],[41,"r"],[3,"r",["f",[17,[15,"q"],"port"]]],[22,[28,[15,"r"]],[46,[53,[22,[20,[17,[15,"q"],"protocol"],"http:"],[46,[53,[3,"r",80]]],[46,[22,[20,[17,[15,"q"],"protocol"],"https:"],[46,[53,[3,"r",443]]],[46,[53,[3,"r",""]]]]]]]]],[36,["g",[15,"r"]]]],[50,"l",[46,"p","q"],[52,"r",["e",[15,"p"]]],[41,"s"],[3,"s",[39,[20,[2,[17,[15,"r"],"pathname"],"indexOf",[7,"/"]],0],[17,[15,"r"],"pathname"],[0,"/",[17,[15,"r"],"pathName"]]]],[22,[20,["d",[15,"q"]],"array"],[46,[53,[52,"t",[2,[15,"s"],"split",[7,"/"]]],[22,[19,[2,[15,"q"],"indexOf",[7,[16,[15,"t"],[37,[17,[15,"t"],"length"],1]]]],0],[46,[53,[43,[15,"t"],[37,[17,[15,"t"],"length"],1],""],[3,"s",[2,[15,"t"],"join",[7,"/"]]]]]]]]],[36,[15,"s"]]],[50,"m",[46,"p"],[52,"q",[17,["e",[15,"p"]],"pathname"]],[52,"r",[2,[15,"q"],"split",[7,"."]]],[41,"s"],[3,"s",[39,[18,[17,[15,"r"],"length"],1],[16,[15,"r"],[37,[17,[15,"r"],"length"],1]],""]],[36,[16,[2,[15,"s"],"split",[7,"/"]],0]]],[50,"n",[46,"p"],[52,"q",[17,["e",[15,"p"]],"hash"]],[36,[2,[15,"q"],"replace",[7,"#",""]]]],[50,"o",[46,"p","q"],[50,"s",[46,"t"],[36,["c",[2,[15,"t"],"replace",[7,["b","\\+","g"]," "]]]]],[52,"r",[2,[17,["e",[15,"p"]],"search"],"replace",[7,"?",""]]],[65,"t",[2,[15,"r"],"split",[7,"&"]],[46,[53,[52,"u",[2,[15,"t"],"split",[7,"="]]],[22,[21,["s",[16,[15,"u"],0]],[15,"q"]],[46,[6]]],[36,["s",[2,[2,[15,"u"],"slice",[7,1]],"join",[7,"="]]]]]]],[36]],[52,"b",["require","internal.createRegex"]],[52,"c",["require","decodeUriComponent"]],[52,"d",["require","getType"]],[52,"e",["require","internal.legacyParseUrl"]],[52,"f",["require","makeNumber"]],[52,"g",["require","makeString"]],[36,[8,"F",[15,"m"],"H",[15,"o"],"G",[15,"n"],"C",[15,"j"],"E",[15,"l"],"D",[15,"k"],"B",[15,"i"],"A",[15,"h"]]]],[36,["a"]]]],["$0"]]]
 
]
,"entities":{
"__e":{"2":true,"5":true}
,
"__f":{"2":true,"5":true}
,
"__u":{"2":true,"5":true}


}
,"blob":{"1":"5","10":"GTM-522R2ZGR","12":"","13":"lvAMHmI5oqHmqe78ct7jWW2rssTeJa3fXTkufB1ELjM,fDCH_9DmnpUI-RSHXjnt1_YZsa2UZdUUXZSMHFbH4YE,NGjdK63wgTOIio871dmfJAPd7o3r2_mFO53futF9a-k,vVQkXUqz96v-hCEKwqRMtaQv6CbJXGsuLUjQwzOM1Uc,tvJdGrfHP5XzpzhpeJPnD624U6hiBKPjRzusG-hhQEw","14":"57g1","15":"0","16":"ChEI8KfywwYQtZnzsLij46XPARIlABaUpshM2lqBgdYmijseU4m1M0jqGAv/sLK7pMpDWuK2U54mQRoCyTc=","19":"dataLayer","20":"","21":"www.googletagmanager.com","22":"eyIwIjoiVVMiLCIxIjoiVVMtQ0EiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9","23":"google.tagmanager.debugui2.queue","24":"tagassistant.google.com","27":0.005,"3":"www.googletagmanager.com","30":"US","31":"US-CA","32":true,"36":"https://adservice.google.com/pagead/regclk","37":"__TAGGY_INSTALLED","38":"cct.google","39":"googTaggyReferrer","40":"https://cct.google/taggy/agent.js","41":"google.tagmanager.ta.prodqueue","42":0.01,"43":"{\"keys\":[{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BHzT6/oair5aOoHdYjEmfzUicVThxHYEl7UgRE/YE2GI/lntBc4CILotyJom55+T7o8VJA67XSfq6dT5RPph5sE=\",\"version\":0},\"id\":\"f0c5b86a-0c49-4602-bc59-93c0ebdc0e5b\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BCccnnob7RTpZnXozKqbUvXw1mtdRlZZJG0WsMn7JboEACHSKM/s+Rta9TyXuMxFmxHM5sBeIfrK96fRPJOe3aA=\",\"version\":0},\"id\":\"856269be-3e07-4432-be53-43bb4fef6799\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BICV1Oo3rVMnzzZPvqF2lPJ7rSGKM63Wquezi8KacvOrbKJczfVxaewJtKDiFC0wtd/usVpi7GfBNgXOUA4f3do=\",\"version\":0},\"id\":\"7bc98b84-7f5e-4a08-b561-134399ffd635\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BBOfSFHCVnwpMDMK/4YvG1aU6mpHrLqapgzYTpW0m4L18YROpTgYE67uzFFI3/+Del+5jK6w4oR7Ga6dcGGkH44=\",\"version\":0},\"id\":\"67490360-94dd-46de-96a7-3e1880072ea3\"},{\"hpkePublicKey\":{\"params\":{\"aead\":\"AES_128_GCM\",\"kdf\":\"HKDF_SHA256\",\"kem\":\"DHKEM_P256_HKDF_SHA256\"},\"publicKey\":\"BBWqoKhV6upED2PWudvnYDnXtdJ+ZVW64FLcSHvapcIQs1LeJzomcNf1bzhQn4If7R1jM/XNbrieqkGH2OX7gL8=\",\"version\":0},\"id\":\"1e127111-8a8e-47b5-a85d-c432304f7b5c\"}]}","44":"101509157~103116026~103200004~103233427~104684208~104684211","5":"GTM-522R2ZGR","6":"216888478","8":"res_ts:1744746180149626,srv_cl:784090092,ds:live,cv:5","9":"GTM-522R2ZGR"}
,"permissions":{
"__e":{"read_event_data":{"eventDataAccess":"specific","keyPatterns":["event"]}}
,
"__f":{"read_data_layer":{"keyPatterns":["gtm.referrer"]},"get_referrer":{"urlParts":"any"}}
,
"__u":{"read_data_layer":{"keyPatterns":["gtm.url"]},"get_url":{"urlParts":"any"}}


}



,"security_groups":{
"google":[
"__e"
,
"__f"
,
"__u"

]


}



};

var productSettings = {
  "AW-1071488781":{"preAutoPii":true},
  "AW-561693641":{"preAutoPii":true}
};




var k,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){for(var b=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global],c=0;c<b.length;++c){var d=b[c];if(d&&d.Math==Math)return d}throw Error("Cannot find global object");
},ea=ca(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",ha={},la={},ma=function(a,b,c){if(!c||a!=null){var d=la[b];if(d==null)return a[b];var e=a[d];return e!==void 0?e:a[b]}},na=function(a,b,c){if(b)a:{var d=a.split("."),e=d.length===1,f=d[0],g;!e&&f in ha?g=ha:g=ea;for(var h=0;h<d.length-1;h++){var m=d[h];if(!(m in g))break a;g=g[m]}var n=d[d.length-1],p=fa&&c==="es6"?g[n]:null,q=b(p);if(q!=null)if(e)ba(ha,n,{configurable:!0,writable:!0,value:q});else if(q!==p){if(la[n]===void 0){var r=
Math.random()*1E9>>>0;la[n]=fa?ea.Symbol(n):"$jscp$"+r+"$"+n}ba(g,la[n],{configurable:!0,writable:!0,value:q})}}};na("Symbol",function(a){if(a)return a;var b=function(f,g){this.C=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.C};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");
var oa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},pa;if(fa&&typeof Object.setPrototypeOf=="function")pa=Object.setPrototypeOf;else{var qa;a:{var ra={a:!0},ta={};try{ta.__proto__=ra;qa=ta.a;break a}catch(a){}qa=!1}pa=qa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ua=pa,va=function(a,b){a.prototype=oa(b.prototype);a.prototype.constructor=a;if(ua)ua(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Dq=b.prototype},l=function(a){var b=typeof ha.Symbol!="undefined"&&ha.Symbol.iterator&&a[ha.Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},xa=function(a){for(var b,
c=[];!(b=a.next()).done;)c.push(b.value);return c},ya=function(a){return a instanceof Array?a:xa(l(a))},Aa=function(a){return za(a,a)},za=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},Ba=fa&&typeof ma(Object,"assign")=="function"?ma(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};na("Object.assign",function(a){return a||Ba},"es6");
var Ca=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var Da=this||self,Ea=function(a,b){function c(){}c.prototype=b.prototype;a.Dq=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Dr=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};var Fa=function(a,b){this.type=a;this.data=b};var Ga=function(){this.map={};this.C={}};Ga.prototype.get=function(a){return this.map["dust."+a]};Ga.prototype.set=function(a,b){var c="dust."+a;this.C.hasOwnProperty(c)||(this.map[c]=b)};Ga.prototype.has=function(a){return this.map.hasOwnProperty("dust."+a)};Ga.prototype.remove=function(a){var b="dust."+a;this.C.hasOwnProperty(b)||delete this.map[b]};
var Ha=function(a,b){var c=[],d;for(d in a.map)if(a.map.hasOwnProperty(d)){var e=d.substring(5);switch(b){case 1:c.push(e);break;case 2:c.push(a.map[d]);break;case 3:c.push([e,a.map[d]])}}return c};Ga.prototype.Aa=function(){return Ha(this,1)};Ga.prototype.Ac=function(){return Ha(this,2)};Ga.prototype.ac=function(){return Ha(this,3)};var Ia=function(){};Ia.prototype.reset=function(){};var Ja=function(a,b){this.P=a;this.parent=b;this.N=this.C=void 0;this.Cb=!1;this.H=function(c,d,e){return c.apply(d,e)};this.values=new Ga};Ja.prototype.add=function(a,b){Ka(this,a,b,!1)};Ja.prototype.nh=function(a,b){Ka(this,a,b,!0)};var Ka=function(a,b,c,d){if(!a.Cb)if(d){var e=a.values;e.set(b,c);e.C["dust."+b]=!0}else a.values.set(b,c)};k=Ja.prototype;k.set=function(a,b){this.Cb||(!this.values.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.values.set(a,b))};
k.get=function(a){return this.values.has(a)?this.values.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.values.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new Ja(this.P,this);this.C&&a.Nb(this.C);a.Vc(this.H);a.Ld(this.N);return a};k.Cd=function(){return this.P};k.Nb=function(a){this.C=a};k.Zl=function(){return this.C};k.Vc=function(a){this.H=a};k.aj=function(){return this.H};k.Wa=function(){this.Cb=!0};k.Ld=function(a){this.N=a};k.ub=function(){return this.N};var La=function(a,b){this.fa=a;this.parent=b;this.P=this.H=void 0;this.Cb=!1;this.N=function(c,d,e){return c.apply(d,e)};this.C=new Map;this.R=new Set};La.prototype.add=function(a,b){Ma(this,a,b,!1)};La.prototype.nh=function(a,b){Ma(this,a,b,!0)};var Ma=function(a,b,c,d){a.Cb||a.R.has(b)||(d&&a.R.add(b),a.C.set(b,c))};k=La.prototype;k.set=function(a,b){this.Cb||(!this.C.has(a)&&this.parent&&this.parent.has(a)?this.parent.set(a,b):this.R.has(a)||this.C.set(a,b))};
k.get=function(a){return this.C.has(a)?this.C.get(a):this.parent?this.parent.get(a):void 0};k.has=function(a){return!!this.C.has(a)||!(!this.parent||!this.parent.has(a))};k.tb=function(){var a=new La(this.fa,this);this.H&&a.Nb(this.H);a.Vc(this.N);a.Ld(this.P);return a};k.Cd=function(){return this.fa};k.Nb=function(a){this.H=a};k.Zl=function(){return this.H};k.Vc=function(a){this.N=a};k.aj=function(){return this.N};k.Wa=function(){this.Cb=!0};k.Ld=function(a){this.P=a};k.ub=function(){return this.P};var Na=function(a,b,c){var d;d=Error.call(this,a.message);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.lm=a;this.Rl=c===void 0?!1:c;this.debugInfo=[];this.C=b};va(Na,Error);var Oa=function(a){return a instanceof Na?a:new Na(a,void 0,!0)};var Pa=[],Qa={};function Ra(a){return Pa[a]===void 0?!1:Pa[a]};var Sa=new Map;function Ta(a,b){for(var c,d=l(b),e=d.next();!e.done&&!(c=Ua(a,e.value),c instanceof Fa);e=d.next());return c}
function Ua(a,b){try{if(Ra(16)){var c=b[0],d=b.slice(1),e=String(c),f=Sa.has(e)?Sa.get(e):a.get(e);if(!f||typeof f.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return f.apply(a,d)}var g=l(b),h=g.next().value,m=xa(g),n=a.get(String(h));if(!n||typeof n.invoke!=="function")throw Oa(Error("Attempting to execute non-function "+b[0]+"."));return n.invoke.apply(n,[a].concat(ya(m)))}catch(q){var p=a.Zl();p&&p(q,b.context?{id:b[0],line:b.context.line}:null);throw q;}}
;var Xa=function(){this.H=new Ia;this.C=Ra(16)?new La(this.H):new Ja(this.H)};k=Xa.prototype;k.Cd=function(){return this.H};k.Nb=function(a){this.C.Nb(a)};k.Vc=function(a){this.C.Vc(a)};k.execute=function(a){return this.Bj([a].concat(ya(Ca.apply(1,arguments))))};k.Bj=function(){for(var a,b=l(Ca.apply(0,arguments)),c=b.next();!c.done;c=b.next())a=Ua(this.C,c.value);return a};
k.eo=function(a){var b=Ca.apply(1,arguments),c=this.C.tb();c.Ld(a);for(var d,e=l(b),f=e.next();!f.done;f=e.next())d=Ua(c,f.value);return d};k.Wa=function(){this.C.Wa()};var Ya=function(){this.Ea=!1;this.aa=new Ga};k=Ya.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};function Za(){for(var a=$a,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function ab(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}var $a,bb;function cb(a){$a=$a||ab();bb=bb||Za();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,f=a.charCodeAt(c),g=d?a.charCodeAt(c+1):0,h=e?a.charCodeAt(c+2):0,m=f>>2,n=(f&3)<<4|g>>4,p=(g&15)<<2|h>>6,q=h&63;e||(q=64,d||(p=64));b.push($a[m],$a[n],$a[p],$a[q])}return b.join("")}
function db(a){function b(m){for(;d<a.length;){var n=a.charAt(d++),p=bb[n];if(p!=null)return p;if(!/^[\s\xa0]*$/.test(n))throw Error("Unknown base64 encoding at char: "+n);}return m}$a=$a||ab();bb=bb||Za();for(var c="",d=0;;){var e=b(-1),f=b(0),g=b(64),h=b(64);if(h===64&&e===-1)return c;c+=String.fromCharCode(e<<2|f>>4);g!==64&&(c+=String.fromCharCode(f<<4&240|g>>2),h!==64&&(c+=String.fromCharCode(g<<6&192|h)))}};var eb={};function fb(a,b){eb[a]=eb[a]||[];eb[a][b]=!0}function gb(){eb.GTAG_EVENT_FEATURE_CHANNEL=hb}function ib(a){var b=eb[a];if(!b||b.length===0)return"";for(var c=[],d=0,e=0;e<b.length;e++)e%8===0&&e>0&&(c.push(String.fromCharCode(d)),d=0),b[e]&&(d|=1<<e%8);d>0&&c.push(String.fromCharCode(d));return cb(c.join("")).replace(/\.+$/,"")}function jb(){for(var a=[],b=eb.fdr||[],c=0;c<b.length;c++)b[c]&&a.push(c);return a.length>0?a:void 0};function kb(){}function lb(a){return typeof a==="function"}function mb(a){return typeof a==="string"}function nb(a){return typeof a==="number"&&!isNaN(a)}function ob(a){return Array.isArray(a)?a:[a]}function pb(a,b){if(a&&Array.isArray(a))for(var c=0;c<a.length;c++)if(a[c]&&b(a[c]))return a[c]}function qb(a,b){if(!nb(a)||!nb(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)}
function rb(a,b){for(var c=new sb,d=0;d<a.length;d++)c.set(a[d],!0);for(var e=0;e<b.length;e++)if(c.get(b[e]))return!0;return!1}function tb(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(c,a[c])}function ub(a){return!!a&&(Object.prototype.toString.call(a)==="[object Arguments]"||Object.prototype.hasOwnProperty.call(a,"callee"))}function vb(a){return Math.round(Number(a))||0}function wb(a){return"false"===String(a).toLowerCase()?!1:!!a}
function xb(a){var b=[];if(Array.isArray(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b}function yb(a){return a?a.replace(/^\s+|\s+$/g,""):""}function zb(){return new Date(Date.now())}function Ab(){return zb().getTime()}var sb=function(){this.prefix="gtm.";this.values={}};sb.prototype.set=function(a,b){this.values[this.prefix+a]=b};sb.prototype.get=function(a){return this.values[this.prefix+a]};sb.prototype.contains=function(a){return this.get(a)!==void 0};
function Bb(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c}function Cb(a){var b=a;return function(){if(b){var c=b;b=void 0;try{c()}catch(d){}}}}function Eb(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])}function Fb(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c}function Gb(a,b){return a.length>=b.length&&a.substring(0,b.length)===b}
function Hb(a,b,c){c=c||[];for(var d=a,e=0;e<b.length-1;e++){if(!d.hasOwnProperty(b[e]))return;d=d[b[e]];if(c.indexOf(d)>=0)return}return d}function Ib(a,b){for(var c={},d=c,e=a.split("."),f=0;f<e.length-1;f++)d=d[e[f]]={};d[e[e.length-1]]=b;return c}var Jb=/^\w{1,9}$/;function Kb(a,b){a=a||{};b=b||",";var c=[];tb(a,function(d,e){Jb.test(d)&&e&&c.push(d)});return c.join(b)}function Lb(a,b){function c(){e&&++d===b&&(e(),e=null,c.done=!0)}var d=0,e=a;c.done=!1;return c}
function Mb(a){if(!a)return a;var b=a;try{b=decodeURIComponent(a)}catch(d){}var c=b.split(",");return c.length===2&&c[0]===c[1]?c[0]:a}
function Nb(a,b,c){function d(n){var p=n.split("=")[0];if(a.indexOf(p)<0)return n;if(c!==void 0)return p+"="+c}function e(n){return n.split("&").map(d).filter(function(p){return p!==void 0}).join("&")}var f=b.href.split(/[?#]/)[0],g=b.search,h=b.hash;g[0]==="?"&&(g=g.substring(1));h[0]==="#"&&(h=h.substring(1));g=e(g);h=e(h);g!==""&&(g="?"+g);h!==""&&(h="#"+h);var m=""+f+g+h;m[m.length-1]==="/"&&(m=m.substring(0,m.length-1));return m}
function Ob(a){for(var b=0;b<3;++b)try{var c=decodeURIComponent(a).replace(/\+/g," ");if(c===a)break;a=c}catch(d){return""}return a}function Pb(){var a=x.crypto||x.msCrypto;if(a&&a.getRandomValues)try{var b=new Uint8Array(25);a.getRandomValues(b);return btoa(String.fromCharCode.apply(String,ya(b))).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}catch(c){}};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Qb=globalThis.trustedTypes,Rb;function Sb(){var a=null;if(!Qb)return a;try{var b=function(c){return c};a=Qb.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a}function Tb(){Rb===void 0&&(Rb=Sb());return Rb};var Vb=function(a){this.C=a};Vb.prototype.toString=function(){return this.C+""};function Wb(a){var b=a,c=Tb(),d=c?c.createScriptURL(b):b;return new Vb(d)}function Xb(a){if(a instanceof Vb)return a.C;throw Error("");};var Yb=Aa([""]),Zb=za(["\x00"],["\\0"]),$b=za(["\n"],["\\n"]),bc=za(["\x00"],["\\u0000"]);function cc(a){return a.toString().indexOf("`")===-1}cc(function(a){return a(Yb)})||cc(function(a){return a(Zb)})||cc(function(a){return a($b)})||cc(function(a){return a(bc)});var dc=function(a){this.C=a};dc.prototype.toString=function(){return this.C};var ec=function(a){this.Sp=a};function fc(a){return new ec(function(b){return b.substr(0,a.length+1).toLowerCase()===a+":"})}var hc=[fc("data"),fc("http"),fc("https"),fc("mailto"),fc("ftp"),new ec(function(a){return/^[^:]*([/?#]|$)/.test(a)})];function ic(a){var b;b=b===void 0?hc:b;if(a instanceof dc)return a;for(var c=0;c<b.length;++c){var d=b[c];if(d instanceof ec&&d.Sp(a))return new dc(a)}}var jc=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;
function kc(a){var b;if(a instanceof dc)if(a instanceof dc)b=a.C;else throw Error("");else b=jc.test(a)?a:void 0;return b};function lc(a,b){var c=kc(b);c!==void 0&&(a.action=c)};function mc(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var nc=function(a){this.C=a};nc.prototype.toString=function(){return this.C+""};var pc=function(){this.C=oc[0].toLowerCase()};pc.prototype.toString=function(){return this.C};function qc(a,b){var c=[new pc];if(c.length===0)throw Error("");var d=c.map(function(f){var g;if(f instanceof pc)g=f.C;else throw Error("");return g}),e=b.toLowerCase();if(d.every(function(f){return e.indexOf(f)!==0}))throw Error('Attribute "'+b+'" does not match any of the allowed prefixes.');a.setAttribute(b,"true")};var rc=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON",
"INPUT"]);function sc(a){return a===null?"null":a===void 0?"undefined":a};var x=window,tc=window.history,z=document,uc=navigator;function vc(){var a;try{a=uc.serviceWorker}catch(b){return}return a}var wc=z.currentScript,xc=wc&&wc.src;function yc(a,b){var c=x,d=c[a];c[a]=d===void 0?b:d;return c[a]}function zc(a){return(uc.userAgent||"").indexOf(a)!==-1}function Ac(){return zc("Firefox")||zc("FxiOS")}function Bc(){return(zc("GSA")||zc("GoogleApp"))&&(zc("iPhone")||zc("iPad"))}function Cc(){return zc("Edg/")||zc("EdgA/")||zc("EdgiOS/")}
var Dc={async:1,nonce:1,onerror:1,onload:1,src:1,type:1},Ec={onload:1,src:1,width:1,height:1,style:1};function Fc(a,b,c){b&&tb(b,function(d,e){d=d.toLowerCase();c.hasOwnProperty(d)||a.setAttribute(d,e)})}
function Gc(a,b,c,d,e){var f=z.createElement("script");Fc(f,d,Dc);f.type="text/javascript";f.async=d&&d.async===!1?!1:!0;var g;g=Wb(sc(a));f.src=Xb(g);var h,m=f.ownerDocument;m=m===void 0?document:m;var n,p,q=(p=(n=m).querySelector)==null?void 0:p.call(n,"script[nonce]");(h=q==null?"":q.nonce||q.getAttribute("nonce")||"")&&f.setAttribute("nonce",h);b&&(f.onload=b);c&&(f.onerror=c);if(e)e.appendChild(f);else{var r=z.getElementsByTagName("script")[0]||z.body||z.head;r.parentNode.insertBefore(f,r)}return f}
function Hc(){if(xc){var a=xc.toLowerCase();if(a.indexOf("https://")===0)return 2;if(a.indexOf("http://")===0)return 3}return 1}function Ic(a,b,c,d,e,f){f=f===void 0?!0:f;var g=e,h=!1;g||(g=z.createElement("iframe"),h=!0);Fc(g,c,Ec);d&&tb(d,function(n,p){g.dataset[n]=p});f&&(g.height="0",g.width="0",g.style.display="none",g.style.visibility="hidden");a!==void 0&&(g.src=a);if(h){var m=z.body&&z.body.lastChild||z.body||z.head;m.parentNode.insertBefore(g,m)}b&&(g.onload=b);return g}
function Jc(a,b,c,d){return Kc(a,b,c,d)}function Lc(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,!!d)}function Mc(a,b,c){a.removeEventListener&&a.removeEventListener(b,c,!1)}function Nc(a){x.setTimeout(a,0)}function Oc(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null}function Pc(a){var b=a.innerText||a.textContent||"";b&&b!==" "&&(b=b.replace(/^[\s\xa0]+/g,""),b=b.replace(/[\s\xa0]+$/g,""));b&&(b=b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b}
function Qc(a){var b=z.createElement("div"),c=b,d,e=sc("A<div>"+a+"</div>"),f=Tb(),g=f?f.createHTML(e):e;d=new nc(g);if(c.nodeType===1&&/^(script|style)$/i.test(c.tagName))throw Error("");var h;if(d instanceof nc)h=d.C;else throw Error("");c.innerHTML=h;b=b.lastChild;for(var m=[];b&&b.firstChild;)m.push(b.removeChild(b.firstChild));return m}
function Rc(a,b,c){c=c||100;for(var d={},e=0;e<b.length;e++)d[b[e]]=!0;for(var f=a,g=0;f&&g<=c;g++){if(d[String(f.tagName).toLowerCase()])return f;f=f.parentElement}return null}function Sc(a,b,c){var d;try{d=uc.sendBeacon&&uc.sendBeacon(a)}catch(e){fb("TAGGING",15)}d?b==null||b():Kc(a,b,c)}function Tc(a,b){try{return uc.sendBeacon(a,b)}catch(c){fb("TAGGING",15)}return!1}var Uc={cache:"no-store",credentials:"include",keepalive:!0,method:"POST",mode:"no-cors",redirect:"follow"};
function Vc(a,b,c,d,e){if(Wc()){var f=ma(Object,"assign").call(Object,{},Uc);b&&(f.body=b);c&&(c.attributionReporting&&(f.attributionReporting=c.attributionReporting),c.browsingTopics&&(f.browsingTopics=c.browsingTopics),c.credentials&&(f.credentials=c.credentials),c.mode&&(f.mode=c.mode),c.method&&(f.method=c.method));try{var g=x.fetch(a,f);if(g)return g.then(function(m){m&&(m.ok||m.status===0)?d==null||d():e==null||e()}).catch(function(){e==null||e()}),!0}catch(m){}}if(c&&c.Ch)return e==null||e(),
!1;if(b){var h=Tc(a,b);h?d==null||d():e==null||e();return h}Xc(a,d,e);return!0}function Wc(){return typeof x.fetch==="function"}function Yc(a,b){var c=a[b];c&&typeof c.animVal==="string"&&(c=c.animVal);return c}function Zc(){var a=x.performance;if(a&&lb(a.now))return a.now()}
function $c(){var a,b=x.performance;if(b&&b.getEntriesByType)try{var c=b.getEntriesByType("navigation");c&&c.length>0&&(a=c[0].type)}catch(d){return"e"}if(!a)return"u";switch(a){case "navigate":return"n";case "back_forward":return"h";case "reload":return"r";case "prerender":return"p";default:return"x"}}function ad(){return x.performance||void 0}function bd(){var a=x.webPixelsManager;return a?a.createShopifyExtend!==void 0:!1}
var Kc=function(a,b,c,d){var e=new Image(1,1);Fc(e,d,{});e.onload=function(){e.onload=null;b&&b()};e.onerror=function(){e.onerror=null;c&&c()};e.src=a;return e},Xc=Sc;function cd(a,b){return this.evaluate(a)&&this.evaluate(b)}function dd(a,b){return this.evaluate(a)===this.evaluate(b)}function ed(a,b){return this.evaluate(a)||this.evaluate(b)}function fd(a,b){var c=this.evaluate(a),d=this.evaluate(b);return String(c).indexOf(String(d))>-1}function gd(a,b){var c=String(this.evaluate(a)),d=String(this.evaluate(b));return c.substring(0,d.length)===d}
function hd(a,b){var c=this.evaluate(a),d=this.evaluate(b);switch(c){case "pageLocation":var e=x.location.href;d instanceof Ya&&d.get("stripProtocol")&&(e=e.replace(/^https?:\/\//,""));return e}};/*
 jQuery (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license.
*/
var id=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,jd=function(a){if(a==null)return String(a);var b=id.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},kd=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},ld=function(a){if(!a||jd(a)!="object"||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!kd(a,"constructor")&&!kd(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return b===void 0||
kd(a,b)},md=function(a,b){var c=b||(jd(a)=="array"?[]:{}),d;for(d in a)if(kd(a,d)){var e=a[d];jd(e)=="array"?(jd(c[d])!="array"&&(c[d]=[]),c[d]=md(e,c[d])):ld(e)?(ld(c[d])||(c[d]={}),c[d]=md(e,c[d])):c[d]=e}return c};function nd(a){if(a==void 0||Array.isArray(a)||ld(a))return!0;switch(typeof a){case "boolean":case "number":case "string":case "function":return!0}return!1}function od(a){return typeof a==="number"&&a>=0&&isFinite(a)&&a%1===0||typeof a==="string"&&a[0]!=="-"&&a===""+parseInt(a)};var pd=function(a){a=a===void 0?[]:a;this.aa=new Ga;this.values=[];this.Ea=!1;for(var b in a)a.hasOwnProperty(b)&&(od(b)?this.values[Number(b)]=a[Number(b)]:this.aa.set(b,a[b]))};k=pd.prototype;k.toString=function(a){if(a&&a.indexOf(this)>=0)return"";for(var b=[],c=0;c<this.values.length;c++){var d=this.values[c];d===null||d===void 0?b.push(""):d instanceof pd?(a=a||[],a.push(this),b.push(d.toString(a)),a.pop()):b.push(String(d))}return b.join(",")};
k.set=function(a,b){if(!this.Ea)if(a==="length"){if(!od(b))throw Oa(Error("RangeError: Length property must be a valid integer."));this.values.length=Number(b)}else od(a)?this.values[Number(a)]=b:this.aa.set(a,b)};k.get=function(a){return a==="length"?this.length():od(a)?this.values[Number(a)]:this.aa.get(a)};k.length=function(){return this.values.length};k.Aa=function(){for(var a=this.aa.Aa(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(String(b));return a};
k.Ac=function(){for(var a=this.aa.Ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push(this.values[b]);return a};k.ac=function(){for(var a=this.aa.ac(),b=0;b<this.values.length;b++)this.values.hasOwnProperty(b)&&a.push([String(b),this.values[b]]);return a};k.remove=function(a){od(a)?delete this.values[Number(a)]:this.Ea||this.aa.remove(a)};k.pop=function(){return this.values.pop()};
k.push=function(){var a=Ca.apply(0,arguments);return Ra(17)&&arguments.length===1?this.values.push(arguments[0]):this.values.push.apply(this.values,ya(a))};k.shift=function(){return this.values.shift()};k.splice=function(a,b){var c=Ca.apply(2,arguments);return b===void 0&&c.length===0?new pd(this.values.splice(a)):new pd(this.values.splice.apply(this.values,[a,b||0].concat(ya(c))))};k.unshift=function(){return this.values.unshift.apply(this.values,ya(Ca.apply(0,arguments)))};
k.has=function(a){return od(a)&&this.values.hasOwnProperty(a)||this.aa.has(a)};k.Wa=function(){this.Ea=!0;Object.freeze(this.values)};k.Cb=function(){return this.Ea};function qd(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var rd=function(a,b){this.functionName=a;this.Sc=b;this.aa=new Ga;this.Ea=!1};k=rd.prototype;k.toString=function(){return this.functionName};k.getName=function(){return this.functionName};k.getKeys=function(){return new pd(this.Aa())};k.invoke=function(a){var b=Ca.apply(1,arguments);return Ra(18)?this.Sc.apply(new sd(this,a),b):this.Sc.call.apply(this.Sc,[new sd(this,a)].concat(ya(b)))};k.apply=function(a,b){return this.Sc.apply(new sd(this,a),b)};
k.Lb=function(a){var b=Ca.apply(1,arguments);try{return this.invoke.apply(this,[a].concat(ya(b)))}catch(c){}};k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};k.Wa=function(){this.Ea=!0};k.Cb=function(){return this.Ea};var td=function(a,b){rd.call(this,a,b)};
va(td,rd);var ud=function(a,b){rd.call(this,a,b)};va(ud,rd);var sd=function(a,b){this.Sc=a;this.K=b};sd.prototype.evaluate=function(a){var b=this.K;return Array.isArray(a)?Ua(b,a):a};sd.prototype.getName=function(){return this.Sc.getName()};sd.prototype.Cd=function(){return this.K.Cd()};var vd=function(){this.map=new Map};vd.prototype.set=function(a,b){this.map.set(a,b)};vd.prototype.get=function(a){return this.map.get(a)};var wd=function(){this.keys=[];this.values=[]};wd.prototype.set=function(a,b){this.keys.push(a);this.values.push(b)};wd.prototype.get=function(a){var b=this.keys.indexOf(a);if(b>-1)return this.values[b]};function xd(){try{return Map?new vd:new wd}catch(a){return new wd}};var yd=function(a){if(a instanceof yd)return a;if(nd(a))throw Error("Type of given value has an equivalent Pixie type.");this.value=a};yd.prototype.getValue=function(){return this.value};yd.prototype.toString=function(){return String(this.value)};var Ad=function(a){this.promise=a;this.Ea=!1;this.aa=new Ga;this.aa.set("then",zd(this));this.aa.set("catch",zd(this,!0));this.aa.set("finally",zd(this,!1,!0))};k=Ad.prototype;k.get=function(a){return this.aa.get(a)};k.set=function(a,b){this.Ea||this.aa.set(a,b)};k.has=function(a){return this.aa.has(a)};k.remove=function(a){this.Ea||this.aa.remove(a)};k.Aa=function(){return this.aa.Aa()};k.Ac=function(){return this.aa.Ac()};k.ac=function(){return this.aa.ac()};
var zd=function(a,b,c){b=b===void 0?!1:b;c=c===void 0?!1:c;return new td("",function(d,e){b&&(e=d,d=void 0);c&&(e=d);d instanceof td||(d=void 0);e instanceof td||(e=void 0);var f=this.K.tb(),g=function(m){return function(n){try{return c?(m.invoke(f),a.promise):m.invoke(f,n)}catch(p){return Promise.reject(p instanceof Error?new yd(p):String(p))}}},h=a.promise.then(d&&g(d),e&&g(e));return new Ad(h)})};Ad.prototype.Wa=function(){this.Ea=!0};Ad.prototype.Cb=function(){return this.Ea};function Bd(a,b,c){var d=xd(),e=function(g,h){for(var m=g.Aa(),n=0;n<m.length;n++)h[m[n]]=f(g.get(m[n]))},f=function(g){if(g===null||g===void 0)return g;var h=d.get(g);if(h)return h;if(g instanceof pd){var m=[];d.set(g,m);for(var n=g.Aa(),p=0;p<n.length;p++)m[n[p]]=f(g.get(n[p]));return m}if(g instanceof Ad)return g.promise.then(function(u){return Bd(u,b,1)},function(u){return Promise.reject(Bd(u,b,1))});if(g instanceof Ya){var q={};d.set(g,q);e(g,q);return q}if(g instanceof td){var r=function(){for(var u=
[],v=0;v<arguments.length;v++)u[v]=Cd(arguments[v],b,c);var w=new Ja(b?b.Cd():new Ia);b&&w.Ld(b.ub());return f(Ra(16)?g.apply(w,u):g.invoke.apply(g,[w].concat(ya(u))))};d.set(g,r);e(g,r);return r}var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;case 3:t=!1;break;default:}if(g instanceof yd&&t)return g.getValue();switch(typeof g){case "boolean":case "number":case "string":case "undefined":return g;
case "object":if(g===null)return null}};return f(a)}
function Cd(a,b,c){var d=xd(),e=function(g,h){for(var m in g)g.hasOwnProperty(m)&&h.set(m,f(g[m]))},f=function(g){var h=d.get(g);if(h)return h;if(Array.isArray(g)||ub(g)){var m=new pd;d.set(g,m);for(var n in g)g.hasOwnProperty(n)&&m.set(n,f(g[n]));return m}if(ld(g)){var p=new Ya;d.set(g,p);e(g,p);return p}if(typeof g==="function"){var q=new td("",function(){for(var u=Ca.apply(0,arguments),v=[],w=0;w<u.length;w++)v[w]=Bd(this.evaluate(u[w]),b,c);return f(this.K.aj()(g,g,v))});d.set(g,q);e(g,q);return q}var r=typeof g;if(g===null||r==="string"||r==="number"||r==="boolean")return g;var t=!1;switch(c){case 1:t=!0;break;case 2:t=!1;break;default:}if(g!==void 0&&t)return new yd(g)};return f(a)};var Dd={supportedMethods:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a){for(var b=[],c=0;c<this.length();c++)b.push(this.get(c));for(var d=1;d<arguments.length;d++)if(arguments[d]instanceof pd)for(var e=arguments[d],f=0;f<e.length();f++)b.push(e.get(f));else b.push(arguments[d]);return new pd(b)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&
d<c;d++)if(this.has(d)&&!b.invoke(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.invoke(a,this.get(e),e,this)&&d.push(this.get(e));return new pd(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.invoke(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=c===void 0?0:Number(c);e<0&&(e=Math.max(d+e,0));for(var f=
e;f<d;f++)if(this.has(f)&&this.get(f)===b)return f;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;c!==void 0&&(e=c<0?d+c:Math.min(c,e));for(var f=e;f>=0;f--)if(this.has(f)&&this.get(f)===b)return f;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.invoke(a,this.get(e),e,this));return new pd(d)},pop:function(){return this.pop()},push:function(a){return this.push.apply(this,
ya(Ca.apply(1,arguments)))},reduce:function(a,b,c){var d=this.length(),e,f=0;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: Reduce on List with no elements."));for(var g=0;g<d;g++)if(this.has(g)){e=this.get(g);f=g+1;break}if(g===d)throw Oa(Error("TypeError: Reduce on List with no elements."));}for(var h=f;h<d;h++)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,f=d-1;if(c!==void 0)e=c;else{if(d===0)throw Oa(Error("TypeError: ReduceRight on List with no elements."));
for(var g=1;g<=d;g++)if(this.has(d-g)){e=this.get(d-g);f=d-(g+1);break}if(g>d)throw Oa(Error("TypeError: ReduceRight on List with no elements."));}for(var h=f;h>=0;h--)this.has(h)&&(e=b.invoke(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=qd(this),b=a.length-1,c=0;b>=0;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();b===void 0&&(b=0);b=b<0?Math.max(d+b,0):Math.min(b,d);c=c===
void 0?d:c<0?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,c);for(var e=[],f=b;f<c;f++)e.push(this.get(f));return new pd(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.invoke(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=qd(this);b===void 0?c.sort():c.sort(function(e,f){return Number(b.invoke(a,e,f))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d);return this},splice:function(a,b,c){return this.splice.apply(this,
[b,c].concat(ya(Ca.apply(3,arguments))))},toString:function(){return this.toString()},unshift:function(a){return this.unshift.apply(this,ya(Ca.apply(1,arguments)))}};var Ed={charAt:1,concat:1,indexOf:1,lastIndexOf:1,match:1,replace:1,search:1,slice:1,split:1,substring:1,toLowerCase:1,toLocaleLowerCase:1,toString:1,toUpperCase:1,toLocaleUpperCase:1,trim:1},Fd=new Fa("break"),Gd=new Fa("continue");function Hd(a,b){return this.evaluate(a)+this.evaluate(b)}function Id(a,b){return this.evaluate(a)&&this.evaluate(b)}
function Jd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!(f instanceof pd))throw Error("Error: Non-List argument given to Apply instruction.");if(d===null||d===void 0)throw Oa(Error("TypeError: Can't read property "+e+" of "+d+"."));var g=typeof d==="number";if(typeof d==="boolean"||g){if(e==="toString"){if(g&&f.length()){var h=Bd(f.get(0));try{return d.toString(h)}catch(v){}}return d.toString()}throw Oa(Error("TypeError: "+d+"."+e+" is not a function."));}if(typeof d===
"string"){if(Ed.hasOwnProperty(e)){var m=2;m=1;var n=Bd(f,void 0,m);return Cd(d[e].apply(d,n),this.K)}throw Oa(Error("TypeError: "+e+" is not a function"));}if(d instanceof pd){if(d.has(e)){var p=d.get(String(e));if(p instanceof td){var q=qd(f);return Ra(16)?p.apply(this.K,q):p.invoke.apply(p,[this.K].concat(ya(q)))}throw Oa(Error("TypeError: "+e+" is not a function"));
}if(Dd.supportedMethods.indexOf(e)>=0){var r=qd(f);return Dd[e].call.apply(Dd[e],[d,this.K].concat(ya(r)))}}if(d instanceof td||d instanceof Ya||d instanceof Ad){if(d.has(e)){var t=d.get(e);if(t instanceof td){var u=qd(f);return Ra(16)?t.apply(this.K,u):t.invoke.apply(t,[this.K].concat(ya(u)))}throw Oa(Error("TypeError: "+e+" is not a function"));}if(e==="toString")return d instanceof td?d.getName():d.toString();if(e==="hasOwnProperty")return d.has(f.get(0))}if(d instanceof yd&&e==="toString")return d.toString();
throw Oa(Error("TypeError: Object has no '"+e+"' property."));}function Kd(a,b){a=this.evaluate(a);if(typeof a!=="string")throw Error("Invalid key name given for assignment.");var c=this.K;if(!c.has(a))throw Error("Attempting to assign to undefined value "+b);var d=this.evaluate(b);c.set(a,d);return d}function Ld(){var a=Ca.apply(0,arguments),b=this.K.tb(),c=Ta(b,a);if(c instanceof Fa)return c}function Md(){return Fd}
function Nd(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof Fa)return d}}function Od(){for(var a=this.K,b=0;b<arguments.length-1;b+=2){var c=arguments[b];if(typeof c==="string"){var d=this.evaluate(arguments[b+1]);a.nh(c,d)}}}function Pd(){return Gd}function Qd(a,b){return new Fa(a,this.evaluate(b))}
function Rd(a,b){var c=Ca.apply(2,arguments),d;if(Ra(17)){for(var e=[],f=this.evaluate(b),g=0;g<f.length;g++)e.push(f[g]);d=new pd(e)}else{d=new pd;for(var h=this.evaluate(b),m=0;m<h.length;m++)d.push(h[m])}var n=[51,a,d].concat(ya(c));this.K.add(a,this.evaluate(n))}function Td(a,b){return this.evaluate(a)/this.evaluate(b)}function Ud(a,b){var c=this.evaluate(a),d=this.evaluate(b),e=c instanceof yd,f=d instanceof yd;return e||f?e&&f?c.getValue()===d.getValue():!1:c==d}
function Vd(){for(var a,b=0;b<arguments.length;b++)a=this.evaluate(arguments[b]);return a}function Wd(a,b,c,d){for(var e=0;e<b();e++){var f=a(c(e)),g=Ta(f,d);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}}}function Xd(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(f){return f},c);if(b instanceof Ya||b instanceof Ad||b instanceof pd||b instanceof td){var d=b.Aa(),e=d.length;return Wd(a,function(){return e},function(f){return d[f]},c)}}
function Yd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){g.set(d,h);return g},e,f)}function Zd(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function $d(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return Xd(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function ae(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){g.set(d,h);return g},e,f)}function ce(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.tb();m.nh(d,h);return m},e,f)}function de(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c),g=this.K;return be(function(h){var m=g.tb();m.add(d,h);return m},e,f)}
function be(a,b,c){if(typeof b==="string")return Wd(a,function(){return b.length},function(d){return b[d]},c);if(b instanceof pd)return Wd(a,function(){return b.length()},function(d){return b.get(d)},c);throw Oa(Error("The value is not iterable."));}
function ee(a,b,c,d){function e(q,r){for(var t=0;t<f.length();t++){var u=f.get(t);r.add(u,q.get(u))}}var f=this.evaluate(a);if(!(f instanceof pd))throw Error("TypeError: Non-List argument given to ForLet instruction.");var g=this.K,h=this.evaluate(d),m=g.tb();for(e(g,m);Ua(m,b);){var n=Ta(m,h);if(n instanceof Fa){if(n.type==="break")break;if(n.type==="return")return n}var p=g.tb();e(m,p);Ua(p,c);m=p}}
function fe(a,b){var c=Ca.apply(2,arguments),d=this.K,e=this.evaluate(b);if(!(e instanceof pd))throw Error("Error: non-List value given for Fn argument names.");return new td(a,function(){return function(){var f=Ca.apply(0,arguments),g=d.tb();g.ub()===void 0&&g.Ld(this.K.ub());for(var h=[],m=0;m<f.length;m++){var n=this.evaluate(f[m]);h[m]=n}for(var p=e.get("length"),q=0;q<p;q++)q<h.length?g.add(e.get(q),h[q]):g.add(e.get(q),void 0);g.add("arguments",new pd(h));var r=Ta(g,c);if(r instanceof Fa)return r.type===
"return"?r.data:r}}())}function ge(a){var b=this.evaluate(a),c=this.K;if(he&&!c.has(b))throw new ReferenceError(b+" is not defined.");return c.get(b)}
function ie(a,b){var c,d=this.evaluate(a),e=this.evaluate(b);if(d===void 0||d===null)throw Oa(Error("TypeError: Cannot read properties of "+d+" (reading '"+e+"')"));if(d instanceof Ya||d instanceof Ad||d instanceof pd||d instanceof td)c=d.get(e);else if(typeof d==="string")e==="length"?c=d.length:od(e)&&(c=d[e]);else if(d instanceof yd)return;return c}function je(a,b){return this.evaluate(a)>this.evaluate(b)}function ke(a,b){return this.evaluate(a)>=this.evaluate(b)}
function le(a,b){var c=this.evaluate(a),d=this.evaluate(b);c instanceof yd&&(c=c.getValue());d instanceof yd&&(d=d.getValue());return c===d}function me(a,b){return!le.call(this,a,b)}function ne(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=Ta(this.K,d);if(e instanceof Fa)return e}var he=!1;
function oe(a,b){return this.evaluate(a)<this.evaluate(b)}function pe(a,b){return this.evaluate(a)<=this.evaluate(b)}function qe(){if(Ra(17)){for(var a=[],b=0;b<arguments.length;b++){var c=this.evaluate(arguments[b]);a.push(c)}return new pd(a)}for(var d=new pd,e=0;e<arguments.length;e++){var f=this.evaluate(arguments[e]);d.push(f)}return d}function re(){for(var a=new Ya,b=0;b<arguments.length-1;b+=2){var c=String(this.evaluate(arguments[b])),d=this.evaluate(arguments[b+1]);a.set(c,d)}return a}
function se(a,b){return this.evaluate(a)%this.evaluate(b)}function te(a,b){return this.evaluate(a)*this.evaluate(b)}function ue(a){return-this.evaluate(a)}function ve(a){return!this.evaluate(a)}function we(a,b){return!Ud.call(this,a,b)}function xe(){return null}function ye(a,b){return this.evaluate(a)||this.evaluate(b)}function ze(a,b){var c=this.evaluate(a);this.evaluate(b);return c}function Ae(a){return this.evaluate(a)}function Be(){return Ca.apply(0,arguments)}
function Ce(a){return new Fa("return",this.evaluate(a))}function De(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(d===null||d===void 0)throw Oa(Error("TypeError: Can't set property "+e+" of "+d+"."));(d instanceof td||d instanceof pd||d instanceof Ya)&&d.set(String(e),f);return f}function Ee(a,b){return this.evaluate(a)-this.evaluate(b)}
function Fe(a,b,c){var d=this.evaluate(a),e=this.evaluate(b),f=this.evaluate(c);if(!Array.isArray(e)||!Array.isArray(f))throw Error("Error: Malformed switch instruction.");for(var g,h=!1,m=0;m<e.length;m++)if(h||d===this.evaluate(e[m]))if(g=this.evaluate(f[m]),g instanceof Fa){var n=g.type;if(n==="break")return;if(n==="return"||n==="continue")return g}else h=!0;if(f.length===e.length+1&&(g=this.evaluate(f[f.length-1]),g instanceof Fa&&(g.type==="return"||g.type==="continue")))return g}
function Ge(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)}function He(a){var b=this.evaluate(a);return b instanceof td?"function":typeof b}function Ie(){for(var a=this.K,b=0;b<arguments.length;b++){var c=arguments[b];typeof c!=="string"||a.add(c,void 0)}}
function Je(a,b,c,d){var e=this.evaluate(d);if(this.evaluate(c)){var f=Ta(this.K,e);if(f instanceof Fa){if(f.type==="break")return;if(f.type==="return")return f}}for(;this.evaluate(a);){var g=Ta(this.K,e);if(g instanceof Fa){if(g.type==="break")break;if(g.type==="return")return g}this.evaluate(b)}}function Ke(a){return~Number(this.evaluate(a))}function Le(a,b){return Number(this.evaluate(a))<<Number(this.evaluate(b))}function Me(a,b){return Number(this.evaluate(a))>>Number(this.evaluate(b))}
function Ne(a,b){return Number(this.evaluate(a))>>>Number(this.evaluate(b))}function Oe(a,b){return Number(this.evaluate(a))&Number(this.evaluate(b))}function Pe(a,b){return Number(this.evaluate(a))^Number(this.evaluate(b))}function Qe(a,b){return Number(this.evaluate(a))|Number(this.evaluate(b))}function Re(){}
function Se(a,b,c){try{var d=this.evaluate(b);if(d instanceof Fa)return d}catch(h){if(!(h instanceof Na&&h.Rl))throw h;var e=this.K.tb();a!==""&&(h instanceof Na&&(h=h.lm),e.add(a,new yd(h)));var f=this.evaluate(c),g=Ta(e,f);if(g instanceof Fa)return g}}function Te(a,b){var c,d;try{d=this.evaluate(a)}catch(f){if(!(f instanceof Na&&f.Rl))throw f;c=f}var e=this.evaluate(b);if(e instanceof Fa)return e;if(c)throw c;if(d instanceof Fa)return d};var Ve=function(){this.C=new Xa;Ue(this)};Ve.prototype.execute=function(a){return this.C.Bj(a)};var Ue=function(a){var b=function(c,d){var e=new ud(String(c),d);e.Wa();var f=String(c);a.C.C.set(f,e);Sa.set(f,e)};b("map",re);b("and",cd);b("contains",fd);b("equals",dd);b("or",ed);b("startsWith",gd);b("variable",hd)};Ve.prototype.Nb=function(a){this.C.Nb(a)};var Xe=function(){this.H=!1;this.C=new Xa;We(this);this.H=!0};Xe.prototype.execute=function(a){return Ze(this.C.Bj(a))};var $e=function(a,b,c){return Ze(a.C.eo(b,c))};Xe.prototype.Wa=function(){this.C.Wa()};
var We=function(a){var b=function(c,d){var e=String(c),f=new ud(e,d);f.Wa();a.C.C.set(e,f);Sa.set(e,f)};b(0,Hd);b(1,Id);b(2,Jd);b(3,Kd);b(56,Oe);b(57,Le);b(58,Ke);b(59,Qe);b(60,Me);b(61,Ne);b(62,Pe);b(53,Ld);b(4,Md);b(5,Nd);b(68,Se);b(52,Od);b(6,Pd);b(49,Qd);b(7,qe);b(8,re);b(9,Nd);b(50,Rd);b(10,Td);b(12,Ud);b(13,Vd);b(67,Te);b(51,fe);b(47,Yd);b(54,Zd);b(55,$d);b(63,ee);b(64,ae);b(65,ce);b(66,de);b(15,ge);b(16,ie);b(17,ie);b(18,je);b(19,ke);b(20,le);b(21,me);b(22,ne);b(23,oe);b(24,pe);b(25,se);b(26,
te);b(27,ue);b(28,ve);b(29,we);b(45,xe);b(30,ye);b(32,ze);b(33,ze);b(34,Ae);b(35,Ae);b(46,Be);b(36,Ce);b(43,De);b(37,Ee);b(38,Fe);b(39,Ge);b(40,He);b(44,Re);b(41,Ie);b(42,Je)};Xe.prototype.Cd=function(){return this.C.Cd()};Xe.prototype.Nb=function(a){this.C.Nb(a)};Xe.prototype.Vc=function(a){this.C.Vc(a)};
function Ze(a){if(a instanceof Fa||a instanceof td||a instanceof pd||a instanceof Ya||a instanceof Ad||a instanceof yd||a===null||a===void 0||typeof a==="string"||typeof a==="number"||typeof a==="boolean")return a};var af=function(a){this.message=a};function bf(a){a.Kr=!0;return a};var cf=bf(function(a){return typeof a==="string"});function df(a){var b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[a];return b===void 0?new af("Value "+a+" can not be encoded in web-safe base64 dictionary."):b};function ef(a){switch(a){case 1:return"1";case 2:case 4:return"0";default:return"-"}};var ff=/^[1-9a-zA-Z_-][1-9a-c][1-9a-v]\d$/;function gf(a,b){for(var c="",d=!0;a>7;){var e=a&31;a>>=5;d?d=!1:e|=32;c=""+df(e)+c}a<<=2;d||(a|=32);return c=""+df(a|b)+c}
function hf(a,b){var c;var d=a.Uc,e=a.Ah;d===void 0?c="":(e||(e=0),c=""+gf(1,1)+df(d<<2|e));var f=a.Ql,g=a.No,h="4"+c+(f?""+gf(2,1)+df(f):"")+(g?""+gf(12,1)+df(g):""),m,n=a.Cj;m=n&&ff.test(n)?""+gf(3,2)+n:"";var p,q=a.yj;p=q?""+gf(4,1)+df(q):"";var r;var t=a.ctid;if(t&&b){var u=gf(5,3),v=t.split("-"),w=v[0].toUpperCase();if(w!=="GTM"&&w!=="OPT")r="";else{var y=v[1];r=""+u+df(1+y.length)+(a.dm||0)+y}}else r="";var A=a.Cq,C=a.we,D=a.Pa,G=a.Or,I=h+m+p+r+(A?""+gf(6,1)+df(A):"")+(C?""+gf(7,3)+df(C.length)+
C:"")+(D?""+gf(8,3)+df(D.length)+D:"")+(G?""+gf(9,3)+df(G.length)+G:""),M;var T=a.Sl;T=T===void 0?{}:T;for(var da=[],N=l(Object.keys(T)),W=N.next();!W.done;W=N.next()){var ia=W.value;da[Number(ia)]=T[ia]}if(da.length){var ka=gf(10,3),Y;if(da.length===0)Y=df(0);else{for(var X=[],ja=0,wa=!1,sa=0;sa<da.length;sa++){wa=!0;var Va=sa%6;da[sa]&&(ja|=1<<Va);Va===5&&(X.push(df(ja)),ja=0,wa=!1)}wa&&X.push(df(ja));Y=X.join("")}var Wa=Y;M=""+ka+df(Wa.length)+Wa}else M="";var Db=a.om,Ub=a.sq;return I+M+(Db?""+
gf(11,3)+df(Db.length)+Db:"")+(Ub?""+gf(13,3)+df(Ub.length)+Ub:"")};var jf=function(){function a(b){return{toString:function(){return b}}}return{Qm:a("consent"),Qj:a("convert_case_to"),Rj:a("convert_false_to"),Sj:a("convert_null_to"),Tj:a("convert_true_to"),Uj:a("convert_undefined_to"),Qq:a("debug_mode_metadata"),Ua:a("function"),Ai:a("instance_name"),io:a("live_only"),jo:a("malware_disabled"),METADATA:a("metadata"),mo:a("original_activity_id"),lr:a("original_vendor_template_id"),kr:a("once_on_load"),lo:a("once_per_event"),pl:a("once_per_load"),nr:a("priority_override"),
ur:a("respected_consent_types"),Al:a("setup_tags"),kh:a("tag_id"),Il:a("teardown_tags")}}();var Ff;var Gf=[],Hf=[],If=[],Jf=[],Kf=[],Lf,Mf,Nf;function Of(a){Nf=Nf||a}
function Pf(){for(var a=data.resource||{},b=a.macros||[],c=0;c<b.length;c++)Gf.push(b[c]);for(var d=a.tags||[],e=0;e<d.length;e++)Jf.push(d[e]);for(var f=a.predicates||[],g=0;g<f.length;g++)If.push(f[g]);for(var h=a.rules||[],m=0;m<h.length;m++){for(var n=h[m],p={},q=0;q<n.length;q++){var r=n[q][0];p[r]=Array.prototype.slice.call(n[q],1);r!=="if"&&r!=="unless"||Qf(p[r])}Hf.push(p)}}
function Qf(a){}var Rf,Sf=[],Tf=[];function Uf(a,b){var c={};c[jf.Ua]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);return c}
function Vf(a,b,c){try{return Mf(Wf(a,b,c))}catch(d){JSON.stringify(a)}return 2}
var Wf=function(a,b,c){c=c||[];var d={},e;for(e in a)a.hasOwnProperty(e)&&(d[e]=Xf(a[e],b,c));return d},Xf=function(a,b,c){if(Array.isArray(a)){var d;switch(a[0]){case "function_id":return a[1];case "list":d=[];for(var e=1;e<a.length;e++)d.push(Xf(a[e],b,c));return d;case "macro":var f=a[1];if(c[f])return;var g=Gf[f];if(!g||b.isBlocked(g))return;c[f]=!0;var h=String(g[jf.Ai]);try{var m=Wf(g,b,c);m.vtp_gtmEventId=b.id;b.priorityId&&(m.vtp_gtmPriorityId=b.priorityId);d=Yf(m,{event:b,index:f,type:2,
name:h});Rf&&(d=Rf.Oo(d,m))}catch(A){b.logMacroError&&b.logMacroError(A,Number(f),h),d=!1}c[f]=!1;return d;case "map":d={};for(var n=1;n<a.length;n+=2)d[Xf(a[n],b,c)]=Xf(a[n+1],b,c);return d;case "template":d=[];for(var p=!1,q=1;q<a.length;q++){var r=Xf(a[q],b,c);Nf&&(p=p||Nf.Pp(r));d.push(r)}return Nf&&p?Nf.To(d):d.join("");case "escape":d=Xf(a[1],b,c);if(Nf&&Array.isArray(a[1])&&a[1][0]==="macro"&&Nf.Qp(a))return Nf.hq(d);d=String(d);for(var t=2;t<a.length;t++)qf[a[t]]&&(d=qf[a[t]](d));return d;
case "tag":var u=a[1];if(!Jf[u])throw Error("Unable to resolve tag reference "+u+".");return{Wl:a[2],index:u};case "zb":var v={arg0:a[2],arg1:a[3],ignore_case:a[5]};v[jf.Ua]=a[1];var w=Vf(v,b,c),y=!!a[4];return y||w!==2?y!==(w===1):null;default:throw Error("Attempting to expand unknown Value type: "+a[0]+".");}}return a},Yf=function(a,b){var c=a[jf.Ua],d=b&&b.event;if(!c)throw Error("Error: No function name given for function call.");var e=Lf[c],f=b&&b.type===2&&(d==null?void 0:d.reportMacroDiscrepancy)&&
e&&Sf.indexOf(c)!==-1,g={},h={},m;for(m in a)a.hasOwnProperty(m)&&Gb(m,"vtp_")&&(e&&(g[m]=a[m]),!e||f)&&(h[m.substring(4)]=a[m]);e&&d&&d.cachedModelValues&&(g.vtp_gtmCachedValues=d.cachedModelValues);if(b){if(b.name==null){var n;a:{var p=b.type,q=b.index;if(q==null)n="";else{var r;switch(p){case 2:r=Gf[q];break;case 1:r=Jf[q];break;default:n="";break a}var t=r&&r[jf.Ai];n=t?String(t):""}}b.name=n}e&&(g.vtp_gtmEntityIndex=b.index,g.vtp_gtmEntityName=b.name)}var u,v,w;if(f&&Tf.indexOf(c)===-1){Tf.push(c);
var y=Ab();u=e(g);var A=Ab()-y,C=Ab();v=Ff(c,h,b);w=A-(Ab()-C)}else if(e&&(u=e(g)),!e||f)v=Ff(c,h,b);f&&d&&(d.reportMacroDiscrepancy(d.id,c,void 0,!0),nd(u)?(Array.isArray(u)?Array.isArray(v):ld(u)?ld(v):typeof u==="function"?typeof v==="function":u===v)||d.reportMacroDiscrepancy(d.id,c):u!==v&&d.reportMacroDiscrepancy(d.id,c),w!==void 0&&d.reportMacroDiscrepancy(d.id,c,w));return e?u:v};var Zf=function(a,b,c){var d;d=Error.call(this,c);this.message=d.message;"stack"in d&&(this.stack=d.stack);this.permissionId=a;this.parameters=b;this.name="PermissionError"};va(Zf,Error);Zf.prototype.getMessage=function(){return this.message};function $f(a,b){if(Array.isArray(a)){Object.defineProperty(a,"context",{value:{line:b[0]}});for(var c=1;c<a.length;c++)$f(a[c],b[c])}};function ag(){return function(a,b){var c;var d=bg;a instanceof Na?(a.C=d,c=a):c=new Na(a,d);var e=c;b&&e.debugInfo.push(b);throw e;}}function bg(a){if(!a.length)return a;a.push({id:"main",line:0});for(var b=a.length-1;b>0;b--)nb(a[b].id)&&a.splice(b++,1);for(var c=a.length-1;c>0;c--)a[c].line=a[c-1].line;a.splice(0,1);return a};function cg(a){function b(r){for(var t=0;t<r.length;t++)d[r[t]]=!0}for(var c=[],d=[],e=dg(a),f=0;f<Hf.length;f++){var g=Hf[f],h=eg(g,e);if(h){for(var m=g.add||[],n=0;n<m.length;n++)c[m[n]]=!0;b(g.block||[])}else h===null&&b(g.block||[]);}for(var p=[],q=0;q<Jf.length;q++)c[q]&&!d[q]&&(p[q]=!0);return p}
function eg(a,b){for(var c=a["if"]||[],d=0;d<c.length;d++){var e=b(c[d]);if(e===0)return!1;if(e===2)return null}for(var f=a.unless||[],g=0;g<f.length;g++){var h=b(f[g]);if(h===2)return null;if(h===1)return!1}return!0}function dg(a){var b=[];return function(c){b[c]===void 0&&(b[c]=Vf(If[c],a));return b[c]}};function fg(a,b){b[jf.Qj]&&typeof a==="string"&&(a=b[jf.Qj]===1?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(jf.Sj)&&a===null&&(a=b[jf.Sj]);b.hasOwnProperty(jf.Uj)&&a===void 0&&(a=b[jf.Uj]);b.hasOwnProperty(jf.Tj)&&a===!0&&(a=b[jf.Tj]);b.hasOwnProperty(jf.Rj)&&a===!1&&(a=b[jf.Rj]);return a};var gg=function(){this.C={}},ig=function(a,b){var c=hg.C,d;(d=c.C)[a]!=null||(d[a]=[]);c.C[a].push(function(){return b.apply(null,ya(Ca.apply(0,arguments)))})};function jg(a,b,c,d){if(a)for(var e=0;e<a.length;e++){var f=void 0,g="A policy function denied the permission request";try{f=a[e](b,c,d),g+="."}catch(h){g=typeof h==="string"?g+(": "+h):h instanceof Error?g+(": "+h.message):g+"."}if(!f)throw new Zf(c,d,g);}}
function kg(a,b,c){return function(d){if(d){var e=a.C[d],f=a.C.all;if(e||f){var g=c.apply(void 0,[d].concat(ya(Ca.apply(1,arguments))));jg(e,b,d,g);jg(f,b,d,g)}}}};var og=function(){var a=data.permissions||{},b=lg.ctid,c=this;this.H={};this.C=new gg;var d={},e={},f=kg(this.C,b,function(g){return g&&d[g]?d[g].apply(void 0,[g].concat(ya(Ca.apply(1,arguments)))):{}});tb(a,function(g,h){function m(p){var q=Ca.apply(1,arguments);if(!n[p])throw mg(p,{},"The requested additional permission "+p+" is not configured.");f.apply(null,[p].concat(ya(q)))}var n={};tb(h,function(p,q){var r=ng(p,q);n[p]=r.assert;d[p]||(d[p]=r.T);r.Ol&&!e[p]&&(e[p]=r.Ol)});c.H[g]=function(p,
q){var r=n[p];if(!r)throw mg(p,{},"The requested permission "+p+" is not configured.");var t=Array.prototype.slice.call(arguments,0);r.apply(void 0,t);f.apply(void 0,t);var u=e[p];u&&u.apply(null,[m].concat(ya(t.slice(1))))}})},pg=function(a){return hg.H[a]||function(){}};
function ng(a,b){var c=Uf(a,b);c.vtp_permissionName=a;c.vtp_createPermissionError=mg;try{return Yf(c)}catch(d){return{assert:function(e){throw new Zf(e,{},"Permission "+e+" is unknown.");},T:function(){throw new Zf(a,{},"Permission "+a+" is unknown.");}}}}function mg(a,b,c){return new Zf(a,b,c)};var qg=!1;var rg={};rg.Hm=wb('');rg.cp=wb('');function wg(a,b){if(a==="")return b;var c=Number(a);return isNaN(c)?b:c};var xg=[];function yg(a){switch(a){case 1:return 0;case 216:return 15;case 222:return 18;case 38:return 12;case 219:return 9;case 220:return 10;case 53:return 1;case 54:return 2;case 52:return 6;case 203:return 16;case 75:return 3;case 103:return 13;case 197:return 14;case 114:return 11;case 116:return 4;case 221:return 17;case 135:return 8;case 136:return 5}}function zg(a,b){xg[a]=b;var c=yg(a);c!==void 0&&(Pa[c]=b)}function B(a){zg(a,!0)}B(39);
B(34);B(35);B(36);B(56);
B(145);B(153);
B(144);
B(120);B(5);B(111);
B(139);B(87);B(92);B(159);
B(132);B(20);B(72);
B(113);B(154);B(116);zg(23,!1),B(24);Qa[1]=wg('1',6E4);Qa[3]=wg('10',1);Qa[2]=wg('',50);B(29);
Ag(26,25);
B(37);B(9);
B(91);B(123);B(158);B(71);
B(136);B(127);
B(27);B(69);
B(135);B(95);B(38);
B(103);B(112);B(63);B(152);
B(101);
B(122);B(121);
B(134);
B(22);

B(19);
B(90);
B(114);B(59);
B(208);
B(175);B(185);
B(186);

B(192);
B(200);B(202);function E(a){return!!xg[a]}function Ag(a,b){for(var c=!1,d=!1,e=0;c===d;)if(c=((Math.random()*4294967296|0)&1)===0,d=((Math.random()*4294967296|0)&1)===0,e++,e>30)return;c?B(b):B(a)};var Cg={},Dg=(Cg.uaa=!0,Cg.uab=!0,Cg.uafvl=!0,Cg.uamb=!0,Cg.uam=!0,Cg.uap=!0,Cg.uapv=!0,Cg.uaw=!0,Cg);
var Lg=function(a,b){for(var c=0;c<b.length;c++){var d=a,e=b[c];if(!Jg.exec(e))throw Error("Invalid key wildcard");var f=e.indexOf(".*"),g=f!==-1&&f===e.length-2,h=g?e.slice(0,e.length-2):e,m;a:if(d.length===0)m=!1;else{for(var n=d.split("."),p=0;p<n.length;p++)if(!Kg.exec(n[p])){m=!1;break a}m=!0}if(!m||h.length>d.length||!g&&d.length!==e.length?0:g?Gb(d,h)&&(d===h||d.charAt(h.length)==="."):d===h)return!0}return!1},Kg=/^[a-z$_][\w-$]*$/i,Jg=/^(?:[a-z_$][a-z-_$0-9]*\.)*[a-z_$][a-z-_$0-9]*(?:\.\*)?$/i;
var Mg=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];function Ng(a,b){var c=String(a),d=String(b),e=c.length-d.length;return e>=0&&c.indexOf(d,e)===e}function Og(a,b){return String(a).split(",").indexOf(String(b))>=0}var Pg=new sb;function Qg(a,b,c){var d=c?"i":void 0;try{var e=String(b)+String(d),f=Pg.get(e);f||(f=new RegExp(b,d),Pg.set(e,f));return f.test(a)}catch(g){return!1}}function Rg(a,b){return String(a).indexOf(String(b))>=0}
function Sg(a,b){return String(a)===String(b)}function Tg(a,b){return Number(a)>=Number(b)}function Ug(a,b){return Number(a)<=Number(b)}function Vg(a,b){return Number(a)>Number(b)}function Wg(a,b){return Number(a)<Number(b)}function Xg(a,b){return Gb(String(a),String(b))};var dh=/^([a-z][a-z0-9]*):(!|\?)(\*|string|boolean|number|Fn|PixieMap|List|OpaqueValue)$/i,eh={Fn:"function",PixieMap:"Object",List:"Array"};
function fh(a,b){for(var c=["input:!*"],d=0;d<c.length;d++){var e=dh.exec(c[d]);if(!e)throw Error("Internal Error in "+a);var f=e[1],g=e[2]==="!",h=e[3],m=b[d];if(m==null){if(g)throw Error("Error in "+a+". Required argument "+f+" not supplied.");}else if(h!=="*"){var n=typeof m;m instanceof td?n="Fn":m instanceof pd?n="List":m instanceof Ya?n="PixieMap":m instanceof Ad?n="PixiePromise":m instanceof yd&&(n="OpaqueValue");if(n!==h)throw Error("Error in "+a+". Argument "+f+" has type "+((eh[n]||n)+", which does not match required type ")+
((eh[h]||h)+"."));}}}function F(a,b,c){for(var d=[],e=l(c),f=e.next();!f.done;f=e.next()){var g=f.value;g instanceof td?d.push("function"):g instanceof pd?d.push("Array"):g instanceof Ya?d.push("Object"):g instanceof Ad?d.push("Promise"):g instanceof yd?d.push("OpaqueValue"):d.push(typeof g)}return Error("Argument error in "+a+". Expected argument types ["+(b.join(",")+"], but received [")+(d.join(",")+"]."))}function gh(a){return a instanceof Ya}function hh(a){return gh(a)||a===null||ih(a)}
function jh(a){return a instanceof td}function kh(a){return jh(a)||a===null||ih(a)}function lh(a){return a instanceof pd}function mh(a){return a instanceof yd}function nh(a){return typeof a==="string"}function oh(a){return nh(a)||a===null||ih(a)}function ph(a){return typeof a==="boolean"}function qh(a){return ph(a)||ih(a)}function rh(a){return ph(a)||a===null||ih(a)}function sh(a){return typeof a==="number"}function ih(a){return a===void 0};function th(a){return""+a}
function uh(a,b){var c=[];return c};function vh(a,b){var c=new td(a,function(){for(var d=Array.prototype.slice.call(arguments,0),e=0;e<d.length;e++)d[e]=this.evaluate(d[e]);try{return b.apply(this,d)}catch(g){throw Oa(g);}});c.Wa();return c}
function wh(a,b){var c=new Ya,d;for(d in b)if(b.hasOwnProperty(d)){var e=b[d];lb(e)?c.set(d,vh(a+"_"+d,e)):ld(e)?c.set(d,wh(a+"_"+d,e)):(nb(e)||mb(e)||typeof e==="boolean")&&c.set(d,e)}c.Wa();return c};function xh(a,b){if(!nh(a))throw F(this.getName(),["string"],arguments);if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);var c={},d=new Ya;return d=wh("AssertApiSubject",
c)};function yh(a,b){if(!oh(b))throw F(this.getName(),["string","undefined"],arguments);if(a instanceof Ad)throw Error("Argument actual cannot have type Promise. Assertions on asynchronous code aren't supported.");var c={},d=new Ya;return d=wh("AssertThatSubject",c)};function zh(a){return function(){for(var b=Ca.apply(0,arguments),c=[],d=this.K,e=0;e<b.length;++e)c.push(Bd(b[e],d));return Cd(a.apply(null,c))}}function Ah(){for(var a=Math,b=Bh,c={},d=0;d<b.length;d++){var e=b[d];a.hasOwnProperty(e)&&(c[e]=zh(a[e].bind(a)))}return c};function Ch(a){return a!=null&&Gb(a,"__cvt_")};function Dh(a){var b;return b};function Eh(a){var b;if(!nh(a))throw F(this.getName(),["string"],arguments);try{b=decodeURIComponent(a)}catch(c){}return b};function Fh(a){try{return encodeURI(a)}catch(b){}};function Gh(a){try{return encodeURIComponent(String(a))}catch(b){}};function Lh(a){if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);};function Mh(a,b){if(!sh(a)||!sh(b))throw F(this.getName(),["number","number"],arguments);return qb(a,b)};function Nh(){return(new Date).getTime()};function Oh(a){if(a===null)return"null";if(a instanceof pd)return"array";if(a instanceof td)return"function";if(a instanceof yd){var b=a.getValue();if((b==null?void 0:b.constructor)===void 0||b.constructor.name===void 0){var c=String(b);return c.substring(8,c.length-1)}return String(b.constructor.name)}return typeof a};function Ph(a){function b(c){return function(d){try{return c(d)}catch(e){(qg||rg.Hm)&&a.call(this,e.message)}}}return{parse:b(function(c){return Cd(JSON.parse(c))}),stringify:b(function(c){return JSON.stringify(Bd(c))}),publicName:"JSON"}};function Qh(a){return vb(Bd(a,this.K))};function Rh(a){return Number(Bd(a,this.K))};function Sh(a){return a===null?"null":a===void 0?"undefined":a.toString()};function Th(a,b,c){var d=null,e=!1;return e?d:null};var Bh="floor ceil round max min abs pow sqrt".split(" ");function Uh(){var a={};return{qp:function(b){return a.hasOwnProperty(b)?a[b]:void 0},Dm:function(b,c){a[b]=c},reset:function(){a={}}}}function Vh(a,b){return function(){return td.prototype.invoke.apply(a,[b].concat(ya(Ca.apply(0,arguments))))}}
function Wh(a,b){if(!nh(a))throw F(this.getName(),["string","any"],arguments);}
function Xh(a,b){if(!nh(a)||!gh(b))throw F(this.getName(),["string","PixieMap"],arguments);};var Yh={};
Yh.keys=function(a){return new pd};
Yh.values=function(a){return new pd};
Yh.entries=function(a){return new pd};
Yh.freeze=function(a){return a};Yh.delete=function(a,b){return!1};function H(a,b){var c=Ca.apply(2,arguments),d=a.K.ub();if(!d)throw Error("Missing program state.");if(d.oq){try{d.Pl.apply(null,[b].concat(ya(c)))}catch(e){throw fb("TAGGING",21),e;}return}d.Pl.apply(null,[b].concat(ya(c)))};var $h=function(){this.H={};this.C={};this.N=!0;};$h.prototype.get=function(a,b){var c=this.contains(a)?this.H[a]:void 0;return c};$h.prototype.contains=function(a){return this.H.hasOwnProperty(a)};
$h.prototype.add=function(a,b,c){if(this.contains(a))throw Error("Attempting to add a function which already exists: "+a+".");if(this.C.hasOwnProperty(a))throw Error("Attempting to add an API with an existing private API name: "+a+".");this.H[a]=c?void 0:lb(b)?vh(a,b):wh(a,b)};function ai(a,b){var c=void 0;return c};function bi(){var a={};return a};var J={m:{Ka:"ad_personalization",U:"ad_storage",V:"ad_user_data",ja:"analytics_storage",hc:"region",da:"consent_updated",rg:"wait_for_update",Zm:"app_remove",bn:"app_store_refund",dn:"app_store_subscription_cancel",fn:"app_store_subscription_convert",gn:"app_store_subscription_renew",hn:"consent_update",Yj:"add_payment_info",Zj:"add_shipping_info",Pd:"add_to_cart",Qd:"remove_from_cart",bk:"view_cart",Xc:"begin_checkout",Rd:"select_item",kc:"view_item_list",Gc:"select_promotion",mc:"view_promotion",
lb:"purchase",Sd:"refund",yb:"view_item",dk:"add_to_wishlist",jn:"exception",kn:"first_open",ln:"first_visit",qa:"gtag.config",Eb:"gtag.get",mn:"in_app_purchase",Yc:"page_view",nn:"screen_view",on:"session_start",pn:"source_update",qn:"timing_complete",rn:"track_social",Td:"user_engagement",sn:"user_id_update",Le:"gclid_link_decoration_source",Me:"gclid_storage_source",nc:"gclgb",mb:"gclid",ek:"gclid_len",Ud:"gclgs",Vd:"gcllp",Wd:"gclst",ya:"ads_data_redaction",Ne:"gad_source",Oe:"gad_source_src",
Zc:"gclid_url",fk:"gclsrc",Pe:"gbraid",Xd:"wbraid",Ga:"allow_ad_personalization_signals",yg:"allow_custom_scripts",Qe:"allow_direct_google_requests",zg:"allow_display_features",Ag:"allow_enhanced_conversions",Ob:"allow_google_signals",nb:"allow_interest_groups",tn:"app_id",un:"app_installer_id",vn:"app_name",wn:"app_version",Pb:"auid",xn:"auto_detection_enabled",bd:"aw_remarketing",Ph:"aw_remarketing_only",Bg:"discount",Cg:"aw_feed_country",Dg:"aw_feed_language",sa:"items",Eg:"aw_merchant_id",gk:"aw_basket_type",
Re:"campaign_content",Se:"campaign_id",Te:"campaign_medium",Ue:"campaign_name",Ve:"campaign",We:"campaign_source",Xe:"campaign_term",Qb:"client_id",hk:"rnd",Qh:"consent_update_type",yn:"content_group",zn:"content_type",Rb:"conversion_cookie_prefix",Ye:"conversion_id",Ra:"conversion_linker",Rh:"conversion_linker_disabled",dd:"conversion_api",Fg:"cookie_deprecation",ob:"cookie_domain",pb:"cookie_expires",zb:"cookie_flags",ed:"cookie_name",Sb:"cookie_path",cb:"cookie_prefix",Hc:"cookie_update",fd:"country",
Sa:"currency",Sh:"customer_buyer_stage",Ze:"customer_lifetime_value",Th:"customer_loyalty",Uh:"customer_ltv_bucket",af:"custom_map",Vh:"gcldc",gd:"dclid",ik:"debug_mode",oa:"developer_id",An:"disable_merchant_reported_purchases",hd:"dc_custom_params",Bn:"dc_natural_search",jk:"dynamic_event_settings",kk:"affiliation",Gg:"checkout_option",Wh:"checkout_step",lk:"coupon",bf:"item_list_name",Xh:"list_name",Cn:"promotions",Yd:"shipping",Yh:"tax",Hg:"engagement_time_msec",Ig:"enhanced_client_id",Zh:"enhanced_conversions",
mk:"enhanced_conversions_automatic_settings",cf:"estimated_delivery_date",ai:"euid_logged_in_state",df:"event_callback",Dn:"event_category",Tb:"event_developer_id_string",En:"event_label",jd:"event",Jg:"event_settings",Kg:"event_timeout",Gn:"description",Hn:"fatal",In:"experiments",bi:"firebase_id",Zd:"first_party_collection",Lg:"_x_20",qc:"_x_19",nk:"fledge_drop_reason",pk:"fledge",qk:"flight_error_code",rk:"flight_error_message",sk:"fl_activity_category",tk:"fl_activity_group",di:"fl_advertiser_id",
uk:"fl_ar_dedupe",ef:"match_id",vk:"fl_random_number",wk:"tran",xk:"u",Mg:"gac_gclid",ae:"gac_wbraid",yk:"gac_wbraid_multiple_conversions",zk:"ga_restrict_domain",ei:"ga_temp_client_id",Jn:"ga_temp_ecid",kd:"gdpr_applies",Ak:"geo_granularity",Ic:"value_callback",rc:"value_key",sc:"google_analysis_params",be:"_google_ng",ce:"google_signals",Bk:"google_tld",ff:"gpp_sid",hf:"gpp_string",Ng:"groups",Ck:"gsa_experiment_id",jf:"gtag_event_feature_usage",Dk:"gtm_up",Jc:"iframe_state",kf:"ignore_referrer",
fi:"internal_traffic_results",Ek:"_is_fpm",Kc:"is_legacy_converted",Lc:"is_legacy_loaded",Og:"is_passthrough",ld:"_lps",Ab:"language",Pg:"legacy_developer_id_string",Ta:"linker",de:"accept_incoming",uc:"decorate_forms",la:"domains",Mc:"url_position",ee:"merchant_feed_label",fe:"merchant_feed_language",he:"merchant_id",Fk:"method",Kn:"name",Gk:"navigation_type",lf:"new_customer",Qg:"non_interaction",Ln:"optimize_id",Hk:"page_hostname",nf:"page_path",Ya:"page_referrer",Fb:"page_title",Ik:"passengers",
Jk:"phone_conversion_callback",Mn:"phone_conversion_country_code",Kk:"phone_conversion_css_class",Nn:"phone_conversion_ids",Lk:"phone_conversion_number",Mk:"phone_conversion_options",On:"_platinum_request_status",Pn:"_protected_audience_enabled",ie:"quantity",Rg:"redact_device_info",gi:"referral_exclusion_definition",Tq:"_request_start_time",Vb:"restricted_data_processing",Qn:"retoken",Rn:"sample_rate",hi:"screen_name",Nc:"screen_resolution",Nk:"_script_source",Sn:"search_term",qb:"send_page_view",
md:"send_to",nd:"server_container_url",pf:"session_duration",Sg:"session_engaged",ii:"session_engaged_time",Wb:"session_id",Tg:"session_number",qf:"_shared_user_id",je:"delivery_postal_code",Uq:"_tag_firing_delay",Vq:"_tag_firing_time",Wq:"temporary_client_id",ji:"_timezone",ki:"topmost_url",Tn:"tracking_id",li:"traffic_type",La:"transaction_id",vc:"transport_url",Ok:"trip_type",pd:"update",Gb:"url_passthrough",Pk:"uptgs",rf:"_user_agent_architecture",tf:"_user_agent_bitness",uf:"_user_agent_full_version_list",
vf:"_user_agent_mobile",wf:"_user_agent_model",xf:"_user_agent_platform",yf:"_user_agent_platform_version",zf:"_user_agent_wow64",eb:"user_data",mi:"user_data_auto_latency",ni:"user_data_auto_meta",oi:"user_data_auto_multi",ri:"user_data_auto_selectors",si:"user_data_auto_status",wc:"user_data_mode",Ug:"user_data_settings",Ma:"user_id",Xb:"user_properties",Qk:"_user_region",Af:"us_privacy_string",za:"value",Rk:"wbraid_multiple_conversions",sd:"_fpm_parameters",yi:"_host_name",al:"_in_page_command",
bl:"_ip_override",kl:"_is_passthrough_cid",xc:"non_personalized_ads",Ji:"_sst_parameters",oc:"conversion_label",Ca:"page_location",Ub:"global_developer_id_string",od:"tc_privacy_string"}};var ci={},di=(ci[J.m.da]="gcu",ci[J.m.nc]="gclgb",ci[J.m.mb]="gclaw",ci[J.m.ek]="gclid_len",ci[J.m.Ud]="gclgs",ci[J.m.Vd]="gcllp",ci[J.m.Wd]="gclst",ci[J.m.Pb]="auid",ci[J.m.Bg]="dscnt",ci[J.m.Cg]="fcntr",ci[J.m.Dg]="flng",ci[J.m.Eg]="mid",ci[J.m.gk]="bttype",ci[J.m.Qb]="gacid",ci[J.m.oc]="label",ci[J.m.dd]="capi",ci[J.m.Fg]="pscdl",ci[J.m.Sa]="currency_code",ci[J.m.Sh]="clobs",ci[J.m.Ze]="vdltv",ci[J.m.Th]="clolo",ci[J.m.Uh]="clolb",ci[J.m.ik]="_dbg",ci[J.m.cf]="oedeld",ci[J.m.Tb]="edid",ci[J.m.nk]=
"fdr",ci[J.m.pk]="fledge",ci[J.m.Mg]="gac",ci[J.m.ae]="gacgb",ci[J.m.yk]="gacmcov",ci[J.m.kd]="gdpr",ci[J.m.Ub]="gdid",ci[J.m.be]="_ng",ci[J.m.ff]="gpp_sid",ci[J.m.hf]="gpp",ci[J.m.Ck]="gsaexp",ci[J.m.jf]="_tu",ci[J.m.Jc]="frm",ci[J.m.Og]="gtm_up",ci[J.m.ld]="lps",ci[J.m.Pg]="did",ci[J.m.ee]="fcntr",ci[J.m.fe]="flng",ci[J.m.he]="mid",ci[J.m.lf]=void 0,ci[J.m.Fb]="tiba",ci[J.m.Vb]="rdp",ci[J.m.Wb]="ecsid",ci[J.m.qf]="ga_uid",ci[J.m.je]="delopc",ci[J.m.od]="gdpr_consent",ci[J.m.La]="oid",ci[J.m.Pk]=
"uptgs",ci[J.m.rf]="uaa",ci[J.m.tf]="uab",ci[J.m.uf]="uafvl",ci[J.m.vf]="uamb",ci[J.m.wf]="uam",ci[J.m.xf]="uap",ci[J.m.yf]="uapv",ci[J.m.zf]="uaw",ci[J.m.mi]="ec_lat",ci[J.m.ni]="ec_meta",ci[J.m.oi]="ec_m",ci[J.m.ri]="ec_sel",ci[J.m.si]="ec_s",ci[J.m.wc]="ec_mode",ci[J.m.Ma]="userId",ci[J.m.Af]="us_privacy",ci[J.m.za]="value",ci[J.m.Rk]="mcov",ci[J.m.yi]="hn",ci[J.m.al]="gtm_ee",ci[J.m.xc]="npa",ci[J.m.Ye]=null,ci[J.m.Nc]=null,ci[J.m.Ab]=null,ci[J.m.sa]=null,ci[J.m.Ca]=null,ci[J.m.Ya]=null,ci[J.m.ki]=
null,ci[J.m.sd]=null,ci[J.m.Le]=null,ci[J.m.Me]=null,ci[J.m.sc]=null,ci);function ei(a,b){if(a){var c=a.split("x");c.length===2&&(fi(b,"u_w",c[0]),fi(b,"u_h",c[1]))}}
function gi(a){var b=hi;b=b===void 0?ii:b;var c;var d=b;if(a&&a.length){for(var e=[],f=0;f<a.length;++f){var g=a[f];g&&e.push({item_id:d(g),quantity:g.quantity,value:g.price,start_date:g.start_date,end_date:g.end_date})}c=e}else c=[];var h;var m=c;if(m){for(var n=[],p=0;p<m.length;p++){var q=m[p],r=[];q&&(r.push(ji(q.value)),r.push(ji(q.quantity)),r.push(ji(q.item_id)),r.push(ji(q.start_date)),r.push(ji(q.end_date)),n.push("("+r.join("*")+")"))}h=n.length>0?n.join(""):""}else h="";return h}
function ii(a){return ki(a.item_id,a.id,a.item_name)}function ki(){for(var a=l(Ca.apply(0,arguments)),b=a.next();!b.done;b=a.next()){var c=b.value;if(c!==null&&c!==void 0)return c}}function li(a){if(a&&a.length){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&d.estimated_delivery_date?b.push(""+d.estimated_delivery_date):b.push("")}return b.join(",")}}function fi(a,b,c){c===void 0||c===null||c===""&&!Dg[b]||(a[b]=c)}function ji(a){return typeof a!=="number"&&typeof a!=="string"?"":a.toString()};var mi={},ni=function(){for(var a=!1,b=!1,c=0;a===b;)if(a=qb(0,1)===0,b=qb(0,1)===0,c++,c>30)return;return a},pi={uq:oi};function qi(a,b){var c=mi[b],d=c.studyId,e=c.experimentId,f=c.probability;if(!(a.studies||{})[d]){var g=a.studies||{};g[d]=!0;a.studies=g;mi[b].active||(mi[b].probability>.5?ri(a,e):f<=0||f>1||pi.uq(a,b))}}
function oi(a,b){var c=mi[b],d=c.controlId2;if(!(qb(0,9999)<c.probability*(c.controlId2&&c.probability<=.25?4:2)*1E4))return a;si(a,{experimentId:c.experimentId,controlId:c.controlId,controlId2:c.controlId2&&c.probability<=.25?d:void 0,experimentCallback:function(){}});return a}function ri(a,b){var c=a.exp||{};c[b]=!0;a.exp=c}
function si(a,b){var c=b.experimentId,d=b.controlId,e=b.controlId2,f=b.experimentCallback;if((a.exp||{})[c])f();else if(!((a.exp||{})[d]||e&&(a.exp||{})[e])){var g=ni()?0:1;e&&(g|=(ni()?0:1)<<1);g===0?(ri(a,c),f()):g===1?ri(a,d):g===2&&ri(a,e)}};var K={J:{Kj:"call_conversion",W:"conversion",Un:"floodlight",Cf:"ga_conversion",Fi:"landing_page",Ha:"page_view",na:"remarketing",jb:"user_data_lead",Na:"user_data_web"}};function vi(a){return wi?z.querySelectorAll(a):null}
function xi(a,b){if(!wi)return null;if(Element.prototype.closest)try{return a.closest(b)}catch(e){return null}var c=Element.prototype.matches||Element.prototype.webkitMatchesSelector||Element.prototype.mozMatchesSelector||Element.prototype.msMatchesSelector||Element.prototype.oMatchesSelector,d=a;if(!z.documentElement.contains(d))return null;do{try{if(c.call(d,b))return d}catch(e){break}d=d.parentElement||d.parentNode}while(d!==null&&d.nodeType===1);return null}var yi=!1;
if(z.querySelectorAll)try{var zi=z.querySelectorAll(":root");zi&&zi.length==1&&zi[0]==z.documentElement&&(yi=!0)}catch(a){}var wi=yi;function Ai(a){switch(a){case 0:break;case 9:return"e4";case 6:return"e5";case 14:return"e6";default:return"e7"}};function Bi(){this.blockSize=-1};function Ci(a,b){this.blockSize=-1;this.blockSize=64;this.N=Da.Uint8Array?new Uint8Array(this.blockSize):Array(this.blockSize);this.P=this.H=0;this.C=[];this.fa=a;this.R=b;this.ma=Da.Int32Array?new Int32Array(64):Array(64);Di===void 0&&(Da.Int32Array?Di=new Int32Array(Ei):Di=Ei);this.reset()}Ea(Ci,Bi);for(var Fi=[],Gi=0;Gi<63;Gi++)Fi[Gi]=0;var Hi=[].concat(128,Fi);
Ci.prototype.reset=function(){this.P=this.H=0;var a;if(Da.Int32Array)a=new Int32Array(this.R);else{var b=this.R,c=b.length;if(c>0){for(var d=Array(c),e=0;e<c;e++)d[e]=b[e];a=d}else a=[]}this.C=a};
var Ii=function(a){for(var b=a.N,c=a.ma,d=0,e=0;e<b.length;)c[d++]=b[e]<<24|b[e+1]<<16|b[e+2]<<8|b[e+3],e=d*4;for(var f=16;f<64;f++){var g=c[f-15]|0,h=c[f-2]|0;c[f]=((c[f-16]|0)+((g>>>7|g<<25)^(g>>>18|g<<14)^g>>>3)|0)+((c[f-7]|0)+((h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10)|0)|0}for(var m=a.C[0]|0,n=a.C[1]|0,p=a.C[2]|0,q=a.C[3]|0,r=a.C[4]|0,t=a.C[5]|0,u=a.C[6]|0,v=a.C[7]|0,w=0;w<64;w++){var y=((m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10))+(m&n^m&p^n&p)|0,A=(v+((r>>>6|r<<26)^(r>>>11|r<<21)^(r>>>25|r<<7))|
0)+(((r&t^~r&u)+(Di[w]|0)|0)+(c[w]|0)|0)|0;v=u;u=t;t=r;r=q+A|0;q=p;p=n;n=m;m=A+y|0}a.C[0]=a.C[0]+m|0;a.C[1]=a.C[1]+n|0;a.C[2]=a.C[2]+p|0;a.C[3]=a.C[3]+q|0;a.C[4]=a.C[4]+r|0;a.C[5]=a.C[5]+t|0;a.C[6]=a.C[6]+u|0;a.C[7]=a.C[7]+v|0};
Ci.prototype.update=function(a,b){b===void 0&&(b=a.length);var c=0,d=this.H;if(typeof a==="string")for(;c<b;)this.N[d++]=a.charCodeAt(c++),d==this.blockSize&&(Ii(this),d=0);else{var e,f=typeof a;e=f!="object"?f:a?Array.isArray(a)?"array":f:"null";if(e=="array"||e=="object"&&typeof a.length=="number")for(;c<b;){var g=a[c++];if(!("number"==typeof g&&0<=g&&255>=g&&g==(g|0)))throw Error("message must be a byte array");this.N[d++]=g;d==this.blockSize&&(Ii(this),d=0)}else throw Error("message must be string or array");
}this.H=d;this.P+=b};Ci.prototype.digest=function(){var a=[],b=this.P*8;this.H<56?this.update(Hi,56-this.H):this.update(Hi,this.blockSize-(this.H-56));for(var c=63;c>=56;c--)this.N[c]=b&255,b/=256;Ii(this);for(var d=0,e=0;e<this.fa;e++)for(var f=24;f>=0;f-=8)a[d++]=this.C[e]>>f&255;return a};
var Ei=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,
4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298],Di;function Ji(){Ci.call(this,8,Ki)}Ea(Ji,Ci);var Ki=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225];var Li=/^[0-9A-Fa-f]{64}$/;function Mi(a){try{return(new TextEncoder).encode(a)}catch(e){for(var b=[],c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b.push(d):d<2048?b.push(192|d>>6,128|d&63):d<55296||d>=57344?b.push(224|d>>12,128|d>>6&63,128|d&63):(d=65536+((d&1023)<<10|a.charCodeAt(++c)&1023),b.push(240|d>>18,128|d>>12&63,128|d>>6&63,128|d&63))}return new Uint8Array(b)}}
function Ni(a){var b=x;if(a===""||a==="e0")return Promise.resolve(a);var c;if((c=b.crypto)==null?0:c.subtle){if(Li.test(a))return Promise.resolve(a);try{var d=Mi(a);return b.crypto.subtle.digest("SHA-256",d).then(function(e){return Oi(e,b)}).catch(function(){return"e2"})}catch(e){return Promise.resolve("e2")}}else return Promise.resolve("e1")}
function Oi(a,b){var c=Array.from(new Uint8Array(a)).map(function(d){return String.fromCharCode(d)}).join("");return b.btoa(c).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")};var Pi=[],Qi;function Ri(a){Qi?Qi(a):Pi.push(a)}function Si(a,b){if(!E(190))return b;var c,d=!1;d=d===void 0?!1:d;var e,f;c=((e=data)==null?0:(f=e.blob)==null?0:f.hasOwnProperty(a))?!!data.blob[a]:d;return c!==b?(Ri(a),b):c}function Ti(a,b){if(!E(190))return b;var c=Ui(a,"");return c!==b?(Ri(a),b):c}function Ui(a,b){b=b===void 0?"":b;var c,d;return((c=data)==null?0:(d=c.blob)==null?0:d.hasOwnProperty(a))?String(data.blob[a]):b}
function Vi(a,b){if(!E(190))return b;var c,d,e;c=((d=data)==null?0:(e=d.blob)==null?0:e.hasOwnProperty(a))?Number(data.blob[a]):0;return c===b||isNaN(c)&&isNaN(b)?c:(Ri(a),b)}function Wi(){Qi=Xi;for(var a=l(Pi),b=a.next();!b.done;b=a.next())Qi(b.value);Pi.length=0};var Yi={Nm:'5000',Om:'5000',Xm:'512',Ym:'1000',Yn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',Zn:'US-CO~US-CT~US-MT~US-NE~US-NH~US-TX~US-MN~US-NJ~US-MD',vo:Ti(44,'101509157~103116026~103200004~103233427~104684208~104684211')},Zi={Do:Number(Yi.Nm)||-1,Eo:Number(Yi.Om)||-1,Zo:Number(Yi.Xm)||0,bp:Number(Yi.Ym)||0,vp:Yi.Yn.split("~"),
wp:Yi.Zn.split("~"),Mq:Yi.vo};ma(Object,"assign").call(Object,{},Zi);function L(a){fb("GTM",a)};var Kj={},Lj=(Kj[J.m.nb]=1,Kj[J.m.nd]=2,Kj[J.m.vc]=2,Kj[J.m.ya]=3,Kj[J.m.Ze]=4,Kj[J.m.yg]=5,Kj[J.m.Hc]=6,Kj[J.m.cb]=6,Kj[J.m.ob]=6,Kj[J.m.ed]=6,Kj[J.m.Sb]=6,Kj[J.m.zb]=6,Kj[J.m.pb]=7,Kj[J.m.Vb]=9,Kj[J.m.zg]=10,Kj[J.m.Ob]=11,Kj),Mj={},Nj=(Mj.unknown=13,Mj.standard=14,Mj.unique=15,Mj.per_session=16,Mj.transactions=17,Mj.items_sold=18,Mj);var hb=[];function Oj(a,b){b=b===void 0?!1:b;for(var c=Object.keys(a),d=l(Object.keys(Lj)),e=d.next();!e.done;e=d.next()){var f=e.value;if(c.includes(f)){var g=Lj[f],h=b;h=h===void 0?!1:h;fb("GTAG_EVENT_FEATURE_CHANNEL",g);h&&(hb[g]=!0)}}};var Pj=function(){this.C=new Set;this.H=new Set},Rj=function(a){var b=Qj.R;a=a===void 0?[]:a;var c=[].concat(ya(b.C)).concat([].concat(ya(b.H))).concat(a);c.sort(function(d,e){return d-e});return c},Sj=function(){var a=[].concat(ya(Qj.R.C));a.sort(function(b,c){return b-c});return a},Tj=function(){var a=Qj.R,b=Zi.Mq;a.C=new Set;if(b!=="")for(var c=l(b.split("~")),d=c.next();!d.done;d=c.next()){var e=Number(d.value);isNaN(e)||a.C.add(e)}};var Uj={},Vj=Ti(14,"57g1"),Wj=Vi(15,Number("0")),Xj=Ti(19,"dataLayer");Ti(20,"");Ti(16,"ChEI8KfywwYQtZnzsLij46XPARIlABaUpshM2lqBgdYmijseU4m1M0jqGAv/sLK7pMpDWuK2U54mQRoCyTc\x3d");var Yj={__cl:1,__ecl:1,__ehl:1,__evl:1,__fal:1,__fil:1,__fsl:1,__hl:1,__jel:1,__lcl:1,__sdl:1,__tl:1,__ytl:1},Zj={__paused:1,__tg:1},ak;for(ak in Yj)Yj.hasOwnProperty(ak)&&(Zj[ak]=1);var bk=Si(11,wb("")),ck=!1;
function dk(){var a=!1;return a}var ek=E(218)?Si(45,dk()):dk(),fk,gk=!1;fk=gk;Uj.wg=Ti(3,"www.googletagmanager.com");var hk=""+Uj.wg+(ek?"/gtag/js":"/gtm.js"),ik=null,jk=null,kk={},lk={};Uj.Rm=Si(2,wb(""));var mk="";
Uj.Ki=mk;var Qj=new function(){this.R=new Pj;this.C=this.N=!1;this.H=0;this.Da=this.Va=this.rb=this.P="";this.fa=this.ma=!1};function nk(){var a;a=a===void 0?[]:a;return Rj(a).join("~")}function ok(){var a=Qj.P.length;return Qj.P[a-1]==="/"?Qj.P.substring(0,a-1):Qj.P}function pk(){return Qj.C?E(84)?Qj.H===0:Qj.H!==1:!1}function qk(a){for(var b={},c=l(a.split("|")),d=c.next();!d.done;d=c.next())b[d.value]=!0;return b};var rk=new sb,sk={},tk={},wk={name:Xj,set:function(a,b){md(Ib(a,b),sk);uk()},get:function(a){return vk(a,2)},reset:function(){rk=new sb;sk={};uk()}};function vk(a,b){return b!=2?rk.get(a):xk(a)}function xk(a,b){var c=a.split(".");b=b||[];for(var d=sk,e=0;e<c.length;e++){if(d===null)return!1;if(d===void 0)break;d=d[c[e]];if(b.indexOf(d)!==-1)return}return d}function yk(a,b){tk.hasOwnProperty(a)||(rk.set(a,b),md(Ib(a,b),sk),uk())}
function zk(){for(var a=["gtm.allowlist","gtm.blocklist","gtm.whitelist","gtm.blacklist","tagTypeBlacklist"],b=0;b<a.length;b++){var c=a[b],d=vk(c,1);if(Array.isArray(d)||ld(d))d=md(d,null);tk[c]=d}}function uk(a){tb(tk,function(b,c){rk.set(b,c);md(Ib(b),sk);md(Ib(b,c),sk);a&&delete tk[b]})}function Ak(a,b){var c,d=(b===void 0?2:b)!==1?xk(a):rk.get(a);jd(d)==="array"||jd(d)==="object"?c=md(d,null):c=d;return c};var Kk=/:[0-9]+$/,Lk=/^\d+\.fls\.doubleclick\.net$/;function Mk(a,b,c,d){var e=Nk(a,!!d,b),f,g;return c?(g=e[b])!=null?g:[]:(f=e[b])==null?void 0:f[0]}function Nk(a,b,c){for(var d={},e=l(a.split("&")),f=e.next();!f.done;f=e.next()){var g=l(f.value.split("=")),h=g.next().value,m=xa(g),n=decodeURIComponent(h.replace(/\+/g," "));if(c===void 0||n===c){var p=m.join("=");d[n]||(d[n]=[]);d[n].push(b?p:decodeURIComponent(p.replace(/\+/g," ")))}}return d}
function Ok(a){try{return decodeURIComponent(a)}catch(b){}}function Pk(a,b,c,d,e){b&&(b=String(b).toLowerCase());if(b==="protocol"||b==="port")a.protocol=Qk(a.protocol)||Qk(x.location.protocol);b==="port"?a.port=String(Number(a.hostname?a.port:x.location.port)||(a.protocol==="http"?80:a.protocol==="https"?443:"")):b==="host"&&(a.hostname=(a.hostname||x.location.hostname).replace(Kk,"").toLowerCase());return Rk(a,b,c,d,e)}
function Rk(a,b,c,d,e){var f,g=Qk(a.protocol);b&&(b=String(b).toLowerCase());switch(b){case "url_no_fragment":f=Sk(a);break;case "protocol":f=g;break;case "host":f=a.hostname.replace(Kk,"").toLowerCase();if(c){var h=/^www\d*\./.exec(f);h&&h[0]&&(f=f.substring(h[0].length))}break;case "port":f=String(Number(a.port)||(g==="http"?80:g==="https"?443:""));break;case "path":a.pathname||a.hostname||fb("TAGGING",1);f=a.pathname.substring(0,1)==="/"?a.pathname:"/"+a.pathname;var m=f.split("/");(d||[]).indexOf(m[m.length-
1])>=0&&(m[m.length-1]="");f=m.join("/");break;case "query":f=a.search.replace("?","");e&&(f=Mk(f,e,!1));break;case "extension":var n=a.pathname.split(".");f=n.length>1?n[n.length-1]:"";f=f.split("/")[0];break;case "fragment":f=a.hash.replace("#","");break;default:f=a&&a.href}return f}function Qk(a){return a?a.replace(":","").toLowerCase():""}function Sk(a){var b="";if(a&&a.href){var c=a.href.indexOf("#");b=c<0?a.href:a.href.substring(0,c)}return b}var Tk={},Uk=0;
function Vk(a){var b=Tk[a];if(!b){var c=z.createElement("a");a&&(c.href=a);var d=c.pathname;d[0]!=="/"&&(a||fb("TAGGING",1),d="/"+d);var e=c.hostname.replace(Kk,"");b={href:c.href,protocol:c.protocol,host:c.host,hostname:e,pathname:d,search:c.search,hash:c.hash,port:c.port};Uk<5&&(Tk[a]=b,Uk++)}return b}function Wk(a,b,c){var d=Vk(a);return Nb(b,d,c)}
function Xk(a){var b=Vk(x.location.href),c=Pk(b,"host",!1);if(c&&c.match(Lk)){var d=Pk(b,"path");if(d){var e=d.split(a+"=");if(e.length>1)return e[1].split(";")[0].split("?")[0]}}};var Yk={"https://www.google.com":"/g","https://www.googleadservices.com":"/as","https://pagead2.googlesyndication.com":"/gs"},Zk=["/as/d/ccm/conversion","/g/d/ccm/conversion","/gs/ccm/conversion","/d/ccm/form-data"];function $k(a,b){if(a){var c=""+a;c.indexOf("http://")!==0&&c.indexOf("https://")!==0&&(c="https://"+c);c[c.length-1]==="/"&&(c=c.substring(0,c.length-1));return Vk(""+c+b).href}}function al(a,b){if(pk()||Qj.N)return $k(a,b)}
function bl(){return!!Uj.Ki&&Uj.Ki.split("@@").join("")!=="SGTM_TOKEN"}function cl(a){for(var b=l([J.m.nd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=O(a,c.value);if(d)return d}}function dl(a,b,c){c=c===void 0?"":c;if(!pk())return a;var d=b?Yk[a]||"":"";d==="/gs"&&(c="");return""+ok()+d+c}function el(a){if(!pk())return a;for(var b=l(Zk),c=b.next();!c.done;c=b.next())if(Gb(a,""+ok()+c.value))return a+"&_uip="+encodeURIComponent("::");return a};function fl(a){var b=String(a[jf.Ua]||"").replace(/_/g,"");return Gb(b,"cvt")?"cvt":b}var gl=x.location.search.indexOf("?gtm_latency=")>=0||x.location.search.indexOf("&gtm_latency=")>=0;var hl={qq:Vi(27,Number("0.005000")),Xo:Vi(42,Number("0.010000"))},il=Math.random(),jl=gl||il<Number(hl.qq),kl=gl||il>=1-Number(hl.Xo);var ll=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d},ml=function(a){var b=a;return function(){if(b){var c=b;b=null;c()}}};var nl,ol;a:{for(var pl=["CLOSURE_FLAGS"],ql=Da,rl=0;rl<pl.length;rl++)if(ql=ql[pl[rl]],ql==null){ol=null;break a}ol=ql}var sl=ol&&ol[610401301];nl=sl!=null?sl:!1;function tl(){var a=Da.navigator;if(a){var b=a.userAgent;if(b)return b}return""}var ul,vl=Da.navigator;ul=vl?vl.userAgentData||null:null;function wl(a){if(!nl||!ul)return!1;for(var b=0;b<ul.brands.length;b++){var c=ul.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function xl(a){return tl().indexOf(a)!=-1};function yl(){return nl?!!ul&&ul.brands.length>0:!1}function zl(){return yl()?!1:xl("Opera")}function Al(){return xl("Firefox")||xl("FxiOS")}function Bl(){return yl()?wl("Chromium"):(xl("Chrome")||xl("CriOS"))&&!(yl()?0:xl("Edge"))||xl("Silk")};var Cl=function(a){Cl[" "](a);return a};Cl[" "]=function(){};var Dl=function(a){return decodeURIComponent(a.replace(/\+/g," "))};function El(){return nl?!!ul&&!!ul.platform:!1}function Fl(){return xl("iPhone")&&!xl("iPod")&&!xl("iPad")}function Gl(){Fl()||xl("iPad")||xl("iPod")};zl();yl()||xl("Trident")||xl("MSIE");xl("Edge");!xl("Gecko")||tl().toLowerCase().indexOf("webkit")!=-1&&!xl("Edge")||xl("Trident")||xl("MSIE")||xl("Edge");tl().toLowerCase().indexOf("webkit")!=-1&&!xl("Edge")&&xl("Mobile");El()||xl("Macintosh");El()||xl("Windows");(El()?ul.platform==="Linux":xl("Linux"))||El()||xl("CrOS");El()||xl("Android");Fl();xl("iPad");xl("iPod");Gl();tl().toLowerCase().indexOf("kaios");var Hl=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{Cl(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},Il=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Jl=function(a,b){for(var c=a,d=0;d<50;++d){var e;try{e=!(!c.frames||!c.frames[b])}catch(h){e=!1}if(e)return c;var f;a:{try{var g=c.parent;if(g&&g!=c){f=g;break a}}catch(h){}f=null}if(!(c=f))break}return null},Kl=function(a){var b=x;if(b.top==b)return 0;if(a===void 0?0:a){var c=
b.location.ancestorOrigins;if(c)return c[c.length-1]==b.location.origin?1:2}return Hl(b.top)?1:2},Ll=function(a){a=a===void 0?document:a;return a.createElement("img")},Ml=function(){for(var a=x,b=a;a&&a!=a.parent;)a=a.parent,Hl(a)&&(b=a);return b};function Nl(a){var b;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Ol(){return Nl("join-ad-interest-group")&&lb(uc.joinAdInterestGroup)}
function Pl(a,b,c){var d=Qa[3]===void 0?1:Qa[3],e='iframe[data-tagging-id="'+b+'"]',f=[];try{if(d===1){var g=z.querySelector(e);g&&(f=[g])}else f=Array.from(z.querySelectorAll(e))}catch(r){}var h;a:{try{h=z.querySelectorAll('iframe[allow="join-ad-interest-group"][data-tagging-id*="-"]');break a}catch(r){}h=void 0}var m=h,n=((m==null?void 0:m.length)||0)>=(Qa[2]===void 0?50:Qa[2]),p;if(p=f.length>=1){var q=Number(f[f.length-1].dataset.loadTime);q!==void 0&&Ab()-q<(Qa[1]===void 0?6E4:Qa[1])?(fb("TAGGING",
9),p=!0):p=!1}if(p)return!1;if(d===1)if(f.length>=1)Ql(f[0]);else{if(n)return fb("TAGGING",10),!1}else f.length>=d?Ql(f[0]):n&&Ql(m[0]);Ic(a,c,{allow:"join-ad-interest-group"},{taggingId:b,loadTime:Ab()});return!0}function Ql(a){try{a.parentNode.removeChild(a)}catch(b){}};function Rl(a,b,c){var d,e=a.GooglebQhCsO;e||(e={},a.GooglebQhCsO=e);d=e;if(d[b])return!1;d[b]=[];d[b][0]=c;return!0};var Sl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e<128?b[c++]=e:(e<2048?b[c++]=e>>6|192:((e&64512)==55296&&d+1<a.length&&(a.charCodeAt(d+1)&64512)==56320?(e=65536+((e&1023)<<10)+(a.charCodeAt(++d)&1023),b[c++]=e>>18|240,b[c++]=e>>12&63|128):b[c++]=e>>12|224,b[c++]=e>>6&63|128),b[c++]=e&63|128)}return b};Al();Fl()||xl("iPod");xl("iPad");!xl("Android")||Bl()||Al()||zl()||xl("Silk");Bl();!xl("Safari")||Bl()||(yl()?0:xl("Coast"))||zl()||(yl()?0:xl("Edge"))||(yl()?wl("Microsoft Edge"):xl("Edg/"))||(yl()?wl("Opera"):xl("OPR"))||Al()||xl("Silk")||xl("Android")||Gl();var Tl={},Ul=null,Vl=function(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);e>255&&(b[c++]=e&255,e>>=8);b[c++]=e}var f=4;f===void 0&&(f=0);if(!Ul){Ul={};for(var g="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),h=["+/=","+/","-_=","-_.","-_"],m=0;m<5;m++){var n=g.concat(h[m].split(""));Tl[m]=n;for(var p=0;p<n.length;p++){var q=n[p];Ul[q]===void 0&&(Ul[q]=p)}}}for(var r=Tl[f],t=Array(Math.floor(b.length/3)),u=r[64]||"",v=0,w=0;v<b.length-2;v+=3){var y=b[v],
A=b[v+1],C=b[v+2],D=r[y>>2],G=r[(y&3)<<4|A>>4],I=r[(A&15)<<2|C>>6],M=r[C&63];t[w++]=""+D+G+I+M}var T=0,da=u;switch(b.length-v){case 2:T=b[v+1],da=r[(T&15)<<2]||u;case 1:var N=b[v];t[w]=""+r[N>>2]+r[(N&3)<<4|T>>4]+da+u}return t.join("")};var Wl=function(a,b,c,d){for(var e=b,f=c.length;(e=a.indexOf(c,e))>=0&&e<d;){var g=a.charCodeAt(e-1);if(g==38||g==63){var h=a.charCodeAt(e+f);if(!h||h==61||h==38||h==35)return e}e+=f+1}return-1},Xl=/#|$/,Yl=function(a,b){var c=a.search(Xl),d=Wl(a,0,b,c);if(d<0)return null;var e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return Dl(a.slice(d,e!==-1?e:0))},Zl=/[?&]($|#)/,$l=function(a,b,c){for(var d,e=a.search(Xl),f=0,g,h=[];(g=Wl(a,f,b,e))>=0;)h.push(a.substring(f,g)),f=Math.min(a.indexOf("&",g)+
1||e,e);h.push(a.slice(f));d=h.join("").replace(Zl,"$1");var m,n=c!=null?"="+encodeURIComponent(String(c)):"";var p=b+n;if(p){var q,r=d.indexOf("#");r<0&&(r=d.length);var t=d.indexOf("?"),u;t<0||t>r?(t=r,u=""):u=d.substring(t+1,r);q=[d.slice(0,t),u,d.slice(r)];var v=q[1];q[1]=p?v?v+"&"+p:p:v;m=q[0]+(q[1]?"?"+q[1]:"")+q[2]}else m=d;return m};function am(a,b,c,d,e,f,g){var h=Yl(c,"fmt");if(d){var m=Yl(c,"random"),n=Yl(c,"label")||"";if(!m)return!1;var p=Vl(Dl(n)+":"+Dl(m));if(!Rl(a,p,d))return!1}h&&Number(h)!==4&&(c=$l(c,"rfmt",h));var q=$l(c,"fmt",4),r=b.getElementsByTagName("script")[0].parentElement;g==null||bm(g);Gc(q,function(){g==null||cm(g);a.google_noFurtherRedirects&&d&&(a.google_noFurtherRedirects=null,d())},function(){g==null||cm(g);e==null||e()},f,r||void 0);return!0};var dm={},em=(dm[1]={},dm[2]={},dm[3]={},dm[4]={},dm);function fm(a,b,c){var d=gm(b,c);if(d){var e=em[b][d];e||(e=em[b][d]=[]);e.push(ma(Object,"assign").call(Object,{},a))}}function hm(a,b){var c=gm(a,b);if(c){var d=em[a][c];d&&(em[a][c]=d.filter(function(e){return!e.zm}))}}function im(a){switch(a){case "script-src":case "script-src-elem":return 1;case "frame-src":return 4;case "connect-src":return 2;case "img-src":return 3}}
function gm(a,b){var c=b;if(b[0]==="/"){var d;c=((d=x.location)==null?void 0:d.origin)+b}try{var e=new URL(c);return a===4?e.origin:e.origin+e.pathname}catch(f){}}function jm(a){var b=Ca.apply(1,arguments);kl&&(fm(a,2,b[0]),fm(a,3,b[0]));Sc.apply(null,ya(b))}function km(a){var b=Ca.apply(1,arguments);kl&&fm(a,2,b[0]);return Tc.apply(null,ya(b))}function lm(a){var b=Ca.apply(1,arguments);kl&&fm(a,3,b[0]);Jc.apply(null,ya(b))}
function mm(a){var b=Ca.apply(1,arguments),c=b[0];kl&&(fm(a,2,c),fm(a,3,c));return Vc.apply(null,ya(b))}function nm(a){var b=Ca.apply(1,arguments);kl&&fm(a,1,b[0]);Gc.apply(null,ya(b))}function om(a){var b=Ca.apply(1,arguments);b[0]&&kl&&fm(a,4,b[0]);Ic.apply(null,ya(b))}function pm(a){var b=Ca.apply(1,arguments);kl&&fm(a,1,b[2]);return am.apply(null,ya(b))}function qm(a){var b=Ca.apply(1,arguments);kl&&fm(a,4,b[0]);Pl.apply(null,ya(b))};var rm=/gtag[.\/]js/,sm=/gtm[.\/]js/,tm=!1;function um(a){if(tm)return"1";var b,c=(b=a.scriptElement)==null?void 0:b.src;if(c){if(rm.test(c))return"3";if(sm.test(c))return"2"}return"0"};function vm(a,b,c){var d=wm(),e=xm().container[a];e&&e.state!==3||(xm().container[a]={state:1,context:b,parent:d},ym({ctid:a,isDestination:!1},c))}function ym(a,b){var c=xm();c.pending||(c.pending=[]);pb(c.pending,function(d){return d.target.ctid===a.ctid&&d.target.isDestination===a.isDestination})||c.pending.push({target:a,onLoad:b})}function zm(){var a=x.google_tags_first_party;Array.isArray(a)||(a=[]);for(var b={},c=l(a),d=c.next();!d.done;d=c.next())b[d.value]=!0;return Object.freeze(b)}
var Am=function(){this.container={};this.destination={};this.canonical={};this.pending=[];this.injectedFirstPartyContainers={};this.injectedFirstPartyContainers=zm()};function xm(){var a=yc("google_tag_data",{}),b=a.tidr;b&&typeof b==="object"||(b=new Am,a.tidr=b);var c=b;c.container||(c.container={});c.destination||(c.destination={});c.canonical||(c.canonical={});c.pending||(c.pending=[]);c.injectedFirstPartyContainers||(c.injectedFirstPartyContainers=zm());return c};var Bm={},lg={ctid:Ti(5,"GTM-522R2ZGR"),canonicalContainerId:Ti(6,"216888478"),rm:Ti(10,"GTM-522R2ZGR"),sm:Ti(9,"GTM-522R2ZGR")};Bm.qe=Si(7,wb(""));function Cm(){return Bm.qe&&Dm().some(function(a){return a===lg.ctid})}function Em(){return lg.canonicalContainerId||"_"+lg.ctid}function Fm(){return lg.rm?lg.rm.split("|"):[lg.ctid]}
function Dm(){return lg.sm?lg.sm.split("|").filter(function(a){return a.indexOf("GTM-")!==0}):[]}function Gm(){var a=Hm(wm()),b=a&&a.parent;if(b)return Hm(b)}function Im(){var a=Hm(wm());if(a){for(;a.parent;){var b=Hm(a.parent);if(!b)break;a=b}return a}}function Hm(a){var b=xm();return a.isDestination?b.destination[a.ctid]:b.container[a.ctid]}
function Jm(){var a=xm();if(a.pending){for(var b,c=[],d=!1,e=Fm(),f=Dm(),g={},h=0;h<a.pending.length;g={kg:void 0},h++)g.kg=a.pending[h],pb(g.kg.target.isDestination?f:e,function(m){return function(n){return n===m.kg.target.ctid}}(g))?d||(b=g.kg.onLoad,d=!0):c.push(g.kg);a.pending=c;if(b)try{b(Em())}catch(m){}}}
function Km(){for(var a=lg.ctid,b=Fm(),c=Dm(),d=function(n,p){var q={canonicalContainerId:lg.canonicalContainerId,scriptContainerId:a,state:2,containers:b.slice(),destinations:c.slice()};wc&&(q.scriptElement=wc);xc&&(q.scriptSource=xc);if(Gm()===void 0){var r;a:{if((q.scriptContainerId||"").indexOf("GTM-")>=0){var t;b:{var u,v=(u=q.scriptElement)==null?void 0:u.src;if(v){for(var w=Qj.C,y=Vk(v),A=w?y.pathname:""+y.hostname+y.pathname,C=z.scripts,D="",G=0;G<C.length;++G){var I=C[G];if(!(I.innerHTML.length===
0||!w&&I.innerHTML.indexOf(q.scriptContainerId||"SHOULD_NOT_BE_SET")<0||I.innerHTML.indexOf(A)<0)){if(I.innerHTML.indexOf("(function(w,d,s,l,i)")>=0){t=String(G);break b}D=String(G)}}if(D){t=D;break b}}t=void 0}var M=t;if(M){tm=!0;r=M;break a}}var T=[].slice.call(z.scripts);r=q.scriptElement?String(T.indexOf(q.scriptElement)):"-1"}q.htmlLoadOrder=r;q.loadScriptType=um(q)}var da=p?e.destination:e.container,N=da[n];N?(p&&N.state===0&&L(93),ma(Object,"assign").call(Object,N,q)):da[n]=q},e=xm(),f=l(b),
g=f.next();!g.done;g=f.next())d(g.value,!1);for(var h=l(c),m=h.next();!m.done;m=h.next())d(m.value,!0);e.canonical[Em()]={};Jm()}function Lm(){var a=Em();return!!xm().canonical[a]}function Mm(a){return!!xm().container[a]}function Nm(a){var b=xm().destination[a];return!!b&&!!b.state}function wm(){return{ctid:lg.ctid,isDestination:Bm.qe}}function Om(){var a=xm().container,b;for(b in a)if(a.hasOwnProperty(b)&&a[b].state===1)return!0;return!1}
function Pm(){var a={};tb(xm().destination,function(b,c){c.state===0&&(a[b]=c)});return a}function Qm(a){return!!(a&&a.parent&&a.context&&a.context.source===1&&a.parent.ctid.indexOf("GTM-")!==0)}function Rm(){for(var a=xm(),b=l(Fm()),c=b.next();!c.done;c=b.next())if(a.injectedFirstPartyContainers[c.value])return!0;return!1};var Sm={Ia:{me:0,pe:1,Gi:2}};Sm.Ia[Sm.Ia.me]="FULL_TRANSMISSION";Sm.Ia[Sm.Ia.pe]="LIMITED_TRANSMISSION";Sm.Ia[Sm.Ia.Gi]="NO_TRANSMISSION";var Tm={X:{Hb:0,Fa:1,Fc:2,Oc:3}};Tm.X[Tm.X.Hb]="NO_QUEUE";Tm.X[Tm.X.Fa]="ADS";Tm.X[Tm.X.Fc]="ANALYTICS";Tm.X[Tm.X.Oc]="MONITORING";function Um(){var a=yc("google_tag_data",{});return a.ics=a.ics||new Vm}var Vm=function(){this.entries={};this.waitPeriodTimedOut=this.wasSetLate=this.accessedAny=this.accessedDefault=this.usedImplicit=this.usedUpdate=this.usedDefault=this.usedDeclare=this.active=!1;this.C=[]};
Vm.prototype.default=function(a,b,c,d,e,f,g){this.usedDefault||this.usedDeclare||!this.accessedDefault&&!this.accessedAny||(this.wasSetLate=!0);this.usedDefault=this.active=!0;fb("TAGGING",19);b==null?fb("TAGGING",18):Wm(this,a,b==="granted",c,d,e,f,g)};Vm.prototype.waitForUpdate=function(a,b,c){for(var d=0;d<a.length;d++)Wm(this,a[d],void 0,void 0,"","",b,c)};
var Wm=function(a,b,c,d,e,f,g,h){var m=a.entries,n=m[b]||{},p=n.region,q=d&&mb(d)?d.toUpperCase():void 0;e=e.toUpperCase();f=f.toUpperCase();if(e===""||q===f||(q===e?p!==f:!q&&!p)){var r=!!(g&&g>0&&n.update===void 0),t={region:q,declare_region:n.declare_region,implicit:n.implicit,default:c!==void 0?c:n.default,declare:n.declare,update:n.update,quiet:r};if(e!==""||n.default!==!1)m[b]=t;r&&x.setTimeout(function(){m[b]===t&&t.quiet&&(fb("TAGGING",2),a.waitPeriodTimedOut=!0,a.clearTimeout(b,void 0,h),
a.notifyListeners())},g)}};k=Vm.prototype;k.clearTimeout=function(a,b,c){var d=[a],e=c.delegatedConsentTypes,f;for(f in e)e.hasOwnProperty(f)&&e[f]===a&&d.push(f);var g=this.entries[a]||{},h=this.getConsentState(a,c);if(g.quiet){g.quiet=!1;for(var m=l(d),n=m.next();!n.done;n=m.next())Xm(this,n.value)}else if(b!==void 0&&h!==b)for(var p=l(d),q=p.next();!q.done;q=p.next())Xm(this,q.value)};
k.update=function(a,b,c){this.usedDefault||this.usedDeclare||this.usedUpdate||!this.accessedAny||(this.wasSetLate=!0);this.usedUpdate=this.active=!0;if(b!=null){var d=this.getConsentState(a,c),e=this.entries;(e[a]=e[a]||{}).update=b==="granted";this.clearTimeout(a,d,c)}};
k.declare=function(a,b,c,d,e){this.usedDeclare=this.active=!0;var f=this.entries,g=f[a]||{},h=g.declare_region,m=c&&mb(c)?c.toUpperCase():void 0;d=d.toUpperCase();e=e.toUpperCase();if(d===""||m===e||(m===d?h!==e:!m&&!h)){var n={region:g.region,declare_region:m,declare:b==="granted",implicit:g.implicit,default:g.default,update:g.update,quiet:g.quiet};if(d!==""||g.declare!==!1)f[a]=n}};
k.implicit=function(a,b){this.usedImplicit=!0;var c=this.entries,d=c[a]=c[a]||{};d.implicit!==!1&&(d.implicit=b==="granted")};
k.getConsentState=function(a,b){var c=this.entries,d=c[a]||{},e=d.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var f=b.containerScopedDefaults[a];if(f===3)return 1;if(f===2)return 2}else if(e=d.default,e!==void 0)return e?1:2;if(b==null?0:b.delegatedConsentTypes.hasOwnProperty(a)){var g=b.delegatedConsentTypes[a],h=c[g]||{};e=h.update;if(e!==void 0)return e?1:2;if(b.usedContainerScopedDefaults){var m=b.containerScopedDefaults[g];if(m===3)return 1;if(m===2)return 2}else if(e=
h.default,e!==void 0)return e?1:2}e=d.declare;if(e!==void 0)return e?1:2;e=d.implicit;return e!==void 0?e?3:4:0};k.addListener=function(a,b){this.C.push({consentTypes:a,Sc:b})};var Xm=function(a,b){for(var c=0;c<a.C.length;++c){var d=a.C[c];Array.isArray(d.consentTypes)&&d.consentTypes.indexOf(b)!==-1&&(d.tm=!0)}};Vm.prototype.notifyListeners=function(a,b){for(var c=0;c<this.C.length;++c){var d=this.C[c];if(d.tm){d.tm=!1;try{d.Sc({consentEventId:a,consentPriorityId:b})}catch(e){}}}};var Ym=!1,Zm=!1,$m={},an={delegatedConsentTypes:{},corePlatformServices:{},usedCorePlatformServices:!1,selectedAllCorePlatformServices:!1,containerScopedDefaults:($m.ad_storage=1,$m.analytics_storage=1,$m.ad_user_data=1,$m.ad_personalization=1,$m),usedContainerScopedDefaults:!1};function bn(a){var b=Um();b.accessedAny=!0;return(mb(a)?[a]:a).every(function(c){switch(b.getConsentState(c,an)){case 1:case 3:return!0;case 2:case 4:return!1;default:return!0}})}
function cn(a){var b=Um();b.accessedAny=!0;return b.getConsentState(a,an)}function dn(a){var b=Um();b.accessedAny=!0;return!(b.entries[a]||{}).quiet}function en(){if(!Ra(7))return!1;var a=Um();a.accessedAny=!0;if(a.active)return!0;if(!an.usedContainerScopedDefaults)return!1;for(var b=l(Object.keys(an.containerScopedDefaults)),c=b.next();!c.done;c=b.next())if(an.containerScopedDefaults[c.value]!==1)return!0;return!1}function fn(a,b){Um().addListener(a,b)}
function gn(a,b){Um().notifyListeners(a,b)}function hn(a,b){function c(){for(var e=0;e<b.length;e++)if(!dn(b[e]))return!0;return!1}if(c()){var d=!1;fn(b,function(e){d||c()||(d=!0,a(e))})}else a({})}
function jn(a,b){function c(){for(var h=[],m=0;m<e.length;m++){var n=e[m];bn(n)&&!f[n]&&h.push(n)}return h}function d(h){for(var m=0;m<h.length;m++)f[h[m]]=!0}var e=mb(b)?[b]:b,f={},g=c();g.length!==e.length&&(d(g),fn(e,function(h){function m(q){q.length!==0&&(d(q),h.consentTypes=q,a(h))}var n=c();if(n.length!==0){var p=Object.keys(f).length;n.length+p>=e.length?m(n):x.setTimeout(function(){m(c())},500)}}))};var kn={},ln=(kn[Tm.X.Hb]=Sm.Ia.me,kn[Tm.X.Fa]=Sm.Ia.me,kn[Tm.X.Fc]=Sm.Ia.me,kn[Tm.X.Oc]=Sm.Ia.me,kn),mn=function(a,b){this.C=a;this.consentTypes=b};mn.prototype.isConsentGranted=function(){switch(this.C){case 0:return this.consentTypes.every(function(a){return bn(a)});case 1:return this.consentTypes.some(function(a){return bn(a)});default:mc(this.C,"consentsRequired had an unknown type")}};
var nn={},on=(nn[Tm.X.Hb]=new mn(0,[]),nn[Tm.X.Fa]=new mn(0,["ad_storage"]),nn[Tm.X.Fc]=new mn(0,["analytics_storage"]),nn[Tm.X.Oc]=new mn(1,["ad_storage","analytics_storage"]),nn);var qn=function(a){var b=this;this.type=a;this.C=[];fn(on[a].consentTypes,function(){pn(b)||b.flush()})};qn.prototype.flush=function(){for(var a=l(this.C),b=a.next();!b.done;b=a.next()){var c=b.value;c()}this.C=[]};var pn=function(a){return ln[a.type]===Sm.Ia.Gi&&!on[a.type].isConsentGranted()},rn=function(a,b){pn(a)?a.C.push(b):b()},sn=new Map;function tn(a){sn.has(a)||sn.set(a,new qn(a));return sn.get(a)};var un={Z:{Mm:"aw_user_data_cache",Lh:"cookie_deprecation_label",xg:"diagnostics_page_id",Vn:"fl_user_data_cache",Xn:"ga4_user_data_cache",Df:"ip_geo_data_cache",Bi:"ip_geo_fetch_in_progress",ol:"nb_data",ql:"page_experiment_ids",Nf:"pt_data",rl:"pt_listener_set",zl:"service_worker_endpoint",Bl:"shared_user_id",Cl:"shared_user_id_requested",jh:"shared_user_id_source"}};var vn=function(a){return bf(function(b){for(var c in a)if(b===a[c]&&!/^[0-9]+$/.test(c))return!0;return!1})}(un.Z);
function wn(a,b){b=b===void 0?!1:b;if(vn(a)){var c,d,e=(d=(c=yc("google_tag_data",{})).xcd)!=null?d:c.xcd={};if(e[a])return e[a];if(b){var f=void 0,g=1,h={},m={set:function(n){f=n;m.notify()},get:function(){return f},subscribe:function(n){h[String(g)]=n;return g++},unsubscribe:function(n){var p=String(n);return h.hasOwnProperty(p)?(delete h[p],!0):!1},notify:function(){for(var n=l(Object.keys(h)),p=n.next();!p.done;p=n.next()){var q=p.value;try{h[q](a,f)}catch(r){}}}};return e[a]=m}}}
function xn(a,b){var c=wn(a,!0);c&&c.set(b)}function yn(a){var b;return(b=wn(a))==null?void 0:b.get()}function zn(a){var b={},c=wn(a);if(!c){c=wn(a,!0);if(!c)return;c.set(b)}return c.get()}function An(a,b){if(typeof b==="function"){var c;return(c=wn(a,!0))==null?void 0:c.subscribe(b)}}function Bn(a,b){var c=wn(a);return c?c.unsubscribe(b):!1};var Cn="https://"+Ti(21,"www.googletagmanager.com"),Dn="/td?id="+lg.ctid,En={},Fn=(En.tdp=1,En.exp=1,En.pid=1,En.dl=1,En.seq=1,En.t=1,En.v=1,En),Gn=["mcc"],Hn={},In={},Jn=!1,Kn=void 0;function Ln(a,b,c){In[a]=b;(c===void 0||c)&&Mn(a)}function Mn(a,b){Hn[a]!==void 0&&(b===void 0||!b)||Gb(lg.ctid,"GTM-")&&a==="mcc"||(Hn[a]=!0)}
function Nn(a){a=a===void 0?!1:a;var b=Object.keys(Hn).filter(function(c){return Hn[c]===!0&&In[c]!==void 0&&(a||!Gn.includes(c))}).map(function(c){var d=In[c];typeof d==="function"&&(d=d());return d?"&"+c+"="+d:""}).join("");return""+dl(Cn)+Dn+(""+b+"&z=0")}function On(){Object.keys(Hn).forEach(function(a){Fn[a]||(Hn[a]=!1)})}
function Pn(a){a=a===void 0?!1:a;if(Qj.fa&&kl&&lg.ctid){var b=tn(Tm.X.Oc);if(pn(b))Jn||(Jn=!0,rn(b,Pn));else{var c=Nn(a),d={destinationId:lg.ctid,endpoint:61};a?mm(d,c,void 0,{Ch:!0},void 0,function(){lm(d,c+"&img=1")}):lm(d,c);On();Jn=!1}}}var Qn={};
function Rn(a){var b=String(a);Qn.hasOwnProperty(b)||(Qn[b]=!0,Ln("csp",Object.keys(Qn).join("~")),Mn("csp",!0),Kn===void 0&&E(171)&&(Kn=x.setTimeout(function(){var c=Hn.csp;Hn.csp=!0;Hn.seq=!1;var d=Nn(!1);Hn.csp=c;Hn.seq=!0;Gc(d+"&script=1");Kn=void 0},500)))}function Sn(){Object.keys(Hn).filter(function(a){return Hn[a]&&!Fn[a]}).length>0&&Pn(!0)}var Tn;
function Un(){if(yn(un.Z.xg)===void 0){var a=function(){xn(un.Z.xg,qb());Tn=0};a();x.setInterval(a,864E5)}else An(un.Z.xg,function(){Tn=0});Tn=0}function Vn(){Un();Ln("v","3");Ln("t","t");Ln("pid",function(){return String(yn(un.Z.xg))});Ln("seq",function(){return String(++Tn)});Ln("exp",nk());Lc(x,"pagehide",Sn)};var Wn=["ad_storage","analytics_storage","ad_user_data","ad_personalization"],Xn=[J.m.nd,J.m.vc,J.m.Zd,J.m.Qb,J.m.Wb,J.m.Ma,J.m.Ta,J.m.cb,J.m.ob,J.m.Sb],Yn=!1,Zn=!1,$n={},ao={};function bo(){!Zn&&Yn&&(Wn.some(function(a){return an.containerScopedDefaults[a]!==1})||co("mbc"));Zn=!0}function co(a){kl&&(Ln(a,"1"),Pn())}function eo(a,b){if(!$n[b]&&($n[b]=!0,ao[b]))for(var c=l(Xn),d=c.next();!d.done;d=c.next())if(O(a,d.value)){co("erc");break}};function fo(a){fb("HEALTH",a)};var go={pp:Ti(22,"eyIwIjoiVVMiLCIxIjoiVVMtQ0EiLCIyIjpmYWxzZSwiMyI6IiIsIjQiOiIiLCI1Ijp0cnVlLCI2IjpmYWxzZSwiNyI6ImFkX3N0b3JhZ2V8YW5hbHl0aWNzX3N0b3JhZ2V8YWRfdXNlcl9kYXRhfGFkX3BlcnNvbmFsaXphdGlvbiJ9")},ho={},io=!1;function jo(){function a(){c!==void 0&&Bn(un.Z.Df,c);try{var e=yn(un.Z.Df);ho=JSON.parse(e)}catch(f){L(123),fo(2),ho={}}io=!0;b()}var b=ko,c=void 0,d=yn(un.Z.Df);d?a(d):(c=An(un.Z.Df,a),lo())}
function lo(){function a(c){xn(un.Z.Df,c||"{}");xn(un.Z.Bi,!1)}if(!yn(un.Z.Bi)){xn(un.Z.Bi,!0);var b="";try{x.fetch(b,{method:"GET",cache:"no-store",mode:"cors",credentials:"omit"}).then(function(c){c.ok?c.text().then(function(d){a(d)},function(){a()}):a()},function(){a()})}catch(c){a()}}}
function mo(){var a=go.pp;try{return JSON.parse(db(a))}catch(b){return L(123),fo(2),{}}}function no(){return ho["0"]||""}function oo(){return ho["1"]||""}function po(){var a=!1;return a}function qo(){return ho["6"]!==!1}function ro(){var a="";return a}
function so(){var a=!1;return a}function to(){var a="";return a};var uo={},vo=Object.freeze((uo[J.m.Ga]=1,uo[J.m.zg]=1,uo[J.m.Ag]=1,uo[J.m.Ob]=1,uo[J.m.sa]=1,uo[J.m.ob]=1,uo[J.m.pb]=1,uo[J.m.zb]=1,uo[J.m.ed]=1,uo[J.m.Sb]=1,uo[J.m.cb]=1,uo[J.m.Hc]=1,uo[J.m.af]=1,uo[J.m.oa]=1,uo[J.m.jk]=1,uo[J.m.df]=1,uo[J.m.Jg]=1,uo[J.m.Kg]=1,uo[J.m.Zd]=1,uo[J.m.zk]=1,uo[J.m.sc]=1,uo[J.m.ce]=1,uo[J.m.Bk]=1,uo[J.m.Ng]=1,uo[J.m.fi]=1,uo[J.m.Kc]=1,uo[J.m.Lc]=1,uo[J.m.Ta]=1,uo[J.m.gi]=1,uo[J.m.Vb]=1,uo[J.m.qb]=1,uo[J.m.md]=1,uo[J.m.nd]=1,uo[J.m.pf]=1,uo[J.m.ii]=1,uo[J.m.je]=1,uo[J.m.vc]=
1,uo[J.m.pd]=1,uo[J.m.Ug]=1,uo[J.m.Xb]=1,uo[J.m.sd]=1,uo[J.m.Ji]=1,uo));Object.freeze([J.m.Ca,J.m.Ya,J.m.Fb,J.m.Ab,J.m.hi,J.m.Ma,J.m.bi,J.m.yn]);
var wo={},xo=Object.freeze((wo[J.m.Zm]=1,wo[J.m.bn]=1,wo[J.m.dn]=1,wo[J.m.fn]=1,wo[J.m.gn]=1,wo[J.m.kn]=1,wo[J.m.ln]=1,wo[J.m.mn]=1,wo[J.m.on]=1,wo[J.m.Td]=1,wo)),yo={},zo=Object.freeze((yo[J.m.Yj]=1,yo[J.m.Zj]=1,yo[J.m.Pd]=1,yo[J.m.Qd]=1,yo[J.m.bk]=1,yo[J.m.Xc]=1,yo[J.m.Rd]=1,yo[J.m.kc]=1,yo[J.m.Gc]=1,yo[J.m.mc]=1,yo[J.m.lb]=1,yo[J.m.Sd]=1,yo[J.m.yb]=1,yo[J.m.dk]=1,yo)),Ao=Object.freeze([J.m.Ga,J.m.Qe,J.m.Ob,J.m.Hc,J.m.Zd,J.m.kf,J.m.qb,J.m.pd]),Bo=Object.freeze([].concat(ya(Ao))),Co=Object.freeze([J.m.pb,
J.m.Kg,J.m.pf,J.m.ii,J.m.Hg]),Do=Object.freeze([].concat(ya(Co))),Eo={},Fo=(Eo[J.m.U]="1",Eo[J.m.ja]="2",Eo[J.m.V]="3",Eo[J.m.Ka]="4",Eo),Go={},Ho=Object.freeze((Go.search="s",Go.youtube="y",Go.playstore="p",Go.shopping="h",Go.ads="a",Go.maps="m",Go));function Io(a){return typeof a!=="object"||a===null?{}:a}function Jo(a){return a===void 0||a===null?"":typeof a==="object"?a.toString():String(a)}function Ko(a){if(a!==void 0&&a!==null)return Jo(a)}function Lo(a){return typeof a==="number"?a:Ko(a)};function Mo(a){return a&&a.indexOf("pending:")===0?No(a.substr(8)):!1}function No(a){if(a==null||a.length===0)return!1;var b=Number(a),c=Ab();return b<c+3E5&&b>c-9E5};var Oo=!1,Po=!1,Qo=!1,Ro=0,So=!1,To=[];function Uo(a){if(Ro===0)So&&To&&(To.length>=100&&To.shift(),To.push(a));else if(Vo()){var b=Ti(41,'google.tagmanager.ta.prodqueue'),c=yc(b,[]);c.length>=50&&c.shift();c.push(a)}}function Wo(){Xo();Mc(z,"TAProdDebugSignal",Wo)}function Xo(){if(!Po){Po=!0;Yo();var a=To;To=void 0;a==null||a.forEach(function(b){Uo(b)})}}
function Yo(){var a=z.documentElement.getAttribute("data-tag-assistant-prod-present");No(a)?Ro=1:!Mo(a)||Oo||Qo?Ro=2:(Qo=!0,Lc(z,"TAProdDebugSignal",Wo,!1),x.setTimeout(function(){Xo();Oo=!0},200))}function Vo(){if(!So)return!1;switch(Ro){case 1:case 0:return!0;case 2:return!1;default:return!1}};var Zo=!1;function $o(a,b){var c=Fm(),d=Dm();if(Vo()){var e=ap("INIT");e.containerLoadSource=a!=null?a:0;b&&(e.parentTargetReference=b);e.aliases=c;e.destinations=d;Uo(e)}}
function bp(a){var b,c,d,e;b=a.targetId;c=a.request;d=a.Oa;e=a.isBatched;var f;if(f=Vo()){var g;a:switch(c.endpoint){case 19:case 47:case 44:g=!0;break a;default:g=!1}f=!g}if(f){var h=ap("GTAG_HIT",{eventId:d.eventId,priorityId:d.priorityId});h.target=b;h.url=c.url;c.postBody&&(h.postBody=c.postBody);h.parameterEncoding=c.parameterEncoding;h.endpoint=c.endpoint;e!==void 0&&(h.isBatched=e);Uo(h)}}function cp(a){Vo()&&bp(a())}
function ap(a,b){b=b===void 0?{}:b;b.groupId=dp;var c,d=b,e={publicId:ep};d.eventId!=null&&(e.eventId=d.eventId);d.priorityId!=null&&(e.priorityId=d.priorityId);d.eventName&&(e.eventName=d.eventName);d.groupId&&(e.groupId=d.groupId);d.tagName&&(e.tagName=d.tagName);c={containerProduct:"GTM",key:e,version:'5',messageType:a};c.containerProduct=Zo?"OGT":"GTM";c.key.targetRef=fp;return c}var ep="",fp={ctid:"",isDestination:!1},dp;
function gp(a){var b=lg.ctid,c=Cm();Ro=0;So=!0;Yo();dp=a;ep=b;Zo=ek;fp={ctid:b,isDestination:c}};var hp=[J.m.U,J.m.ja,J.m.V,J.m.Ka],ip,jp;function kp(a){var b=a[J.m.hc];b||(b=[""]);for(var c={cg:0};c.cg<b.length;c={cg:c.cg},++c.cg)tb(a,function(d){return function(e,f){if(e!==J.m.hc){var g=Jo(f),h=b[d.cg],m=no(),n=oo();Zm=!0;Ym&&fb("TAGGING",20);Um().declare(e,g,h,m,n)}}}(c))}
function lp(a){bo();!jp&&ip&&co("crc");jp=!0;var b=a[J.m.rg];b&&L(41);var c=a[J.m.hc];c?L(40):c=[""];for(var d={dg:0};d.dg<c.length;d={dg:d.dg},++d.dg)tb(a,function(e){return function(f,g){if(f!==J.m.hc&&f!==J.m.rg){var h=Ko(g),m=c[e.dg],n=Number(b),p=no(),q=oo();n=n===void 0?0:n;Ym=!0;Zm&&fb("TAGGING",20);Um().default(f,h,m,p,q,n,an)}}}(d))}
function mp(a){an.usedContainerScopedDefaults=!0;var b=a[J.m.hc];if(b){var c=Array.isArray(b)?b:[b];if(!c.includes(oo())&&!c.includes(no()))return}tb(a,function(d,e){switch(d){case "ad_storage":case "analytics_storage":case "ad_user_data":case "ad_personalization":break;default:return}an.usedContainerScopedDefaults=!0;an.containerScopedDefaults[d]=e==="granted"?3:2})}
function np(a,b){bo();ip=!0;tb(a,function(c,d){var e=Jo(d);Ym=!0;Zm&&fb("TAGGING",20);Um().update(c,e,an)});gn(b.eventId,b.priorityId)}function op(a){a.hasOwnProperty("all")&&(an.selectedAllCorePlatformServices=!0,tb(Ho,function(b){an.corePlatformServices[b]=a.all==="granted";an.usedCorePlatformServices=!0}));tb(a,function(b,c){b!=="all"&&(an.corePlatformServices[b]=c==="granted",an.usedCorePlatformServices=!0)})}function P(a){Array.isArray(a)||(a=[a]);return a.every(function(b){return bn(b)})}
function pp(a,b){fn(a,b)}function qp(a,b){jn(a,b)}function rp(a,b){hn(a,b)}function sp(){var a=[J.m.U,J.m.Ka,J.m.V];Um().waitForUpdate(a,500,an)}function tp(a){for(var b=l(a),c=b.next();!c.done;c=b.next()){var d=c.value;Um().clearTimeout(d,void 0,an)}gn()}function up(){if(!fk)for(var a=qo()?qk(Qj.Va):qk(Qj.rb),b=0;b<hp.length;b++){var c=hp[b],d=c,e=a[c]?"granted":"denied";Um().implicit(d,e)}};var vp=!1,wp=[];function xp(){if(!vp){vp=!0;for(var a=wp.length-1;a>=0;a--)wp[a]();wp=[]}};var yp=x.google_tag_manager=x.google_tag_manager||{};function zp(a,b){return yp[a]=yp[a]||b()}function Ap(){var a=lg.ctid,b=Bp;yp[a]=yp[a]||b}function Cp(){var a=yp.sequence||1;yp.sequence=a+1;return a}x.google_tag_data=x.google_tag_data||{};function Dp(){if(yp.pscdl!==void 0)yn(un.Z.Lh)===void 0&&xn(un.Z.Lh,yp.pscdl);else{var a=function(c){yp.pscdl=c;xn(un.Z.Lh,c)},b=function(){a("error")};try{uc.cookieDeprecationLabel?(a("pending"),uc.cookieDeprecationLabel.getValue().then(a).catch(b)):a("noapi")}catch(c){b(c)}}};var Ep=0;function Fp(a){kl&&a===void 0&&Ep===0&&(Ln("mcc","1"),Ep=1)};var Gp={Bf:{Sm:"cd",Tm:"ce",Um:"cf",Vm:"cpf",Wm:"cu"}};var Hp=/^(?:AW|DC|G|GF|GT|HA|MC|UA)$/,Ip=/\s/;
function Jp(a,b){if(mb(a)){a=yb(a);var c=a.indexOf("-");if(!(c<0)){var d=a.substring(0,c);if(Hp.test(d)){var e=a.substring(c+1),f;if(b){var g=function(n){var p=n.indexOf("/");return p<0?[n]:[n.substring(0,p),n.substring(p+1)]};f=g(e);if(d==="DC"&&f.length===2){var h=g(f[1]);h.length===2&&(f[1]=h[0],f.push(h[1]))}}else{f=e.split("/");for(var m=0;m<f.length;m++)if(!f[m]||Ip.test(f[m])&&(d!=="AW"||m!==1))return}return{id:a,prefix:d,destinationId:d+"-"+f[0],ids:f}}}}}
function Kp(a,b){for(var c={},d=0;d<a.length;++d){var e=Jp(a[d],b);e&&(c[e.id]=e)}var f=[],g;for(g in c)if(c.hasOwnProperty(g)){var h=c[g];h.prefix==="AW"&&h.ids[Lp[1]]&&f.push(h.destinationId)}for(var m=0;m<f.length;++m)delete c[f[m]];for(var n=[],p=l(Object.keys(c)),q=p.next();!q.done;q=p.next())n.push(c[q.value]);return n}var Mp={},Lp=(Mp[0]=0,Mp[1]=1,Mp[2]=2,Mp[3]=0,Mp[4]=1,Mp[5]=0,Mp[6]=0,Mp[7]=0,Mp);var Np=Number('')||500,Op={},Pp={},Qp={initialized:11,complete:12,interactive:13},Rp={},Sp=Object.freeze((Rp[J.m.qb]=!0,Rp)),Tp=void 0;function Up(a,b){if(b.length&&kl){var c;(c=Op)[a]!=null||(c[a]=[]);Pp[a]!=null||(Pp[a]=[]);var d=b.filter(function(e){return!Pp[a].includes(e)});Op[a].push.apply(Op[a],ya(d));Pp[a].push.apply(Pp[a],ya(d));!Tp&&d.length>0&&(Mn("tdc",!0),Tp=x.setTimeout(function(){Pn();Op={};Tp=void 0},Np))}}
function Vp(a,b){var c={},d;for(d in b)b.hasOwnProperty(d)&&(c[d]=!0);for(var e in a)a.hasOwnProperty(e)&&(c[e]=!0);return c}
function Wp(a,b,c,d){c=c===void 0?{}:c;d=d===void 0?"":d;if(a===b)return[];var e=function(r,t){var u;jd(t)==="object"?u=t[r]:jd(t)==="array"&&(u=t[r]);return u===void 0?Sp[r]:u},f=Vp(a,b),g;for(g in f)if(f.hasOwnProperty(g)){var h=(d?d+".":"")+g,m=e(g,a),n=e(g,b),p=jd(m)==="object"||jd(m)==="array",q=jd(n)==="object"||jd(n)==="array";if(p&&q)Wp(m,n,c,h);else if(p||q||m!==n)c[h]=!0}return Object.keys(c)}
function Xp(){Ln("tdc",function(){Tp&&(x.clearTimeout(Tp),Tp=void 0);var a=[],b;for(b in Op)Op.hasOwnProperty(b)&&a.push(b+"*"+Op[b].join("."));return a.length?a.join("!"):void 0},!1)};var Yp=function(a,b,c,d,e,f,g,h,m,n,p){this.eventId=a;this.priorityId=b;this.C=c;this.R=d;this.N=e;this.P=f;this.H=g;this.eventMetadata=h;this.onSuccess=m;this.onFailure=n;this.isGtmEvent=p},Zp=function(a,b){var c=[];switch(b){case 3:c.push(a.C);c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 2:c.push(a.C);break;case 1:c.push(a.R);c.push(a.N);c.push(a.P);c.push(a.H);break;case 4:c.push(a.C),c.push(a.R),c.push(a.N),c.push(a.P)}return c},O=function(a,b,c,d){for(var e=l(Zp(a,d===void 0?3:
d)),f=e.next();!f.done;f=e.next()){var g=f.value;if(g[b]!==void 0)return g[b]}return c},$p=function(a){for(var b={},c=Zp(a,4),d=l(c),e=d.next();!e.done;e=d.next())for(var f=Object.keys(e.value),g=l(f),h=g.next();!h.done;h=g.next())b[h.value]=1;return Object.keys(b)};
Yp.prototype.getMergedValues=function(a,b,c){function d(n){ld(n)&&tb(n,function(p,q){f=!0;e[p]=q})}b=b===void 0?3:b;var e={},f=!1;c&&d(c);var g=Zp(this,b);g.reverse();for(var h=l(g),m=h.next();!m.done;m=h.next())d(m.value[a]);return f?e:void 0};
var aq=function(a){for(var b=[J.m.Ve,J.m.Re,J.m.Se,J.m.Te,J.m.Ue,J.m.We,J.m.Xe],c=Zp(a,3),d=l(c),e=d.next();!e.done;e=d.next()){for(var f=e.value,g={},h=!1,m=l(b),n=m.next();!n.done;n=m.next()){var p=n.value;f[p]!==void 0&&(g[p]=f[p],h=!0)}var q=h?g:void 0;if(q)return q}return{}},bq=function(a,b){this.eventId=a;this.priorityId=b;this.H={};this.R={};this.C={};this.N={};this.fa={};this.P={};this.eventMetadata={};this.isGtmEvent=!1;this.onSuccess=function(){};this.onFailure=function(){}},cq=function(a,
b){a.H=b;return a},dq=function(a,b){a.R=b;return a},eq=function(a,b){a.C=b;return a},fq=function(a,b){a.N=b;return a},gq=function(a,b){a.fa=b;return a},hq=function(a,b){a.P=b;return a},iq=function(a,b){a.eventMetadata=b||{};return a},jq=function(a,b){a.onSuccess=b;return a},kq=function(a,b){a.onFailure=b;return a},lq=function(a,b){a.isGtmEvent=b;return a},mq=function(a){return new Yp(a.eventId,a.priorityId,a.H,a.R,a.C,a.N,a.P,a.eventMetadata,a.onSuccess,a.onFailure,a.isGtmEvent)};var Q={A:{Hj:"accept_by_default",qg:"add_tag_timing",Hh:"allow_ad_personalization",Jj:"batch_on_navigation",Lj:"client_id_source",He:"consent_event_id",Ie:"consent_priority_id",Pq:"consent_state",da:"consent_updated",Wc:"conversion_linker_enabled",xa:"cookie_options",tg:"create_dc_join",ug:"create_fpm_geo_join",vg:"create_fpm_signals_join",Od:"create_google_join",Ke:"em_event",Sq:"endpoint_for_debug",Xj:"enhanced_client_id_source",Oh:"enhanced_match_result",ke:"euid_mode_enabled",fb:"event_start_timestamp_ms",
Vk:"event_usage",Wg:"extra_tag_experiment_ids",Zq:"add_parameter",wi:"attribution_reporting_experiment",xi:"counting_method",Xg:"send_as_iframe",ar:"parameter_order",Yg:"parsed_target",Wn:"ga4_collection_subdomain",Yk:"gbraid_cookie_marked",ia:"hit_type",ud:"hit_type_override",bo:"is_config_command",Ef:"is_consent_update",Ff:"is_conversion",fl:"is_ecommerce",vd:"is_external_event",Ci:"is_fallback_aw_conversion_ping_allowed",Gf:"is_first_visit",il:"is_first_visit_conversion",Zg:"is_fl_fallback_conversion_flow_allowed",
Hf:"is_fpm_encryption",ah:"is_fpm_split",ne:"is_gcp_conversion",jl:"is_google_signals_allowed",wd:"is_merchant_center",bh:"is_new_to_site",eh:"is_server_side_destination",oe:"is_session_start",ml:"is_session_start_conversion",hr:"is_sgtm_ga_ads_conversion_study_control_group",ir:"is_sgtm_prehit",nl:"is_sgtm_service_worker",Di:"is_split_conversion",co:"is_syn",If:"join_id",Ei:"join_elapsed",Jf:"join_timer_sec",se:"tunnel_updated",mr:"prehit_for_retry",qr:"promises",rr:"record_aw_latency",yc:"redact_ads_data",
te:"redact_click_ids",oo:"remarketing_only",xl:"send_ccm_parallel_ping",ih:"send_fledge_experiment",vr:"send_ccm_parallel_test_ping",Of:"send_to_destinations",Ii:"send_to_targets",yl:"send_user_data_hit",hb:"source_canonical_id",Ba:"speculative",Dl:"speculative_in_message",El:"suppress_script_load",Fl:"syn_or_mod",Jl:"transient_ecsid",Pf:"transmission_type",ib:"user_data",yr:"user_data_from_automatic",zr:"user_data_from_automatic_getter",ve:"user_data_from_code",mh:"user_data_from_manual",Ll:"user_data_mode",
Qf:"user_id_updated"}};var nq={Lm:Number("5"),Qr:Number("")},oq=[],pq=!1;function qq(a){oq.push(a)}var rq="?id="+lg.ctid,sq=void 0,tq={},uq=void 0,vq=new function(){var a=5;nq.Lm>0&&(a=nq.Lm);this.H=a;this.C=0;this.N=[]},wq=1E3;
function xq(a,b){var c=sq;if(c===void 0)if(b)c=Cp();else return"";for(var d=[dl("https://www.googletagmanager.com"),"/a",rq],e=l(oq),f=e.next();!f.done;f=e.next())for(var g=f.value,h=g({eventId:c,Nd:!!a}),m=l(h),n=m.next();!n.done;n=m.next()){var p=l(n.value),q=p.next().value,r=p.next().value;d.push("&"+q+"="+r)}d.push("&z=0");return d.join("")}
function yq(){if(Qj.fa&&(uq&&(x.clearTimeout(uq),uq=void 0),sq!==void 0&&zq)){var a=tn(Tm.X.Oc);if(pn(a))pq||(pq=!0,rn(a,yq));else{var b;if(!(b=tq[sq])){var c=vq;b=c.C<c.H?!1:Ab()-c.N[c.C%c.H]<1E3}if(b||wq--<=0)L(1),tq[sq]=!0;else{var d=vq,e=d.C++%d.H;d.N[e]=Ab();var f=xq(!0);lm({destinationId:lg.ctid,endpoint:56,eventId:sq},f);pq=zq=!1}}}}function Aq(){if(jl&&Qj.fa){var a=xq(!0,!0);lm({destinationId:lg.ctid,endpoint:56,eventId:sq},a)}}var zq=!1;
function Bq(a){tq[a]||(a!==sq&&(yq(),sq=a),zq=!0,uq||(uq=x.setTimeout(yq,500)),xq().length>=2022&&yq())}var Cq=qb();function Dq(){Cq=qb()}function Eq(){return[["v","3"],["t","t"],["pid",String(Cq)]]};var Fq={};function Gq(a,b,c){jl&&a!==void 0&&(Fq[a]=Fq[a]||[],Fq[a].push(c+b),Bq(a))}function Hq(a){var b=a.eventId,c=a.Nd,d=[],e=Fq[b]||[];e.length&&d.push(["epr",e.join(".")]);c&&delete Fq[b];return d};function Iq(a,b,c,d){var e=Jp(a,!0);e&&Jq.register(e,b,c,d)}function Kq(a,b,c,d){var e=Jp(c,d.isGtmEvent);e&&(ck&&(d.deferrable=!0),Jq.push("event",[b,a],e,d))}function Lq(a,b,c,d){var e=Jp(c,d.isGtmEvent);e&&Jq.push("get",[a,b],e,d)}function Mq(a){var b=Jp(a,!0),c;b?c=Nq(Jq,b).C:c={};return c}function Oq(a,b){var c=Jp(a,!0);c&&Pq(Jq,c,b)}
var Rq=function(){this.R={};this.C={};this.H={};this.fa=null;this.P={};this.N=!1;this.status=1},Sq=function(a,b,c,d){this.H=Ab();this.C=b;this.args=c;this.messageContext=d;this.type=a},Tq=function(){this.destinations={};this.C={};this.commands=[]},Nq=function(a,b){return a.destinations[b.destinationId]=a.destinations[b.destinationId]||new Rq},Uq=function(a,b,c,d){if(d.C){var e=Nq(a,d.C),f=e.fa;if(f){var g=md(c,null),h=md(e.R[d.C.id],null),m=md(e.P,null),n=md(e.C,null),p=md(a.C,null),q={};if(jl)try{q=
md(sk,null)}catch(w){L(72)}var r=d.C.prefix,t=function(w){Gq(d.messageContext.eventId,r,w)},u=mq(lq(kq(jq(iq(gq(fq(hq(eq(dq(cq(new bq(d.messageContext.eventId,d.messageContext.priorityId),g),h),m),n),p),q),d.messageContext.eventMetadata),function(){if(t){var w=t;t=void 0;w("2");if(d.messageContext.onSuccess)d.messageContext.onSuccess()}}),function(){if(t){var w=t;t=void 0;w("3");if(d.messageContext.onFailure)d.messageContext.onFailure()}}),!!d.messageContext.isGtmEvent)),v=function(){try{Gq(d.messageContext.eventId,
r,"1");var w=d.type,y=d.C.id;if(kl&&w==="config"){var A,C=(A=Jp(y))==null?void 0:A.ids;if(!(C&&C.length>1)){var D,G=yc("google_tag_data",{});G.td||(G.td={});D=G.td;var I=md(u.P);md(u.C,I);var M=[],T;for(T in D)D.hasOwnProperty(T)&&Wp(D[T],I).length&&M.push(T);M.length&&(Up(y,M),fb("TAGGING",Qp[z.readyState]||14));D[y]=I}}f(d.C.id,b,d.H,u)}catch(da){Gq(d.messageContext.eventId,r,"4")}};b==="gtag.get"?v():rn(e.ma,v)}}};
Tq.prototype.register=function(a,b,c,d){var e=Nq(this,a);e.status!==3&&(e.fa=b,e.status=3,e.ma=tn(c),Pq(this,a,d||{}),this.flush())};
Tq.prototype.push=function(a,b,c,d){c!==void 0&&(Nq(this,c).status===1&&(Nq(this,c).status=2,this.push("require",[{}],c,{})),Nq(this,c).N&&(d.deferrable=!1),d.eventMetadata||(d.eventMetadata={}),d.eventMetadata[Q.A.Of]||(d.eventMetadata[Q.A.Of]=[c.destinationId]),d.eventMetadata[Q.A.Ii]||(d.eventMetadata[Q.A.Ii]=[c.id]));this.commands.push(new Sq(a,c,b,d));d.deferrable||this.flush()};
Tq.prototype.flush=function(a){for(var b=this,c=[],d=!1,e={};this.commands.length;e={Qc:void 0,rh:void 0}){var f=this.commands[0],g=f.C;if(f.messageContext.deferrable)!g||Nq(this,g).N?(f.messageContext.deferrable=!1,this.commands.push(f)):c.push(f),this.commands.shift();else{switch(f.type){case "require":if(Nq(this,g).status!==3&&!a){this.commands.push.apply(this.commands,c);return}break;case "set":var h=f.args[0];tb(h,function(t,u){md(Ib(t,u),b.C)});Oj(h,!0);break;case "config":var m=Nq(this,g);
e.Qc={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.Qc)}}(e));var n=!!e.Qc[J.m.pd];delete e.Qc[J.m.pd];var p=g.destinationId===g.id;Oj(e.Qc,!0);n||(p?m.P={}:m.R[g.id]={});m.N&&n||Uq(this,J.m.qa,e.Qc,f);m.N=!0;p?md(e.Qc,m.P):(md(e.Qc,m.R[g.id]),L(70));d=!0;break;case "event":e.rh={};tb(f.args[0],function(t){return function(u,v){md(Ib(u,v),t.rh)}}(e));Oj(e.rh);Uq(this,f.args[1],e.rh,f);break;case "get":var q={},r=(q[J.m.rc]=f.args[0],q[J.m.Ic]=f.args[1],q);Uq(this,J.m.Eb,r,f)}this.commands.shift();
Vq(this,f)}}this.commands.push.apply(this.commands,c);d&&this.flush()};var Vq=function(a,b){if(b.type!=="require")if(b.C)for(var c=Nq(a,b.C).H[b.type]||[],d=0;d<c.length;d++)c[d]();else for(var e in a.destinations)if(a.destinations.hasOwnProperty(e)){var f=a.destinations[e];if(f&&f.H)for(var g=f.H[b.type]||[],h=0;h<g.length;h++)g[h]()}},Pq=function(a,b,c){var d=md(c,null);md(Nq(a,b).C,d);Nq(a,b).C=d},Jq=new Tq;function Wq(a,b,c){return typeof a.addEventListener==="function"?(a.addEventListener(b,c,!1),!0):!1}function Xq(a,b,c){typeof a.removeEventListener==="function"&&a.removeEventListener(b,c,!1)};function Yq(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Ll(a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=rc(g,e);h>=0&&Array.prototype.splice.call(g,h,1)}Xq(e,"load",f);Xq(e,"error",f)};Wq(e,"load",f);Wq(e,"error",f)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}
function Zq(a){var b;b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=tcfe";Il(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});$q(c,b)}
function $q(a,b){var c=window,d;b=b===void 0?!1:b;d=d===void 0?!1:d;if(c.fetch){var e={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"};d&&(e.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?e.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:e.headers={"Attribution-Reporting-Eligible":"event-source"});c.fetch(a,e)}else Yq(c,a,b===void 0?!1:b,d===void 0?!1:d)};var ar=function(){this.fa=this.fa;this.P=this.P};ar.prototype.fa=!1;ar.prototype.dispose=function(){this.fa||(this.fa=!0,this.N())};ar.prototype[ha.Symbol.dispose]=function(){this.dispose()};ar.prototype.addOnDisposeCallback=function(a,b){this.fa?b!==void 0?a.call(b):a():(this.P||(this.P=[]),b&&(a=a.bind(b)),this.P.push(a))};ar.prototype.N=function(){if(this.P)for(;this.P.length;)this.P.shift()()};function br(a){a.addtlConsent!==void 0&&typeof a.addtlConsent!=="string"&&(a.addtlConsent=void 0);a.gdprApplies!==void 0&&typeof a.gdprApplies!=="boolean"&&(a.gdprApplies=void 0);return a.tcString!==void 0&&typeof a.tcString!=="string"||a.listenerId!==void 0&&typeof a.listenerId!=="number"?2:a.cmpStatus&&a.cmpStatus!=="error"?0:3}
var cr=function(a,b){b=b===void 0?{}:b;ar.call(this);this.C=null;this.ma={};this.rb=0;this.R=null;this.H=a;var c;this.Va=(c=b.timeoutMs)!=null?c:500;var d;this.Da=(d=b.Fr)!=null?d:!1};va(cr,ar);cr.prototype.N=function(){this.ma={};this.R&&(Xq(this.H,"message",this.R),delete this.R);delete this.ma;delete this.H;delete this.C;ar.prototype.N.call(this)};var er=function(a){return typeof a.H.__tcfapi==="function"||dr(a)!=null};
cr.prototype.addEventListener=function(a){var b=this,c={internalBlockOnErrors:this.Da},d=ml(function(){return a(c)}),e=0;this.Va!==-1&&(e=setTimeout(function(){c.tcString="tcunavailable";c.internalErrorState=1;d()},this.Va));var f=function(g,h){clearTimeout(e);g?(c=g,c.internalErrorState=br(c),c.internalBlockOnErrors=b.Da,h&&c.internalErrorState===0||(c.tcString="tcunavailable",h||(c.internalErrorState=3))):(c.tcString="tcunavailable",c.internalErrorState=3);a(c)};try{fr(this,"addEventListener",f)}catch(g){c.tcString=
"tcunavailable",c.internalErrorState=3,e&&(clearTimeout(e),e=0),d()}};cr.prototype.removeEventListener=function(a){a&&a.listenerId&&fr(this,"removeEventListener",null,a.listenerId)};
var hr=function(a,b,c){var d;d=d===void 0?"755":d;var e;a:{if(a.publisher&&a.publisher.restrictions){var f=a.publisher.restrictions[b];if(f!==void 0){e=f[d===void 0?"755":d];break a}}e=void 0}var g=e;if(g===0)return!1;var h=c;c===2?(h=0,g===2&&(h=1)):c===3&&(h=1,g===1&&(h=0));var m;if(h===0)if(a.purpose&&a.vendor){var n=gr(a.vendor.consents,d===void 0?"755":d);m=n&&b==="1"&&a.purposeOneTreatment&&a.publisherCC==="CH"?!0:n&&gr(a.purpose.consents,b)}else m=!0;else m=h===1?a.purpose&&a.vendor?gr(a.purpose.legitimateInterests,
b)&&gr(a.vendor.legitimateInterests,d===void 0?"755":d):!0:!0;return m},gr=function(a,b){return!(!a||!a[b])},fr=function(a,b,c,d){c||(c=function(){});var e=a.H;if(typeof e.__tcfapi==="function"){var f=e.__tcfapi;f(b,2,c,d)}else if(dr(a)){ir(a);var g=++a.rb;a.ma[g]=c;if(a.C){var h={};a.C.postMessage((h.__tcfapiCall={command:b,version:2,callId:g,parameter:d},h),"*")}}else c({},!1)},dr=function(a){if(a.C)return a.C;a.C=Jl(a.H,"__tcfapiLocator");return a.C},ir=function(a){if(!a.R){var b=function(c){try{var d;
d=(typeof c.data==="string"?JSON.parse(c.data):c.data).__tcfapiReturn;a.ma[d.callId](d.returnValue,d.success)}catch(e){}};a.R=b;Wq(a.H,"message",b)}},jr=function(a){if(a.gdprApplies===!1)return!0;a.internalErrorState===void 0&&(a.internalErrorState=br(a));return a.cmpStatus==="error"||a.internalErrorState!==0?a.internalBlockOnErrors?(Zq({e:String(a.internalErrorState)}),!1):!0:a.cmpStatus!=="loaded"||a.eventStatus!=="tcloaded"&&a.eventStatus!=="useractioncomplete"?!1:!0};var kr={1:0,3:0,4:0,7:3,9:3,10:3};function lr(){return zp("tcf",function(){return{}})}var mr=function(){return new cr(x,{timeoutMs:-1})};
function nr(){var a=lr(),b=mr();er(b)&&!or()&&!pr()&&L(124);if(!a.active&&er(b)){or()&&(a.active=!0,a.purposes={},a.cmpId=0,a.tcfPolicyVersion=0,Um().active=!0,a.tcString="tcunavailable");sp();try{b.addEventListener(function(c){if(c.internalErrorState!==0)qr(a),tp([J.m.U,J.m.Ka,J.m.V]),Um().active=!0;else if(a.gdprApplies=c.gdprApplies,a.cmpId=c.cmpId,a.enableAdvertiserConsentMode=c.enableAdvertiserConsentMode,pr()&&(a.active=!0),!rr(c)||or()||pr()){a.tcfPolicyVersion=c.tcfPolicyVersion;var d;if(c.gdprApplies===
!1){var e={},f;for(f in kr)kr.hasOwnProperty(f)&&(e[f]=!0);d=e;b.removeEventListener(c)}else if(rr(c)){var g={},h;for(h in kr)if(kr.hasOwnProperty(h))if(h==="1"){var m,n=c,p={op:!0};p=p===void 0?{}:p;m=jr(n)?n.gdprApplies===!1?!0:n.tcString==="tcunavailable"?!p.idpcApplies:(p.idpcApplies||n.gdprApplies!==void 0||p.op)&&(p.idpcApplies||typeof n.tcString==="string"&&n.tcString.length)?hr(n,"1",0):!0:!1;g["1"]=m}else g[h]=hr(c,h,kr[h]);d=g}if(d){a.tcString=c.tcString||"tcempty";a.purposes=d;var q={},
r=(q[J.m.U]=a.purposes["1"]?"granted":"denied",q);a.gdprApplies!==!0?(tp([J.m.U,J.m.Ka,J.m.V]),Um().active=!0):(r[J.m.Ka]=a.purposes["3"]&&a.purposes["4"]?"granted":"denied",typeof a.tcfPolicyVersion==="number"&&a.tcfPolicyVersion>=4?r[J.m.V]=a.purposes["1"]&&a.purposes["7"]?"granted":"denied":tp([J.m.V]),np(r,{eventId:0},{gdprApplies:a?a.gdprApplies:void 0,tcString:sr()||""}))}}else tp([J.m.U,J.m.Ka,J.m.V])})}catch(c){qr(a),tp([J.m.U,J.m.Ka,J.m.V]),Um().active=!0}}}
function qr(a){a.type="e";a.tcString="tcunavailable"}function rr(a){return a.eventStatus==="tcloaded"||a.eventStatus==="useractioncomplete"||a.eventStatus==="cmpuishown"}function or(){return x.gtag_enable_tcf_support===!0}function pr(){return lr().enableAdvertiserConsentMode===!0}function sr(){var a=lr();if(a.active)return a.tcString}function tr(){var a=lr();if(a.active&&a.gdprApplies!==void 0)return a.gdprApplies?"1":"0"}
function ur(a){if(!kr.hasOwnProperty(String(a)))return!0;var b=lr();return b.active&&b.purposes?!!b.purposes[String(a)]:!0};var vr=[J.m.U,J.m.ja,J.m.V,J.m.Ka],wr={},xr=(wr[J.m.U]=1,wr[J.m.ja]=2,wr);function yr(a){if(a===void 0)return 0;switch(O(a,J.m.Ga)){case void 0:return 1;case !1:return 3;default:return 2}}function zr(){return(E(183)?Zi.vp:Zi.wp).indexOf(oo())!==-1&&uc.globalPrivacyControl===!0}function Ar(a){if(zr())return!1;var b=yr(a);if(b===3)return!1;switch(cn(J.m.Ka)){case 1:case 3:return!0;case 2:return!1;case 4:return b===2;case 0:return!0;default:return!1}}
function Br(){return en()||!bn(J.m.U)||!bn(J.m.ja)}function Cr(){var a={},b;for(b in xr)xr.hasOwnProperty(b)&&(a[xr[b]]=cn(b));return"G1"+ef(a[1]||0)+ef(a[2]||0)}var Dr={},Er=(Dr[J.m.U]=0,Dr[J.m.ja]=1,Dr[J.m.V]=2,Dr[J.m.Ka]=3,Dr);function Fr(a){switch(a){case void 0:return 1;case !0:return 3;case !1:return 2;default:return 0}}
function Gr(a){for(var b="1",c=0;c<vr.length;c++){var d=b,e,f=vr[c],g=an.delegatedConsentTypes[f];e=g===void 0?0:Er.hasOwnProperty(g)?12|Er[g]:8;var h=Um();h.accessedAny=!0;var m=h.entries[f]||{};e=e<<2|Fr(m.implicit);b=d+(""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[e]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[Fr(m.declare)<<4|Fr(m.default)<<2|Fr(m.update)])}var n=b,p=(zr()?1:0)<<3,q=(en()?1:0)<<2,r=yr(a);b=n+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[p|
q|r];return b+=""+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[an.containerScopedDefaults.ad_storage<<4|an.containerScopedDefaults.analytics_storage<<2|an.containerScopedDefaults.ad_user_data]+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[(an.usedContainerScopedDefaults?1:0)<<2|an.containerScopedDefaults.ad_personalization]}
function Hr(){if(!bn(J.m.V))return"-";for(var a=Object.keys(Ho),b={},c=l(a),d=c.next();!d.done;d=c.next()){var e=d.value;b[e]=an.corePlatformServices[e]!==!1}for(var f="",g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;b[m]&&(f+=Ho[m])}(an.usedCorePlatformServices?an.selectedAllCorePlatformServices:1)&&(f+="o");return f||"-"}function Ir(){return qo()||(or()||pr())&&tr()==="1"?"1":"0"}function Jr(){return(qo()?!0:!(!or()&&!pr())&&tr()==="1")||!bn(J.m.V)}
function Kr(){var a="0",b="0",c;var d=lr();c=d.active?d.cmpId:void 0;typeof c==="number"&&c>=0&&c<=4095&&(a="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c>>6&63],b="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[c&63]);var e="0",f;var g=lr();f=g.active?g.tcfPolicyVersion:void 0;typeof f==="number"&&f>=0&&f<=63&&(e="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[f]);var h=0;qo()&&(h|=1);tr()==="1"&&(h|=2);or()&&(h|=4);var m;var n=lr();m=n.enableAdvertiserConsentMode!==
void 0?n.enableAdvertiserConsentMode?"1":"0":void 0;m==="1"&&(h|=8);Um().waitPeriodTimedOut&&(h|=16);return"1"+a+b+e+"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_"[h]}function Lr(){return oo()==="US-CO"};var Mr;function Nr(){if(xc===null)return 0;var a=ad();if(!a)return 0;var b=a.getEntriesByName(xc,"resource")[0];if(!b)return 0;switch(b.deliveryType){case "":return 1;case "cache":return 2;case "navigational-prefetch":return 3;default:return 0}}var Or={UA:1,AW:2,DC:3,G:4,GF:5,GT:12,GTM:14,HA:6,MC:7};
function Pr(a){a=a===void 0?{}:a;var b=lg.ctid.split("-")[0].toUpperCase(),c={ctid:lg.ctid,yj:Wj,Cj:Vj,dm:Bm.qe?2:1,Cq:a.Cm,we:lg.canonicalContainerId};if(E(210)){var d;c.sq=(d=Im())==null?void 0:d.canonicalContainerId}if(E(204)){var e;c.No=(e=Mr)!=null?e:Mr=Nr()}c.we!==a.Pa&&(c.Pa=a.Pa);var f=Gm();c.om=f?f.canonicalContainerId:void 0;ek?(c.Uc=Or[b],c.Uc||(c.Uc=0)):c.Uc=fk?13:10;Qj.C?(c.Ah=0,c.Ql=2):c.Ah=Qj.N?1:3;var g={6:!1};Qj.H===2?g[7]=!0:Qj.H===1&&(g[2]=!0);if(xc){var h=Pk(Vk(xc),"host");h&&
(g[8]=h.match(/^(www\.)?googletagmanager\.com$/)===null)}c.Sl=g;return hf(c,a.oh)}
function Qr(){if(!E(192))return Pr();if(E(193))return hf({yj:Wj,Cj:Vj});var a=lg.ctid.split("-")[0].toUpperCase(),b={ctid:lg.ctid,yj:Wj,Cj:Vj,dm:Bm.qe?2:1,we:lg.canonicalContainerId},c=Gm();b.om=c?c.canonicalContainerId:void 0;ek?(b.Uc=Or[a],b.Uc||(b.Uc=0)):b.Uc=fk?13:10;Qj.C?(b.Ah=0,b.Ql=2):b.Ah=Qj.N?1:3;var d={6:!1};Qj.H===2?d[7]=!0:Qj.H===1&&(d[2]=!0);if(xc){var e=Pk(Vk(xc),"host");e&&(d[8]=e.match(/^(www\.)?googletagmanager\.com$/)===null)}b.Sl=d;return hf(b)};function Rr(a,b,c,d){var e,f=Number(a.Cc!=null?a.Cc:void 0);f!==0&&(e=new Date((b||Ab())+1E3*(f||7776E3)));return{path:a.path,domain:a.domain,flags:a.flags,encode:!!c,expires:e,Ec:d}};var Sr=["ad_storage","ad_user_data"];function Tr(a,b){if(!a)return fb("TAGGING",32),10;if(b===null||b===void 0||b==="")return fb("TAGGING",33),11;var c=Ur(!1);if(c.error!==0)return fb("TAGGING",34),c.error;if(!c.value)return fb("TAGGING",35),2;c.value[a]=b;var d=Vr(c);d!==0&&fb("TAGGING",36);return d}
function Wr(a){if(!a)return fb("TAGGING",27),{error:10};var b=Ur();if(b.error!==0)return fb("TAGGING",29),b;if(!b.value)return fb("TAGGING",30),{error:2};if(!(a in b.value))return fb("TAGGING",31),{value:void 0,error:15};var c=b.value[a];return c===null||c===void 0||c===""?(fb("TAGGING",28),{value:void 0,error:11}):{value:c,error:0}}
function Ur(a){a=a===void 0?!0:a;if(!bn(Sr))return fb("TAGGING",43),{error:3};try{if(!x.localStorage)return fb("TAGGING",44),{error:1}}catch(f){return fb("TAGGING",45),{error:14}}var b={schema:"gcl",version:1},c=void 0;try{c=x.localStorage.getItem("_gcl_ls")}catch(f){return fb("TAGGING",46),{error:13}}try{if(c){var d=JSON.parse(c);if(d&&typeof d==="object")b=d;else return fb("TAGGING",47),{error:12}}}catch(f){return fb("TAGGING",48),{error:8}}if(b.schema!=="gcl")return fb("TAGGING",49),{error:4};
if(b.version!==1)return fb("TAGGING",50),{error:5};try{var e=Xr(b);a&&e&&Vr({value:b,error:0})}catch(f){return fb("TAGGING",48),{error:8}}return{value:b,error:0}}
function Xr(a){if(!a||typeof a!=="object")return!1;if("expires"in a&&"value"in a){var b;typeof a.expires==="number"?b=a.expires:b=typeof a.expires==="string"?Number(a.expires):NaN;if(isNaN(b)||!(Date.now()<=b))return a.value=null,a.error=9,fb("TAGGING",54),!0}else{for(var c=!1,d=l(Object.keys(a)),e=d.next();!e.done;e=d.next())c=Xr(a[e.value])||c;return c}return!1}
function Vr(a){if(a.error)return a.error;if(!a.value)return fb("TAGGING",42),2;var b=a.value,c;try{c=JSON.stringify(b)}catch(d){return fb("TAGGING",52),6}try{x.localStorage.setItem("_gcl_ls",c)}catch(d){return fb("TAGGING",53),7}return 0};var Yr={oj:"value",sb:"conversionCount"},Zr={bm:9,wm:10,oj:"timeouts",sb:"timeouts"},$r=[Yr,Zr];function as(a){if(!bs(a))return{};var b=cs($r),c=b[a.sb];if(c===void 0||c===-1)return b;var d={},e=ma(Object,"assign").call(Object,{},b,(d[a.sb]=c+1,d));return ds(e)?e:b}
function cs(a){var b;a:{var c=Wr("gcl_ctr");if(c.error===0&&c.value&&typeof c.value==="object"){var d=c.value;try{b="value"in d&&typeof d.value==="object"?d.value:void 0;break a}catch(p){}}b=void 0}for(var e=b,f={},g=l(a),h=g.next();!h.done;h=g.next()){var m=h.value;if(e&&bs(m)){var n=e[m.oj];n===void 0||Number.isNaN(n)?f[m.sb]=-1:f[m.sb]=Number(n)}else f[m.sb]=-1}return f}
function es(){var a=as(Yr),b=a[Yr.sb];if(b===void 0||b<=0)return"";var c=a[Zr.sb];return c===void 0||c<0?b.toString():[b.toString(),c.toString()].join("~")}function ds(a,b){b=b||{};for(var c=Ab(),d=Rr(b,c,!0),e={},f=l($r),g=f.next();!g.done;g=f.next()){var h=g.value,m=a[h.sb];m!==void 0&&m!==-1&&(e[h.oj]=m)}e.creationTimeMs=c;return Tr("gcl_ctr",{value:e,expires:Number(d.expires)})===0?!0:!1}function bs(a){return bn(["ad_storage","ad_user_data"])?!a.wm||Ra(a.wm):!1}
function fs(a){return bn(["ad_storage","ad_user_data"])?!a.bm||Ra(a.bm):!1};function gs(a){var b=1,c,d,e;if(a)for(b=0,d=a.length-1;d>=0;d--)e=a.charCodeAt(d),b=(b<<6&268435455)+e+(e<<14),c=b&266338304,b=c!==0?b^c>>21:b;return b};var hs={O:{po:0,Ij:1,sg:2,Oj:3,Jh:4,Mj:5,Nj:6,Pj:7,Kh:8,Tk:9,Sk:10,ui:11,Uk:12,Vg:13,Xk:14,Lf:15,no:16,ue:17,Ni:18,Oi:19,Pi:20,Hl:21,Qi:22,Mh:23,Wj:24}};hs.O[hs.O.po]="RESERVED_ZERO";hs.O[hs.O.Ij]="ADS_CONVERSION_HIT";hs.O[hs.O.sg]="CONTAINER_EXECUTE_START";hs.O[hs.O.Oj]="CONTAINER_SETUP_END";hs.O[hs.O.Jh]="CONTAINER_SETUP_START";hs.O[hs.O.Mj]="CONTAINER_BLOCKING_END";hs.O[hs.O.Nj]="CONTAINER_EXECUTE_END";hs.O[hs.O.Pj]="CONTAINER_YIELD_END";hs.O[hs.O.Kh]="CONTAINER_YIELD_START";hs.O[hs.O.Tk]="EVENT_EXECUTE_END";
hs.O[hs.O.Sk]="EVENT_EVALUATION_END";hs.O[hs.O.ui]="EVENT_EVALUATION_START";hs.O[hs.O.Uk]="EVENT_SETUP_END";hs.O[hs.O.Vg]="EVENT_SETUP_START";hs.O[hs.O.Xk]="GA4_CONVERSION_HIT";hs.O[hs.O.Lf]="PAGE_LOAD";hs.O[hs.O.no]="PAGEVIEW";hs.O[hs.O.ue]="SNIPPET_LOAD";hs.O[hs.O.Ni]="TAG_CALLBACK_ERROR";hs.O[hs.O.Oi]="TAG_CALLBACK_FAILURE";hs.O[hs.O.Pi]="TAG_CALLBACK_SUCCESS";hs.O[hs.O.Hl]="TAG_EXECUTE_END";hs.O[hs.O.Qi]="TAG_EXECUTE_START";hs.O[hs.O.Mh]="CUSTOM_PERFORMANCE_START";hs.O[hs.O.Wj]="CUSTOM_PERFORMANCE_END";var is=[],js={},ks={};var ls=["2"];function ms(a){return a.origin!=="null"};function ns(a,b,c){for(var d=[],e=b.split(";"),f=function(p){return Ra(11)?p.trim():p.replace(/^\s*|\s*$/g,"")},g=0;g<e.length;g++){var h=e[g].split("="),m=f(h[0]);if(m&&m===a){var n=f(h.slice(1).join("="));n&&c&&(n=decodeURIComponent(n));d.push(n)}}return d};var os;function ps(a,b,c,d){return qs(d)?ns(a,String(b||rs()),c):[]}function ss(a,b,c,d,e){if(qs(e)){var f=ts(a,d,e);if(f.length===1)return f[0];if(f.length!==0){f=us(f,function(g){return g.Yo},b);if(f.length===1)return f[0];f=us(f,function(g){return g.bq},c);return f[0]}}}function vs(a,b,c,d){var e=rs(),f=window;ms(f)&&(f.document.cookie=a);var g=rs();return e!==g||c!==void 0&&ps(b,g,!1,d).indexOf(c)>=0}
function ws(a,b,c,d){function e(w,y,A){if(A==null)return delete h[y],w;h[y]=A;return w+"; "+y+"="+A}function f(w,y){if(y==null)return w;h[y]=!0;return w+"; "+y}if(!qs(c.Ec))return 2;var g;b==null?g=a+"=deleted; expires="+(new Date(0)).toUTCString():(c.encode&&(b=encodeURIComponent(b)),b=xs(b),g=a+"="+b);var h={};g=e(g,"path",c.path);var m;c.expires instanceof Date?m=c.expires.toUTCString():c.expires!=null&&(m=""+c.expires);g=e(g,"expires",m);g=e(g,"max-age",c.Wp);g=e(g,"samesite",c.tq);c.secure&&
(g=f(g,"secure"));var n=c.domain;if(n&&n.toLowerCase()==="auto"){for(var p=ys(),q=void 0,r=!1,t=0;t<p.length;++t){var u=p[t]!=="none"?p[t]:void 0,v=e(g,"domain",u);v=f(v,c.flags);try{d&&d(a,h)}catch(w){q=w;continue}r=!0;if(!zs(u,c.path)&&vs(v,a,b,c.Ec))return Ra(15)&&(os=u),0}if(q&&!r)throw q;return 1}n&&n.toLowerCase()!=="none"&&(g=e(g,"domain",n));g=f(g,c.flags);d&&d(a,h);return zs(n,c.path)?1:vs(g,a,b,c.Ec)?0:1}
function As(a,b,c){c.path==null&&(c.path="/");c.domain||(c.domain="auto");if(is.includes("2")){var d;(d=ad())==null||d.mark("2-"+hs.O.Mh+"-"+(ks["2"]||0))}var e=ws(a,b,c);if(is.includes("2")){var f="2-"+hs.O.Wj+"-"+(ks["2"]||0),g={start:"2-"+hs.O.Mh+"-"+(ks["2"]||0),end:f},h;(h=ad())==null||h.mark(f);var m,n,p=(n=(m=ad())==null?void 0:m.measure(f,g))==null?void 0:n.duration;p!==void 0&&(ks["2"]=(ks["2"]||0)+1,js["2"]=p+(js["2"]||0))}return e}
function us(a,b,c){for(var d=[],e=[],f,g=0;g<a.length;g++){var h=a[g],m=b(h);m===c?d.push(h):f===void 0||m<f?(e=[h],f=m):m===f&&e.push(h)}return d.length>0?d:e}function ts(a,b,c){for(var d=[],e=ps(a,void 0,void 0,c),f=0;f<e.length;f++){var g=e[f].split("."),h=g.shift();if(!b||!h||b.indexOf(h)!==-1){var m=g.shift();if(m){var n=m.split("-");d.push({Po:e[f],Qo:g.join("."),Yo:Number(n[0])||1,bq:Number(n[1])||1})}}}return d}function xs(a){a&&a.length>1200&&(a=a.substring(0,1200));return a}
var Bs=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,Cs=/(^|\.)doubleclick\.net$/i;function zs(a,b){return a!==void 0&&(Cs.test(window.document.location.hostname)||b==="/"&&Bs.test(a))}function Ds(a){if(!a)return 1;var b=a;Ra(6)&&a==="none"&&(b=window.document.location.hostname);b=b.indexOf(".")===0?b.substring(1):b;return b.split(".").length}function Es(a){if(!a||a==="/")return 1;a[0]!=="/"&&(a="/"+a);a[a.length-1]!=="/"&&(a+="/");return a.split("/").length-1}
function Fs(a,b){var c=""+Ds(a),d=Es(b);d>1&&(c+="-"+d);return c}
var rs=function(){return ms(window)?window.document.cookie:""},qs=function(a){return a&&Ra(7)?(Array.isArray(a)?a:[a]).every(function(b){return dn(b)&&bn(b)}):!0},ys=function(){var a=os,b=[];a&&b.push(a);var c=window.document.location.hostname.split(".");if(c.length===4){var d=c[c.length-1];if(Number(d).toString()===d)return["none"]}for(var e=c.length-2;e>=0;e--){var f=c.slice(e).join(".");f!==a&&b.push(f)}var g=window.document.location.hostname;Cs.test(g)||Bs.test(g)||b.push("none");return b};function Gs(a){var b=Math.round(Math.random()*2147483647);return a?String(b^gs(a)&2147483647):String(b)}function Hs(a){return[Gs(a),Math.round(Ab()/1E3)].join(".")}function Is(a,b,c,d,e){var f=Ds(b),g;return(g=ss(a,f,Es(c),d,e))==null?void 0:g.Qo};var Js;function Ks(){function a(g){c(g.target||g.srcElement||{})}function b(g){d(g.target||g.srcElement||{})}var c=Ls,d=Ms,e=Ns();if(!e.init){Lc(z,"mousedown",a);Lc(z,"keyup",a);Lc(z,"submit",b);var f=HTMLFormElement.prototype.submit;HTMLFormElement.prototype.submit=function(){d(this);f.call(this)};e.init=!0}}function Os(a,b,c,d,e){var f={callback:a,domains:b,fragment:c===2,placement:c,forms:d,sameHost:e};Ns().decorators.push(f)}
function Ps(a,b,c){for(var d=Ns().decorators,e={},f=0;f<d.length;++f){var g=d[f],h;if(h=!c||g.forms)a:{var m=g.domains,n=a,p=!!g.sameHost;if(m&&(p||n!==z.location.hostname))for(var q=0;q<m.length;q++)if(m[q]instanceof RegExp){if(m[q].test(n)){h=!0;break a}}else if(n.indexOf(m[q])>=0||p&&m[q].indexOf(n)>=0){h=!0;break a}h=!1}if(h){var r=g.placement;r===void 0&&(r=g.fragment?2:1);r===b&&Eb(e,g.callback())}}return e}
function Ns(){var a=yc("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Qs=/(.*?)\*(.*?)\*(.*)/,Rs=/^https?:\/\/([^\/]*?)\.?cdn\.ampproject\.org\/?(.*)/,Ss=/^(?:www\.|m\.|amp\.)+/,Ts=/([^?#]+)(\?[^#]*)?(#.*)?/;function Us(a){var b=Ts.exec(a);if(b)return{uj:b[1],query:b[2],fragment:b[3]}}function Vs(a){return new RegExp("(.*?)(^|&)"+a+"=([^&]*)&?(.*)")}
function Ws(a,b){var c=[uc.userAgent,(new Date).getTimezoneOffset(),uc.userLanguage||uc.language,Math.floor(Ab()/60/1E3)-(b===void 0?0:b),a].join("*"),d;if(!(d=Js)){for(var e=Array(256),f=0;f<256;f++){for(var g=f,h=0;h<8;h++)g=g&1?g>>>1^3988292384:g>>>1;e[f]=g}d=e}Js=d;for(var m=4294967295,n=0;n<c.length;n++)m=m>>>8^Js[(m^c.charCodeAt(n))&255];return((m^-1)>>>0).toString(36)}
function Xs(a){return function(b){var c=Vk(x.location.href),d=c.search.replace("?",""),e=Mk(d,"_gl",!1,!0)||"";b.query=Ys(e)||{};var f=Pk(c,"fragment"),g;var h=-1;if(Gb(f,"_gl="))h=4;else{var m=f.indexOf("&_gl=");m>0&&(h=m+3+2)}if(h<0)g=void 0;else{var n=f.indexOf("&",h);g=n<0?f.substring(h):f.substring(h,n)}b.fragment=Ys(g||"")||{};a&&Zs(c,d,f)}}function $s(a,b){var c=Vs(a).exec(b),d=b;if(c){var e=c[2],f=c[4];d=c[1];f&&(d=d+e+f)}return d}
function Zs(a,b,c){function d(g,h){var m=$s("_gl",g);m.length&&(m=h+m);return m}if(tc&&tc.replaceState){var e=Vs("_gl");if(e.test(b)||e.test(c)){var f=Pk(a,"path");b=d(b,"?");c=d(c,"#");tc.replaceState({},"",""+f+b+c)}}}function at(a,b){var c=Xs(!!b),d=Ns();d.data||(d.data={query:{},fragment:{}},c(d.data));var e={},f=d.data;f&&(Eb(e,f.query),a&&Eb(e,f.fragment));return e}
var Ys=function(a){try{var b=bt(a,3);if(b!==void 0){for(var c={},d=b?b.split("*"):[],e=0;e+1<d.length;e+=2){var f=d[e],g=db(d[e+1]);c[f]=g}fb("TAGGING",6);return c}}catch(h){fb("TAGGING",8)}};function bt(a,b){if(a){var c;a:{for(var d=a,e=0;e<3;++e){var f=Qs.exec(d);if(f){c=f;break a}d=Ok(d)||""}c=void 0}var g=c;if(g&&g[1]==="1"){var h=g[3],m;a:{for(var n=g[2],p=0;p<b;++p)if(n===Ws(h,p)){m=!0;break a}m=!1}if(m)return h;fb("TAGGING",7)}}}
function ct(a,b,c,d,e){function f(p){p=$s(a,p);var q=p.charAt(p.length-1);p&&q!=="&"&&(p+="&");return p+n}d=d===void 0?!1:d;e=e===void 0?!1:e;var g=Us(c);if(!g)return"";var h=g.query||"",m=g.fragment||"",n=a+"="+b;d?m.substring(1).length!==0&&e||(m="#"+f(m.substring(1))):h="?"+f(h.substring(1));return""+g.uj+h+m}
function dt(a,b){function c(n,p,q){var r;a:{for(var t in n)if(n.hasOwnProperty(t)){r=!0;break a}r=!1}if(r){var u,v=[],w;for(w in n)if(n.hasOwnProperty(w)){var y=n[w];y!==void 0&&y===y&&y!==null&&y.toString()!=="[object Object]"&&(v.push(w),v.push(cb(String(y))))}var A=v.join("*");u=["1",Ws(A),A].join("*");d?(Ra(3)||Ra(1)||!p)&&et("_gl",u,a,p,q):ft("_gl",u,a,p,q)}}var d=(a.tagName||"").toUpperCase()==="FORM",e=Ps(b,1,d),f=Ps(b,2,d),g=Ps(b,4,d),h=Ps(b,3,d);c(e,!1,!1);c(f,!0,!1);Ra(1)&&c(g,!0,!0);for(var m in h)h.hasOwnProperty(m)&&
gt(m,h[m],a)}function gt(a,b,c){c.tagName.toLowerCase()==="a"?ft(a,b,c):c.tagName.toLowerCase()==="form"&&et(a,b,c)}function ft(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;var f;if(f=c.href){var g;if(!(g=!Ra(4)||d)){var h=x.location.href,m=Us(c.href),n=Us(h);g=!(m&&n&&m.uj===n.uj&&m.query===n.query&&m.fragment)}f=g}if(f){var p=ct(a,b,c.href,d,e);jc.test(p)&&(c.href=p)}}
function et(a,b,c,d,e){d=d===void 0?!1:d;e=e===void 0?!1:e;if(c){var f=c.getAttribute("action")||"";if(f){var g=(c.method||"").toLowerCase();if(g!=="get"||d){if(g==="get"||g==="post"){var h=ct(a,b,f,d,e);jc.test(h)&&(c.action=h)}}else{for(var m=c.childNodes||[],n=!1,p=0;p<m.length;p++){var q=m[p];if(q.name===a){q.setAttribute("value",b);n=!0;break}}if(!n){var r=z.createElement("input");r.setAttribute("type","hidden");r.setAttribute("name",a);r.setAttribute("value",b);c.appendChild(r)}}}}}
function Ls(a){try{var b;a:{for(var c=a,d=100;c&&d>0;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var f=e.protocol;f!=="http:"&&f!=="https:"||dt(e,e.hostname)}}catch(g){}}function Ms(a){try{var b=a.getAttribute("action");if(b){var c=Pk(Vk(b),"host");dt(a,c)}}catch(d){}}function ht(a,b,c,d){Ks();var e=c==="fragment"?2:1;d=!!d;Os(a,b,e,d,!1);e===2&&fb("TAGGING",23);d&&fb("TAGGING",24)}
function it(a,b){Ks();Os(a,[Rk(x.location,"host",!0)],b,!0,!0)}function jt(){var a=z.location.hostname,b=Rs.exec(z.referrer);if(!b)return!1;var c=b[2],d=b[1],e="";if(c){var f=c.split("/"),g=f[1];e=g==="s"?Ok(f[2])||"":Ok(g)||""}else if(d){if(d.indexOf("xn--")===0)return!1;e=d.replace(/-/g,".").replace(/\.\./g,"-")}var h=a.replace(Ss,""),m=e.replace(Ss,""),n;if(!(n=h===m)){var p="."+m;n=h.length>=p.length&&h.substring(h.length-p.length,h.length)===p}return n}
function kt(a,b){return a===!1?!1:a||b||jt()};var lt=["1"],mt={},nt={};function ot(a,b){b=b===void 0?!0:b;var c=pt(a.prefix);if(mt[c])qt(a);else if(rt(c,a.path,a.domain)){var d=nt[pt(a.prefix)]||{id:void 0,zh:void 0};b&&st(a,d.id,d.zh);qt(a)}else{var e=Xk("auiddc");if(e)fb("TAGGING",17),mt[c]=e;else if(b){var f=pt(a.prefix),g=Hs();tt(f,g,a);rt(c,a.path,a.domain);qt(a,!0)}}}
function qt(a,b){if((b===void 0?0:b)&&bs(Yr)){var c=Ur(!1);c.error!==0?fb("TAGGING",38):c.value?"gcl_ctr"in c.value?(delete c.value.gcl_ctr,Vr(c)!==0&&fb("TAGGING",41)):fb("TAGGING",40):fb("TAGGING",39)}if(fs(Yr)&&cs([Yr])[Yr.sb]===-1){for(var d={},e=(d[Yr.sb]=0,d),f=l($r),g=f.next();!g.done;g=f.next()){var h=g.value;h!==Yr&&fs(h)&&(e[h.sb]=0)}ds(e,a)}}
function st(a,b,c){var d=pt(a.prefix),e=mt[d];if(e){var f=e.split(".");if(f.length===2){var g=Number(f[1])||0;if(g){var h=e;b&&(h=e+"."+b+"."+(c?c:Math.floor(Ab()/1E3)));tt(d,h,a,g*1E3)}}}}function tt(a,b,c,d){var e;e=["1",Fs(c.domain,c.path),b].join(".");var f=Rr(c,d);f.Ec=ut();As(a,e,f)}function rt(a,b,c){var d=Is(a,b,c,lt,ut());if(!d)return!1;vt(a,d);return!0}
function vt(a,b){var c=b.split(".");c.length===5?(mt[a]=c.slice(0,2).join("."),nt[a]={id:c.slice(2,4).join("."),zh:Number(c[4])||0}):c.length===3?nt[a]={id:c.slice(0,2).join("."),zh:Number(c[2])||0}:mt[a]=b}function pt(a){return(a||"_gcl")+"_au"}function wt(a){function b(){bn(c)&&a()}var c=ut();hn(function(){b();bn(c)||jn(b,c)},c)}
function xt(a){var b=at(!0),c=pt(a.prefix);wt(function(){var d=b[c];if(d){vt(c,d);var e=Number(mt[c].split(".")[1])*1E3;if(e){fb("TAGGING",16);var f=Rr(a,e);f.Ec=ut();var g=["1",Fs(a.domain,a.path),d].join(".");As(c,g,f)}}})}function zt(a,b,c,d,e){e=e||{};var f=function(){var g={},h=Is(a,e.path,e.domain,lt,ut());h&&(g[a]=h);return g};wt(function(){ht(f,b,c,d)})}function ut(){return["ad_storage","ad_user_data"]};function At(a){for(var b=[],c=z.cookie.split(";"),d=new RegExp("^\\s*"+(a||"_gac")+"_(UA-\\d+-\\d+)=\\s*(.+?)\\s*$"),e=0;e<c.length;e++){var f=c[e].match(d);f&&b.push({Fj:f[1],value:f[2],timestamp:Number(f[2].split(".")[1])||0})}b.sort(function(g,h){return h.timestamp-g.timestamp});return b}
function Bt(a,b){var c=At(a),d={};if(!c||!c.length)return d;for(var e=0;e<c.length;e++){var f=c[e].value.split(".");if(!(f[0]!=="1"||b&&f.length<3||!b&&f.length!==3)&&Number(f[1])){d[c[e].Fj]||(d[c[e].Fj]=[]);var g={version:f[0],timestamp:Number(f[1])*1E3,gclid:f[2]};b&&f.length>3&&(g.labels=f.slice(3));d[c[e].Fj].push(g)}}return d};var Ct={},Dt=(Ct.k={ba:/^[\w-]+$/},Ct.b={ba:/^[\w-]+$/,zj:!0},Ct.i={ba:/^[1-9]\d*$/},Ct.h={ba:/^\d+$/},Ct.t={ba:/^[1-9]\d*$/},Ct.d={ba:/^[A-Za-z0-9_-]+$/},Ct.j={ba:/^\d+$/},Ct.u={ba:/^[1-9]\d*$/},Ct.l={ba:/^[01]$/},Ct.o={ba:/^[1-9]\d*$/},Ct.g={ba:/^[01]$/},Ct.s={ba:/^.+$/},Ct);var Et={},It=(Et[5]={Gh:{2:Ft},nj:"2",ph:["k","i","b","u"]},Et[4]={Gh:{2:Ft,GCL:Gt},nj:"2",ph:["k","i","b"]},Et[2]={Gh:{GS2:Ft,GS1:Ht},nj:"GS2",ph:"sogtjlhd".split("")},Et);function Jt(a,b,c){var d=It[b];if(d){var e=a.split(".")[0];c==null||c(e);if(e){var f=d.Gh[e];if(f)return f(a,b)}}}
function Ft(a,b){var c=a.split(".");if(c.length===3){var d=c[2];if(d.indexOf("$")===-1&&d.indexOf("%24")!==-1)try{d=decodeURIComponent(d)}catch(t){}var e={},f=It[b];if(f){for(var g=f.ph,h=l(d.split("$")),m=h.next();!m.done;m=h.next()){var n=m.value,p=n[0];if(g.indexOf(p)!==-1)try{var q=decodeURIComponent(n.substring(1)),r=Dt[p];r&&(r.zj?(e[p]=e[p]||[],e[p].push(q)):e[p]=q)}catch(t){}}return e}}}function Kt(a,b,c){var d=It[b];if(d)return[d.nj,c||"1",Lt(a,b)].join(".")}
function Lt(a,b){var c=It[b];if(c){for(var d=[],e=l(c.ph),f=e.next();!f.done;f=e.next()){var g=f.value,h=Dt[g];if(h){var m=a[g];if(m!==void 0)if(h.zj&&Array.isArray(m))for(var n=l(m),p=n.next();!p.done;p=n.next())d.push(encodeURIComponent(""+g+p.value));else d.push(encodeURIComponent(""+g+m))}}return d.join("$")}}function Gt(a){var b=a.split(".");b.shift();var c=b.shift(),d=b.shift(),e={};return e.k=d,e.i=c,e.b=b,e}
function Ht(a){var b=a.split(".").slice(2);if(!(b.length<5||b.length>7)){var c={};return c.s=b[0],c.o=b[1],c.g=b[2],c.t=b[3],c.j=b[4],c.l=b[5],c.h=b[6],c}};var Mt=new Map([[5,"ad_storage"],[4,["ad_storage","ad_user_data"]],[2,"analytics_storage"]]);function Nt(a,b,c){if(It[b]){for(var d=[],e=ps(a,void 0,void 0,Mt.get(b)),f=l(e),g=f.next();!g.done;g=f.next()){var h=Jt(g.value,b,c);h&&d.push(Ot(h))}return d}}function Pt(a,b,c,d,e){d=d||{};var f=Fs(d.domain,d.path),g=Kt(b,c,f);if(!g)return 1;var h=Rr(d,e,void 0,Mt.get(c));return As(a,g,h)}function Qt(a,b){var c=b.ba;return typeof c==="function"?c(a):c.test(a)}
function Ot(a){for(var b=l(Object.keys(a)),c=b.next(),d={};!c.done;d={Tf:void 0},c=b.next()){var e=c.value,f=a[e];d.Tf=Dt[e];d.Tf?d.Tf.zj?a[e]=Array.isArray(f)?f.filter(function(g){return function(h){return Qt(h,g.Tf)}}(d)):void 0:typeof f==="string"&&Qt(f,d.Tf)||(a[e]=void 0):a[e]=void 0}return a};var Rt=function(){this.value=0};Rt.prototype.set=function(a){return this.value|=1<<a};var St=function(a,b){b<=0||(a.value|=1<<b-1)};Rt.prototype.get=function(){return this.value};Rt.prototype.clear=function(a){this.value&=~(1<<a)};Rt.prototype.clearAll=function(){this.value=0};Rt.prototype.equals=function(a){return this.value===a.value};function Tt(a){if(a)try{return new Uint8Array(atob(a.replace(/-/g,"+").replace(/_/g,"/")).split("").map(function(b){return b.charCodeAt(0)}))}catch(b){}}function Ut(a,b){var c=0,d=0,e,f=b;do{if(f>=a.length)return;e=a[f++];c|=(e&127)<<d;d+=7}while(e&128);return[c,f]};function Vt(){var a=String,b=x.location.hostname,c=x.location.pathname,d=b=Ob(b);d.split(".").length>2&&(d=d.replace(/^(www[0-9]*|web|ftp|wap|home|m|w|amp|mobile)\./,""));b=d;c=Ob(c);var e=c.split(";")[0];e=e.replace(/\/(ar|slp|web|index)?\/?$/,"");return a(gs((""+b+e).toLowerCase()))};var Wt={},Xt=(Wt.gclid=!0,Wt.dclid=!0,Wt.gbraid=!0,Wt.wbraid=!0,Wt),Yt=/^\w+$/,Zt=/^[\w-]+$/,$t={},au=($t.aw="_aw",$t.dc="_dc",$t.gf="_gf",$t.gp="_gp",$t.gs="_gs",$t.ha="_ha",$t.ag="_ag",$t.gb="_gb",$t),bu=/^(?:www\.)?google(?:\.com?)?(?:\.[a-z]{2}t?)?$/,cu=/^www\.googleadservices\.com$/;function du(){return["ad_storage","ad_user_data"]}function eu(a){return!Ra(7)||bn(a)}function fu(a,b){function c(){var d=eu(b);d&&a();return d}hn(function(){c()||jn(c,b)},b)}
function gu(a){return hu(a).map(function(b){return b.gclid})}function iu(a){return ju(a).filter(function(b){return b.gclid}).map(function(b){return b.gclid})}function ju(a){var b=ku(a.prefix),c=lu("gb",b),d=lu("ag",b);if(!d||!c)return[];var e=function(h){return function(m){m.type=h;return m}},f=hu(c).map(e("gb")),g=mu(d).map(e("ag"));return f.concat(g).sort(function(h,m){return m.timestamp-h.timestamp})}
function nu(a,b,c,d,e,f){var g=pb(a,function(h){return h.gclid===c});g?(g.timestamp<d&&(g.timestamp=d,g.Fd=f),g.labels=ou(g.labels||[],e||[])):a.push({version:b,gclid:c,timestamp:d,labels:e,Fd:f})}function mu(a){for(var b=Nt(a,5)||[],c=[],d=l(b),e=d.next();!e.done;e=d.next()){var f=e.value,g=f,h=pu(f);h&&nu(c,"2",g.k,h,g.b||[],f.u)}return c.sort(function(m,n){return n.timestamp-m.timestamp})}
function hu(a){for(var b=[],c=ps(a,z.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);if(f!=null){var g=f;nu(b,g.version,g.gclid,g.timestamp,g.labels)}}b.sort(function(h,m){return m.timestamp-h.timestamp});return ru(b)}function su(a,b){for(var c=[],d=l(a),e=d.next();!e.done;e=d.next()){var f=e.value;c.includes(f)||c.push(f)}for(var g=l(b),h=g.next();!h.done;h=g.next()){var m=h.value;c.includes(m)||c.push(m)}return c}
function tu(a,b,c){c=c===void 0?!1:c;for(var d,e,f=l(a),g=f.next();!g.done;g=f.next()){var h=g.value;if(h.gclid===b.gclid){d=h;break}h.Ja&&b.Ja&&h.Ja.equals(b.Ja)&&(e=h)}if(d){var m,n,p=(m=d.Ja)!=null?m:new Rt,q=(n=b.Ja)!=null?n:new Rt;p.value|=q.value;d.Ja=p;d.timestamp<b.timestamp&&(d.timestamp=b.timestamp,d.Fd=b.Fd);d.labels=su(d.labels||[],b.labels||[]);d.Db=su(d.Db||[],b.Db||[])}else c&&e?ma(Object,"assign").call(Object,e,b):a.push(b)}
function uu(a){if(!a)return new Rt;var b=new Rt;if(a===1)return St(b,2),St(b,3),b;St(b,a);return b}
function vu(){var a=Wr("gclid");if(!a||a.error||!a.value||typeof a.value!=="object")return null;var b=a.value;try{if(!("value"in b&&b.value)||typeof b.value!=="object")return null;var c=b.value,d=c.value;if(!d||!d.match(Zt))return null;var e=c.linkDecorationSource,f=c.linkDecorationSources,g=new Rt;typeof e==="number"?g=uu(e):typeof f==="number"&&(g.value=f);return{version:"",gclid:d,timestamp:Number(c.creationTimeMs)||0,labels:[],Ja:g,Db:[2]}}catch(h){return null}}
function wu(){var a=Wr("gcl_aw");if(a.error!==0)return null;try{return a.value.reduce(function(b,c){if(!c.value||typeof c.value!=="object")return b;var d=c.value,e=d.value;if(!e||!e.match(Zt))return b;var f=new Rt,g=d.linkDecorationSources;typeof g==="number"&&(f.value=g);b.push({version:"",gclid:e,timestamp:Number(d.creationTimeMs)||0,expires:Number(c.expires)||0,labels:[],Ja:f,Db:[2]});return b},[])}catch(b){return null}}
function xu(a){for(var b=[],c=ps(a,z.cookie,void 0,du()),d=l(c),e=d.next();!e.done;e=d.next()){var f=qu(e.value);f!=null&&(f.Fd=void 0,f.Ja=new Rt,f.Db=[1],tu(b,f))}var g=vu();g&&(g.Fd=void 0,g.Db=g.Db||[2],tu(b,g));if(Ra(13)){var h=wu();if(h)for(var m=l(h),n=m.next();!n.done;n=m.next()){var p=n.value;p.Fd=void 0;p.Db=p.Db||[2];tu(b,p)}}b.sort(function(q,r){return r.timestamp-q.timestamp});return ru(b)}
function ou(a,b){if(!a.length)return b;if(!b.length)return a;var c={};return a.concat(b).filter(function(d){return c.hasOwnProperty(d)?!1:c[d]=!0})}function ku(a){return a&&typeof a==="string"&&a.match(Yt)?a:"_gcl"}function yu(a,b){if(a){var c={value:a,Ja:new Rt};St(c.Ja,b);return c}}
function zu(a,b,c){var d=Vk(a),e=Pk(d,"query",!1,void 0,"gclsrc"),f=yu(Pk(d,"query",!1,void 0,"gclid"),c?4:2);if(b&&(!f||!e)){var g=d.hash.replace("#","");f||(f=yu(Mk(g,"gclid",!1),3));e||(e=Mk(g,"gclsrc",!1))}return!f||e!==void 0&&e!=="aw"&&e!=="aw.ds"?[]:[f]}
function Au(a,b){var c=Vk(a),d=Pk(c,"query",!1,void 0,"gclid"),e=Pk(c,"query",!1,void 0,"gclsrc"),f=Pk(c,"query",!1,void 0,"wbraid");f=Mb(f);var g=Pk(c,"query",!1,void 0,"gbraid"),h=Pk(c,"query",!1,void 0,"gad_source"),m=Pk(c,"query",!1,void 0,"dclid");if(b&&!(d&&e&&f&&g)){var n=c.hash.replace("#","");d=d||Mk(n,"gclid",!1);e=e||Mk(n,"gclsrc",!1);f=f||Mk(n,"wbraid",!1);g=g||Mk(n,"gbraid",!1);h=h||Mk(n,"gad_source",!1)}return Bu(d,e,m,f,g,h)}function Cu(){return Au(x.location.href,!0)}
function Bu(a,b,c,d,e,f){var g={},h=function(m,n){g[n]||(g[n]=[]);g[n].push(m)};g.gclid=a;g.gclsrc=b;g.dclid=c;if(a!==void 0&&a.match(Zt))switch(b){case void 0:h(a,"aw");break;case "aw.ds":h(a,"aw");h(a,"dc");break;case "ds":h(a,"dc");break;case "3p.ds":h(a,"dc");break;case "gf":h(a,"gf");break;case "ha":h(a,"ha")}c&&h(c,"dc");d!==void 0&&Zt.test(d)&&(g.wbraid=d,h(d,"gb"));e!==void 0&&Zt.test(e)&&(g.gbraid=e,h(e,"ag"));f!==void 0&&Zt.test(f)&&(g.gad_source=f,h(f,"gs"));return g}
function Du(a){for(var b=Cu(),c=!0,d=l(Object.keys(b)),e=d.next();!e.done;e=d.next())if(b[e.value]!==void 0){c=!1;break}c&&(b=Au(x.document.referrer,!1),b.gad_source=void 0);Eu(b,!1,a)}
function Fu(a){Du(a);var b=zu(x.location.href,!0,!1);b.length||(b=zu(x.document.referrer,!1,!0));a=a||{};Gu(a);if(b.length){var c=b[0],d=Ab(),e=Rr(a,d,!0),f=du(),g=function(){eu(f)&&e.expires!==void 0&&Tr("gclid",{value:{value:c.value,creationTimeMs:d,linkDecorationSources:c.Ja.get()},expires:Number(e.expires)})};hn(function(){g();eu(f)||jn(g,f)},f)}}
function Gu(a){var b;if(b=Ra(14)){var c=Hu();b=bu.test(c)||cu.test(c)||Iu()}if(b){var d;a:{for(var e=Vk(x.location.href),f=Nk(Pk(e,"query")),g=l(Object.keys(f)),h=g.next();!h.done;h=g.next()){var m=h.value;if(!Xt[m]){var n=f[m][0]||"",p;if(!n||n.length<50||n.length>200)p=!1;else{var q=Tt(n),r;if(q)c:{var t=q;if(t&&t.length!==0){var u=0;try{for(;u<t.length;){var v=Ut(t,u);if(v===void 0)break;var w=l(v),y=w.next().value,A=w.next().value,C=y,D=A,G=C&7;if(C>>3===16382){if(G!==0)break;var I=Ut(t,D);if(I===
void 0)break;r=l(I).next().value===1;break c}var M;d:{var T=void 0,da=t,N=D;switch(G){case 0:M=(T=Ut(da,N))==null?void 0:T[1];break d;case 1:M=N+8;break d;case 2:var W=Ut(da,N);if(W===void 0)break;var ia=l(W),ka=ia.next().value;M=ia.next().value+ka;break d;case 5:M=N+4;break d}M=void 0}if(M===void 0||M>t.length)break;u=M}}catch(X){}}r=!1}else r=!1;p=r}if(p){d=n;break a}}}d=void 0}var Y=d;Y&&Ju(Y,7,a)}}
function Ju(a,b,c){c=c||{};var d=Ab(),e=Rr(c,d,!0),f=du(),g=function(){if(eu(f)&&e.expires!==void 0){var h=wu()||[];tu(h,{version:"",gclid:a,timestamp:d,expires:Number(e.expires),Ja:uu(b)},!0);Tr("gcl_aw",h.map(function(m){return{value:{value:m.gclid,creationTimeMs:m.timestamp,linkDecorationSources:m.Ja?m.Ja.get():0},expires:Number(m.expires)}}))}};hn(function(){eu(f)?g():jn(g,f)},f)}
function Eu(a,b,c,d,e){c=c||{};e=e||[];var f=ku(c.prefix),g=d||Ab(),h=Math.round(g/1E3),m=du(),n=!1,p=!1,q=function(){if(eu(m)){var r=Rr(c,g,!0);r.Ec=m;for(var t=function(T,da){var N=lu(T,f);N&&(As(N,da,r),T!=="gb"&&(n=!0))},u=function(T){var da=["GCL",h,T];e.length>0&&da.push(e.join("."));return da.join(".")},v=l(["aw","dc","gf","ha","gp"]),w=v.next();!w.done;w=v.next()){var y=w.value;a[y]&&t(y,u(a[y][0]))}if(!n&&a.gb){var A=a.gb[0],C=lu("gb",f);!b&&hu(C).some(function(T){return T.gclid===A&&T.labels&&
T.labels.length>0})||t("gb",u(A))}}if(!p&&a.gbraid&&eu("ad_storage")&&(p=!0,!n)){var D=a.gbraid,G=lu("ag",f);if(b||!mu(G).some(function(T){return T.gclid===D&&T.labels&&T.labels.length>0})){var I={},M=(I.k=D,I.i=""+h,I.b=e,I);Pt(G,M,5,c,g)}}Ku(a,f,g,c)};hn(function(){q();eu(m)||jn(q,m)},m)}
function Ku(a,b,c,d){if(a.gad_source!==void 0&&eu("ad_storage")){var e=$c();if(e!=="r"&&e!=="h"){var f=a.gad_source,g=lu("gs",b);if(g){var h=Math.floor((Ab()-(Zc()||0))/1E3),m,n=Vt(),p={};m=(p.k=f,p.i=""+h,p.u=n,p);Pt(g,m,5,d,c)}}}}
function Lu(a,b){var c=at(!0);fu(function(){for(var d=ku(b.prefix),e=0;e<a.length;++e){var f=a[e];if(au[f]!==void 0){var g=lu(f,d),h=c[g];if(h){var m=Math.min(Mu(h),Ab()),n;b:{for(var p=m,q=ps(g,z.cookie,void 0,du()),r=0;r<q.length;++r)if(Mu(q[r])>p){n=!0;break b}n=!1}if(!n){var t=Rr(b,m,!0);t.Ec=du();As(g,h,t)}}}}Eu(Bu(c.gclid,c.gclsrc),!1,b)},du())}
function Nu(a){var b=["ag"],c=at(!0),d=ku(a.prefix);fu(function(){for(var e=0;e<b.length;++e){var f=lu(b[e],d);if(f){var g=c[f];if(g){var h=Jt(g,5);if(h){var m=pu(h);m||(m=Ab());var n;a:{for(var p=m,q=Nt(f,5),r=0;r<q.length;++r)if(pu(q[r])>p){n=!0;break a}n=!1}if(n)break;h.i=""+Math.round(m/1E3);Pt(f,h,5,a,m)}}}}},["ad_storage"])}function lu(a,b){var c=au[a];if(c!==void 0)return b+c}function Mu(a){return Ou(a.split(".")).length!==0?(Number(a.split(".")[1])||0)*1E3:0}
function pu(a){return a?(Number(a.i)||0)*1E3:0}function qu(a){var b=Ou(a.split("."));return b.length===0?null:{version:b[0],gclid:b[2],timestamp:(Number(b[1])||0)*1E3,labels:b.slice(3)}}function Ou(a){return a.length<3||a[0]!=="GCL"&&a[0]!=="1"||!/^\d+$/.test(a[1])||!Zt.test(a[2])?[]:a}
function Pu(a,b,c,d,e){if(Array.isArray(b)&&ms(x)){var f=ku(e),g=function(){for(var h={},m=0;m<a.length;++m){var n=lu(a[m],f);if(n){var p=ps(n,z.cookie,void 0,du());p.length&&(h[n]=p.sort()[p.length-1])}}return h};fu(function(){ht(g,b,c,d)},du())}}
function Qu(a,b,c,d){if(Array.isArray(a)&&ms(x)){var e=["ag"],f=ku(d),g=function(){for(var h={},m=0;m<e.length;++m){var n=lu(e[m],f);if(!n)return{};var p=Nt(n,5);if(p.length){var q=p.sort(function(r,t){return pu(t)-pu(r)})[0];h[n]=Kt(q,5)}}return h};fu(function(){ht(g,a,b,c)},["ad_storage"])}}function ru(a){return a.filter(function(b){return Zt.test(b.gclid)})}
function Ru(a,b){if(ms(x)){for(var c=ku(b.prefix),d={},e=0;e<a.length;e++)au[a[e]]&&(d[a[e]]=au[a[e]]);fu(function(){tb(d,function(f,g){var h=ps(c+g,z.cookie,void 0,du());h.sort(function(t,u){return Mu(u)-Mu(t)});if(h.length){var m=h[0],n=Mu(m),p=Ou(m.split(".")).length!==0?m.split(".").slice(3):[],q={},r;r=Ou(m.split(".")).length!==0?m.split(".")[2]:void 0;q[f]=[r];Eu(q,!0,b,n,p)}})},du())}}
function Su(a){var b=["ag"],c=["gbraid"];fu(function(){for(var d=ku(a.prefix),e=0;e<b.length;++e){var f=lu(b[e],d);if(!f)break;var g=Nt(f,5);if(g.length){var h=g.sort(function(q,r){return pu(r)-pu(q)})[0],m=pu(h),n=h.b,p={};p[c[e]]=h.k;Eu(p,!0,a,m,n)}}},["ad_storage"])}function Tu(a,b){for(var c=0;c<b.length;++c)if(a[b[c]])return!0;return!1}
function Uu(a){function b(h,m,n){n&&(h[m]=n)}if(en()){var c=Cu(),d;a.includes("gad_source")&&(d=c.gad_source!==void 0?c.gad_source:at(!1)._gs);if(Tu(c,a)||d){var e={};b(e,"gclid",c.gclid);b(e,"dclid",c.dclid);b(e,"gclsrc",c.gclsrc);b(e,"wbraid",c.wbraid);b(e,"gbraid",c.gbraid);it(function(){return e},3);var f={},g=(f._up="1",f);b(g,"_gs",d);it(function(){return g},1)}}}function Iu(){var a=Vk(x.location.href);return Pk(a,"query",!1,void 0,"gad_source")}
function Vu(a){if(!Ra(1))return null;var b=at(!0).gad_source;if(b!=null)return x.location.hash="",b;if(Ra(2)){b=Iu();if(b!=null)return b;var c=Cu();if(Tu(c,a))return"0"}return null}function Wu(a){var b=Vu(a);b!=null&&it(function(){var c={};return c.gad_source=b,c},4)}function Xu(a,b,c){var d=[];if(b.length===0)return d;for(var e={},f=0;f<b.length;f++){var g=b[f],h=g.type?g.type:"gcl";(g.labels||[]).indexOf(c)===-1?(a.push(0),e[h]||d.push(g)):a.push(1);e[h]=!0}return d}
function Yu(a,b,c,d){var e=[];c=c||{};if(!eu(du()))return e;var f=hu(a),g=Xu(e,f,b);if(g.length&&!d)for(var h=l(g),m=h.next();!m.done;m=h.next()){var n=m.value,p=n.timestamp,q=[n.version,Math.round(p/1E3),n.gclid].concat(n.labels||[],[b]).join("."),r=Rr(c,p,!0);r.Ec=du();As(a,q,r)}return e}
function Zu(a,b){var c=[];b=b||{};var d=ju(b),e=Xu(c,d,a);if(e.length)for(var f=l(e),g=f.next();!g.done;g=f.next()){var h=g.value,m=ku(b.prefix),n=lu(h.type,m);if(!n)break;var p=h,q=p.version,r=p.gclid,t=p.labels,u=p.timestamp,v=Math.round(u/1E3);if(h.type==="ag"){var w={},y=(w.k=r,w.i=""+v,w.b=(t||[]).concat([a]),w);Pt(n,y,5,b,u)}else if(h.type==="gb"){var A=[q,v,r].concat(t||[],[a]).join("."),C=Rr(b,u,!0);C.Ec=du();As(n,A,C)}}return c}
function $u(a,b){var c=ku(b),d=lu(a,c);if(!d)return 0;var e;e=a==="ag"?mu(d):hu(d);for(var f=0,g=0;g<e.length;g++)f=Math.max(f,e[g].timestamp);return f}function av(a){for(var b=0,c=l(Object.keys(a)),d=c.next();!d.done;d=c.next())for(var e=a[d.value],f=0;f<e.length;f++)b=Math.max(b,Number(e[f].timestamp));return b}function bv(a){var b=Math.max($u("aw",a),av(eu(du())?Bt():{})),c=Math.max($u("gb",a),av(eu(du())?Bt("_gac_gb",!0):{}));c=Math.max(c,$u("ag",a));return c>b}
function Hu(){return z.referrer?Pk(Vk(z.referrer),"host"):""};function qv(){return zp("dedupe_gclid",function(){return Hs()})};var rv=/^(www\.)?google(\.com?)?(\.[a-z]{2}t?)?$/,sv=/^www.googleadservices.com$/;function tv(a){a||(a=uv());return a.Lq?!1:a.Dp||a.Fp||a.Ip||a.Gp||a.Yf||a.np||a.Hp||a.tp?!0:!1}function uv(){var a={},b=at(!0);a.Lq=!!b._up;var c=Cu();a.Dp=c.aw!==void 0;a.Fp=c.dc!==void 0;a.Ip=c.wbraid!==void 0;a.Gp=c.gbraid!==void 0;a.Hp=c.gclsrc==="aw.ds";a.Yf=ev().Yf;var d=z.referrer?Pk(Vk(z.referrer),"host"):"";a.tp=rv.test(d);a.np=sv.test(d);return a};function vv(a){var b=window,c=b.webkit;delete b.webkit;a(b.webkit);b.webkit=c}function wv(a){var b={action:"gcl_setup"};if("CWVWebViewMessage"in a.messageHandlers)return a.messageHandlers.CWVWebViewMessage.postMessage({command:"awb",payload:b}),!0;var c=a.messageHandlers.awb;return c?(c.postMessage(b),!0):!1};function xv(){return["ad_storage","ad_user_data"]}function yv(a){if(E(38)&&!yn(un.Z.ol)&&"webkit"in window&&window.webkit.messageHandlers){var b=function(){try{vv(function(c){c&&("CWVWebViewMessage"in c.messageHandlers||"awb"in c.messageHandlers)&&(xn(un.Z.ol,function(d){d.gclid&&Ju(d.gclid,5,a)}),wv(c)||L(178))})}catch(c){L(177)}};hn(function(){eu(xv())?b():jn(b,xv())},xv())}};var zv=["https://www.google.com","https://www.youtube.com","https://m.youtube.com"];function Av(a){a.data.action==="gcl_transfer"&&a.data.gadSource?xn(un.Z.Nf,{gadSource:a.data.gadSource}):L(173)}
function Bv(a,b){if(E(a)){if(yn(un.Z.Nf))return L(176),un.Z.Nf;if(yn(un.Z.rl))return L(170),un.Z.Nf;var c=Ml();if(!c)L(171);else if(c.opener){var d=function(g){if(zv.includes(g.origin)){a===119?Av(g):a===200&&(Av(g),g.data.gclid&&Ju(String(g.data.gclid),6,b));var h;(h=g.stopImmediatePropagation)==null||h.call(g);Xq(c,"message",d)}else L(172)};if(Wq(c,"message",d)){xn(un.Z.rl,!0);for(var e=l(zv),f=e.next();!f.done;f=e.next())c.opener.postMessage({action:"gcl_setup"},f.value);L(174);return un.Z.Nf}L(175)}}}
;var Cv=function(){this.C=this.gppString=void 0};Cv.prototype.reset=function(){this.C=this.gppString=void 0};var Dv=new Cv;var Ev=RegExp("^UA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*(?:%3BUA-\\d+-\\d+%3A[\\w-]+(?:%2C[\\w-]+)*)*$"),Fv=/^~?[\w-]+(?:\.~?[\w-]+)*$/,Gv=/^\d+\.fls\.doubleclick\.net$/,Hv=/;gac=([^;?]+)/,Iv=/;gacgb=([^;?]+)/;
function Jv(a,b){if(Gv.test(z.location.host)){var c=z.location.href.match(b);return c&&c.length===2&&c[1].match(Ev)?Ok(c[1])||"":""}for(var d=[],e=l(Object.keys(a)),f=e.next();!f.done;f=e.next()){for(var g=f.value,h=[],m=a[g],n=0;n<m.length;n++)h.push(m[n].gclid);d.push(g+":"+h.join(","))}return d.length>0?d.join(";"):""}
function Kv(a,b,c){for(var d=eu(du())?Bt("_gac_gb",!0):{},e=[],f=!1,g=l(Object.keys(d)),h=g.next();!h.done;h=g.next()){var m=h.value,n=Yu("_gac_gb_"+m,a,b,c);f=f||n.length!==0&&n.some(function(p){return p===1});e.push(m+":"+n.join(","))}return{mp:f?e.join(";"):"",lp:Jv(d,Iv)}}function Lv(a){var b=z.location.href.match(new RegExp(";"+a+"=([^;?]+)"));return b&&b.length===2&&b[1].match(Fv)?b[1]:void 0}
function Mv(a){var b={},c,d,e;Gv.test(z.location.host)&&(c=Lv("gclgs"),d=Lv("gclst"),e=Lv("gcllp"));if(c&&d&&e)b.sh=c,b.uh=d,b.th=e;else{var f=Ab(),g=mu((a||"_gcl")+"_gs"),h=g.map(function(p){return p.gclid}),m=g.map(function(p){return f-p.timestamp}),n=g.map(function(p){return p.Fd});h.length>0&&m.length>0&&n.length>0&&(b.sh=h.join("."),b.uh=m.join("."),b.th=n.join("."))}return b}
function Nv(a,b,c,d){d=d===void 0?!1:d;if(Gv.test(z.location.host)){var e=Lv(c);if(e){if(d){var f=new Rt;St(f,2);St(f,3);return e.split(".").map(function(h){return{gclid:h,Ja:f,Db:[1]}})}return e.split(".").map(function(h){return{gclid:h}})}}else{if(b==="gclid"){var g=(a||"_gcl")+"_aw";return d?xu(g):hu(g)}if(b==="wbraid")return hu((a||"_gcl")+"_gb");if(b==="braids")return ju({prefix:a})}return[]}function Ov(a){return Gv.test(z.location.host)?!(Lv("gclaw")||Lv("gac")):bv(a)}
function Pv(a,b,c){var d;d=c?Zu(a,b):Yu((b&&b.prefix||"_gcl")+"_gb",a,b);return d.length===0||d.every(function(e){return e===0})?"":d.join(".")};function Qv(){var a=x.__uspapi;if(lb(a)){var b="";try{a("getUSPData",1,function(c,d){if(d&&c){var e=c.uspString;e&&RegExp("^[\\da-zA-Z-]{1,20}$").test(e)&&(b=e)}})}catch(c){}return b}};function cw(a){var b=O(a.D,J.m.Lc),c=O(a.D,J.m.Kc);b&&!c?(a.eventName!==J.m.qa&&a.eventName!==J.m.Td&&L(131),a.isAborted=!0):!b&&c&&(L(132),a.isAborted=!0)}function dw(a){var b=P(J.m.U)?yp.pscdl:"denied";b!=null&&U(a,J.m.Fg,b)}function ew(a){var b=Kl(!0);U(a,J.m.Jc,b)}function fw(a){Lr()&&U(a,J.m.be,1)}
function Uv(){var a=z.title;if(a===void 0||a==="")return"";a=encodeURIComponent(a);for(var b=256;b>0&&Ok(a.substring(0,b))===void 0;)b--;return Ok(a.substring(0,b))||""}function gw(a){hw(a,Gp.Bf.Tm,O(a.D,J.m.pb))}function hw(a,b,c){Tv(a,J.m.sd)||U(a,J.m.sd,{});Tv(a,J.m.sd)[b]=c}function iw(a){S(a,Q.A.Pf,Tm.X.Fa)}function jw(a){var b=ib("GTAG_EVENT_FEATURE_CHANNEL");b&&(U(a,J.m.jf,b),gb())}function kw(a){var b=a.D.getMergedValues(J.m.sc);b&&a.mergeHitDataForKey(J.m.sc,b)}
function lw(a,b){b=b===void 0?!1:b;var c=R(a,Q.A.Of);if(c)if(c.indexOf(a.target.destinationId)<0){if(S(a,Q.A.Hj,!1),b||!mw(a,"custom_event_accept_rules",!1))a.isAborted=!0}else S(a,Q.A.Hj,!0)}function nw(a){kl&&(Yn=!0,a.eventName===J.m.qa?eo(a.D,a.target.id):(R(a,Q.A.Ke)||(ao[a.target.id]=!0),Fp(R(a,Q.A.hb))))};function xw(a,b,c,d){var e=Hc(),f;if(e===1)a:{var g=hk;g=g.toLowerCase();for(var h="https://"+g,m="http://"+g,n=1,p=z.getElementsByTagName("script"),q=0;q<p.length&&q<100;q++){var r=p[q].src;if(r){r=r.toLowerCase();if(r.indexOf(m)===0){f=3;break a}n===1&&r.indexOf(h)===0&&(n=2)}}f=n}else f=e;return(f===2||d||"http:"!==x.location.protocol?a:b)+c};function Jw(a){return{getDestinationId:function(){return a.target.destinationId},getEventName:function(){return a.eventName},setEventName:function(b){a.eventName=b},getHitData:function(b){return Tv(a,b)},setHitData:function(b,c){U(a,b,c)},setHitDataIfNotDefined:function(b,c){Tv(a,b)===void 0&&U(a,b,c)},copyToHitData:function(b,c){a.copyToHitData(b,c)},getMetadata:function(b){return R(a,b)},setMetadata:function(b,c){S(a,b,c)},isAborted:function(){return a.isAborted},abort:function(){a.isAborted=!0},
getFromEventContext:function(b){return O(a.D,b)},Bb:function(){return a},getHitKeys:function(){return Object.keys(a.C)},getMergedValues:function(b){return a.D.getMergedValues(b,3)},mergeHitDataForKey:function(b,c){return ld(c)?a.mergeHitDataForKey(b,c):!1}}};function Ow(a,b){return arguments.length===1?Pw("set",a):Pw("set",a,b)}function Qw(a,b){return arguments.length===1?Pw("config",a):Pw("config",a,b)}function Rw(a,b,c){c=c||{};c[J.m.md]=a;return Pw("event",b,c)}function Pw(){return arguments};var Tw=function(){this.messages=[];this.C=[]};Tw.prototype.enqueue=function(a,b,c){var d=this.messages.length+1;a["gtm.uniqueEventId"]=b;a["gtm.priorityId"]=d;var e=ma(Object,"assign").call(Object,{},c,{eventId:b,priorityId:d,fromContainerExecution:!0}),f={message:a,notBeforeEventId:b,priorityId:d,messageContext:e};this.messages.push(f);for(var g=0;g<this.C.length;g++)try{this.C[g](f)}catch(h){}};Tw.prototype.listen=function(a){this.C.push(a)};
Tw.prototype.get=function(){for(var a={},b=0;b<this.messages.length;b++){var c=this.messages[b],d=a[c.notBeforeEventId];d||(d=[],a[c.notBeforeEventId]=d);d.push(c)}return a};Tw.prototype.prune=function(a){for(var b=[],c=[],d=0;d<this.messages.length;d++){var e=this.messages[d];e.notBeforeEventId===a?b.push(e):c.push(e)}this.messages=c;return b};function Uw(a,b,c){c.eventMetadata=c.eventMetadata||{};c.eventMetadata[Q.A.hb]=lg.canonicalContainerId;Vw().enqueue(a,b,c)}
function Ww(){var a=Xw;Vw().listen(a)}function Vw(){return zp("mb",function(){return new Tw})};var Yw,Zw=!1;function $w(){Zw=!0;Yw=productSettings,productSettings=void 0;Yw=Yw||{}}function ax(a){Zw||$w();return Yw[a]};function bx(){var a=x.screen;return{width:a?a.width:0,height:a?a.height:0}}
function cx(a){if(z.hidden)return!0;var b=a.getBoundingClientRect();if(b.top===b.bottom||b.left===b.right||!x.getComputedStyle)return!0;var c=x.getComputedStyle(a,null);if(c.visibility==="hidden")return!0;for(var d=a,e=c;d;){if(e.display==="none")return!0;var f=e.opacity,g=e.filter;if(g){var h=g.indexOf("opacity(");h>=0&&(g=g.substring(h+8,g.indexOf(")",h)),g.charAt(g.length-1)==="%"&&(g=g.substring(0,g.length-1)),f=String(Math.min(Number(g),Number(f))))}if(f!==void 0&&Number(f)<=0)return!0;(d=d.parentElement)&&
(e=x.getComputedStyle(d,null))}return!1}var hg;var py=Number('')||5,qy=Number('')||50,ry=qb();
var ty=function(a,b){a&&(sy("sid",a.targetId,b),sy("cc",a.clientCount,b),sy("tl",a.totalLifeMs,b),sy("hc",a.heartbeatCount,b),sy("cl",a.clientLifeMs,b))},sy=function(a,b,c){b!=null&&c.push(a+"="+b)},uy=function(){var a=z.referrer;if(a){var b;return Pk(Vk(a),"host")===((b=x.location)==null?void 0:b.host)?1:2}return 0},vy="https://"+Ti(21,"www.googletagmanager.com")+"/a?",xy=function(){this.R=wy;this.N=0};xy.prototype.H=function(a,b,c,d){var e=uy(),f,
g=[];f=x===x.top&&e!==0&&b?(b==null?void 0:b.clientCount)>1?e===2?1:2:e===2?0:3:4;a&&sy("si",a.gg,g);sy("m",0,g);sy("iss",f,g);sy("if",c,g);ty(b,g);d&&sy("fm",encodeURIComponent(d.substring(0,qy)),g);this.P(g);};xy.prototype.C=function(a,b,c,d,e){var f=[];sy("m",1,f);sy("s",a,f);sy("po",uy(),f);b&&(sy("st",b.state,f),sy("si",b.gg,f),sy("sm",b.mg,f));ty(c,f);sy("c",d,f);e&&sy("fm",encodeURIComponent(e.substring(0,
qy)),f);this.P(f);};xy.prototype.P=function(a){a=a===void 0?[]:a;!jl||this.N>=py||(sy("pid",ry,a),sy("bc",++this.N,a),a.unshift("ctid="+lg.ctid+"&t=s"),this.R(""+vy+a.join("&")))};var yy=Number('')||500,zy=Number('')||5E3,Ay=Number('20')||10,By=Number('')||5E3;function Cy(a){return a.performance&&a.performance.now()||Date.now()}
var Dy=function(a,b){var c=x,d;var e=function(f,g,h){h=h===void 0?{jm:function(){},km:function(){},im:function(){},onFailure:function(){}}:h;this.wo=f;this.C=g;this.N=h;this.fa=this.ma=this.heartbeatCount=this.uo=0;this.gh=!1;this.H={};this.id=String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()));this.state=0;this.gg=Cy(this.C);this.mg=Cy(this.C);this.R=10};e.prototype.init=function(){this.P(1);this.Da()};e.prototype.getState=function(){return{state:this.state,
gg:Math.round(Cy(this.C)-this.gg),mg:Math.round(Cy(this.C)-this.mg)}};e.prototype.P=function(f){this.state!==f&&(this.state=f,this.mg=Cy(this.C))};e.prototype.Gl=function(){return String(this.uo++)};e.prototype.Da=function(){var f=this;this.heartbeatCount++;this.Va({type:0,clientId:this.id,requestId:this.Gl(),maxDelay:this.hh()},function(g){if(g.type===0){var h;if(((h=g.failure)==null?void 0:h.failureType)!=null)if(g.stats&&(f.stats=g.stats),f.fa++,g.isDead||f.fa>Ay){var m=g.isDead&&g.failure.failureType;
f.R=m||10;f.P(4);f.so();var n,p;(p=(n=f.N).im)==null||p.call(n,{failureType:m||10,data:g.failure.data})}else f.P(3),f.Kl();else{if(f.heartbeatCount>g.stats.heartbeatCount+Ay){f.heartbeatCount=g.stats.heartbeatCount;var q,r;(r=(q=f.N).onFailure)==null||r.call(q,{failureType:13})}f.stats=g.stats;var t=f.state;f.P(2);if(t!==2)if(f.gh){var u,v;(v=(u=f.N).km)==null||v.call(u)}else{f.gh=!0;var w,y;(y=(w=f.N).jm)==null||y.call(w)}f.fa=0;f.xo();f.Kl()}}})};e.prototype.hh=function(){return this.state===2?
zy:yy};e.prototype.Kl=function(){var f=this;this.C.setTimeout(function(){f.Da()},Math.max(0,this.hh()-(Cy(this.C)-this.ma)))};e.prototype.Bo=function(f,g,h){var m=this;this.Va({type:1,clientId:this.id,requestId:this.Gl(),command:f},function(n){if(n.type===1)if(n.result)g(n.result);else{var p,q,r,t={failureType:(r=(p=n.failure)==null?void 0:p.failureType)!=null?r:12,data:(q=n.failure)==null?void 0:q.data},u,v;(v=(u=m.N).onFailure)==null||v.call(u,t);h(t)}})};e.prototype.Va=function(f,g){var h=this;
if(this.state===4)f.failure={failureType:this.R},g(f);else{var m=this.state!==2&&f.type!==0,n=f.requestId,p,q=this.C.setTimeout(function(){var t=h.H[n];t&&h.Kf(t,7)},(p=f.maxDelay)!=null?p:By),r={request:f,Am:g,vm:m,Vp:q};this.H[n]=r;m||this.sendRequest(r)}};e.prototype.sendRequest=function(f){this.ma=Cy(this.C);f.vm=!1;this.wo(f.request)};e.prototype.xo=function(){for(var f=l(Object.keys(this.H)),g=f.next();!g.done;g=f.next()){var h=this.H[g.value];h.vm&&this.sendRequest(h)}};e.prototype.so=function(){for(var f=
l(Object.keys(this.H)),g=f.next();!g.done;g=f.next())this.Kf(this.H[g.value],this.R)};e.prototype.Kf=function(f,g){this.rb(f);var h=f.request;h.failure={failureType:g};f.Am(h)};e.prototype.rb=function(f){delete this.H[f.request.requestId];this.C.clearTimeout(f.Vp)};e.prototype.Bp=function(f){this.ma=Cy(this.C);var g=this.H[f.requestId];if(g)this.rb(g),g.Am(f);else{var h,m;(m=(h=this.N).onFailure)==null||m.call(h,{failureType:14})}};d=new e(a,c,b);return d};var Ey;
var Fy=function(){Ey||(Ey=new xy);return Ey},wy=function(a){rn(tn(Tm.X.Oc),function(){Kc(a)})},Gy=function(a){var b=a.substring(0,a.indexOf("/_/service_worker"));return"&1p=1"+(b?"&path="+encodeURIComponent(b):"")},Hy=function(a){var b=a,c=Qj.Da;b?(b.charAt(b.length-1)!=="/"&&(b+="/"),a=b+c):a="https://www.googletagmanager.com/static/service_worker/"+c+"/";var d;try{d=new URL(a)}catch(e){return null}return d.protocol!=="https:"?null:d},Iy=function(a){var b=yn(un.Z.zl);return b&&b[a]},Jy=function(a,
b,c,d,e){var f=this;this.H=d;this.R=this.P=!1;this.fa=null;this.initTime=c;this.C=15;this.N=this.So(a);x.setTimeout(function(){f.initialize()},1E3);Nc(function(){f.Mp(a,b,e)})};k=Jy.prototype;k.delegate=function(a,b,c){this.getState()!==2?(this.H.C(this.C,{state:this.getState(),gg:this.initTime,mg:Math.round(Ab())-this.initTime},void 0,a.commandType),c({failureType:this.C})):this.N.Bo(a,b,c)};k.getState=function(){return this.N.getState().state};k.Mp=function(a,b,c){var d=x.location.origin,e=this,
f=Ic();try{var g=f.contentDocument.createElement("iframe"),h=a.pathname,m=h[h.length-1]==="/"?a.toString():a.toString()+"/",n=b?Gy(h):"",p;E(133)&&(p={sandbox:"allow-same-origin allow-scripts"});Ic(m+"sw_iframe.html?origin="+encodeURIComponent(d)+n+(c?"&e=1":""),void 0,p,void 0,g);var q=function(){f.contentDocument.body.appendChild(g);g.addEventListener("load",function(){e.fa=g.contentWindow;f.contentWindow.addEventListener("message",function(r){r.origin===a.origin&&e.N.Bp(r.data)});e.initialize()})};
f.contentDocument.readyState==="complete"?q():f.contentWindow.addEventListener("load",function(){q()})}catch(r){f.parentElement.removeChild(f),this.C=11,this.H.H(void 0,void 0,this.C,r.toString())}};k.So=function(a){var b=this,c=Dy(function(d){var e;(e=b.fa)==null||e.postMessage(d,a.origin)},{jm:function(){b.P=!0;b.H.H(c.getState(),c.stats)},km:function(){},im:function(d){b.P?(b.C=(d==null?void 0:d.failureType)||10,b.H.C(b.C,c.getState(),c.stats,void 0,d==null?void 0:d.data)):(b.C=(d==null?void 0:
d.failureType)||4,b.H.H(c.getState(),c.stats,b.C,d==null?void 0:d.data))},onFailure:function(d){b.C=d.failureType;b.H.C(b.C,c.getState(),c.stats,d.command,d.data)}});return c};k.initialize=function(){this.R||this.N.init();this.R=!0};function Ky(){var a=kg(hg.C,"",function(){return{}});try{return a("internal_sw_allowed"),!0}catch(b){return!1}}
function Ly(a,b){var c=Math.round(Ab());b=b===void 0?!1:b;var d=x.location.origin;if(!d||!Ky()||E(168))return;pk()&&(a=""+d+ok()+"/_/service_worker");var e=Hy(a);if(e===null||Iy(e.origin))return;if(!vc()){Fy().H(void 0,void 0,6);return}var f=new Jy(e,!!a,c||Math.round(Ab()),Fy(),b);zn(un.Z.zl)[e.origin]=f;}
var My=function(a,b,c,d){var e;if((e=Iy(a))==null||!e.delegate){var f=vc()?16:6;Fy().C(f,void 0,void 0,b.commandType);d({failureType:f});return}Iy(a).delegate(b,c,d);};
function Ny(a,b,c,d,e){var f=Hy();if(f===null){d(vc()?16:6);return}var g,h=(g=Iy(f.origin))==null?void 0:g.initTime,m=Math.round(Ab()),n={commandType:0,params:{url:a,method:0,templates:b,body:"",processResponse:!1,sinceInit:h?m-h:void 0}};e&&(n.params.encryptionKeyString=e);My(f.origin,n,function(p){c(p)},function(p){d(p.failureType)});}
function Oy(a,b,c,d){var e=Hy(a);if(e===null){d("_is_sw=f"+(vc()?16:6)+"te");return}var f=b?1:0,g=Math.round(Ab()),h,m=(h=Iy(e.origin))==null?void 0:h.initTime,n=m?g-m:void 0,p=!1;E(169)&&(p=!0);My(e.origin,{commandType:0,params:{url:a,method:f,templates:c,body:b||"",processResponse:!0,suppressSuccessCallback:p,sinceInit:n,attributionReporting:!0,referer:x.location.href}},function(){},function(q){var r="_is_sw=f"+q.failureType,t,u=(t=Iy(e.origin))==
null?void 0:t.getState();u!==void 0&&(r+="s"+u);d(n?r+("t"+n):r+"te")});};function Py(a){if(E(10)||pk()||Qj.N||cl(a.D)||E(168))return;Ly(void 0,E(131));};var Qy="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Ry(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Sy(a){var b=a.google_tag_data,c;if(b!=null&&b.uach){var d=b.uach,e=ma(Object,"assign").call(Object,{},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));c=e}else c=null;return c}function Ty(a){var b,c;return(c=(b=a.google_tag_data)==null?void 0:b.uach_promise)!=null?c:null}
function Uy(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Vy(a){if(!Uy(a))return null;var b=Ry(a);if(b.uach_promise)return b.uach_promise;var c=a.navigator.userAgentData.getHighEntropyValues(Qy).then(function(d){b.uach!=null||(b.uach=d);return d});return b.uach_promise=c};function az(a){var b=a.location.href;if(a===a.top)return{url:b,Rp:!0};var c=!1,d=a.document;d&&d.referrer&&(b=d.referrer,a.parent===a.top&&(c=!0));var e=a.location.ancestorOrigins;if(e){var f=e[e.length-1];f&&b.indexOf(f)===-1&&(c=!1,b=f)}return{url:b,Rp:c}};function Rz(a,b){var c=!!pk();switch(a){case 45:return"https://www.google.com/ccm/collect";case 46:return c?ok()+"/gs/ccm/collect":"https://pagead2.googlesyndication.com/ccm/collect";case 51:return"https://www.google.com/travel/flights/click/conversion";case 9:return"https://googleads.g.doubleclick.net/pagead/viewthroughconversion";case 17:return c?E(90)&&ro()?Pz():""+ok()+"/ag/g/c":Pz();case 16:return c?E(90)&&ro()?Qz():""+ok()+"/ga/g/c":Qz();case 1:return"https://ad.doubleclick.net/activity;";case 2:return c?
ok()+"/ddm/activity/":"https://ade.googlesyndication.com/ddm/activity/";case 33:return"https://ad.doubleclick.net/activity;register_conversion=1;";case 11:return c?ok()+"/d/pagead/form-data":E(141)?"https://www.google.com/pagead/form-data":"https://google.com/pagead/form-data";case 3:return"https://"+b.Co+".fls.doubleclick.net/activityi;";case 5:return"https://www.googleadservices.com/pagead/conversion";case 6:return c?ok()+"/gs/pagead/conversion":"https://pagead2.googlesyndication.com/pagead/conversion";
case 8:return"https://www.google.com/pagead/1p-conversion";case 22:return c?ok()+"/as/d/ccm/conversion":"https://www.googleadservices.com/ccm/conversion";case 60:return c?ok()+"/gs/ccm/conversion":"https://pagead2.googlesyndication.com/ccm/conversion";case 23:return c?ok()+"/g/d/ccm/conversion":"https://www.google.com/ccm/conversion";case 55:return c?ok()+"/gs/measurement/conversion/":"https://pagead2.googlesyndication.com/measurement/conversion/";case 54:return E(205)?"https://www.google.com/measurement/conversion/":
c?ok()+"/g/measurement/conversion/":"https://www.google.com/measurement/conversion/";case 21:return c?ok()+"/d/ccm/form-data":E(141)?"https://www.google.com/ccm/form-data":"https://google.com/ccm/form-data";case 7:case 52:case 53:case 39:case 38:case 40:case 37:case 49:case 48:case 14:case 24:case 19:case 27:case 30:case 36:case 62:case 26:case 29:case 32:case 35:case 57:case 58:case 50:case 12:case 13:case 20:case 18:case 59:case 47:case 44:case 43:case 15:case 0:case 61:case 56:case 25:case 28:case 31:case 34:throw Error("Unsupported endpoint");
default:mc(a,"Unknown endpoint")}};function Sz(a){a=a===void 0?[]:a;return Rj(a).join("~")}function Tz(){if(!E(118))return"";var a,b;return(((a=Hm(wm()))==null?void 0:(b=a.context)==null?void 0:b.loadExperiments)||[]).join("~")};function Uz(a,b){b&&tb(b,function(c,d){typeof d!=="object"&&d!==void 0&&(a["1p."+c]=String(d))})};var bA={};bA.O=hs.O;var cA={jr:"L",qo:"S",Ar:"Y",Oq:"B",Yq:"E",gr:"I",xr:"TC",er:"HTC"},dA={qo:"S",Xq:"V",Rq:"E",wr:"tag"},eA={},fA=(eA[bA.O.Oi]="6",eA[bA.O.Pi]="5",eA[bA.O.Ni]="7",eA);function gA(){function a(c,d){var e=ib(d);e&&b.push([c,e])}var b=[];a("u","GTM");a("ut","TAGGING");a("h","HEALTH");return b};var hA=!1;
function AA(a){}function BA(a){}
function CA(){}function DA(a){}
function EA(a){}function FA(a){}
function GA(){}function HA(a,b){}
function IA(a,b,c){}
function JA(){};var KA=Object.freeze({cache:"no-store",credentials:"include",method:"GET",keepalive:!0,redirect:"follow"});
function LA(a,b,c,d,e,f,g,h){var m=ma(Object,"assign").call(Object,{},KA);c&&(m.body=c,m.method="POST");ma(Object,"assign").call(Object,m,e);h==null||bm(h);x.fetch(b,m).then(function(n){h==null||cm(h);if(!n.ok)g==null||g();else if(n.body){var p=n.body.getReader(),q=new TextDecoder;return new Promise(function(r){function t(){p.read().then(function(u){var v;v=u.done;var w=q.decode(u.value,{stream:!v});MA(d,w);v?(f==null||f(),r()):t()}).catch(function(){r()})}t()})}}).catch(function(){h==null||cm(h);
g?g():E(128)&&(b+="&_z=retryFetch",c?km(a,b,c):jm(a,b))})};var NA=function(a){this.P=a;this.C=""},OA=function(a,b){a.H=b;return a},PA=function(a,b){a.N=b;return a},MA=function(a,b){b=a.C+b;for(var c=b.indexOf("\n\n");c!==-1;){var d=a,e;a:{var f=l(b.substring(0,c).split("\n")),g=f.next().value,h=f.next().value;if(g.indexOf("event: message")===0&&h.indexOf("data: ")===0)try{e=JSON.parse(h.substring(h.indexOf(":")+1));break a}catch(m){}e=void 0}QA(d,e);b=b.substring(c+2);c=b.indexOf("\n\n")}a.C=b},RA=function(a,b){return function(){if(b.fallback_url&&b.fallback_url_method){var c=
{};QA(a,(c[b.fallback_url_method]=[b.fallback_url],c.options={},c))}}},QA=function(a,b){b&&(SA(b.send_pixel,b.options,a.P),SA(b.create_iframe,b.options,a.H),SA(b.fetch,b.options,a.N))};function TA(a){var b=a.search;return a.protocol+"//"+a.hostname+a.pathname+(b?b+"&richsstsse":"?richsstsse")}function SA(a,b,c){if(a&&c){var d=a||[];if(Array.isArray(d))for(var e=ld(b)?b:{},f=l(d),g=f.next();!g.done;g=f.next())c(g.value,e)}};var UA=function(a,b){this.Zp=a;this.timeoutMs=b;this.Xa=void 0},bm=function(a){a.Xa||(a.Xa=setTimeout(function(){a.Zp();a.Xa=void 0},a.timeoutMs))},cm=function(a){a.Xa&&(clearTimeout(a.Xa),a.Xa=void 0)};var HB=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),IB={cl:["ecl"],customPixels:["nonGooglePixels"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},JB={cl:["ecl"],customPixels:["customScripts",
"html"],ecl:["cl"],ehl:["hl"],gaawc:["googtag"],hl:["ehl"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels","customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},KB="google customPixels customScripts html nonGooglePixels nonGoogleScripts nonGoogleIframes".split(" ");
function LB(){var a=vk("gtm.allowlist")||vk("gtm.whitelist");a&&L(9);ek&&!E(212)?a=["google","gtagfl","lcl","zone","cmpPartners"]:E(212)&&(a=void 0);HB.test(x.location&&x.location.hostname)&&(ek?L(116):(L(117),MB&&(a=[],window.console&&window.console.log&&window.console.log("GTM blocked. See go/13687728."))));var b=a&&Fb(xb(a),IB),c=vk("gtm.blocklist")||vk("gtm.blacklist");c||(c=vk("tagTypeBlacklist"))&&L(3);c?L(8):c=[];HB.test(x.location&&x.location.hostname)&&(c=xb(c),c.push("nonGooglePixels","nonGoogleScripts",
"sandboxedScripts"));xb(c).indexOf("google")>=0&&L(2);var d=c&&Fb(xb(c),JB),e={};return function(f){var g=f&&f[jf.Ua];if(!g||typeof g!=="string")return!0;g=g.replace(/^_*/,"");if(e[g]!==void 0)return e[g];var h=lk[g]||[],m=!0;if(a){var n;if(n=m)a:{if(b.indexOf(g)<0){if(ek&&h.indexOf("cmpPartners")>=0){n=!0;break a}if(h&&h.length>0)for(var p=0;p<h.length;p++){if(b.indexOf(h[p])<0){L(11);n=!1;break a}}else{n=!1;break a}}n=!0}m=n}var q=!1;if(c){var r=d.indexOf(g)>=0;if(r)q=r;else{var t=rb(d,h||[]);t&&
L(10);q=t}}var u=!m||q;!u&&(h.indexOf("sandboxedScripts")===-1?0:ek&&h.indexOf("cmpPartners")>=0?!NB():b&&b.indexOf("sandboxedScripts")!==-1?0:rb(d,KB))&&(u=!0);return e[g]=u}}function NB(){var a=kg(hg.C,lg.ctid,function(){return{}});try{return a("inject_cmp_banner"),!0}catch(b){return!1}}var MB=!1;MB=!0;function OB(a,b,c,d,e){if(!Mm(a)){d.loadExperiments=Sj();vm(a,d,e);var f=PB(a),g=function(){xm().container[a]&&(xm().container[a].state=3);QB()},h={destinationId:a,endpoint:0};if(pk())nm(h,ok()+"/"+f,void 0,g);else{var m=Gb(a,"GTM-"),n=bl(),p=c?"/gtag/js":"/gtm.js",q=al(b,p+f);if(!q){var r=Uj.wg+p;n&&xc&&m&&(r=xc.replace(/^(?:https?:\/\/)?/i,"").split(/[?#]/)[0]);q=xw("https://","http://",r+f)}nm(h,q,void 0,g)}}}function QB(){Om()||tb(Pm(),function(a,b){RB(a,b.transportUrl,b.context);L(92)})}
function RB(a,b,c,d){if(!Nm(a))if(c.loadExperiments||(c.loadExperiments=Sj()),Om()){var e;(e=xm().destination)[a]!=null||(e[a]={state:0,transportUrl:b,context:c,parent:wm()});xm().destination[a].state=0;ym({ctid:a,isDestination:!0},d);L(91)}else{var f;(f=xm().destination)[a]!=null||(f[a]={context:c,state:1,parent:wm()});xm().destination[a].state=1;ym({ctid:a,isDestination:!0},d);var g={destinationId:a,endpoint:0};if(pk())nm(g,ok()+("/gtd"+PB(a,!0)));else{var h="/gtag/destination"+PB(a,!0),m=al(b,
h);m||(m=xw("https://","http://",Uj.wg+h));nm(g,m)}}}function PB(a,b){b=b===void 0?!1:b;var c="?id="+encodeURIComponent(a);Xj!=="dataLayer"&&(c+="&l="+Xj);if(!Gb(a,"GTM-")||b)c=E(130)?c+(pk()?"&sc=1":"&cx=c"):c+"&cx=c";c+="&gtm="+Qr();bl()&&(c+="&sign="+Uj.Ki);var d=Qj.H;d===1?c+="&fps=fc":d===2&&(c+="&fps=fe");!E(191)&&Sj().join("~")&&(c+="&tag_exp="+Sj().join("~"));return c};var SB=function(){this.H=0;this.C={}};SB.prototype.addListener=function(a,b,c){var d=++this.H;this.C[a]=this.C[a]||{};this.C[a][String(d)]={listener:b,Fe:c};return d};SB.prototype.removeListener=function(a,b){var c=this.C[a],d=String(b);if(!c||!c[d])return!1;delete c[d];return!0};var UB=function(a,b){var c=[];tb(TB.C[a],function(d,e){c.indexOf(e.listener)<0&&(e.Fe===void 0||b.indexOf(e.Fe)>=0)&&c.push(e.listener)});return c};function VB(a,b,c){return{entityType:a,indexInOriginContainer:b,nameInOriginContainer:c,originContainerId:lg.ctid}};function WB(a,b){if(data.entities){var c=data.entities[a];if(c)return c[b]}};var YB=function(a,b){this.C=!1;this.P=[];this.eventData={tags:[]};this.R=!1;this.H=this.N=0;XB(this,a,b)},ZB=function(a,b,c,d){if(Zj.hasOwnProperty(b)||b==="__zone")return-1;var e={};ld(d)&&(e=md(d,e));e.id=c;e.status="timeout";return a.eventData.tags.push(e)-1},$B=function(a,b,c,d){var e=a.eventData.tags[b];e&&(e.status=c,e.executionTime=d)},aC=function(a){if(!a.C){for(var b=a.P,c=0;c<b.length;c++)b[c]();a.C=!0;a.P.length=0}},XB=function(a,b,c){b!==void 0&&a.Rf(b);c&&x.setTimeout(function(){aC(a)},
Number(c))};YB.prototype.Rf=function(a){var b=this,c=Cb(function(){Nc(function(){a(lg.ctid,b.eventData)})});this.C?c():this.P.push(c)};var bC=function(a){a.N++;return Cb(function(){a.H++;a.R&&a.H>=a.N&&aC(a)})},cC=function(a){a.R=!0;a.H>=a.N&&aC(a)};var dC={};function eC(){return x[fC()]}
function fC(){return x.GoogleAnalyticsObject||"ga"}function iC(){var a=lg.ctid;}
function jC(a,b){return function(){var c=eC(),d=c&&c.getByName&&c.getByName(a);if(d){var e=d.get("sendHitTask");d.set("sendHitTask",function(f){var g=f.get("hitPayload"),h=f.get("hitCallback"),m=g.indexOf("&tid="+b)<0;m&&(f.set("hitPayload",g.replace(/&tid=UA-[0-9]+-[0-9]+/,"&tid="+b),!0),f.set("hitCallback",void 0,!0));e(f);m&&(f.set("hitPayload",g,!0),f.set("hitCallback",h,!0),f.set("_x_19",void 0,!0),e(f))})}}};var pC=["es","1"],qC={},rC={};function sC(a,b){if(jl){var c;c=b.match(/^(gtm|gtag)\./)?encodeURIComponent(b):"*";qC[a]=[["e",c],["eid",a]];Bq(a)}}function tC(a){var b=a.eventId,c=a.Nd;if(!qC[b])return[];var d=[];rC[b]||d.push(pC);d.push.apply(d,ya(qC[b]));c&&(rC[b]=!0);return d};var uC={},vC={},wC={};function xC(a,b,c,d){jl&&E(120)&&((d===void 0?0:d)?(wC[b]=wC[b]||0,++wC[b]):c!==void 0?(vC[a]=vC[a]||{},vC[a][b]=Math.round(c)):(uC[a]=uC[a]||{},uC[a][b]=(uC[a][b]||0)+1))}function yC(a){var b=a.eventId,c=a.Nd,d=uC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete uC[b];return e.length?[["md",e.join(".")]]:[]}
function zC(a){var b=a.eventId,c=a.Nd,d=vC[b]||{},e=[],f;for(f in d)d.hasOwnProperty(f)&&e.push(""+f+d[f]);c&&delete vC[b];return e.length?[["mtd",e.join(".")]]:[]}function AC(){for(var a=[],b=l(Object.keys(wC)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(""+d+wC[d])}return a.length?[["mec",a.join(".")]]:[]};var BC={},CC={};function DC(a,b,c){if(jl&&b){var d=fl(b);BC[a]=BC[a]||[];BC[a].push(c+d);var e=b[jf.Ua];if(!e)throw Error("Error: No function name given for function call.");var f=(Lf[e]?"1":"2")+d;CC[a]=CC[a]||[];CC[a].push(f);Bq(a)}}function EC(a){var b=a.eventId,c=a.Nd,d=[],e=BC[b]||[];e.length&&d.push(["tr",e.join(".")]);var f=CC[b]||[];f.length&&d.push(["ti",f.join(".")]);c&&(delete BC[b],delete CC[b]);return d};function FC(a,b,c){c=c===void 0?!1:c;GC().addRestriction(0,a,b,c)}function HC(a,b,c){c=c===void 0?!1:c;GC().addRestriction(1,a,b,c)}function IC(){var a=Em();return GC().getRestrictions(1,a)}var JC=function(){this.container={};this.C={}},KC=function(a,b){var c=a.container[b];c||(c={_entity:{internal:[],external:[]},_event:{internal:[],external:[]}},a.container[b]=c);return c};
JC.prototype.addRestriction=function(a,b,c,d){d=d===void 0?!1:d;if(!d||!this.C[b]){var e=KC(this,b);a===0?d?e._entity.external.push(c):e._entity.internal.push(c):a===1&&(d?e._event.external.push(c):e._event.internal.push(c))}};
JC.prototype.getRestrictions=function(a,b){var c=KC(this,b);if(a===0){var d,e;return[].concat(ya((c==null?void 0:(d=c._entity)==null?void 0:d.internal)||[]),ya((c==null?void 0:(e=c._entity)==null?void 0:e.external)||[]))}if(a===1){var f,g;return[].concat(ya((c==null?void 0:(f=c._event)==null?void 0:f.internal)||[]),ya((c==null?void 0:(g=c._event)==null?void 0:g.external)||[]))}return[]};
JC.prototype.getExternalRestrictions=function(a,b){var c=KC(this,b),d,e;return a===0?(c==null?void 0:(d=c._entity)==null?void 0:d.external)||[]:(c==null?void 0:(e=c._event)==null?void 0:e.external)||[]};JC.prototype.removeExternalRestrictions=function(a){var b=KC(this,a);b._event&&(b._event.external=[]);b._entity&&(b._entity.external=[]);this.C[a]=!0};function GC(){return zp("r",function(){return new JC})};function LC(a,b,c,d){var e=Jf[a],f=MC(a,b,c,d);if(!f)return null;var g=Xf(e[jf.Al],c,[]);if(g&&g.length){var h=g[0];f=LC(h.index,{onSuccess:f,onFailure:h.Wl===1?b.terminate:f,terminate:b.terminate},c,d)}return f}
function MC(a,b,c,d){function e(){function w(){fo(3);var M=Ab()-I;DC(c.id,f,"7");$B(c.Pc,D,"exception",M);E(109)&&IA(c,f,bA.O.Ni);G||(G=!0,h())}if(f[jf.jo])h();else{var y=Wf(f,c,[]),A=y[jf.Qm];if(A!=null)for(var C=0;C<A.length;C++)if(!P(A[C])){h();return}var D=ZB(c.Pc,String(f[jf.Ua]),Number(f[jf.kh]),y[jf.METADATA]),G=!1;y.vtp_gtmOnSuccess=function(){if(!G){G=!0;var M=Ab()-I;DC(c.id,Jf[a],"5");$B(c.Pc,D,"success",M);E(109)&&IA(c,f,bA.O.Pi);g()}};y.vtp_gtmOnFailure=function(){if(!G){G=!0;var M=Ab()-
I;DC(c.id,Jf[a],"6");$B(c.Pc,D,"failure",M);E(109)&&IA(c,f,bA.O.Oi);h()}};y.vtp_gtmTagId=f.tag_id;y.vtp_gtmEventId=c.id;c.priorityId&&(y.vtp_gtmPriorityId=c.priorityId);DC(c.id,f,"1");E(109)&&HA(c,f);var I=Ab();try{Yf(y,{event:c,index:a,type:1})}catch(M){w(M)}E(109)&&IA(c,f,bA.O.Hl)}}var f=Jf[a],g=b.onSuccess,h=b.onFailure,m=b.terminate;if(c.isBlocked(f))return null;var n=Xf(f[jf.Il],c,[]);if(n&&n.length){var p=n[0],q=LC(p.index,{onSuccess:g,onFailure:h,terminate:m},c,d);if(!q)return null;g=q;h=p.Wl===
2?m:q}if(f[jf.pl]||f[jf.lo]){var r=f[jf.pl]?Kf:c.Eq,t=g,u=h;if(!r[a]){var v=NC(a,r,Cb(e));g=v.onSuccess;h=v.onFailure}return function(){r[a](t,u)}}return e}function NC(a,b,c){var d=[],e=[];b[a]=OC(d,e,c);return{onSuccess:function(){b[a]=PC;for(var f=0;f<d.length;f++)d[f]()},onFailure:function(){b[a]=QC;for(var f=0;f<e.length;f++)e[f]()}}}function OC(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function PC(a){a()}function QC(a,b){b()};var TC=function(a,b){for(var c=[],d=0;d<Jf.length;d++)if(a[d]){var e=Jf[d];var f=bC(b.Pc);try{var g=LC(d,{onSuccess:f,onFailure:f,terminate:f},b,d);if(g){var h=e[jf.Ua];if(!h)throw Error("Error: No function name given for function call.");var m=Lf[h];c.push({Gm:d,priorityOverride:(m?m.priorityOverride||0:0)||WB(e[jf.Ua],1)||0,execute:g})}else RC(d,b),f()}catch(p){f()}}c.sort(SC);for(var n=0;n<c.length;n++)c[n].execute();
return c.length>0};function UC(a,b){if(!TB)return!1;var c=a["gtm.triggers"]&&String(a["gtm.triggers"]),d=UB(a.event,c?String(c).split(","):[]);if(!d.length)return!1;for(var e=0;e<d.length;++e){var f=bC(b);try{d[e](a,f)}catch(g){f()}}return!0}function SC(a,b){var c,d=b.priorityOverride,e=a.priorityOverride;c=d>e?1:d<e?-1:0;var f;if(c!==0)f=c;else{var g=a.Gm,h=b.Gm;f=g>h?1:g<h?-1:0}return f}
function RC(a,b){if(jl){var c=function(d){var e=b.isBlocked(Jf[d])?"3":"4",f=Xf(Jf[d][jf.Al],b,[]);f&&f.length&&c(f[0].index);DC(b.id,Jf[d],e);var g=Xf(Jf[d][jf.Il],b,[]);g&&g.length&&c(g[0].index)};c(a)}}var VC=!1,TB;function WC(){TB||(TB=new SB);return TB}
function XC(a){var b=a["gtm.uniqueEventId"],c=a["gtm.priorityId"],d=a.event;if(E(109)){}if(d==="gtm.js"){if(VC)return!1;VC=!0}var e=!1,f=IC(),g=md(a,null);if(!f.every(function(t){return t({originalEventData:g})})){if(d!=="gtm.js"&&d!=="gtm.init"&&d!=="gtm.init_consent")return!1;e=!0}sC(b,d);var h=a.eventCallback,m=
a.eventTimeout,n={id:b,priorityId:c,name:d,isBlocked:YC(g,e),Eq:[],logMacroError:function(){L(6);fo(0)},cachedModelValues:ZC(),Pc:new YB(function(){if(E(109)){}h&&h.apply(h,Array.prototype.slice.call(arguments,0))},m),
originalEventData:g};E(120)&&jl&&(n.reportMacroDiscrepancy=xC);E(109)&&EA(n.id);var p=cg(n);E(109)&&FA(n.id);e&&(p=$C(p));E(109)&&DA(b);var q=TC(p,n),r=UC(a,n.Pc);cC(n.Pc);d!=="gtm.js"&&d!=="gtm.sync"||iC();return aD(p,q)||r}function ZC(){var a={};a.event=Ak("event",1);a.ecommerce=Ak("ecommerce",1);a.gtm=Ak("gtm");a.eventModel=Ak("eventModel");return a}
function YC(a,b){var c=LB();return function(d){if(c(d))return!0;var e=d&&d[jf.Ua];if(!e||typeof e!=="string")return!0;e=e.replace(/^_*/,"");var f,g=Em();f=GC().getRestrictions(0,g);var h=a;b&&(h=md(a,null),h["gtm.uniqueEventId"]=Number.MAX_SAFE_INTEGER);for(var m=lk[e]||[],n=l(f),p=n.next();!p.done;p=n.next()){var q=p.value;try{if(!q({entityId:e,securityGroups:m,originalEventData:h}))return!0}catch(r){return!0}}return!1}}
function $C(a){for(var b=[],c=0;c<a.length;c++)if(a[c]){var d=String(Jf[c][jf.Ua]);if(Yj[d]||Jf[c][jf.mo]!==void 0||WB(d,2))b[c]=!0}return b}function aD(a,b){if(!b)return b;for(var c=0;c<a.length;c++)if(a[c]&&Jf[c]&&!Zj[String(Jf[c][jf.Ua])])return!0;return!1};function bD(){WC().addListener("gtm.init",function(a,b){Qj.fa=!0;Pn();b()})};var cD=!1,dD=0,eD=[];function fD(a){if(!cD){var b=z.createEventObject,c=z.readyState==="complete",d=z.readyState==="interactive";if(!a||a.type!=="readystatechange"||c||!b&&d){cD=!0;for(var e=0;e<eD.length;e++)Nc(eD[e])}eD.push=function(){for(var f=Ca.apply(0,arguments),g=0;g<f.length;g++)Nc(f[g]);return 0}}}function gD(){if(!cD&&dD<140){dD++;try{var a,b;(b=(a=z.documentElement).doScroll)==null||b.call(a,"left");fD()}catch(c){x.setTimeout(gD,50)}}}
function hD(){var a=x;cD=!1;dD=0;if(z.readyState==="interactive"&&!z.createEventObject||z.readyState==="complete")fD();else{Lc(z,"DOMContentLoaded",fD);Lc(z,"readystatechange",fD);if(z.createEventObject&&z.documentElement.doScroll){var b=!0;try{b=!a.frameElement}catch(c){}b&&gD()}Lc(a,"load",fD)}}function iD(a){cD?a():eD.push(a)};var jD={},kD={};function lD(a,b){for(var c=[],d=[],e={},f=0;f<a.length;e={wj:void 0,cj:void 0},f++){var g=a[f];if(g.indexOf("-")>=0){if(e.wj=Jp(g,b),e.wj){var h=Dm();pb(h,function(r){return function(t){return r.wj.destinationId===t}}(e))?c.push(g):d.push(g)}}else{var m=jD[g]||[];e.cj={};m.forEach(function(r){return function(t){r.cj[t]=!0}}(e));for(var n=Fm(),p=0;p<n.length;p++)if(e.cj[n[p]]){c=c.concat(Dm());break}var q=kD[g]||[];q.length&&(c=c.concat(q))}}return{qj:c,Xp:d}}
function mD(a){tb(jD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})}function nD(a){tb(kD,function(b,c){var d=c.indexOf(a);d>=0&&c.splice(d,1)})};var oD=!1,pD=!1;function qD(a,b){var c={},d=(c.event=a,c);b&&(d.eventModel=md(b,null),b[J.m.df]&&(d.eventCallback=b[J.m.df]),b[J.m.Kg]&&(d.eventTimeout=b[J.m.Kg]));return d}function rD(a,b){a.hasOwnProperty("gtm.uniqueEventId")||Object.defineProperty(a,"gtm.uniqueEventId",{value:Cp()});b.eventId=a["gtm.uniqueEventId"];b.priorityId=a["gtm.priorityId"];return{eventId:b.eventId,priorityId:b.priorityId}}
function sD(a,b){var c=a&&a[J.m.md];c===void 0&&(c=vk(J.m.md,2),c===void 0&&(c="default"));if(mb(c)||Array.isArray(c)){var d;d=b.isGtmEvent?mb(c)?[c]:c:c.toString().replace(/\s+/g,"").split(",");var e=lD(d,b.isGtmEvent),f=e.qj,g=e.Xp;if(g.length)for(var h=tD(a),m=0;m<g.length;m++){var n=Jp(g[m],b.isGtmEvent);if(n){var p=n.destinationId,q=n.destinationId,r=xm().destination[q];r&&r.state===0||RB(p,h,{source:3,fromContainerExecution:b.fromContainerExecution})}}var t=f.concat(g);return{qj:Kp(f,b.isGtmEvent),
Fo:Kp(t,b.isGtmEvent)}}}var uD=void 0,vD=void 0;function wD(a,b,c){var d=md(a,null);d.eventId=void 0;d.inheritParentConfig=void 0;Object.keys(b).some(function(f){return b[f]!==void 0})&&L(136);var e=md(b,null);md(c,e);Uw(Qw(Fm()[0],e),a.eventId,d)}function tD(a){for(var b=l([J.m.nd,J.m.vc]),c=b.next();!c.done;c=b.next()){var d=c.value,e=a&&a[d]||Jq.C[d];if(e)return e}}
var xD={config:function(a,b){var c=rD(a,b);if(!(a.length<2)&&mb(a[1])){var d={};if(a.length>2){if(a[2]!==void 0&&!ld(a[2])||a.length>3)return;d=a[2]}var e=Jp(a[1],b.isGtmEvent);if(e){var f,g,h;a:{if(!Bm.qe){var m=Hm(wm());if(Qm(m)){var n=m.parent,p=n.isDestination;h={aq:Hm(n),Tp:p};break a}}h=void 0}var q=h;q&&(f=q.aq,g=q.Tp);sC(c.eventId,"gtag.config");var r=e.destinationId,t=e.id!==r;if(t?Dm().indexOf(r)===-1:Fm().indexOf(r)===-1){if(!b.inheritParentConfig&&!d[J.m.Lc]){var u=tD(d);if(t)RB(r,u,{source:2,
fromContainerExecution:b.fromContainerExecution});else if(f!==void 0&&f.containers.indexOf(r)!==-1){var v=d;uD?wD(b,v,uD):vD||(vD=md(v,null))}else OB(r,u,!0,{source:2,fromContainerExecution:b.fromContainerExecution})}}else{if(f&&(L(128),g&&L(130),b.inheritParentConfig)){var w;var y=d;vD?(wD(b,vD,y),w=!1):(!y[J.m.pd]&&bk&&uD||(uD=md(y,null)),w=!0);w&&f.containers&&f.containers.join(",");return}kl&&(Ep===1&&(Hn.mcc=!1),Ep=2);if(bk&&!t&&!d[J.m.pd]){var A=pD;pD=!0;if(A)return}oD||L(43);if(!b.noTargetGroup)if(t){nD(e.id);
var C=e.id,D=d[J.m.Ng]||"default";D=String(D).split(",");for(var G=0;G<D.length;G++){var I=kD[D[G]]||[];kD[D[G]]=I;I.indexOf(C)<0&&I.push(C)}}else{mD(e.id);var M=e.id,T=d[J.m.Ng]||"default";T=T.toString().split(",");for(var da=0;da<T.length;da++){var N=jD[T[da]]||[];jD[T[da]]=N;N.indexOf(M)<0&&N.push(M)}}delete d[J.m.Ng];var W=b.eventMetadata||{};W.hasOwnProperty(Q.A.vd)||(W[Q.A.vd]=!b.fromContainerExecution);b.eventMetadata=W;delete d[J.m.df];for(var ia=t?[e.id]:Dm(),ka=0;ka<ia.length;ka++){var Y=
d,X=ia[ka],ja=md(b,null),wa=Jp(X,ja.isGtmEvent);wa&&Jq.push("config",[Y],wa,ja)}}}}},consent:function(a,b){if(a.length===3){L(39);var c=rD(a,b),d=a[1],e={},f=Io(a[2]),g;for(g in f)if(f.hasOwnProperty(g)){var h=f[g];e[g]=g===J.m.rg?Array.isArray(h)?NaN:Number(h):g===J.m.hc?(Array.isArray(h)?h:[h]).map(Jo):Ko(h)}b.fromContainerExecution||(e[J.m.V]&&L(139),e[J.m.Ka]&&L(140));d==="default"?lp(e):d==="update"?np(e,c):d==="declare"&&b.fromContainerExecution&&kp(e)}},event:function(a,b){var c=a[1];if(!(a.length<
2)&&mb(c)){var d=void 0;if(a.length>2){if(!ld(a[2])&&a[2]!==void 0||a.length>3)return;d=a[2]}var e=qD(c,d),f=rD(a,b),g=f.eventId,h=f.priorityId;e["gtm.uniqueEventId"]=g;h&&(e["gtm.priorityId"]=h);if(c==="optimize.callback")return e.eventModel=e.eventModel||{},e;var m=sD(d,b);if(m){for(var n=m.qj,p=m.Fo,q=p.map(function(M){return M.id}),r=p.map(function(M){return M.destinationId}),t=n.map(function(M){return M.id}),u=l(Dm()),v=u.next();!v.done;v=u.next()){var w=v.value;r.indexOf(w)<0&&t.push(w)}sC(g,
c);for(var y=l(t),A=y.next();!A.done;A=y.next()){var C=A.value,D=md(b,null),G=md(d,null);delete G[J.m.df];var I=D.eventMetadata||{};I.hasOwnProperty(Q.A.vd)||(I[Q.A.vd]=!D.fromContainerExecution);I[Q.A.Ii]=q.slice();I[Q.A.Of]=r.slice();D.eventMetadata=I;Kq(c,G,C,D)}e.eventModel=e.eventModel||{};q.length>0?e.eventModel[J.m.md]=q.join(","):delete e.eventModel[J.m.md];oD||L(43);b.noGtmEvent===void 0&&b.eventMetadata&&b.eventMetadata[Q.A.Fl]&&(b.noGtmEvent=!0);e.eventModel[J.m.Kc]&&(b.noGtmEvent=!0);
return b.noGtmEvent?void 0:e}}},get:function(a,b){L(53);if(a.length===4&&mb(a[1])&&mb(a[2])&&lb(a[3])){var c=Jp(a[1],b.isGtmEvent),d=String(a[2]),e=a[3];if(c){oD||L(43);var f=tD();if(pb(Dm(),function(h){return c.destinationId===h})){rD(a,b);var g={};md((g[J.m.rc]=d,g[J.m.Ic]=e,g),null);Lq(d,function(h){Nc(function(){e(h)})},c.id,b)}else RB(c.destinationId,f,{source:4,fromContainerExecution:b.fromContainerExecution})}}},js:function(a,b){if(a.length===2&&a[1].getTime){oD=!0;var c=rD(a,b),d=c.eventId,
e=c.priorityId,f={};return f.event="gtm.js",f["gtm.start"]=a[1].getTime(),f["gtm.uniqueEventId"]=d,f["gtm.priorityId"]=e,f}},policy:function(a){if(a.length===3&&mb(a[1])&&lb(a[2])){if(ig(a[1],a[2]),L(74),a[1]==="all"){L(75);var b=!1;try{b=a[2](lg.ctid,"unknown",{})}catch(c){}b||L(76)}}else L(73)},set:function(a,b){var c=void 0;a.length===2&&ld(a[1])?c=md(a[1],null):a.length===3&&mb(a[1])&&(c={},ld(a[2])||Array.isArray(a[2])?c[a[1]]=md(a[2],null):c[a[1]]=a[2]);if(c){var d=rD(a,b),e=d.eventId,f=d.priorityId;
md(c,null);var g=md(c,null);Jq.push("set",[g],void 0,b);c["gtm.uniqueEventId"]=e;f&&(c["gtm.priorityId"]=f);delete c.event;b.overwriteModelFields=!0;return c}}},yD={policy:!0};var AD=function(a){if(zD(a))return a;this.value=a};AD.prototype.getUntrustedMessageValue=function(){return this.value};var zD=function(a){return!a||jd(a)!=="object"||ld(a)?!1:"getUntrustedMessageValue"in a};AD.prototype.getUntrustedMessageValue=AD.prototype.getUntrustedMessageValue;var BD=!1,CD=[];function DD(){if(!BD){BD=!0;for(var a=0;a<CD.length;a++)Nc(CD[a])}}function ED(a){BD?Nc(a):CD.push(a)};var FD=0,GD={},HD=[],ID=[],JD=!1,KD=!1;function LD(a,b){return a.messageContext.eventId-b.messageContext.eventId||a.messageContext.priorityId-b.messageContext.priorityId}function MD(a,b,c){a.eventCallback=b;c&&(a.eventTimeout=c);return ND(a)}function OD(a,b){if(!nb(b)||b<0)b=0;var c=yp[Xj],d=0,e=!1,f=void 0;f=x.setTimeout(function(){e||(e=!0,a());f=void 0},b);return function(){var g=c?c.subscribers:1;++d===g&&(f&&(x.clearTimeout(f),f=void 0),e||(a(),e=!0))}}
function PD(a){if(a==null||typeof a!=="object")return!1;if(a.event)return!0;if(ub(a)){var b=a[0];if(b==="config"||b==="event"||b==="js"||b==="get")return!0}return!1}
function QD(){var a;if(ID.length)a=ID.shift();else if(HD.length)a=HD.shift();else return;var b;var c=a;if(JD||!PD(c.message))b=c;else{JD=!0;var d=c.message["gtm.uniqueEventId"],e,f;typeof d==="number"?(e=d-2,f=d-1):(e=Cp(),f=Cp(),c.message["gtm.uniqueEventId"]=Cp());var g={},h={message:(g.event="gtm.init_consent",g["gtm.uniqueEventId"]=e,g),messageContext:{eventId:e}},m={},n={message:(m.event="gtm.init",m["gtm.uniqueEventId"]=f,m),messageContext:{eventId:f}};HD.unshift(n,c);b=h}return b}
function RD(){for(var a=!1,b;!KD&&(b=QD());){KD=!0;delete sk.eventModel;uk();var c=b,d=c.message,e=c.messageContext;if(d==null)KD=!1;else{e.fromContainerExecution&&zk();try{if(lb(d))try{d.call(wk)}catch(G){}else if(Array.isArray(d)){if(mb(d[0])){var f=d[0].split("."),g=f.pop(),h=d.slice(1),m=vk(f.join("."),2);if(m!=null)try{m[g].apply(m,h)}catch(G){}}}else{var n=void 0;if(ub(d))a:{if(d.length&&mb(d[0])){var p=xD[d[0]];if(p&&(!e.fromContainerExecution||!yD[d[0]])){n=p(d,e);break a}}n=void 0}else n=
d;if(n){var q;for(var r=n,t=r._clear||e.overwriteModelFields,u=l(Object.keys(r)),v=u.next();!v.done;v=u.next()){var w=v.value;w!=="_clear"&&(t&&yk(w),yk(w,r[w]))}ik||(ik=r["gtm.start"]);var y=r["gtm.uniqueEventId"];r.event?(typeof y!=="number"&&(y=Cp(),r["gtm.uniqueEventId"]=y,yk("gtm.uniqueEventId",y)),q=XC(r)):q=!1;a=q||a}}}finally{e.fromContainerExecution&&uk(!0);var A=d["gtm.uniqueEventId"];if(typeof A==="number"){for(var C=GD[String(A)]||[],D=0;D<C.length;D++)ID.push(SD(C[D]));C.length&&ID.sort(LD);
delete GD[String(A)];A>FD&&(FD=A)}KD=!1}}}return!a}
function TD(){if(E(109)){var a=!Qj.ma;}var c=RD();if(E(109)){}try{var e=lg.ctid,f=x[Xj].hide;if(f&&f[e]!==void 0&&f.end){f[e]=
!1;var g=!0,h;for(h in f)if(f.hasOwnProperty(h)&&f[h]===!0){g=!1;break}g&&(f.end(),f.end=null)}}catch(m){}return c}function Xw(a){if(FD<a.notBeforeEventId){var b=String(a.notBeforeEventId);GD[b]=GD[b]||[];GD[b].push(a)}else ID.push(SD(a)),ID.sort(LD),Nc(function(){KD||RD()})}function SD(a){return{message:a.message,messageContext:a.messageContext}}
function UD(){function a(f){var g={};if(zD(f)){var h=f;f=zD(h)?h.getUntrustedMessageValue():void 0;g.fromContainerExecution=!0}return{message:f,messageContext:g}}var b=yc(Xj,[]),c=yp[Xj]=yp[Xj]||{};c.pruned===!0&&L(83);GD=Vw().get();Ww();iD(function(){if(!c.gtmDom){c.gtmDom=!0;var f={};b.push((f.event="gtm.dom",f))}});ED(function(){if(!c.gtmLoad){c.gtmLoad=!0;var f={};b.push((f.event="gtm.load",f))}});c.subscribers=(c.subscribers||0)+1;var d=b.push;b.push=function(){var f;if(yp.SANDBOXED_JS_SEMAPHORE>
0){f=[];for(var g=0;g<arguments.length;g++)f[g]=new AD(arguments[g])}else f=[].slice.call(arguments,0);var h=f.map(function(q){return a(q)});HD.push.apply(HD,h);var m=d.apply(b,f),n=Math.max(100,Number("1000")||300);if(this.length>n)for(L(4),c.pruned=!0;this.length>n;)this.shift();var p=typeof m!=="boolean"||m;return RD()&&p};var e=b.slice(0).map(function(f){return a(f)});HD.push.apply(HD,e);if(!Qj.ma){if(E(109)){}Nc(TD)}}var ND=function(a){return x[Xj].push(a)};function VD(a){ND(a)};function WD(){var a,b=Vk(x.location.href);(a=b.hostname+b.pathname)&&Ln("dl",encodeURIComponent(a));var c;var d=lg.ctid;if(d){var e=Bm.qe?1:0,f,g=Hm(wm());f=g&&g.context;c=d+";"+lg.canonicalContainerId+";"+(f&&f.fromContainerExecution?1:0)+";"+(f&&f.source||0)+";"+e}else c=void 0;var h=c;h&&Ln("tdp",h);var m=Kl(!0);m!==void 0&&Ln("frm",String(m))};function XD(){(Vo()||kl)&&x.addEventListener("securitypolicyviolation",function(a){if(a.disposition==="enforce"){L(179);var b=im(a.effectiveDirective);if(b){var c;var d=gm(b,a.blockedURI);c=d?em[b][d]:void 0;if(c){var e;a:{try{var f=new URL(a.blockedURI),g=f.pathname.indexOf(";");e=g>=0?f.origin+f.pathname.substring(0,g):f.origin+f.pathname;break a}catch(v){}e=void 0}var h=e;if(h){for(var m=l(c),n=m.next();!n.done;n=m.next()){var p=n.value;if(!p.zm){p.zm=!0;if(E(59)){var q={eventId:p.eventId,priorityId:p.priorityId};
if(Vo()){var r=q,t={type:1,blockedUrl:h,endpoint:p.endpoint,violation:a.effectiveDirective};if(Vo()){var u=ap("TAG_DIAGNOSTICS",{eventId:r==null?void 0:r.eventId,priorityId:r==null?void 0:r.priorityId});u.tagDiagnostics=t;Uo(u)}}}Rn(p.endpoint)}}hm(b,a.blockedURI)}}}}})};function YD(){var a;var b=Gm();if(b)if(b.canonicalContainerId)a=b.canonicalContainerId;else{var c,d=b.scriptContainerId||((c=b.destinations)==null?void 0:c[0]);a=d?"_"+d:void 0}else a=void 0;var e=a;e&&Ln("pcid",e)};var ZD=/^(https?:)?\/\//;
function $D(){var a=Im();if(a){var b;a:{var c,d=(c=a.scriptElement)==null?void 0:c.src;if(d){var e;try{var f;e=(f=ad())==null?void 0:f.getEntriesByType("resource")}catch(q){}if(e){for(var g=-1,h=l(e),m=h.next();!m.done;m=h.next()){var n=m.value;if(n.initiatorType==="script"&&(g+=1,n.name.replace(ZD,"")===d.replace(ZD,""))){b=g;break a}}L(146)}else L(145)}b=void 0}var p=b;p!==void 0&&(a.canonicalContainerId&&Ln("rtg",String(a.canonicalContainerId)),Ln("slo",String(p)),Ln("hlo",a.htmlLoadOrder||"-1"),
Ln("lst",String(a.loadScriptType||"0")))}else L(144)};function aE(){var a=[],b=Number('')||0,c=Number('')||0;c||(c=b/100);var d=function(){var t=!1;return t}();a.push({Fh:219,studyId:219,experimentId:104948811,
controlId:104948812,controlId2:0,probability:c,active:d,Uf:0});var e=Number('')||0,f=Number('0.01')||0;f||(f=e/100);var g=function(){var t=!1;
return t}();a.push({Fh:220,studyId:220,experimentId:104948813,controlId:104948814,controlId2:0,probability:f,active:g,Uf:0});var h=Number('')||0,m=Number('')||0;m||(m=h/100);var n=function(){var t=!1;return t}();a.push({Fh:195,studyId:195,experimentId:104527906,controlId:104527907,controlId2:104898015,
probability:m,active:n,Uf:1});var p=Number('')||0,q=Number('')||0;q||(q=p/100);var r=function(){var t=!1;return t}();a.push({Fh:196,studyId:196,experimentId:104528500,controlId:104528501,controlId2:104898016,probability:q,active:r,Uf:0});return a};var bE={};function cE(a){for(var b=l(Object.keys(a.exp||{})),c=b.next();!c.done;c=b.next())Qj.R.H.add(Number(c.value))}function dE(a){var b=zn(un.Z.ql);return!!mi[a].active||mi[a].probability>.5||!!(b.exp||{})[mi[a].experimentId]||!!mi[a].active||mi[a].probability>.5||!!(bE.exp||{})[mi[a].experimentId]}
function eE(){for(var a=l(aE()),b=a.next();!b.done;b=a.next()){var c=b.value,d=c.Fh;mi[d]=c;if(c.Uf===1){var e=d,f=zn(un.Z.ql);qi(f,e);cE(f);dE(e)&&B(e)}else if(c.Uf===0){var g=d,h=bE;qi(h,g);cE(h);dE(g)&&B(g)}}};
function zE(){};var AE=function(){};AE.prototype.toString=function(){return"undefined"};var BE=new AE;function IE(){E(212)&&ek&&(ig("all",function(a,b,c){var d=c.options;switch(b){case "detect_link_click_events":case "detect_form_submit_events":return(d==null?void 0:d.waitForTags)!==!0;case "detect_youtube_activity_events":return(d==null?void 0:d.fixMissingApi)!==!0;default:return!0}}),FC(Em(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return WB("__"+b,5)||c.includes("cmpPartners")}))};function JE(a,b){function c(g){var h=Vk(g),m=Pk(h,"protocol"),n=Pk(h,"host",!0),p=Pk(h,"port"),q=Pk(h,"path").toLowerCase().replace(/\/$/,"");if(m===void 0||m==="http"&&p==="80"||m==="https"&&p==="443")m="web",p="default";return[m,n,p,q]}for(var d=c(String(a)),e=c(String(b)),f=0;f<d.length;f++)if(d[f]!==e[f])return!1;return!0}
function KE(a){return LE(a)?1:0}
function LE(a){var b=a.arg0,c=a.arg1;if(a.any_of&&Array.isArray(c)){for(var d=0;d<c.length;d++){var e=md(a,{});md({arg1:c[d],any_of:void 0},e);if(KE(e))return!0}return!1}switch(a["function"]){case "_cn":return Rg(b,c);case "_css":var f;a:{if(b)try{for(var g=0;g<Mg.length;g++){var h=Mg[g];if(b[h]!=null){f=b[h](c);break a}}}catch(m){}f=!1}return f;case "_ew":return Ng(b,c);case "_eq":return Sg(b,c);case "_ge":return Tg(b,c);case "_gt":return Vg(b,c);case "_lc":return Og(b,c);case "_le":return Ug(b,
c);case "_lt":return Wg(b,c);case "_re":return Qg(b,c,a.ignore_case);case "_sw":return Xg(b,c);case "_um":return JE(b,c)}return!1};[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});[2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2,2].reduce(function(a,b){return a+b});var ME=function(a,b,c,d){ar.call(this);this.gh=b;this.Kf=c;this.rb=d;this.Va=new Map;this.hh=0;this.ma=new Map;this.Da=new Map;this.R=void 0;this.H=a};va(ME,ar);ME.prototype.N=function(){delete this.C;this.Va.clear();this.ma.clear();this.Da.clear();this.R&&(Xq(this.H,"message",this.R),delete this.R);delete this.H;delete this.rb;ar.prototype.N.call(this)};
var NE=function(a){if(a.C)return a.C;a.Kf&&a.Kf(a.H)?a.C=a.H:a.C=Jl(a.H,a.gh);var b;return(b=a.C)!=null?b:null},PE=function(a,b,c){if(NE(a))if(a.C===a.H){var d=a.Va.get(b);d&&d(a.C,c)}else{var e=a.ma.get(b);if(e&&e.pj){OE(a);var f=++a.hh;a.Da.set(f,{Dh:e.Dh,Wo:e.fm(c),persistent:b==="addEventListener"});a.C.postMessage(e.pj(c,f),"*")}}},OE=function(a){a.R||(a.R=function(b){try{var c;c=a.rb?a.rb(b):void 0;if(c){var d=c.fq,e=a.Da.get(d);if(e){e.persistent||a.Da.delete(d);var f;(f=e.Dh)==null||f.call(e,
e.Wo,c.payload)}}}catch(g){}},Wq(a.H,"message",a.R))};var QE=function(a,b){var c=b.listener,d=(0,a.__gpp)("addEventListener",c);d&&c(d,!0)},RE=function(a,b){(0,a.__gpp)("removeEventListener",b.listener,b.listenerId)},SE={fm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"addEventListener",version:"1.1"},c},Dh:function(a,b){var c=b.__gppReturn;a(c.returnValue,c.success)}},TE={fm:function(a){return a.listener},pj:function(a,b){var c={};return c.__gppCall={callId:b,command:"removeEventListener",version:"1.1",
parameter:a.listenerId},c},Dh:function(a,b){var c=b.__gppReturn,d=c.returnValue.data;a==null||a(d,c.success)}};function UE(a){var b={};typeof a.data==="string"?b=JSON.parse(a.data):b=a.data;return{payload:b,fq:b.__gppReturn.callId}}
var VE=function(a,b){var c;c=(b===void 0?{}:b).timeoutMs;ar.call(this);this.caller=new ME(a,"__gppLocator",function(d){return typeof d.__gpp==="function"},UE);this.caller.Va.set("addEventListener",QE);this.caller.ma.set("addEventListener",SE);this.caller.Va.set("removeEventListener",RE);this.caller.ma.set("removeEventListener",TE);this.timeoutMs=c!=null?c:500};va(VE,ar);VE.prototype.N=function(){this.caller.dispose();ar.prototype.N.call(this)};
VE.prototype.addEventListener=function(a){var b=this,c=ml(function(){a(WE,!0)}),d=this.timeoutMs===-1?void 0:setTimeout(function(){c()},this.timeoutMs);PE(this.caller,"addEventListener",{listener:function(e,f){clearTimeout(d);try{var g;var h;((h=e.pingData)==null?void 0:h.gppVersion)===void 0||e.pingData.gppVersion==="1"||e.pingData.gppVersion==="1.0"?(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:1,gppString:"GPP_ERROR_STRING_IS_DEPRECATED_SPEC",
applicableSections:[-1]}}):Array.isArray(e.pingData.applicableSections)?g=e:(b.removeEventListener(e.listenerId),g={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_EXPECTED_APPLICATION_SECTION_ARRAY",applicableSections:[-1]}});a(g,f)}catch(m){if(e==null?0:e.listenerId)try{b.removeEventListener(e.listenerId)}catch(n){a(XE,!0);return}a(YE,!0)}}})};
VE.prototype.removeEventListener=function(a){PE(this.caller,"removeEventListener",{listener:function(){},listenerId:a})};
var YE={eventName:"signalStatus",data:"ready",pingData:{internalErrorState:2,gppString:"GPP_ERROR_STRING_UNAVAILABLE",applicableSections:[-1]},listenerId:-1},WE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_LISTENER_REGISTRATION_TIMEOUT",internalErrorState:2,applicableSections:[-1]},listenerId:-1},XE={eventName:"signalStatus",data:"ready",pingData:{gppString:"GPP_ERROR_STRING_REMOVE_EVENT_LISTENER_ERROR",internalErrorState:2,applicableSections:[-1]},listenerId:-1};function ZE(a){var b;if(!(b=a.pingData.signalStatus==="ready")){var c=a.pingData.applicableSections;b=!c||c.length===1&&c[0]===-1}if(b){Dv.gppString=a.pingData.gppString;var d=a.pingData.applicableSections.join(",");Dv.C=d}}function $E(){try{var a=new VE(x,{timeoutMs:-1});NE(a.caller)&&a.addEventListener(ZE)}catch(b){}};function aF(){var a=[["cv",Ui(1)],["rv",Vj],["tc",Jf.filter(function(b){return b}).length]];Wj&&a.push(["x",Wj]);nk()&&a.push(["tag_exp",nk()]);return a};var bF={};function Xi(a){bF[a]=(bF[a]||0)+1}function cF(){for(var a=[],b=l(Object.keys(bF)),c=b.next();!c.done;c=b.next()){var d=c.value;a.push(d+"."+bF[d])}return a.length===0?[]:[["bdm",a.join("~")]]};var dF={},eF={};function fF(a){var b=a.eventId,c=a.Nd,d=[],e=dF[b]||[];e.length&&d.push(["hf",e.join(".")]);var f=eF[b]||[];f.length&&d.push(["ht",f.join(".")]);c&&(delete dF[b],delete eF[b]);return d};function gF(){return!1}function hF(){var a={};return function(b,c,d){}};function iF(){var a=jF;return function(b,c,d){var e=d&&d.event;kF(c);var f=Ch(b)?void 0:1,g=new Ya;tb(c,function(r,t){var u=Cd(t,void 0,f);u===void 0&&t!==void 0&&L(44);g.set(r,u)});a.Nb(ag());var h={Pl:pg(b),eventId:e==null?void 0:e.id,priorityId:e!==void 0?e.priorityId:void 0,Rf:e!==void 0?function(r){e.Pc.Rf(r)}:void 0,Jb:function(){return b},log:function(){},hp:{index:d==null?void 0:d.index,type:d==null?void 0:d.type,name:d==null?void 0:d.name},oq:!!WB(b,3),originalEventData:e==null?void 0:e.originalEventData};
e&&e.cachedModelValues&&(h.cachedModelValues={gtm:e.cachedModelValues.gtm,ecommerce:e.cachedModelValues.ecommerce});if(gF()){var m=hF(),n,p;h.xb={Ej:[],Sf:{},bc:function(r,t,u){t===1&&(n=r);t===7&&(p=u);m(r,t,u)},Bh:Uh()};h.log=function(r){var t=Ca.apply(1,arguments);n&&m(n,4,{level:r,source:p,message:t})}}var q=$e(a,h,[b,g]);a.Nb();q instanceof Fa&&(q.type==="return"?q=q.data:q=void 0);return Bd(q,void 0,f)}}function kF(a){var b=a.gtmOnSuccess,c=a.gtmOnFailure;lb(b)&&(a.gtmOnSuccess=function(){Nc(b)});lb(c)&&(a.gtmOnFailure=function(){Nc(c)})};function lF(a){}lF.M="internal.addAdsClickIds";function mF(a,b){var c=this;}mF.publicName="addConsentListener";var nF=!1;function oF(a){for(var b=0;b<a.length;++b)if(nF)try{a[b]()}catch(c){L(77)}else a[b]()}function pF(a,b,c){var d=this,e;return e}pF.M="internal.addDataLayerEventListener";function qF(a,b,c){}qF.publicName="addDocumentEventListener";function rF(a,b,c,d){}rF.publicName="addElementEventListener";function sF(a){return a.K.ub()};function tF(a){}tF.publicName="addEventCallback";
function IF(a){}IF.M="internal.addFormAbandonmentListener";function JF(a,b,c,d){}
JF.M="internal.addFormData";var KF={},LF=[],MF={},NF=0,OF=0;
function VF(a,b){}VF.M="internal.addFormInteractionListener";
function bG(a,b){}bG.M="internal.addFormSubmitListener";
function gG(a){}gG.M="internal.addGaSendListener";function hG(a){if(!a)return{};var b=a.hp;return VB(b.type,b.index,b.name)}function iG(a){return a?{originatingEntity:hG(a)}:{}};function qG(a){var b=yp.zones;return b?b.getIsAllowedFn(Fm(),a):function(){return!0}}function rG(){var a=yp.zones;a&&a.unregisterChild(Fm())}
function sG(){HC(Em(),function(a){var b=yp.zones;return b?b.isActive(Fm(),a.originalEventData["gtm.uniqueEventId"]):!0});FC(Em(),function(a){var b,c;b=a.entityId;c=a.securityGroups;return qG(Number(a.originalEventData["gtm.uniqueEventId"]))(b,c)})};var tG=function(a,b){this.tagId=a;this.we=b};
function uG(a,b){var c=this;return a}uG.M="internal.loadGoogleTag";function vG(a){return new td("",function(b){var c=this.evaluate(b);if(c instanceof td)return new td("",function(){var d=Ca.apply(0,arguments),e=this,f=md(sF(this),null);f.eventId=a.eventId;f.priorityId=a.priorityId;f.originalEventData=a.originalEventData;var g=d.map(function(m){return e.evaluate(m)}),h=this.K.tb();h.Ld(f);return c.Lb.apply(c,[h].concat(ya(g)))})})};function wG(a,b,c){var d=this;}wG.M="internal.addGoogleTagRestriction";var xG={},yG=[];
function FG(a,b){}
FG.M="internal.addHistoryChangeListener";function GG(a,b,c){}GG.publicName="addWindowEventListener";function HG(a,b){return!0}HG.publicName="aliasInWindow";function IG(a,b,c){}IG.M="internal.appendRemoteConfigParameter";function JG(a){var b;return b}
JG.publicName="callInWindow";function KG(a){}KG.publicName="callLater";function LG(a){}LG.M="callOnDomReady";function MG(a){}MG.M="callOnWindowLoad";function NG(a,b){var c;return c}NG.M="internal.computeGtmParameter";function OG(a,b){var c=this;}OG.M="internal.consentScheduleFirstTry";function PG(a,b){var c=this;}PG.M="internal.consentScheduleRetry";function QG(a){var b;return b}QG.M="internal.copyFromCrossContainerData";function RG(a,b){var c;if(!nh(a)||!sh(b)&&b!==null&&!ih(b))throw F(this.getName(),["string","number|undefined"],arguments);H(this,"read_data_layer",a);c=(b||2)!==2?vk(a,1):xk(a,[x,z]);var d=Cd(c,this.K,Ch(sF(this).Jb())?2:1);d===void 0&&c!==void 0&&L(45);return d}RG.publicName="copyFromDataLayer";
function SG(a){var b=void 0;return b}SG.M="internal.copyFromDataLayerCache";function TG(a){var b;return b}TG.publicName="copyFromWindow";function UG(a){var b=void 0;return Cd(b,this.K,1)}UG.M="internal.copyKeyFromWindow";var VG=function(a){return a===Tm.X.Fa&&ln[a]===Sm.Ia.pe&&!P(J.m.U)};var WG=function(){return"0"},XG=function(a){if(typeof a!=="string")return"";var b=["gclid","dclid","wbraid","_gl"];E(102)&&b.push("gbraid");return Wk(a,b,"0")};var YG={},ZG={},$G={},aH={},bH={},cH={},dH={},eH={},fH={},gH={},hH={},iH={},jH={},kH={},lH={},mH={},nH={},oH={},pH={},qH={},rH={},sH={},tH={},uH={},vH={},wH={},xH=(wH[J.m.Ma]=(YG[2]=[VG],YG),wH[J.m.qf]=(ZG[2]=[VG],ZG),wH[J.m.ef]=($G[2]=[VG],$G),wH[J.m.mi]=(aH[2]=[VG],aH),wH[J.m.ni]=(bH[2]=[VG],bH),wH[J.m.oi]=(cH[2]=[VG],cH),wH[J.m.ri]=(dH[2]=[VG],dH),wH[J.m.si]=(eH[2]=[VG],eH),wH[J.m.wc]=(fH[2]=[VG],fH),wH[J.m.rf]=(gH[2]=[VG],gH),wH[J.m.tf]=(hH[2]=[VG],hH),wH[J.m.uf]=(iH[2]=[VG],iH),wH[J.m.vf]=(jH[2]=
[VG],jH),wH[J.m.wf]=(kH[2]=[VG],kH),wH[J.m.xf]=(lH[2]=[VG],lH),wH[J.m.yf]=(mH[2]=[VG],mH),wH[J.m.zf]=(nH[2]=[VG],nH),wH[J.m.mb]=(oH[1]=[VG],oH),wH[J.m.Zc]=(pH[1]=[VG],pH),wH[J.m.gd]=(qH[1]=[VG],qH),wH[J.m.Xd]=(rH[1]=[VG],rH),wH[J.m.Pe]=(sH[1]=[function(a){return E(102)&&VG(a)}],sH),wH[J.m.hd]=(tH[1]=[VG],tH),wH[J.m.Ca]=(uH[1]=[VG],uH),wH[J.m.Ya]=(vH[1]=[VG],vH),wH),yH={},zH=(yH[J.m.mb]=WG,yH[J.m.Zc]=WG,yH[J.m.gd]=WG,yH[J.m.Xd]=WG,yH[J.m.Pe]=WG,yH[J.m.hd]=function(a){if(!ld(a))return{};var b=md(a,
null);delete b.match_id;return b},yH[J.m.Ca]=XG,yH[J.m.Ya]=XG,yH),AH={},BH={},CH=(BH[Q.A.ib]=(AH[2]=[VG],AH),BH),DH={};var EH=function(a,b,c,d){this.C=a;this.N=b;this.P=c;this.R=d};EH.prototype.getValue=function(a){a=a===void 0?Tm.X.Hb:a;if(!this.N.some(function(b){return b(a)}))return this.P.some(function(b){return b(a)})?this.R(this.C):this.C};EH.prototype.H=function(){return jd(this.C)==="array"||ld(this.C)?md(this.C,null):this.C};
var FH=function(){},GH=function(a,b){this.conditions=a;this.C=b},HH=function(a,b,c){var d,e=((d=a.conditions[b])==null?void 0:d[2])||[],f,g=((f=a.conditions[b])==null?void 0:f[1])||[];return new EH(c,e,g,a.C[b]||FH)},IH,JH;var KH=function(a,b,c){this.eventName=b;this.D=c;this.C={};this.isAborted=!1;this.target=a;this.metadata={};for(var d=c.eventMetadata||{},e=l(Object.keys(d)),f=e.next();!f.done;f=e.next()){var g=f.value;S(this,g,d[g])}},Tv=function(a,b){var c,d;return(c=a.C[b])==null?void 0:(d=c.getValue)==null?void 0:d.call(c,R(a,Q.A.Pf))},U=function(a,b,c){var d=a.C,e;c===void 0?e=void 0:(IH!=null||(IH=new GH(xH,zH)),e=HH(IH,b,c));d[b]=e};
KH.prototype.mergeHitDataForKey=function(a,b){var c,d,e;c=(d=this.C[a])==null?void 0:(e=d.H)==null?void 0:e.call(d);if(!c)return U(this,a,b),!0;if(!ld(c))return!1;U(this,a,ma(Object,"assign").call(Object,c,b));return!0};var LH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.C)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.C[e])==null?void 0:(h=(g=f).H)==null?void 0:h.call(g)}return b};
KH.prototype.copyToHitData=function(a,b,c){var d=O(this.D,a);d===void 0&&(d=b);if(d!==void 0&&c!==void 0&&mb(d)&&E(92))try{d=c(d)}catch(e){}d!==void 0&&U(this,a,d)};
var R=function(a,b){var c=a.metadata[b];if(b===Q.A.Pf){var d;return c==null?void 0:(d=c.H)==null?void 0:d.call(c)}var e;return c==null?void 0:(e=c.getValue)==null?void 0:e.call(c,R(a,Q.A.Pf))},S=function(a,b,c){var d=a.metadata,e;c===void 0?e=void 0:(JH!=null||(JH=new GH(CH,DH)),e=HH(JH,b,c));d[b]=e},MH=function(a,b){b=b===void 0?{}:b;for(var c=l(Object.keys(a.metadata)),d=c.next();!d.done;d=c.next()){var e=d.value,f=void 0,g=void 0,h=void 0;b[e]=(f=a.metadata[e])==null?void 0:(h=(g=f).H)==null?void 0:
h.call(g)}return b},mw=function(a,b,c){var d=ax(a.target.destinationId);return d&&d[b]!==void 0?d[b]:c};function NH(a,b){var c;return c}NH.M="internal.copyPreHit";function OH(a,b){var c=null;return Cd(c,this.K,2)}OH.publicName="createArgumentsQueue";function PH(a){return Cd(function(c){var d=eC();if(typeof c==="function")d(function(){c(function(f,g,h){var m=
eC(),n=m&&m.getByName&&m.getByName(f);return(new x.gaplugins.Linker(n)).decorate(g,h)})});else if(Array.isArray(c)){var e=String(c[0]).split(".");b[e.length===1?e[0]:e[1]]&&d.apply(null,c)}else if(c==="isLoaded")return!!d.loaded},this.K,1)}PH.M="internal.createGaCommandQueue";function QH(a){return Cd(function(){if(!lb(e.push))throw Error("Object at "+a+" in window is not an array.");e.push.apply(e,Array.prototype.slice.call(arguments,0))},this.K,
Ch(sF(this).Jb())?2:1)}QH.publicName="createQueue";function RH(a,b){var c=null;if(!nh(a)||!oh(b))throw F(this.getName(),["string","string|undefined"],arguments);try{var d=(b||"").split("").filter(function(e){return"ig".indexOf(e)>=0}).join("");c=new yd(new RegExp(a,d))}catch(e){}return c}RH.M="internal.createRegex";function SH(a){}SH.M="internal.declareConsentState";function TH(a){var b="";return b}TH.M="internal.decodeUrlHtmlEntities";function UH(a,b,c){var d;return d}UH.M="internal.decorateUrlWithGaCookies";function VH(){}VH.M="internal.deferCustomEvents";function WH(a){var b;return b}WH.M="internal.detectUserProvidedData";
function aI(a,b){return f}aI.M="internal.enableAutoEventOnClick";
function iI(a,b){return p}iI.M="internal.enableAutoEventOnElementVisibility";function jI(){}jI.M="internal.enableAutoEventOnError";var kI={},lI=[],mI={},nI=0,oI=0;
function uI(a,b){var c=this;return d}uI.M="internal.enableAutoEventOnFormInteraction";
function zI(a,b){var c=this;return f}zI.M="internal.enableAutoEventOnFormSubmit";
function EI(){var a=this;}EI.M="internal.enableAutoEventOnGaSend";var FI={},GI=[];
function NI(a,b){var c=this;return f}NI.M="internal.enableAutoEventOnHistoryChange";var OI=["http://","https://","javascript:","file://"];
function SI(a,b){var c=this;return h}SI.M="internal.enableAutoEventOnLinkClick";var TI,UI;
function eJ(a,b){var c=this;return d}eJ.M="internal.enableAutoEventOnScroll";function fJ(a){return function(){if(a.limit&&a.sj>=a.limit)a.yh&&x.clearInterval(a.yh);else{a.sj++;var b=Ab();ND({event:a.eventName,"gtm.timerId":a.yh,"gtm.timerEventNumber":a.sj,"gtm.timerInterval":a.interval,"gtm.timerLimit":a.limit,"gtm.timerStartTime":a.Fm,"gtm.timerCurrentTime":b,"gtm.timerElapsedTime":b-a.Fm,"gtm.triggers":a.Kq})}}}
function gJ(a,b){
return f}gJ.M="internal.enableAutoEventOnTimer";var oc=Aa(["data-gtm-yt-inspected-"]),iJ=["www.youtube.com","www.youtube-nocookie.com"],jJ,kJ=!1;
function uJ(a,b){var c=this;return e}uJ.M="internal.enableAutoEventOnYouTubeActivity";kJ=!1;function vJ(a,b){if(!nh(a)||!hh(b))throw F(this.getName(),["string","Object|undefined"],arguments);var c=b?Bd(b):{},d=a,e=!1;return e}vJ.M="internal.evaluateBooleanExpression";var wJ;function xJ(a){var b=!1;return b}xJ.M="internal.evaluateMatchingRules";function gK(){return ur(7)&&ur(9)&&ur(10)};function bL(a,b,c,d){}bL.M="internal.executeEventProcessor";function cL(a){var b;return Cd(b,this.K,1)}cL.M="internal.executeJavascriptString";function dL(a){var b;return b};function eL(a){var b="";return b}eL.M="internal.generateClientId";function fL(a){var b={};return Cd(b)}fL.M="internal.getAdsCookieWritingOptions";function gL(a,b){var c=!1;return c}gL.M="internal.getAllowAdPersonalization";function hL(){var a;return a}hL.M="internal.getAndResetEventUsage";function iL(a,b){b=b===void 0?!0:b;var c;return c}iL.M="internal.getAuid";var jL=null;
function kL(){var a=new Ya;return a}
kL.publicName="getContainerVersion";function lL(a,b){b=b===void 0?!0:b;var c;return c}lL.publicName="getCookieValues";function mL(){var a="";return a}mL.M="internal.getCorePlatformServicesParam";function nL(){return no()}nL.M="internal.getCountryCode";function oL(){var a=[];return Cd(a)}oL.M="internal.getDestinationIds";function pL(a){var b=new Ya;return b}pL.M="internal.getDeveloperIds";function qL(a){var b;return b}qL.M="internal.getEcsidCookieValue";function rL(a,b){var c=null;return c}rL.M="internal.getElementAttribute";function sL(a){var b=null;return b}sL.M="internal.getElementById";function tL(a){var b="";return b}tL.M="internal.getElementInnerText";function uL(a,b){var c=null;return Cd(c)}uL.M="internal.getElementProperty";function vL(a){var b;return b}vL.M="internal.getElementValue";function wL(a){var b=0;return b}wL.M="internal.getElementVisibilityRatio";function xL(a){var b=null;return b}xL.M="internal.getElementsByCssSelector";
function yL(a){var b;if(!nh(a))throw F(this.getName(),["string"],arguments);H(this,"read_event_data",a);var c;a:{var d=a,e=sF(this).originalEventData;if(e){for(var f=e,g={},h={},m={},n=[],p=d.split("\\\\"),q=0;q<p.length;q++){for(var r=p[q].split("\\."),t=0;t<r.length;t++){for(var u=r[t].split("."),v=0;v<u.length;v++)n.push(u[v]),v!==u.length-1&&n.push(m);t!==r.length-1&&n.push(h)}q!==p.length-1&&n.push(g)}for(var w=[],y="",A=l(n),C=A.next();!C.done;C=
A.next()){var D=C.value;D===m?(w.push(y),y=""):y=D===g?y+"\\":D===h?y+".":y+D}y&&w.push(y);for(var G=l(w),I=G.next();!I.done;I=G.next()){if(f==null){c=void 0;break a}f=f[I.value]}c=f}else c=void 0}b=Cd(c,this.K,1);return b}yL.M="internal.getEventData";var zL={};zL.disableUserDataWithoutCcd=E(223);zL.enableDecodeUri=E(92);zL.enableGaAdsConversions=E(122);zL.enableGaAdsConversionsClientId=E(121);zL.enableOverrideAdsCps=E(170);zL.enableUrlDecodeEventUsage=E(139);function AL(){return Cd(zL)}AL.M="internal.getFlags";function BL(){var a;return a}BL.M="internal.getGsaExperimentId";function CL(){return new yd(BE)}CL.M="internal.getHtmlId";function DL(a){var b;return b}DL.M="internal.getIframingState";function EL(a,b){var c={};return Cd(c)}EL.M="internal.getLinkerValueFromLocation";function FL(){var a=new Ya;return a}FL.M="internal.getPrivacyStrings";function GL(a,b){var c;return c}GL.M="internal.getProductSettingsParameter";function HL(a,b){var c;return c}HL.publicName="getQueryParameters";function IL(a,b){var c;return c}IL.publicName="getReferrerQueryParameters";function JL(a){var b="";if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_referrer",a);b=Rk(Vk(z.referrer),a);return b}JL.publicName="getReferrerUrl";function KL(){return oo()}KL.M="internal.getRegionCode";function LL(a,b){var c;return c}LL.M="internal.getRemoteConfigParameter";function ML(){var a=new Ya;a.set("width",0);a.set("height",0);return a}ML.M="internal.getScreenDimensions";function NL(){var a="";return a}NL.M="internal.getTopSameDomainUrl";function OL(){var a="";return a}OL.M="internal.getTopWindowUrl";function PL(a){var b="";if(!oh(a))throw F(this.getName(),["string|undefined"],arguments);H(this,"get_url",a);b=Pk(Vk(x.location.href),a);return b}PL.publicName="getUrl";function QL(){H(this,"get_user_agent");return uc.userAgent}QL.M="internal.getUserAgent";function RL(){var a;return a?Cd(Wy(a)):a}RL.M="internal.getUserAgentClientHints";function ZL(){var a=x;return a.gaGlobal=a.gaGlobal||{}}function $L(){var a=ZL();a.hid=a.hid||qb();return a.hid}function aM(a,b){var c=ZL();if(c.vid===void 0||b&&!c.from_cookie)c.vid=a,c.from_cookie=b};
function yM(a){(ny(a)||pk())&&U(a,J.m.Qk,oo()||no());!ny(a)&&pk()&&U(a,J.m.bl,"::")}function zM(a){if(pk()&&!ny(a)&&(ro()||U(a,J.m.Ek,!0),E(78))){gw(a);hw(a,Gp.Bf.Vm,Lo(O(a.D,J.m.cb)));var b=Gp.Bf.Wm;var c=O(a.D,J.m.Hc);hw(a,b,c===!0?1:c===!1?0:void 0);hw(a,Gp.Bf.Um,Lo(O(a.D,J.m.zb)));hw(a,Gp.Bf.Sm,Fs(Ko(O(a.D,J.m.ob)),Ko(O(a.D,J.m.Sb))))}};var UM={AW:un.Z.Mm,G:un.Z.Xn,DC:un.Z.Vn};function VM(a){var b=ej(a);return""+gs(b.map(function(c){return c.value}).join("!"))}function WM(a){var b=Jp(a);return b&&UM[b.prefix]}function XM(a,b){var c=a[b];c&&(c.clearTimerId&&x.clearTimeout(c.clearTimerId),c.clearTimerId=x.setTimeout(function(){delete a[b]},36E5))};var BN=window,CN=document,DN=function(a){var b=BN._gaUserPrefs;if(b&&b.ioo&&b.ioo()||CN.documentElement.hasAttribute("data-google-analytics-opt-out")||a&&BN["ga-disable-"+a]===!0)return!0;try{var c=BN.external;if(c&&c._gaUserPrefs&&c._gaUserPrefs=="oo")return!0}catch(p){}for(var d=[],e=String(CN.cookie).split(";"),f=0;f<e.length;f++){var g=e[f].split("="),h=g[0].replace(/^\s*|\s*$/g,"");if(h&&h=="AMP_TOKEN"){var m=g.slice(1).join("=").replace(/^\s*|\s*$/g,"");m&&(m=decodeURIComponent(m));d.push(m)}}for(var n=
0;n<d.length;n++)if(d[n]=="$OPT_OUT")return!0;return CN.getElementById("__gaOptOutExtension")?!0:!1};
function PN(a){tb(a,function(c){c.charAt(0)==="_"&&delete a[c]});var b=a[J.m.Xb]||{};tb(b,function(c){c.charAt(0)==="_"&&delete b[c]})};function xO(a,b){}function yO(a,b){var c=function(){};return c}
function zO(a,b,c){};var AO=yO;function CO(a,b,c){var d=this;}CO.M="internal.gtagConfig";
function EO(a,b){}
EO.publicName="gtagSet";function FO(){var a={};return a};function GO(a){}GO.M="internal.initializeServiceWorker";function HO(a,b){}HO.publicName="injectHiddenIframe";var IO=function(){var a=0;return function(b){switch(b){case 1:a|=1;break;case 2:a|=2;break;case 3:a|=4}return a}}();
function JO(a,b,c,d,e){}JO.M="internal.injectHtml";var NO={};
function PO(a,b,c,d){}var QO={dl:1,id:1},RO={};
function SO(a,b,c,d){}E(160)?SO.publicName="injectScript":PO.publicName="injectScript";SO.M="internal.injectScript";function TO(){return so()}TO.M="internal.isAutoPiiEligible";function UO(a){var b=!0;return b}UO.publicName="isConsentGranted";function VO(a){var b=!1;return b}VO.M="internal.isDebugMode";function WO(){return qo()}WO.M="internal.isDmaRegion";function XO(a){var b=!1;return b}XO.M="internal.isEntityInfrastructure";function YO(a){var b=!1;return b}YO.M="internal.isFeatureEnabled";function ZO(){var a=!1;return a}ZO.M="internal.isFpfe";function $O(){var a=!1;return a}$O.M="internal.isGcpConversion";function aP(){var a=!1;return a}aP.M="internal.isLandingPage";function bP(){var a=!1;return a}bP.M="internal.isOgt";function cP(){var a;return a}cP.M="internal.isSafariPcmEligibleBrowser";function dP(){var a=Ph(function(b){sF(this).log("error",b)});a.publicName="JSON";return a};function eP(a){var b=void 0;if(!nh(a))throw F(this.getName(),["string"],arguments);b=Vk(a);return Cd(b)}eP.M="internal.legacyParseUrl";function fP(){return!1}
var gP={getItem:function(a){var b=null;return b},setItem:function(a,b){return!1},removeItem:function(a){}};function hP(){}hP.publicName="logToConsole";function iP(a,b){}iP.M="internal.mergeRemoteConfig";function jP(a,b,c){c=c===void 0?!0:c;var d=[];return Cd(d)}jP.M="internal.parseCookieValuesFromString";function kP(a){var b=void 0;if(typeof a!=="string")return;a&&Gb(a,"//")&&(a=z.location.protocol+a);if(typeof URL==="function"){var c;a:{var d;try{d=new URL(a)}catch(w){c=void 0;break a}for(var e={},f=Array.from(d.searchParams),g=0;g<f.length;g++){var h=f[g][0],m=f[g][1];e.hasOwnProperty(h)?typeof e[h]==="string"?e[h]=[e[h],m]:e[h].push(m):e[h]=m}c=Cd({href:d.href,origin:d.origin,protocol:d.protocol,username:d.username,password:d.password,host:d.host,
hostname:d.hostname,port:d.port,pathname:d.pathname,search:d.search,searchParams:e,hash:d.hash})}return c}var n;try{n=Vk(a)}catch(w){return}if(!n.protocol||!n.host)return;var p={};if(n.search)for(var q=n.search.replace("?","").split("&"),r=0;r<q.length;r++){var t=q[r].split("="),u=t[0],v=Ok(t.splice(1).join("="))||"";v=v.replace(/\+/g," ");p.hasOwnProperty(u)?typeof p[u]==="string"?p[u]=[p[u],v]:p[u].push(v):p[u]=v}n.searchParams=p;n.origin=n.protocol+"//"+n.host;n.username="";n.password="";b=Cd(n);
return b}kP.publicName="parseUrl";function lP(a){}lP.M="internal.processAsNewEvent";function mP(a,b,c){var d;return d}mP.M="internal.pushToDataLayer";function nP(a){var b=Ca.apply(1,arguments),c=!1;return c}nP.publicName="queryPermission";function oP(a){var b=this;}oP.M="internal.queueAdsTransmission";function pP(a,b){var c=void 0;return c}pP.publicName="readAnalyticsStorage";function qP(){var a="";return a}qP.publicName="readCharacterSet";function rP(){return Xj}rP.M="internal.readDataLayerName";function sP(){var a="";return a}sP.publicName="readTitle";function tP(a,b){var c=this;}tP.M="internal.registerCcdCallback";function uP(a,b){return!0}uP.M="internal.registerDestination";var vP=["config","event","get","set"];function wP(a,b,c){}wP.M="internal.registerGtagCommandListener";function xP(a,b){var c=!1;return c}xP.M="internal.removeDataLayerEventListener";function yP(a,b){}
yP.M="internal.removeFormData";function zP(){}zP.publicName="resetDataLayer";function AP(a,b,c){var d=void 0;return d}AP.M="internal.scrubUrlParams";function BP(a){}BP.M="internal.sendAdsHit";function CP(a,b,c,d){}CP.M="internal.sendGtagEvent";function DP(a,b,c){}DP.publicName="sendPixel";function EP(a,b){}EP.M="internal.setAnchorHref";function FP(a){}FP.M="internal.setContainerConsentDefaults";function GP(a,b,c,d){var e=this;d=d===void 0?!0:d;var f=!1;
return f}GP.publicName="setCookie";function HP(a){}HP.M="internal.setCorePlatformServices";function IP(a,b){}IP.M="internal.setDataLayerValue";function JP(a){}JP.publicName="setDefaultConsentState";function KP(a,b){}KP.M="internal.setDelegatedConsentType";function LP(a,b){}LP.M="internal.setFormAction";function MP(a,b,c){c=c===void 0?!1:c;}MP.M="internal.setInCrossContainerData";function NP(a,b,c){return!1}NP.publicName="setInWindow";function OP(a,b,c){}OP.M="internal.setProductSettingsParameter";function PP(a,b,c){}PP.M="internal.setRemoteConfigParameter";function QP(a,b){}QP.M="internal.setTransmissionMode";function RP(a,b,c,d){var e=this;}RP.publicName="sha256";function SP(a,b,c){}
SP.M="internal.sortRemoteConfigParameters";function TP(a){}TP.M="internal.storeAdsBraidLabels";function UP(a,b){var c=void 0;return c}UP.M="internal.subscribeToCrossContainerData";var VP={},WP={};VP.getItem=function(a){var b=null;return b};VP.setItem=function(a,b){};
VP.removeItem=function(a){};VP.clear=function(){};VP.publicName="templateStorage";function XP(a,b){var c=!1;return c}XP.M="internal.testRegex";function YP(a){var b;return b};function ZP(a,b){var c;return c}ZP.M="internal.unsubscribeFromCrossContainerData";function $P(a){}$P.publicName="updateConsentState";function aQ(a){var b=!1;return b}aQ.M="internal.userDataNeedsEncryption";var bQ;function cQ(a,b,c){bQ=bQ||new $h;bQ.add(a,b,c)}function dQ(a,b){var c=bQ=bQ||new $h;if(c.C.hasOwnProperty(a))throw Error("Attempting to add a private function which already exists: "+a+".");if(c.contains(a))throw Error("Attempting to add a private function with an existing API name: "+a+".");c.C[a]=lb(b)?vh(a,b):wh(a,b)}
function eQ(){return function(a){var b;var c=bQ;if(c.contains(a))b=c.get(a,this);else{var d;if(d=c.C.hasOwnProperty(a)){var e=this.K.ub();if(e){var f=!1,g=e.Jb();if(g){Ch(g)||(f=!0);}d=f}else d=!0}if(d){var h=c.C.hasOwnProperty(a)?c.C[a]:void 0;
b=h}else throw Error(a+" is not a valid API name.");}return b}};function fQ(){var a=function(c){return void dQ(c.M,c)},b=function(c){return void cQ(c.publicName,c)};b(mF);b(tF);b(HG);b(JG);b(KG);b(RG);b(TG);b(OH);b(dP());b(QH);b(kL);b(lL);b(HL);b(IL);b(JL);b(PL);b(EO);b(HO);b(UO);b(hP);b(kP);b(nP);b(qP);b(sP);b(DP);b(GP);b(JP);b(NP);b(RP);b(VP);b($P);cQ("Math",Ah());cQ("Object",Yh);cQ("TestHelper",bi());cQ("assertApi",xh);cQ("assertThat",yh);cQ("decodeUri",Dh);cQ("decodeUriComponent",Eh);cQ("encodeUri",Fh);cQ("encodeUriComponent",Gh);cQ("fail",Lh);cQ("generateRandom",
Mh);cQ("getTimestamp",Nh);cQ("getTimestampMillis",Nh);cQ("getType",Oh);cQ("makeInteger",Qh);cQ("makeNumber",Rh);cQ("makeString",Sh);cQ("makeTableMap",Th);cQ("mock",Wh);cQ("mockObject",Xh);cQ("fromBase64",dL,!("atob"in x));cQ("localStorage",gP,!fP());cQ("toBase64",YP,!("btoa"in x));a(lF);a(pF);a(JF);a(VF);a(bG);a(gG);a(wG);a(FG);a(IG);a(LG);a(MG);a(NG);a(OG);a(PG);a(QG);a(SG);a(UG);a(NH);a(PH);a(RH);a(SH);a(TH);a(UH);a(VH);a(WH);a(aI);a(iI);a(jI);a(uI);a(zI);a(EI);a(NI);a(SI);a(eJ);a(gJ);a(uJ);a(vJ);
a(xJ);a(bL);a(cL);a(eL);a(fL);a(gL);a(hL);a(iL);a(nL);a(oL);a(pL);a(qL);a(rL);a(sL);a(tL);a(uL);a(vL);a(wL);a(xL);a(yL);a(AL);a(BL);a(CL);a(DL);a(EL);a(FL);a(GL);a(KL);a(LL);a(ML);a(NL);a(OL);a(RL);a(CO);a(GO);a(JO);a(SO);a(TO);a(VO);a(WO);a(XO);a(YO);a(ZO);a($O);a(aP);a(bP);a(cP);a(eP);a(uG);a(iP);a(jP);a(lP);a(mP);a(oP);a(rP);a(tP);a(uP);a(wP);a(xP);a(yP);a(AP);a(BP);a(CP);a(EP);a(FP);a(HP);a(IP);a(KP);a(LP);a(MP);a(OP);a(PP);a(QP);a(SP);a(TP);a(UP);a(XP);a(ZP);a(aQ);dQ("internal.IframingStateSchema",
FO());
E(104)&&a(mL);E(160)?b(SO):b(PO);E(177)&&b(pP);return eQ()};var jF;
function gQ(){var a=data.sandboxed_scripts,b=data.security_groups;a:{var c=data.runtime||[],d=data.runtime_lines;jF=new Xe;hQ();Ff=iF();var e=jF,f=fQ(),g=new ud("require",f);g.Wa();e.C.C.set("require",g);Sa.set("require",g);for(var h=[],m=0;m<c.length;m++){var n=c[m];if(!Array.isArray(n)||n.length<3){if(n.length===0)continue;break a}d&&d[m]&&d[m].length&&$f(n,d[m]);try{jF.execute(n),E(120)&&jl&&n[0]===50&&h.push(n[1])}catch(r){}}E(120)&&(Sf=h)}if(a&&a.length)for(var p=0;p<a.length;p++){var q=a[p].replace(/^_*/,
"");lk[q]=["sandboxedScripts"]}iQ(b)}function hQ(){jF.Vc(function(a,b,c){yp.SANDBOXED_JS_SEMAPHORE=yp.SANDBOXED_JS_SEMAPHORE||0;yp.SANDBOXED_JS_SEMAPHORE++;try{return a.apply(b,c)}finally{yp.SANDBOXED_JS_SEMAPHORE--}})}function iQ(a){a&&tb(a,function(b,c){for(var d=0;d<c.length;d++){var e=c[d].replace(/^_*/,"");lk[e]=lk[e]||[];lk[e].push(b)}})};function jQ(a){Uw(Ow("developer_id."+a,!0),0,{})};var kQ=Array.isArray;function lQ(a,b){return md(a,b||null)}function V(a){return window.encodeURIComponent(a)}function mQ(a,b,c){Kc(a,b,c)}
function nQ(a){var b=["veinteractive.com","ve-interactive.cn"];if(!a)return!1;var c=Pk(Vk(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var f=c.length-e.length;f>0&&e.charAt(0)!=="."&&(f--,e="."+e);if(f>=0&&c.indexOf(e,f)===f)return!0}}return!1}function oQ(a,b,c){for(var d={},e=!1,f=0;a&&f<a.length;f++)a[f]&&a[f].hasOwnProperty(b)&&a[f].hasOwnProperty(c)&&(d[a[f][b]]=a[f][c],e=!0);return e?d:null}
function pQ(a,b){var c={};if(a)for(var d in a)a.hasOwnProperty(d)&&(c[d]=a[d]);if(b){var e=oQ(b,"parameter","parameterValue");e&&(c=lQ(e,c))}return c}function qQ(a,b,c){return a===void 0||a===c?b:a}function rQ(a,b,c){return Gc(a,b,c,void 0)}function sQ(){return x.location.href}function tQ(a,b){return vk(a,b||2)}function uQ(a,b){x[a]=b}function vQ(a,b,c){var d=x;b&&(d[a]===void 0||c&&!d[a])&&(d[a]=b);return d[a]}

var wQ={};var Z={securityGroups:{}};

Z.securityGroups.get_referrer=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_referrer=b;Z.__get_referrer.F="get_referrer";Z.__get_referrer.isVendorTemplate=!0;Z.__get_referrer.priorityOverride=0;Z.__get_referrer.isInfrastructure=!1;Z.__get_referrer["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),
b.vtp_query&&c.push("query"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!mb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!mb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+
h);}}else if(c)throw e(f,{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();
Z.securityGroups.read_event_data=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_event_data=b;Z.__read_event_data.F="read_event_data";Z.__read_event_data.isVendorTemplate=!0;Z.__read_event_data.priorityOverride=0;Z.__read_event_data.isInfrastructure=!1;Z.__read_event_data["5"]=!1})(function(b){var c=b.vtp_eventDataAccess,d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(g!=null&&!mb(g))throw e(f,{key:g},"Key must be a string.");if(c!=="any"){try{if(c===
"specific"&&g!=null&&Lg(g,d))return}catch(h){throw e(f,{key:g},"Invalid key filter.");}throw e(f,{key:g},"Prohibited read from event data.");}},T:a}})}();

Z.securityGroups.read_data_layer=["google"],function(){function a(b,c){return{key:c}}(function(b){Z.__read_data_layer=b;Z.__read_data_layer.F="read_data_layer";Z.__read_data_layer.isVendorTemplate=!0;Z.__read_data_layer.priorityOverride=0;Z.__read_data_layer.isInfrastructure=!1;Z.__read_data_layer["5"]=!1})(function(b){var c=b.vtp_allowedKeys||"specific",d=b.vtp_keyPatterns||[],e=b.vtp_createPermissionError;return{assert:function(f,g){if(!mb(g))throw e(f,{},"Keys must be strings.");if(c!=="any"){try{if(Lg(g,
d))return}catch(h){throw e(f,{},"Invalid key filter.");}throw e(f,{},"Prohibited read from data layer variable: "+g+".");}},T:a}})}();









Z.securityGroups.get_url=["google"],function(){function a(b,c,d){return{component:c,queryKey:d}}(function(b){Z.__get_url=b;Z.__get_url.F="get_url";Z.__get_url.isVendorTemplate=!0;Z.__get_url.priorityOverride=0;Z.__get_url.isInfrastructure=!1;Z.__get_url["5"]=!1})(function(b){var c=b.vtp_urlParts==="any"?null:[];c&&(b.vtp_protocol&&c.push("protocol"),b.vtp_host&&c.push("host"),b.vtp_port&&c.push("port"),b.vtp_path&&c.push("path"),b.vtp_extension&&c.push("extension"),b.vtp_query&&c.push("query"),b.vtp_fragment&&
c.push("fragment"));var d=c&&b.vtp_queriesAllowed!=="any"?b.vtp_queryKeys||[]:null,e=b.vtp_createPermissionError;return{assert:function(f,g,h){if(g){if(!mb(g))throw e(f,{},"URL component must be a string.");if(c&&c.indexOf(g)<0)throw e(f,{},"Prohibited URL component: "+g);if(g==="query"&&d){if(!h)throw e(f,{},"Prohibited from getting entire URL query when query keys are specified.");if(!mb(h))throw e(f,{},"Query key must be a string.");if(d.indexOf(h)<0)throw e(f,{},"Prohibited query key: "+h);}}else if(c)throw e(f,
{},"Prohibited from getting entire URL when components are specified.");},T:a}})}();










var Bp={dataLayer:wk,callback:function(a){kk.hasOwnProperty(a)&&lb(kk[a])&&kk[a]();delete kk[a]},bootstrap:0};
function xQ(){Ap();Km();QB();Eb(lk,Z.securityGroups);var a=Hm(wm()),b,c=a==null?void 0:(b=a.context)==null?void 0:b.source;$o(c,a==null?void 0:a.parent);c!==2&&c!==4&&c!==3||L(142);Rf={Oo:fg}}var yQ=!1;
function ko(){try{if(yQ||!Rm()){Tj();Qj.P=Ti(18,"");
Qj.rb="ad_storage|analytics_storage|ad_user_data|ad_personalization";Qj.Va="ad_storage|analytics_storage|ad_user_data";Qj.Da="57f0";Qj.Da="57f0";if(E(109)){}Pa[7]=!0;var a=zp("debugGroupId",function(){return String(Math.floor(Number.MAX_SAFE_INTEGER*Math.random()))});gp(a);xp();$E();nr();Dp();if(Lm()){rG();GC().removeExternalRestrictions(Em());}else{Pf();Lf=Z;Mf=KE;hg=new og;gQ();xQ();IE();io||(ho=mo());
up();UD();hD();BD=!1;z.readyState==="complete"?DD():Lc(x,"load",DD);bD();jl&&(qq(Eq),x.setInterval(Dq,864E5),qq(aF),qq(tC),qq(gA),qq(Hq),qq(fF),qq(EC),E(120)&&(qq(yC),qq(zC),qq(AC)),bF={},qq(cF),Wi());kl&&(Vn(),Xp(),WD(),$D(),YD(),Ln("bt",String(Qj.C?2:Qj.N?1:0)),Ln("ct",String(Qj.C?0:Qj.N?1:3)),XD());
zE();fo(1);sG();eE();jk=Ab();Bp.bootstrap=jk;Qj.ma&&TD();E(109)&&CA();E(134)&&(typeof x.name==="string"&&Gb(x.name,"web-pixel-sandbox-CUSTOM")&&bd()?jQ("dMDg0Yz"):x.Shopify&&(jQ("dN2ZkMj"),bd()&&jQ("dNTU0Yz")))}}}catch(b){fo(4),Aq()}}
(function(a){function b(){n=z.documentElement.getAttribute("data-tag-assistant-present");No(n)&&(m=h.Wk)}function c(){m&&xc?g(m):a()}if(!x[Ti(37,"__TAGGY_INSTALLED")]){var d=!1;if(z.referrer){var e=Vk(z.referrer);d=Rk(e,"host")===Ti(38,"cct.google")}if(!d){var f=ps(Ti(39,"googTaggyReferrer"));d=!(!f.length||!f[0].length)}d&&(x[Ti(37,"__TAGGY_INSTALLED")]=!0,Gc(Ti(40,"https://cct.google/taggy/agent.js")))}var g=function(u){var v="GTM",w="GTM";ek&&(v="OGT",w="GTAG");
var y=Ti(23,"google.tagmanager.debugui2.queue"),A=x[y];A||(A=[],x[y]=A,Gc("https://"+Uj.wg+"/debug/bootstrap?id="+lg.ctid+"&src="+w+"&cond="+String(u)+"&gtm="+Pr()));var C={messageType:"CONTAINER_STARTING",data:{scriptSource:xc,containerProduct:v,debug:!1,id:lg.ctid,targetRef:{ctid:lg.ctid,isDestination:Cm()},aliases:Fm(),destinations:Dm()}};C.data.resume=function(){a()};Uj.Rm&&(C.data.initialPublish=!0);A.push(C)},h={ao:1,Zk:2,vl:3,Vj:4,Wk:5};h[h.ao]="GTM_DEBUG_LEGACY_PARAM";h[h.Zk]="GTM_DEBUG_PARAM";h[h.vl]="REFERRER";
h[h.Vj]="COOKIE";h[h.Wk]="EXTENSION_PARAM";var m=void 0,n=void 0,p=Pk(x.location,"query",!1,void 0,"gtm_debug");No(p)&&(m=h.Zk);if(!m&&z.referrer){var q=Vk(z.referrer);Rk(q,"host")===Ti(24,"tagassistant.google.com")&&(m=h.vl)}if(!m){var r=ps("__TAG_ASSISTANT");r.length&&r[0].length&&(m=h.Vj)}m||b();if(!m&&Mo(n)){var t=!1;Lc(z,"TADebugSignal",function(){t||(t=!0,b(),c())},!1);x.setTimeout(function(){t||(t=!0,b(),c())},200)}else c()})(function(){E(83)&&yQ&&!mo()["0"]?jo():ko()});

})()

