(function(a){var f,e,d=this;if(typeof define==="function"&&define.amd){try{define(["sortablejs","jquery"],function(g,h){f=g;e=h;b();a(g,h);});}catch(c){b();}return;}else{if(typeof exports==="object"){try{f=require("sortablejs");e=require("jquery");}catch(c){}}}if(typeof jQuery==="function"||typeof $==="function"){e=jQuery||$;}if(typeof Sortable!=="undefined"){f=Sortable;}function b(){if(!e){throw new Error("jQuery is required for jquery-sortablejs");}if(!f){throw new Error("SortableJS is required for jquery-sortablejs (https://github.com/SortableJS/Sortable)");}}b();a(f,e);})(function(a,b){b.fn.sortable=function(d){var e,c=arguments;this.each(function(){var f=b(this),g=f.data("sortable");if(!g&&(d instanceof Object||!d)){g=new a(this,d);f.data("sortable",g);}else{if(g){if(d==="destroy"){g.destroy();f.removeData("sortable");}else{if(d==="widget"){e=g;}else{if(typeof g[d]==="function"){e=g[d].apply(g,[].slice.call(c,1));}else{if(d in g.options){e=g.option.apply(g,c);}}}}}}});return(e===void 0)?this:e;};});