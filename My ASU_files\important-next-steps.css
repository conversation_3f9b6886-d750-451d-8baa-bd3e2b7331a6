.important-next-steps {
	margin-left: 10px;
}

.important-next-steps-list .enrollmentNextSteps {
	text-decoration: none;
	border-bottom: none;
}


.important-next-steps-list .enrollmentNextStepsEnabledButton {
   background-image: -webkit-linear-gradient(top, rgba(255,200,105,1) 50%,rgba(255,166,0,1) 100%);
   background-image:    -moz-linear-gradient(top, rgba(255,200,105,1) 50%,rgba(255,166,0,1) 100%);
   background-image:     -ms-linear-gradient(top, rgba(255,200,105,1) 50%,rgba(255,166,0,1) 100%);
   background-image:      -o-linear-gradient(top, rgba(255,200,105,1) 50%,rgba(255,166,0,1) 100%);
   background-image:         linear-gradient(top, rgba(255,200,105,1) 50%,rgba(255,166,0,1) 100%);
   border: solid 1px rgba(170,170,170,1) ;
   padding: 5px;
   display: inline-block;
   font-size: 0.833em;
   line-height: 10px;
   text-align: center;
   color: rgba(89,89,89,1) ;
   font-weight: bold;
   text-transform: uppercase;
   width: 90%;
}

.important-next-steps-list .enrollmentNextStepsEnabledButton:hover {
   background-image: -webkit-linear-gradient(top, rgba(254,167,15,1) 50%,rgba(228,150,0,1) 100%);
   background-image:    -moz-linear-gradient(top, rgba(254,167,15,1) 50%,rgba(228,150,0,1) 100%);
   background-image:     -ms-linear-gradient(top, rgba(254,167,15,1) 50%,rgba(228,150,0,1) 100%);
   background-image:      -o-linear-gradient(top, rgba(254,167,15,1) 50%,rgba(228,150,0,1) 100%);
   background-image:         linear-gradient(top, rgba(254,167,15,1) 50%,rgba(228,150,0,1) 100%);
   border: solid 1px rgba(110,110,110,1) ;
}

ul.important-next-steps-list {
	padding-left: 0.5em;
}	

ul.important-next-steps-list > li {
	margin: 1em 0;
	list-style-type: none;
}

ul.important-next-steps-list > li.checked {
	list-style-image: url('/myasu/images/icons/checkbox_complete.png');
}

ul.important-next-steps-list > li.unchecked {
	list-style-image: url('/myasu/images/icons/checkbox_empty.png');
}

.nextStepsRolldown {
	padding: 6px;
	width: 90%;
}