var i20TrackerView = {
	"inProgress": function() {
		return this.current && this.status != "Completed";
	},
	"isComplete": function() {
		return this.status == "Completed";
	},
	"needsAttention": function() {
		return this.status == "Attention";
	},
	"isIncomplete": function() {
		return this.status == "Incomplete";
	},
	"needsAttention": function() {
		return this.status == "Attention";
	},
	"isIncomplete": function() {
		return this.status == "Incomplete";
	},
	"isTaskTypeLink": function () {
		return this.type == "link";
	},
	"isTaskTypeText": function () {
		return this.type == "text";
	},
	"isTaskTypeDownload": function () {
		return this.type === "download";
	},
}

$(document).ready(function() {
	if (document.getElementById("i20_tracker_content")) {
		$.ajax({
			url: "i20data",
			success: function(data) {
				if (data.showTracker) {
					var i20Tracker = {};
					
					var step1Complete = false;
					
					if ((data.submitFinancialGuaranteeSubstepShows == false || data.submitFinancialGuaranteeSubstepCompleted)
							&& (data.submitFinancialDocumentsSubstepShows == false || data.submitFinancialDocumentsSubstepCompleted)
							&& (data.submitPassportSubstepShows == false || data.submitPassportSubstepCompleted)
							&& (data.submitSevisTransferFormSubstepShows == false  || data.submitSevisTransferFormSubstepCompleted)) {
						step1Complete = true;
					}
					
					var step2Complete = false;
					
					if (step1Complete && data.i20StatusSubstepCompleted) {
						step2Complete = true;
					}
					
					var step3Complete = false;
					
					if (step2Complete && data.hasBeenShipped) {
						step3Complete = true;
					}
	
					i20Tracker.current_step = step1Complete ? "2" : "1";
					i20Tracker.current_step = step2Complete && step1Complete ? "3" : i20Tracker.current_step;
					
					i20Tracker.i20_tracker_step_data = [];
						
					var step1 = {
						"step": "1",
						"title": "Submit I-20 Information",
						"status": step1Complete ? "Completed" : "Incomplete",
						"current": i20Tracker.current_step == "1" ? true : false,
						"is_last_step": false
					}
					
					if (step1Complete) {
						step1.step_header_class = "step_complete";
					} else if (i20Tracker.current_step == "1") {
						step1.step_header_class = "step_current";
					} else if (!step1Complete) {
						step1.step_header_class = "step_incomplete";
					}
					
					var step1Tasks = [];
					
					var step1NumberOfTasks = 1;
					
					if (data.submitFinancialGuaranteeSubstepShows) {
						var task = null;
						
						if (data.submitFinancialGuaranteeSubstepCompleted) {
							task = createTextTask(step1NumberOfTasks++,
									data.submitFinancialGuaranteeSubstepCompleted ? "Completed" : "Incomplete",
									data.submitFinancialGuaranteeSubstepCompleted ? "Completed" : "Incomplete",
									"Financial Guarantee",
									false);
						} else {
							var links = [
							    {
									url: "https://students.asu.edu/forms/FinancialGuarantee",
									url_text: "Download Financial Guarantee Form",
									newTab: true
							    },
							    {
									url: "https://webapp4.asu.edu/admissionsupload/I20 ",
									url_text: "Submit Financial Guarantee Form",
							    }
							];
							
							task = createLinkTask(step1NumberOfTasks++,
									data.submitFinancialGuaranteeSubstepCompleted ? "Completed" : "Incomplete",
									data.submitFinancialGuaranteeSubstepCompleted ? "Completed" : "Incomplete",
									links,
									false);
						}
						
						step1Tasks.push(task);
					}
					
					if (data.submitFinancialDocumentsSubstepShows) {
						var task = null;
						
						if (data.submitFinancialDocumentsSubstepCompleted) {
							task = createTextTask(step1NumberOfTasks++,
									data.submitFinancialDocumentsSubstepCompleted ? "Completed" : "Incomplete",
									data.submitFinancialDocumentsSubstepCompleted ? "Completed" : "Incomplete",
									"Financial Documents",
									false);
						} else {
							var links = [
							    {
									url: "https://webapp4.asu.edu/admissionsupload/I20",
									url_text: "Submit Financial Documents",
							    }
							];
							
							task = createLinkTask(step1NumberOfTasks++,
									data.submitFinancialDocumentsSubstepCompleted ? "Completed" : "Incomplete",
									data.submitFinancialDocumentsSubstepCompleted ? "Completed" : "Incomplete",
									links,
									false);
						}
						
						step1Tasks.push(task);
					}
					
					if (data.submitPassportSubstepShows) {
						var task = null;
						
						if (data.submitPassportSubstepCompleted) {
							task = createTextTask(step1NumberOfTasks++,
									data.submitPassportSubstepCompleted ? "Completed" : "Incomplete",
									data.submitPassportSubstepCompleted ? "Completed" : "Incomplete",
									"Passport",
									false);
						} else {
							var links = [
							    {
									url: "https://webapp4.asu.edu/admissionsupload/I20",
									url_text: "Submit Passport",
							    }
							];
							
							task = createLinkTask(step1NumberOfTasks++,
									data.submitPassportSubstepCompleted ? "Completed" : "Incomplete",
									data.submitPassportSubstepCompleted ? "Completed" : "Incomplete",
									links,
									false);
						}
						
						step1Tasks.push(task);
					}
					
					if (data.submitSevisTransferFormSubstepShows) {
						var task = null;
						
						if (data.submitSevisTransferFormSubstepCompleted) {
							task = createTextTask(step1NumberOfTasks++,
									data.submitSevisTransferFormSubstepCompleted ? "Completed" : "Incomplete",
									data.submitSevisTransferFormSubstepCompleted ? "Completed" : "Incomplete",
									"SEVIS Transfer Form",
									false);
						} else {
							var links = [
								{
									url: "https://students.asu.edu/forms/sevis-transfer-form",
									url_text: "Download SEVIS Transfer Form",
									newTab: true
								},
							    {
									url: "https://webapp4.asu.edu/admissionsupload/I20",
									url_text: "Submit SEVIS Transfer Form",
							    }
							];
							
							task = createLinkTask(step1NumberOfTasks++,
									data.submitSevisTransferFormSubstepCompleted ? "Completed" : "Incomplete",
									data.submitSevisTransferFormSubstepCompleted ? "Completed" : "Incomplete",
									links,
									false);
						}
						
						step1Tasks.push(task);
					}
					
					step1.tasks = step1Tasks;
					
					i20Tracker.i20_tracker_step_data.push(step1);
					
					var step2 = {
						"step": "2",
						"title": "ASU Review and Approval",
						"status": step1Complete && step2Complete ? "Completed" : "Incomplete",
						"current": i20Tracker.current_step == "2" ? true : false,
						"is_last_step": false
					}
					
					if (step1Complete && step2Complete) {
						step2.step_header_class = "step_complete";
					} else if (i20Tracker.current_step == "2") {
						step2.step_header_class = "step_current";
					} else if (!step1Complete || !step2Complete) {
						step2.step_header_class = "step_incomplete";
					}
					
					var step2Tasks = [];
					
					var step2NumberOfTasks = 1;
					
					if (step2Complete && data.i20StatusSubstepReviewStatus == "approved") {
						var isLastTask = true;
						
						if (data.i20StatusSubstepNeededDocuments) {
							isLastTask = false;
						}
						
						var task = createTextTask(step2NumberOfTasks++,
								"Completed",
								"Status: Approved",
								null,
								isLastTask);
						
						step2Tasks.push(task);
					} else if (data.i20StatusSubstepReviewStatus == "documents-needed") {
						var isLastTask = true;
						
						if (data.i20StatusSubstepNeededDocuments) {
							isLastTask = false;
						}
						
						var task = createTextTask(step2NumberOfTasks++,
								"Attention",
								"Status: Documents Needed",
								"Please submit the document(s) below for review and approval.",
								isLastTask);
						
						step2Tasks.push(task);
					} else if (data.i20StatusSubstepReviewStatus == "under-review") {
						var isLastTask = true;
						
						if (data.i20StatusSubstepNeededDocuments) {
							isLastTask = false;
						}
						
						var task = createTextTask(step2NumberOfTasks++,
								"Incomplete",
								"Status: Under Review",
								"Your I-20 is under review. This process typically takes 5-7 business days. During peak processing times (March-July), it may take up to 4 weeks. If approved, your I-20 will be emailed to your asu.edu email address.",
								isLastTask);
						
						step2Tasks.push(task);
						
						var task = createTextTask(step2NumberOfTasks++,
								"Incomplete",
								"Additional Actions: Pending Review",
								"If your application has invalid documents then you will need to re-submit some I-20 information. Documents needed for the re-submission will show up here.",
								isLastTask);
						
						step2Tasks.push(task);
					} else {
						var isLastTask = true;
						
						if (data.i20StatusSubstepNeededDocuments) {
							isLastTask = false;
						}
						
						var task = createTextTask(step2NumberOfTasks++,
								"Incomplete",
								"Status: Pending Review",
								"Your documents will be reviewed for I-20 issuance after you are fully admitted to ASU.",
								isLastTask);
						
						step2Tasks.push(task);
						
						var task = createTextTask(step2NumberOfTasks++,
								"Incomplete",
								"Additional Actions: Pending Review",
								"If your application has invalid documents then you will need to re-submit some I-20 information. Documents needed for the re-submission will show up here.",
								isLastTask);
						
						step2Tasks.push(task);
					}
					
					if (data.i20StatusSubstepNeededDocuments) {
						data.i20StatusSubstepNeededDocuments.forEach(function(neededDocument) {
							var links = [
							    {
									url: neededDocument.actionUrl,
									url_text: neededDocument.linkText,
							    }
							];
							
							var task = createLinkTask(step2NumberOfTasks++,
									"Incomplete",
									"Incomplete",
									links,
									false);
	
							step2Tasks.push(task);
						});
					}
					
					step2.tasks = step2Tasks;
					
					i20Tracker.i20_tracker_step_data.push(step2);
					
					var step3 = {
						"step": "3",
						"title": "Download I-20",
						"status": step1Complete && step2Complete && step3Complete ? "Completed" : "Incomplete",
						"current": i20Tracker.current_step == "3" ? true : false,
						"is_last_step": true
					}
					
					if (step1Complete && step2Complete && step3Complete) {
						step3.step_header_class = "step_complete";
					} else if (i20Tracker.current_step == "3") {
						step3.step_header_class = "step_current";
					} else if (!step1Complete || !step2Complete || !step3Complete) {
						step3.step_header_class = "step_incomplete";
					}
					
					var step3Tasks = [];
					
					var step3NumberOfTasks = 1;

					if (!data.hasBeenShipped) {
						step3Tasks.push(createTextTask(step3NumberOfTasks++,
							"Incomplete",
							"Status: Not available",
							"A link to download your I-20 document will display here once it is available.",
							true));
					} else {
						step3Tasks.push(createDownloadTask(step1NumberOfTasks++,
							"Completed",
							"Available starting " + new Date(data.shippedDate).toDateString(),
							"https://myissc.asu.edu/istart/controllers/client/ClientEngine.cfm?serviceid=DocumentListProvider",
							true));
					}

					step3.tasks = step3Tasks;
					
					i20Tracker.i20_tracker_step_data.push(step3);
					
					var i20StepsTemplate = $("#i20_steps_tpl").html();
					Mustache.parse(i20StepsTemplate);
					$("#i20_tracker_content").html(Mustache.render(i20StepsTemplate, $.extend(i20Tracker, i20TrackerView)));
					
					$("#i20_section").show();
				}
			}
		});
		
		$("#i20_tracker_content").on("click", ".step_header", function() {
			var step = $(this).data("step");
			
			$("#step_container_" + step).find(".step_header_title_expand").toggle();
			$("#step_container_" + step).find(".step_header_title_collapse").toggle();
			$("#tasks_content_container_" + step).toggle();
		});
	}
});

function createTextTask(taskNumber, status, statusText, text, isLastTask) {
	var task = {};
	task.task = taskNumber;
	task.status = status;
	task.statusText = statusText;

	task.type = "text";
	
	if (text) {
		task.text = text;
	}
	
	task.is_last_task = isLastTask;
	
	return task;
}

function createLinkTask(taskNumber, status, statusText, links, isLastTask) {
	var task = {};
	task.task = taskNumber;
	task.status = status;
	task.statusText = statusText;

	task.type = "link";
	task.links = links;
	
	task.is_last_task = isLastTask;
	
	return task;
}

function createDownloadTask(taskNumber, status, statusText, downloadUrl, isLastTask) {
	var task = {};
	task.task = taskNumber;
	task.status = status;
	task.statusText = statusText;

	task.type = "download";
	task.downloadUrl = downloadUrl;
	
	task.is_last_task = isLastTask;
	
	return task;
}
